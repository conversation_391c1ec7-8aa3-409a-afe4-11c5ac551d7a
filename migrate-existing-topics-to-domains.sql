-- Migration script to organize existing topics into domains
-- This script should be run after the domain schema has been created

-- First, let's see what topics we have
-- SELECT title, description FROM public.topics ORDER BY title;

-- Update existing topics to assign them to appropriate domains
-- Cybersecurity Foundations Domain
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'cybersecurity-foundations'
  AND title IN (
    'Cybersecurity Awareness Skill',
    'Security Best Practices',
    'Phishing Awareness',
    'Password Security',
    'Social Engineering Awareness',
    'Mobile Device Security',
    'Home Office Security',
    'ISC2 Certification',
    'CISSP Fundamentals',
    'Security Architecture',
    'Asset Security',
    'Security Engineering',
    'Communication and Network Security',
    'Identity and Access Management',
    'Security Assessment and Testing',
    'Security Operations',
    'Software Development Security'
  );

-- Network Security Domain
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'network-security'
  AND title IN (
    'Network Security Fundamentals',
    'Advanced Network Security',
    'Firewall Management',
    'Network Protocols',
    'VPN Technologies',
    'Network Monitoring',
    'Intrusion Detection Systems',
    'Network Access Control'
  );

-- Cloud Security Domain
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'cloud-security'
  AND title IN (
    'Cloud Security Basics',
    'AWS Security',
    'Azure Security',
    'Multi-Cloud Security',
    'Cloud Compliance',
    'Container Security',
    'Serverless Security'
  );

-- Incident Response Domain
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'incident-response'
  AND title IN (
    'Incident Response',
    'Digital Forensics',
    'Malware Analysis',
    'Threat Hunting',
    'Security Operations Center',
    'Incident Management',
    'Crisis Communication'
  );

-- Ethical Hacking Domain (renamed from Penetration Testing)
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'ethical-hacking'
  AND title IN (
    'Penetration Testing',
    'Ethical Hacking',
    'Vulnerability Assessment',
    'Web Application Testing',
    'Network Penetration Testing',
    'Social Engineering',
    'Red Team Operations',
    'Linux Fundamentals',
    'Web Security',
    'Web Application Security'
  );

-- Governance & Compliance Domain
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'governance-compliance'
  AND title IN (
    'Risk Management',
    'Compliance and Governance',
    'Security Frameworks',
    'ISO 27001',
    'NIST Framework',
    'SOX Compliance',
    'GDPR Compliance',
    'NDPR Compliance',
    'Security Policies'
  );

-- Cryptography Domain
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'cryptography'
  AND title IN (
    'Cryptography',
    'PKI Implementation',
    'Encryption Protocols',
    'Digital Signatures',
    'Hash Functions',
    'Key Management',
    'Blockchain Security'
  );

-- Security Awareness Domain (basic awareness topics only)
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'security-awareness'
  AND title IN (
    'Cybersecurity Awareness Skill',
    'Security Best Practices',
    'Phishing Awareness',
    'Password Security',
    'Social Engineering Awareness',
    'Mobile Device Security',
    'Home Office Security'
  );

-- CISSP Preparation Domain (specific CISSP content)
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'cissp-preparation'
  AND title IN (
    'CISSP Fundamentals',
    'ISC2 Certification'
  );

-- Digital Forensics Domain
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'digital-forensics'
  AND title IN (
    'Digital Forensics',
    'Evidence Collection',
    'Forensic Analysis',
    'Mobile Forensics',
    'Network Forensics',
    'Memory Forensics',
    'Legal Aspects of Forensics'
  );

-- Application Security Domain
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'application-security'
  AND title IN (
    'Application Security',
    'Secure Coding',
    'OWASP Top 10',
    'Web Application Security',
    'API Security',
    'Code Review',
    'Security Testing',
    'DevSecOps'
  );

-- Handle any remaining topics that don't fit into specific domains
-- Assign them to Cybersecurity Foundations as a default
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'cybersecurity-foundations'
  AND domain_id IS NULL;

-- Create basic learning paths for each domain using existing topics
-- Cybersecurity Foundations Learning Path
INSERT INTO public.path_topics (learning_path_id, topic_id, sort_order, is_required)
SELECT
  dlp.id,
  t.id,
  ROW_NUMBER() OVER (ORDER BY t.title),
  true
FROM public.topics t
JOIN public.domains d ON t.domain_id = d.id
JOIN public.domain_learning_paths dlp ON d.id = dlp.domain_id
WHERE d.slug = 'cybersecurity-foundations'
  AND dlp.name = 'Security Fundamentals'
  AND t.title IN ('Cybersecurity Awareness Skill', 'Security Best Practices', 'Password Security')
ON CONFLICT (learning_path_id, topic_id) DO NOTHING;

-- Network Security Learning Path
INSERT INTO public.path_topics (learning_path_id, topic_id, sort_order, is_required)
SELECT
  dlp.id,
  t.id,
  ROW_NUMBER() OVER (ORDER BY t.title),
  true
FROM public.topics t
JOIN public.domains d ON t.domain_id = d.id
JOIN public.domain_learning_paths dlp ON d.id = dlp.domain_id
WHERE d.slug = 'network-security'
  AND dlp.name = 'Network Fundamentals'
  AND t.title IN ('Network Security Fundamentals', 'Network Protocols', 'Firewall Management')
ON CONFLICT (learning_path_id, topic_id) DO NOTHING;

-- Cloud Security Learning Path
INSERT INTO public.path_topics (learning_path_id, topic_id, sort_order, is_required)
SELECT
  dlp.id,
  t.id,
  ROW_NUMBER() OVER (ORDER BY t.title),
  true
FROM public.topics t
JOIN public.domains d ON t.domain_id = d.id
JOIN public.domain_learning_paths dlp ON d.id = dlp.domain_id
WHERE d.slug = 'cloud-security'
  AND dlp.name = 'Cloud Security Basics'
  AND t.title IN ('Cloud Security Basics', 'AWS Security', 'Azure Security')
ON CONFLICT (learning_path_id, topic_id) DO NOTHING;

-- Ethical Hacking Learning Path
INSERT INTO public.path_topics (learning_path_id, topic_id, sort_order, is_required)
SELECT
  dlp.id,
  t.id,
  ROW_NUMBER() OVER (ORDER BY t.title),
  true
FROM public.topics t
JOIN public.domains d ON t.domain_id = d.id
JOIN public.domain_learning_paths dlp ON d.id = dlp.domain_id
WHERE d.slug = 'ethical-hacking'
  AND dlp.name = 'Ethical Hacking Basics'
  AND t.title IN ('Ethical Hacking', 'Linux Fundamentals', 'Web Security')
ON CONFLICT (learning_path_id, topic_id) DO NOTHING;

-- Security Awareness Learning Path
INSERT INTO public.path_topics (learning_path_id, topic_id, sort_order, is_required)
SELECT
  dlp.id,
  t.id,
  ROW_NUMBER() OVER (ORDER BY t.title),
  true
FROM public.topics t
JOIN public.domains d ON t.domain_id = d.id
JOIN public.domain_learning_paths dlp ON d.id = dlp.domain_id
WHERE d.slug = 'security-awareness'
  AND dlp.name = 'Cybersecurity Basics'
ON CONFLICT (learning_path_id, topic_id) DO NOTHING;

-- CISSP Preparation Learning Paths
INSERT INTO public.path_topics (learning_path_id, topic_id, sort_order, is_required)
SELECT
  dlp.id,
  t.id,
  ROW_NUMBER() OVER (ORDER BY t.title),
  true
FROM public.topics t
JOIN public.domains d ON t.domain_id = d.id
JOIN public.domain_learning_paths dlp ON d.id = dlp.domain_id
WHERE d.slug = 'cissp-preparation'
  AND dlp.name = 'CISSP Domain 1-4'
  AND t.title IN ('CISSP Fundamentals', 'ISC2 Certification')
ON CONFLICT (learning_path_id, topic_id) DO NOTHING;

-- Add more learning path mappings as needed...

-- Verify the migration
SELECT 
  d.name as domain_name,
  COUNT(t.id) as topic_count,
  COUNT(DISTINCT dlp.id) as learning_path_count
FROM public.domains d
LEFT JOIN public.topics t ON d.id = t.domain_id
LEFT JOIN public.domain_learning_paths dlp ON d.id = dlp.domain_id
GROUP BY d.id, d.name, d.sort_order
ORDER BY d.sort_order;

-- Show topics that might need manual review
SELECT 
  t.title,
  t.description,
  d.name as assigned_domain
FROM public.topics t
LEFT JOIN public.domains d ON t.domain_id = d.id
ORDER BY d.name, t.title;
