/**
 * Quiz Session Integration Tests
 * Tests the integration between QuizRandomizationService and QuizSession model
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { QuizRandomizationService } from '../quiz-randomization-service';
import { QuizSession } from '@/models/QuizSession';

// Mock the dependencies
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(),
          order: vi.fn(() => ({
            limit: vi.fn()
          })),
          is: vi.fn(() => ({
            gt: vi.fn(() => ({
              order: vi.fn()
            }))
          }))
        })),
        in: vi.fn()
      })),
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn()
        }))
      })),
      update: vi.fn(() => ({
        eq: vi.fn()
      })),
      delete: vi.fn(() => ({
        eq: vi.fn()
      }))
    })),
    rpc: vi.fn()
  }
}));

vi.mock('@/models/QuizSession');

describe('Quiz Session Integration', () => {
  const mockQuestions = [
    {
      id: 'q1',
      topic_id: 'topic-1',
      question_text: 'What is 2+2?',
      options: { '0': 'Three', '1': 'Four', '2': 'Five', '3': 'Six' },
      correct_answer: '1',
      explanation: 'Basic math',
      usage_count: 0,
      last_used: null,
      correct_answer_rate: null,
      created_at: '2025-01-01T10:00:00Z',
      updated_at: '2025-01-01T10:00:00Z'
    },
    {
      id: 'q2',
      topic_id: 'topic-1',
      question_text: 'What is 3+3?',
      options: { '0': 'Five', '1': 'Six', '2': 'Seven', '3': 'Eight' },
      correct_answer: '1',
      explanation: 'Basic math',
      usage_count: 0,
      last_used: null,
      correct_answer_rate: null,
      created_at: '2025-01-01T10:00:00Z',
      updated_at: '2025-01-01T10:00:00Z'
    }
  ];

  const mockSessionData = {
    id: 'session-123',
    user_id: 'user-456',
    topic_id: 'topic-1',
    questions_data: {
      questions: [
        {
          id: 'q1',
          originalCorrectIndex: 1,
          shuffledCorrectIndex: 2,
          optionMapping: [2, 0, 1, 3]
        }
      ],
      metadata: {
        totalQuestions: 1,
        topicId: 'topic-1',
        createdAt: '2025-01-01T10:00:00Z'
      }
    },
    quiz_length: 2,
    created_at: '2025-01-01T10:00:00Z',
    expires_at: '2025-01-01T12:00:00Z',
    completed_at: null,
    score: null,
    total_questions: 2,
    time_taken: null
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Session Creation Integration', () => {
    it('should create session through randomization service', async () => {
      // Mock QuizSession.create
      const mockSession = {
        id: 'session-123',
        userId: 'user-456',
        topicId: 'topic-1',
        quizLength: 2
      };

      (QuizSession.create as any).mockResolvedValue(mockSession);

      // Mock question selection
      vi.spyOn(QuizRandomizationService, 'selectRandomQuestions').mockResolvedValue(mockQuestions);

      const result = await QuizRandomizationService.generateQuizSession('topic-1', 'user-456', 2);

      expect(result.session).toBe(mockSession);
      expect(result.sessionId).toBe('session-123');
      expect(result.questions).toHaveLength(2);
      expect(QuizSession.create).toHaveBeenCalledWith({
        userId: 'user-456',
        topicId: 'topic-1',
        questionsData: expect.objectContaining({
          questions: expect.arrayContaining([
            expect.objectContaining({
              id: expect.any(String),
              originalCorrectIndex: expect.any(Number),
              shuffledCorrectIndex: expect.any(Number),
              optionMapping: expect.any(Array)
            })
          ]),
          metadata: expect.objectContaining({
            totalQuestions: 2,
            topicId: 'topic-1'
          })
        }),
        quizLength: 2
      });
    });

    it('should handle session creation failure gracefully', async () => {
      // Mock QuizSession.create to throw error
      (QuizSession.create as any).mockRejectedValue(new Error('Database error'));

      // Mock question selection
      vi.spyOn(QuizRandomizationService, 'selectRandomQuestions').mockResolvedValue(mockQuestions);

      await expect(
        QuizRandomizationService.generateQuizSession('topic-1', 'user-456', 2)
      ).rejects.toThrow('Database error');
    });
  });

  describe('Session Retrieval Integration', () => {
    it('should retrieve session through randomization service', async () => {
      // Mock QuizSession.findById
      const mockSession = {
        id: 'session-123',
        userId: 'user-456',
        topicId: 'topic-1',
        questionsData: mockSessionData.questions_data,
        validate: () => ({ isValid: true, isExpired: false, isCompleted: false })
      };

      (QuizSession.findById as any).mockResolvedValue(mockSession);

      // Mock question fetch from database
      const mockSupabaseResponse = {
        data: mockQuestions,
        error: null
      };

      const { supabase } = await import('@/integrations/supabase/client');
      (supabase.from as any).mockReturnValue({
        select: vi.fn().mockReturnValue({
          in: vi.fn().mockResolvedValue(mockSupabaseResponse)
        })
      });

      const result = await QuizRandomizationService.getQuizSession('session-123', 'user-456');

      expect(result).not.toBeNull();
      expect(result?.session).toBe(mockSession);
      expect(result?.sessionId).toBe('session-123');
      expect(QuizSession.findById).toHaveBeenCalledWith('session-123', 'user-456');
    });

    it('should return null for invalid session', async () => {
      // Mock QuizSession.findById to return invalid session
      const mockSession = {
        validate: () => ({ isValid: false, isExpired: true, isCompleted: false, message: 'Session expired' })
      };

      (QuizSession.findById as any).mockResolvedValue(mockSession);

      const result = await QuizRandomizationService.getQuizSession('session-123', 'user-456');

      expect(result).toBeNull();
    });

    it('should return null for non-existent session', async () => {
      (QuizSession.findById as any).mockResolvedValue(null);

      const result = await QuizRandomizationService.getQuizSession('nonexistent', 'user-456');

      expect(result).toBeNull();
    });
  });

  describe('Session Completion Integration', () => {
    it('should complete session through randomization service', async () => {
      // Mock QuizSession.findById and complete
      const mockSession = {
        complete: vi.fn().mockResolvedValue(true)
      };

      (QuizSession.findById as any).mockResolvedValue(mockSession);

      const success = await QuizRandomizationService.completeQuizSession('session-123', 'user-456', 8, 300);

      expect(success).toBe(true);
      expect(QuizSession.findById).toHaveBeenCalledWith('session-123', 'user-456');
      expect(mockSession.complete).toHaveBeenCalledWith(8, 300);
    });

    it('should handle completion failure gracefully', async () => {
      // Mock QuizSession.findById to return null
      (QuizSession.findById as any).mockResolvedValue(null);

      const success = await QuizRandomizationService.completeQuizSession('session-123', 'user-456', 8, 300);

      expect(success).toBe(false);
    });
  });

  describe('Session Management Integration', () => {
    it('should get active sessions through randomization service', async () => {
      const mockSessions = [
        { id: 'session-1', userId: 'user-456' },
        { id: 'session-2', userId: 'user-456' }
      ];

      (QuizSession.findActiveSessions as any).mockResolvedValue(mockSessions);

      const sessions = await QuizRandomizationService.getActiveSessions('user-456');

      expect(sessions).toEqual(mockSessions);
      expect(QuizSession.findActiveSessions).toHaveBeenCalledWith('user-456');
    });

    it('should get user session stats through randomization service', async () => {
      const mockStats = {
        totalSessions: 5,
        completedSessions: 3,
        averageScore: 7.5,
        averageTime: 350,
        totalQuestionsAnswered: 30,
        uniqueTopics: 2
      };

      (QuizSession.getUserSessionStats as any).mockResolvedValue(mockStats);

      const stats = await QuizRandomizationService.getUserSessionStats('user-456');

      expect(stats).toEqual(mockStats);
      expect(QuizSession.getUserSessionStats).toHaveBeenCalledWith('user-456');
    });

    it('should extend session expiration through randomization service', async () => {
      const mockSession = {
        extendExpiration: vi.fn().mockResolvedValue(true)
      };

      (QuizSession.findById as any).mockResolvedValue(mockSession);

      const success = await QuizRandomizationService.extendSessionExpiration('session-123', 'user-456', 30);

      expect(success).toBe(true);
      expect(QuizSession.findById).toHaveBeenCalledWith('session-123', 'user-456');
      expect(mockSession.extendExpiration).toHaveBeenCalledWith(30);
    });

    it('should cleanup expired sessions through randomization service', async () => {
      (QuizSession.cleanupExpired as any).mockResolvedValue(5);

      const cleanedCount = await QuizRandomizationService.cleanupExpiredSessions();

      expect(cleanedCount).toBe(5);
      expect(QuizSession.cleanupExpired).toHaveBeenCalled();
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle session model errors gracefully', async () => {
      (QuizSession.findActiveSessions as any).mockRejectedValue(new Error('Database connection failed'));

      const sessions = await QuizRandomizationService.getActiveSessions('user-456');

      expect(sessions).toEqual([]);
    });

    it('should handle session stats errors by rethrowing', async () => {
      (QuizSession.getUserSessionStats as any).mockRejectedValue(new Error('Stats calculation failed'));

      await expect(
        QuizRandomizationService.getUserSessionStats('user-456')
      ).rejects.toThrow('Stats calculation failed');
    });

    it('should handle session extension errors gracefully', async () => {
      (QuizSession.findById as any).mockRejectedValue(new Error('Session lookup failed'));

      const success = await QuizRandomizationService.extendSessionExpiration('session-123', 'user-456', 30);

      expect(success).toBe(false);
    });
  });
});