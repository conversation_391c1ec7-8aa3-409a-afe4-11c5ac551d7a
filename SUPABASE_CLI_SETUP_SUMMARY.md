# Supabase CLI Setup Summary

## 🎯 Quick Solution for "supabase: command not found"

### Immediate Fix (Choose One):

#### Option 1: Automated Setup (Recommended)
```bash
# Make script executable and run
chmod +x setup-secquiz-local.sh
./setup-secquiz-local.sh
```

#### Option 2: Manual Installation

**macOS:**
```bash
brew install supabase/tap/supabase
```

**Linux:**
```bash
npm install -g supabase
```

**Windows PowerShell:**
```powershell
# Run as Administrator
.\install-supabase-cli.ps1
```

#### Option 3: Universal npm Installation
```bash
npm install -g supabase
```

## 📁 Files Created

| File | Purpose | Usage |
|------|---------|-------|
| `SUPABASE_CLI_SETUP_GUIDE.md` | Complete installation guide | Reference documentation |
| `SUPABASE_CLI_TROUBLESHOOTING.md` | Common issues and solutions | When things go wrong |
| `install-supabase-cli.sh` | Linux/macOS installation script | `./install-supabase-cli.sh` |
| `install-supabase-cli.ps1` | Windows PowerShell script | `.\install-supabase-cli.ps1` |
| `setup-secquiz-local.sh` | SecQuiz-specific setup | `./setup-secquiz-local.sh` |

## 🚀 Quick Start for SecQuiz

### Step 1: Install Prerequisites
- **Node.js**: https://nodejs.org/
- **Docker**: https://www.docker.com/products/docker-desktop
- **Git**: https://git-scm.com/

### Step 2: Run Setup Script
```bash
# Navigate to SecQuiz project
cd /path/to/secquiz

# Run automated setup
chmod +x setup-secquiz-local.sh
./setup-secquiz-local.sh
```

### Step 3: Start Development
```bash
# Start Supabase (if not already running)
npm run supabase:start

# Start your app
npm run dev

# Start backend (in another terminal)
cd server && npm run dev
```

### Step 4: Access Services
- **Your App**: http://localhost:5173
- **Supabase Studio**: http://localhost:54323
- **API**: http://localhost:54321
- **Database**: postgresql://postgres:postgres@localhost:54322/postgres

## 🔧 New npm Scripts Added

| Script | Command | Purpose |
|--------|---------|---------|
| `npm run setup:local` | Runs setup script | One-time local setup |
| `npm run supabase:start` | `supabase start` | Start local Supabase |
| `npm run supabase:stop` | `supabase stop` | Stop local Supabase |
| `npm run supabase:status` | `supabase status` | Check status |
| `npm run supabase:reset` | `supabase db reset` | Reset database |
| `npm run dev:local` | Start Supabase + dev server | Combined startup |

## 🌍 Environment Configuration

### Local Development (.env.local)
```env
VITE_SUPABASE_URL=http://localhost:54321
VITE_SUPABASE_ANON_KEY=your-local-anon-key
VITE_APP_URL=http://localhost:5173
VITE_API_URL=http://localhost:3001
```

### Production (.env)
```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-production-anon-key
VITE_APP_URL=https://your-domain.com
VITE_API_URL=https://your-api-domain.com
```

## 🆚 VS Code Extension vs CLI

| Feature | VS Code Extension | Supabase CLI |
|---------|-------------------|--------------|
| **Purpose** | Database management | Local development |
| **Local Instance** | ❌ Cannot start | ✅ Full support |
| **Schema Editing** | ✅ GUI interface | ✅ Migration files |
| **SQL Queries** | ✅ Built-in editor | ✅ Via Studio |
| **Project Setup** | ❌ Limited | ✅ Complete |
| **Migrations** | ❌ No support | ✅ Full support |

**Recommendation**: Use CLI for local development, extension for remote database management.

## 🐛 Common Issues & Quick Fixes

### Issue: "supabase: command not found"
```bash
# Check installation
which supabase

# Install via npm
npm install -g supabase

# Add to PATH (if needed)
echo 'export PATH="/usr/local/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc
```

### Issue: "Docker daemon not running"
```bash
# Linux
sudo systemctl start docker

# macOS/Windows
# Start Docker Desktop application
```

### Issue: "Port already in use"
```bash
# Find and kill process
lsof -i :54321
kill -9 PID

# Or change ports in supabase/config.toml
```

### Issue: VS Code extension not connecting
**Solution**: Use CLI instead. The extension doesn't support local instances well.

## 📋 Verification Checklist

After setup, verify everything works:

- [ ] `supabase --version` shows version number
- [ ] `docker --version` shows Docker is installed
- [ ] `supabase status` shows running services
- [ ] http://localhost:54323 opens Supabase Studio
- [ ] Your app loads at http://localhost:5173
- [ ] No authentication errors in browser console

## 🔄 Daily Development Workflow

### Starting Work
```bash
# Start Supabase
npm run supabase:start

# Start your app
npm run dev

# Start backend (separate terminal)
cd server && npm run dev
```

### During Development
```bash
# Check status
npm run supabase:status

# Reset database (if needed)
npm run supabase:reset

# View logs
docker logs supabase_db_secquiz
```

### Ending Work
```bash
# Stop Supabase
npm run supabase:stop
```

## 🚨 Emergency Troubleshooting

### If Everything Breaks
```bash
# Nuclear reset
supabase stop
docker system prune -a
rm -rf supabase/.temp
supabase start
```

### If CLI Won't Install
1. Try different installation method (npm, brew, manual)
2. Check PATH environment variable
3. Restart terminal/computer
4. Use Docker Compose directly as fallback

### If Docker Issues Persist
1. Restart Docker Desktop
2. Clear Docker cache: `docker system prune -a`
3. Reinstall Docker Desktop
4. Check available disk space

## 📞 Getting Help

### Resources
- **Setup Guide**: `SUPABASE_CLI_SETUP_GUIDE.md`
- **Troubleshooting**: `SUPABASE_CLI_TROUBLESHOOTING.md`
- **Supabase Docs**: https://supabase.com/docs/guides/cli
- **GitHub Issues**: https://github.com/supabase/cli/issues

### Community
- **Discord**: https://discord.supabase.com
- **GitHub Discussions**: https://github.com/supabase/supabase/discussions

## 🎉 Success Indicators

You'll know everything is working when:

1. ✅ `supabase --version` returns a version number
2. ✅ `supabase status` shows all services running
3. ✅ Supabase Studio opens at http://localhost:54323
4. ✅ Your SecQuiz app loads without authentication errors
5. ✅ You can create/read data in the local database
6. ✅ Free tier implementation works with local auth

## 📈 Next Steps After Setup

1. **Test Free Tier**: Verify the free tier implementation works locally
2. **Database Schema**: Set up your database tables and relationships
3. **Migrations**: Create migration files for schema changes
4. **Seed Data**: Add test data for development
5. **Deploy**: When ready, deploy to production Supabase

The local development environment is now ready for your SecQuiz project with the free tier implementation!
