import { supabase } from '@/integrations/supabase/client';
import { v4 as uuidv4 } from 'uuid';
import type { Tables, TablesInsert } from '@/types/supabase';

// Topic type from database
export type Topic = Tables<'topics'>;

// Topic creation input
export interface CreateTopicInput {
  title: string;
  description?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  is_premium?: boolean;
}

// Topic resolution result for batch processing
export interface TopicResolutionResult {
  resolved: Map<string, string>; // Maps topic reference to topic_id
  missing: string[];
  created: string[];
  errors: string[];
}

/**
 * Service for managing topics and topic resolution
 */
export class TopicService {
  /**
   * Find existing topic by name or ID
   * @param reference Topic name or ID to search for
   * @returns Promise resolving to Topic or null if not found
   */
  async findTopic(reference: string): Promise<Topic | null> {
    try {
      // First try to find by ID (exact match)
      const { data: topicById, error: idError } = await supabase
        .from('topics')
        .select('*')
        .eq('id', reference)
        .eq('is_active', true)
        .single();

      if (!idError && topicById) {
        return topicById;
      }

      // If not found by ID, try to find by title (case-insensitive)
      const { data: topicByTitle, error: titleError } = await supabase
        .from('topics')
        .select('*')
        .ilike('title', reference)
        .eq('is_active', true)
        .single();

      if (!titleError && topicByTitle) {
        return topicByTitle;
      }

      return null;
    } catch (error) {
      console.error('Error finding topic:', error);
      return null;
    }
  }

  /**
   * Create new topic with validation
   * @param input Topic creation data
   * @returns Promise resolving to created Topic
   */
  async createTopic(input: CreateTopicInput): Promise<Topic> {
    // Validate input
    if (!input.title || input.title.trim().length === 0) {
      throw new Error('Topic title is required');
    }

    if (input.title.trim().length > 100) {
      throw new Error('Topic title must be 100 characters or less');
    }

    // Check if topic with same title already exists
    const existingTopic = await this.findTopic(input.title.trim());
    if (existingTopic) {
      throw new Error(`Topic with title "${input.title.trim()}" already exists`);
    }

    // Validate difficulty if provided
    if (input.difficulty && !['easy', 'medium', 'hard'].includes(input.difficulty)) {
      throw new Error('Difficulty must be easy, medium, or hard');
    }

    const now = new Date().toISOString();
    const topicData: TablesInsert<'topics'> = {
      id: uuidv4(),
      title: input.title.trim(),
      description: input.description?.trim() || null,
      difficulty: input.difficulty || 'medium',
      is_active: true,
      is_premium: input.is_premium || false,
      created_at: now,
      updated_at: now,
    };

    const { data, error } = await supabase
      .from('topics')
      .insert(topicData)
      .select()
      .single();

    if (error) {
      console.error('Error creating topic:', error);
      throw new Error(`Failed to create topic: ${error.message}`);
    }

    return data;
  }

  /**
   * Batch resolve multiple topic references
   * @param references Array of topic names or IDs to resolve
   * @param autoCreate Whether to automatically create missing topics
   * @returns Promise resolving to TopicResolutionResult
   */
  async resolveTopicReferences(
    references: string[],
    autoCreate: boolean = false
  ): Promise<TopicResolutionResult> {
    const result: TopicResolutionResult = {
      resolved: new Map(),
      missing: [],
      created: [],
      errors: [],
    };

    // Remove duplicates and empty references
    const uniqueReferences = [...new Set(references.filter(ref => ref && ref.trim().length > 0))];

    for (const reference of uniqueReferences) {
      try {
        // Try to find existing topic
        const existingTopic = await this.findTopic(reference.trim());
        
        if (existingTopic) {
          result.resolved.set(reference, existingTopic.id);
          continue;
        }

        // Topic not found
        if (autoCreate) {
          try {
            // Attempt to create the topic
            const newTopic = await this.createTopic({
              title: reference.trim(),
              difficulty: 'medium', // Default difficulty
              is_premium: false, // Default to non-premium
            });
            
            result.resolved.set(reference, newTopic.id);
            result.created.push(reference);
          } catch (createError) {
            const errorMessage = createError instanceof Error ? createError.message : String(createError);
            result.errors.push(`Failed to create topic "${reference}": ${errorMessage}`);
            result.missing.push(reference);
          }
        } else {
          result.missing.push(reference);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        result.errors.push(`Error resolving topic "${reference}": ${errorMessage}`);
        result.missing.push(reference);
      }
    }

    return result;
  }

  /**
   * Get all active topics
   * @returns Promise resolving to array of active topics
   */
  async getAllActiveTopics(): Promise<Topic[]> {
    const { data, error } = await supabase
      .from('topics')
      .select('*')
      .eq('is_active', true)
      .order('title');

    if (error) {
      console.error('Error fetching topics:', error);
      throw new Error(`Failed to fetch topics: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get topic by ID
   * @param id Topic ID
   * @returns Promise resolving to Topic or null if not found
   */
  async getTopicById(id: string): Promise<Topic | null> {
    const { data, error } = await supabase
      .from('topics')
      .select('*')
      .eq('id', id)
      .eq('is_active', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return null;
      }
      console.error('Error fetching topic by ID:', error);
      throw new Error(`Failed to fetch topic: ${error.message}`);
    }

    return data;
  }
}

// Export a singleton instance
export const topicService = new TopicService();