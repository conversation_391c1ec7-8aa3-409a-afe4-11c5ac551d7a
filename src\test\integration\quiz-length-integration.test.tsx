/**
 * Integration tests for Quiz Length Configuration
 * Tests the complete quiz length selection and validation flow
 */

import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { QuizLengthSelector } from '@/components/quiz/QuizLengthSelector';
import { UserPreferencesService } from '@/services/user-preferences-service';
import { QuizRandomizationService } from '@/services/quiz-randomization-service';

// Mock the services
vi.mock('@/services/quiz-randomization-service');

describe('Quiz Length Integration Tests', () => {

  it('should validate quiz length options correctly', () => {
    // Test with sufficient questions
    const validation1 = UserPreferencesService.validateQuizLength(15, 30);
    expect(validation1.isValid).toBe(true);
    expect(validation1.adjustedLength).toBe(15);
    expect(validation1.severity).toBe('info');

    // Test with insufficient questions
    const validation2 = UserPreferencesService.validateQuizLength(25, 10);
    expect(validation2.isValid).toBe(true);
    expect(validation2.adjustedLength).toBe(10);
    expect(validation2.severity).toBe('warning');
    expect(validation2.message).toContain('Only 10 questions available');

    // Test with no questions
    const validation3 = UserPreferencesService.validateQuizLength(15, 0);
    expect(validation3.isValid).toBe(false);
    expect(validation3.adjustedLength).toBe(0);
    expect(validation3.severity).toBe('error');
  });

  it('should provide valid quiz length options based on available questions', () => {
    // Test with plenty of questions
    const options1 = UserPreferencesService.getValidQuizLengthOptions(50);
    expect(options1).toEqual([10, 15, 20, 25]);

    // Test with limited questions
    const options2 = UserPreferencesService.getValidQuizLengthOptions(12);
    expect(options2).toEqual([10, 12]);

    // Test with very few questions
    const options3 = UserPreferencesService.getValidQuizLengthOptions(3);
    expect(options3).toEqual([]);

    // Test with custom number of questions
    const options4 = UserPreferencesService.getValidQuizLengthOptions(18);
    expect(options4).toEqual([10, 15, 18]);
  });

  it('should handle user preferences for default quiz length', async () => {
    // Mock user preferences service
    vi.mocked(UserPreferencesService.getUserPreferences).mockResolvedValue({
      defaultQuizLength: 20,
      autoStartQuiz: false,
      showQuizInstructions: true
    });

    vi.mocked(UserPreferencesService.setDefaultQuizLength).mockResolvedValue(true);

    // Test getting default quiz length
    const defaultLength = await UserPreferencesService.getDefaultQuizLength(mockUser);
    expect(defaultLength).toBe(20);

    // Test setting default quiz length
    const result = await UserPreferencesService.setDefaultQuizLength(mockUser, 25);
    expect(result).toBe(true);
    expect(UserPreferencesService.setDefaultQuizLength).toHaveBeenCalledWith(mockUser, 25);
  });

  it('should render quiz length selector with correct options', () => {
    const mockProps = {
      availableQuestions: 30,
      selectedLength: 15,
      onLengthChange: vi.fn(),
      onStartQuiz: vi.fn(),
      loading: false,
      disabled: false
    };

    render(<QuizLengthSelector {...mockProps} />);

    // Check that the component renders
    expect(screen.getByText('Choose Quiz Length')).toBeInTheDocument();
    expect(screen.getByText(/Available questions:\s*30/)).toBeInTheDocument();

    // Check that quiz length options are rendered
    expect(screen.getByText('10 Questions')).toBeInTheDocument();
    expect(screen.getByText('15 Questions')).toBeInTheDocument();
    expect(screen.getByText('20 Questions')).toBeInTheDocument();
    expect(screen.getByText('25 Questions')).toBeInTheDocument();

    // Check that start button is present
    expect(screen.getByText('Start Quiz')).toBeInTheDocument();
  });

  it('should handle quiz length selection and validation', () => {
    const onLengthChange = vi.fn();
    const onStartQuiz = vi.fn();

    const mockProps = {
      availableQuestions: 15,
      selectedLength: 10,
      onLengthChange,
      onStartQuiz,
      loading: false,
      disabled: false
    };

    render(<QuizLengthSelector {...mockProps} />);

    // Should show warning for limited questions
    expect(screen.getByText(/limited questions \(15\)/)).toBeInTheDocument();

    // Click on a quiz length option
    const option15 = screen.getByText('15 Questions').closest('.cursor-pointer');
    fireEvent.click(option15!);

    expect(onLengthChange).toHaveBeenCalledWith(15);

    // Click start quiz button
    const startButton = screen.getByText('Start Quiz');
    fireEvent.click(startButton);

    expect(onStartQuiz).toHaveBeenCalled();
  });

  it('should disable options when insufficient questions available', () => {
    const mockProps = {
      availableQuestions: 8,
      selectedLength: 10,
      onLengthChange: vi.fn(),
      onStartQuiz: vi.fn(),
      loading: false,
      disabled: false
    };

    render(<QuizLengthSelector {...mockProps} />);

    // Should show adjusted lengths for all options
    const adjustedOptions = screen.getAllByText('8 Questions');
    expect(adjustedOptions.length).toBeGreaterThan(0);

    // Should show warning message
    expect(screen.getByText(/Only 8 questions available/)).toBeInTheDocument();

    // Should have disabled options
    const disabledOptions = document.querySelectorAll('.cursor-not-allowed');
    expect(disabledOptions.length).toBeGreaterThan(0);
  });

  it('should handle edge cases correctly', () => {
    // Test with no questions
    const mockProps1 = {
      availableQuestions: 0,
      selectedLength: 10,
      onLengthChange: vi.fn(),
      onStartQuiz: vi.fn(),
      loading: false,
      disabled: false
    };

    render(<QuizLengthSelector {...mockProps1} />);

    expect(screen.getByText('No questions available for this topic.')).toBeInTheDocument();
    expect(screen.getByText('Start Quiz')).toBeDisabled();
  });

  it('should validate quiz length for topic using randomization service', async () => {
    // Mock the randomization service
    vi.mocked(QuizRandomizationService.validateQuizLengthForTopic).mockResolvedValue({
      isValid: true,
      availableQuestions: 25,
      recommendedLength: 20,
      severity: 'info'
    });

    const result = await QuizRandomizationService.validateQuizLengthForTopic('test-topic-id', 20);

    expect(result.isValid).toBe(true);
    expect(result.availableQuestions).toBe(25);
    expect(result.recommendedLength).toBe(20);
    expect(result.severity).toBe('info');
  });

  it('should get available quiz lengths for topic', async () => {
    // Mock the randomization service
    vi.mocked(QuizRandomizationService.getAvailableQuizLengths).mockResolvedValue({
      options: [
        { value: 10, label: '10 Questions', available: true, recommended: true },
        { value: 15, label: '15 Questions', available: true, recommended: true },
        { value: 20, label: '20 Questions', available: true, recommended: false },
        { value: 25, label: '25 Questions', available: false, recommended: false }
      ],
      maxLength: 20
    });

    const result = await QuizRandomizationService.getAvailableQuizLengths('test-topic-id');

    expect(result.maxLength).toBe(20);
    expect(result.options).toHaveLength(4);
    expect(result.options.filter(opt => opt.available)).toHaveLength(3);
    expect(result.options.filter(opt => opt.recommended)).toHaveLength(2);
  });

  it('should handle invalid quiz length inputs', () => {
    // Test invalid lengths
    expect(() => UserPreferencesService.validateQuizLength(-1, 10)).not.toThrow();
    expect(() => UserPreferencesService.validateQuizLength(100, 10)).not.toThrow();

    const result1 = UserPreferencesService.validateQuizLength(-1, 10);
    expect(result1.isValid).toBe(false);
    expect(result1.severity).toBe('error');

    const result2 = UserPreferencesService.validateQuizLength(100, 10);
    expect(result2.isValid).toBe(false);
    expect(result2.severity).toBe('error');
  });

  it('should provide appropriate recommendations for question pool sizes', () => {
    // Test with excellent variety (50+ questions)
    const validation1 = UserPreferencesService.validateQuizLength(25, 60);
    expect(validation1.isValid).toBe(true);
    expect(validation1.severity).toBe('info');
    expect(validation1.message).toBeUndefined(); // No warning needed

    // Test with good variety (30-49 questions)
    const validation2 = UserPreferencesService.validateQuizLength(20, 35);
    expect(validation2.isValid).toBe(true);
    expect(validation2.severity).toBe('info');

    // Test with limited variety (< 20 questions)
    const validation3 = UserPreferencesService.validateQuizLength(10, 15);
    expect(validation3.isValid).toBe(true);
    expect(validation3.severity).toBe('info');
    expect(validation3.message).toContain('limited questions');
  });
});