import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://agdyycknlxojiwhlqicq.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFnZHl5Y2tubHhvaml3aGxxaWNxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyMjkzOTgsImV4cCI6MjA1OTgwNTM5OH0.lWZLRByfsyRqkK7XZfi21qSeEuOZHJKkFJGC_2ojQR8';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testSchemaRefresh() {
  console.log('Testing schema refresh and feedback submission...');
  
  try {
    // Step 1: Try to refresh schema by making multiple queries
    console.log('Step 1: Attempting to refresh schema cache...');
    
    // Try different approaches to refresh the schema
    const queries = [
      () => supabase.from('feedback').select('count').limit(1),
      () => supabase.from('feedback').select('id').limit(1),
      () => supabase.from('feedback').select('*').limit(1),
    ];
    
    for (let i = 0; i < queries.length; i++) {
      console.log(`Trying query ${i + 1}...`);
      const { data, error } = await queries[i]();
      
      if (error) {
        console.log(`Query ${i + 1} error:`, error.message);
      } else {
        console.log(`Query ${i + 1} success:`, data);
      }
      
      // Wait a bit between queries
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Step 2: Try to insert feedback after schema refresh attempts
    console.log('Step 2: Testing feedback insertion after schema refresh...');
    
    const testFeedback = {
      name: 'Test User After Refresh',
      email: '<EMAIL>',
      subject: 'Schema Refresh Test',
      message: 'This message tests if feedback submission works after schema refresh.',
      status: 'new'
    };
    
    const { data: insertData, error: insertError } = await supabase
      .from('feedback')
      .insert(testFeedback)
      .select();
    
    if (insertError) {
      console.error('❌ Insert still failing after refresh attempts:', insertError);
      
      // Try alternative approach - use upsert
      console.log('Trying upsert approach...');
      const { data: upsertData, error: upsertError } = await supabase
        .from('feedback')
        .upsert(testFeedback)
        .select();
      
      if (upsertError) {
        console.error('❌ Upsert also failed:', upsertError);
        
        // The issue might be with the table structure
        console.log('🔍 Let\'s check what columns the table actually has...');
        
        // Try to get table information
        const { data: tableInfo, error: tableError } = await supabase
          .rpc('get_table_columns', { table_name: 'feedback' });
        
        if (tableError) {
          console.log('Cannot get table info:', tableError.message);
        } else {
          console.log('Table columns:', tableInfo);
        }
        
        return false;
      } else {
        console.log('✅ Upsert worked:', upsertData);
        return true;
      }
    } else {
      console.log('✅ Insert worked after refresh:', insertData);
      return true;
    }
    
  } catch (e) {
    console.error('❌ Exception during test:', e);
    return false;
  }
}

testSchemaRefresh();
