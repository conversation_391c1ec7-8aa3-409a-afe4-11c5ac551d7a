-- Migration: Add Quiz Randomization Schema Enhancements
-- This migration adds support for question randomization, usage tracking, and analytics

-- Add usage tracking columns to questions table
ALTER TABLE questions 
ADD COLUMN IF NOT EXISTS usage_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_used TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS correct_answer_rate DECIMAL(5,2) DEFAULT 0.00;

-- Create index for performance on usage tracking
CREATE INDEX IF NOT EXISTS idx_questions_usage_count ON questions(usage_count);
CREATE INDEX IF NOT EXISTS idx_questions_last_used ON questions(last_used);
CREATE INDEX IF NOT EXISTS idx_questions_correct_answer_rate ON questions(correct_answer_rate);

-- Create quiz_sessions table for tracking randomized quiz instances
CREATE TABLE IF NOT EXISTS quiz_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  topic_id UUID REFERENCES topics(id) ON DELETE SET NULL,
  questions_data JSONB NOT NULL, -- Stores randomized question order and option mappings
  quiz_length INTEGER NOT NULL DEFAULT 10,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() + INTERVAL '2 hours',
  completed_at TIMESTAMP WITH TIME ZONE,
  score INTEGER,
  total_questions INTEGER,
  time_taken INTEGER -- in seconds
);

-- Enable Row Level Security for quiz_sessions
ALTER TABLE quiz_sessions ENABLE ROW LEVEL SECURITY;

-- Create policies for quiz_sessions
CREATE POLICY "Users can view their own quiz sessions"
  ON quiz_sessions FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own quiz sessions"
  ON quiz_sessions FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own quiz sessions"
  ON quiz_sessions FOR UPDATE
  USING (auth.uid() = user_id);

-- Allow service role to manage quiz sessions (for cleanup and admin operations)
CREATE POLICY "Service role can manage quiz sessions"
  ON quiz_sessions FOR ALL
  TO service_role
  USING (true);

-- Create indexes for quiz_sessions
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_user_id ON quiz_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_topic_id ON quiz_sessions(topic_id);
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_created_at ON quiz_sessions(created_at);
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_expires_at ON quiz_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_completed_at ON quiz_sessions(completed_at);

-- Create question_analytics table for performance tracking
CREATE TABLE IF NOT EXISTS question_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  question_id UUID REFERENCES questions(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  quiz_session_id UUID REFERENCES quiz_sessions(id) ON DELETE SET NULL,
  answered_correctly BOOLEAN NOT NULL,
  time_to_answer INTEGER, -- in seconds
  selected_option INTEGER, -- the option index selected by user
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security for question_analytics
ALTER TABLE question_analytics ENABLE ROW LEVEL SECURITY;

-- Create policies for question_analytics
CREATE POLICY "Users can view their own question analytics"
  ON question_analytics FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own question analytics"
  ON question_analytics FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Allow service role to manage question analytics (for admin operations and cleanup)
CREATE POLICY "Service role can manage question analytics"
  ON question_analytics FOR ALL
  TO service_role
  USING (true);

-- Admins can view all question analytics for reporting
CREATE POLICY "Admins can view all question analytics"
  ON question_analytics FOR SELECT
  USING (is_admin());

-- Create indexes for question_analytics
CREATE INDEX IF NOT EXISTS idx_question_analytics_question_id ON question_analytics(question_id);
CREATE INDEX IF NOT EXISTS idx_question_analytics_user_id ON question_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_question_analytics_quiz_session_id ON question_analytics(quiz_session_id);
CREATE INDEX IF NOT EXISTS idx_question_analytics_created_at ON question_analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_question_analytics_answered_correctly ON question_analytics(answered_correctly);

-- Create function to update question usage statistics
CREATE OR REPLACE FUNCTION update_question_usage_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- Update usage count and last used timestamp
  UPDATE questions 
  SET 
    usage_count = usage_count + 1,
    last_used = NOW()
  WHERE id = NEW.question_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically update question usage when analytics are inserted
DROP TRIGGER IF EXISTS trigger_update_question_usage ON question_analytics;
CREATE TRIGGER trigger_update_question_usage
  AFTER INSERT ON question_analytics
  FOR EACH ROW
  EXECUTE FUNCTION update_question_usage_stats();

-- Create function to update question correct answer rate
CREATE OR REPLACE FUNCTION update_question_correct_answer_rate()
RETURNS TRIGGER AS $$
DECLARE
  total_attempts INTEGER;
  correct_attempts INTEGER;
  new_rate DECIMAL(5,2);
BEGIN
  -- Calculate correct answer rate for the question
  SELECT 
    COUNT(*) as total,
    COUNT(*) FILTER (WHERE answered_correctly = true) as correct
  INTO total_attempts, correct_attempts
  FROM question_analytics 
  WHERE question_id = NEW.question_id;
  
  -- Calculate percentage (avoid division by zero)
  IF total_attempts > 0 THEN
    new_rate := ROUND((correct_attempts::DECIMAL / total_attempts::DECIMAL) * 100, 2);
  ELSE
    new_rate := 0.00;
  END IF;
  
  -- Update the questions table
  UPDATE questions 
  SET correct_answer_rate = new_rate
  WHERE id = NEW.question_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically update correct answer rate when analytics are inserted
DROP TRIGGER IF EXISTS trigger_update_correct_answer_rate ON question_analytics;
CREATE TRIGGER trigger_update_correct_answer_rate
  AFTER INSERT ON question_analytics
  FOR EACH ROW
  EXECUTE FUNCTION update_question_correct_answer_rate();

-- Create function to clean up expired quiz sessions
CREATE OR REPLACE FUNCTION cleanup_expired_quiz_sessions()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  -- Delete expired quiz sessions that haven't been completed
  DELETE FROM quiz_sessions 
  WHERE expires_at < NOW() 
    AND completed_at IS NULL;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get question pool statistics for a topic
CREATE OR REPLACE FUNCTION get_topic_question_stats(topic_uuid UUID)
RETURNS TABLE (
  total_questions INTEGER,
  avg_usage_count DECIMAL,
  avg_correct_rate DECIMAL,
  questions_never_used INTEGER,
  questions_low_performance INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::INTEGER as total_questions,
    ROUND(AVG(COALESCE(usage_count, 0)), 2) as avg_usage_count,
    ROUND(AVG(COALESCE(correct_answer_rate, 0)), 2) as avg_correct_rate,
    COUNT(*) FILTER (WHERE usage_count = 0 OR usage_count IS NULL)::INTEGER as questions_never_used,
    COUNT(*) FILTER (WHERE correct_answer_rate < 30.00)::INTEGER as questions_low_performance
  FROM questions 
  WHERE topic_id = topic_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON quiz_sessions TO authenticated;
GRANT ALL ON question_analytics TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_expired_quiz_sessions() TO service_role;
GRANT EXECUTE ON FUNCTION get_topic_question_stats(UUID) TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE quiz_sessions IS 'Tracks randomized quiz instances with question order and option mappings';
COMMENT ON TABLE question_analytics IS 'Stores individual question performance data for analytics';
COMMENT ON COLUMN questions.usage_count IS 'Number of times this question has been used in quizzes';
COMMENT ON COLUMN questions.last_used IS 'Timestamp when this question was last used';
COMMENT ON COLUMN questions.correct_answer_rate IS 'Percentage of users who answered this question correctly';
COMMENT ON FUNCTION cleanup_expired_quiz_sessions() IS 'Removes expired quiz sessions to keep database clean';
COMMENT ON FUNCTION get_topic_question_stats(UUID) IS 'Returns statistics about question pool for a given topic';