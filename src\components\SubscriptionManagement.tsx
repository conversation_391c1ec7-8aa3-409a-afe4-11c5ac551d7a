import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  CreditCard,
  // Calendar,
  AlertTriangle,
  RefreshCw,
  Download,
  Mail,
  Phone,
  ExternalLink,
  History,
  Settings,
  Crown
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { useAuth } from '@/hooks/use-auth';
import { getUserSubscriptionDetails } from '@/utils/auth-helpers';
import { toast } from 'sonner';

interface PaymentHistory {
  id: string;
  reference: string;
  amount: number;
  plan_id: string;
  status: 'success' | 'failed' | 'pending';
  created_at: string;
}

interface SubscriptionManagementProps {
  className?: string;
}

const SubscriptionManagement = ({ className = '' }: SubscriptionManagementProps) => {
  const { user } = useAuth();
  const [subscriptionDetails, setSubscriptionDetails] = useState<any>(null);
  const [paymentHistory, setPaymentHistory] = useState<PaymentHistory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch subscription and payment data
  const fetchData = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      
      // Get subscription details
      const details = await getUserSubscriptionDetails(user);
      setSubscriptionDetails(details);

      // Fetch payment history (mock data for now - replace with actual API call)
      const mockHistory: PaymentHistory[] = [
        {
          id: '1',
          reference: 'PAY_123456789',
          amount: 1979,
          plan_id: 'pro',
          status: 'success',
          created_at: new Date().toISOString()
        }
      ];
      setPaymentHistory(mockHistory);

    } catch (error) {
      console.error('Error fetching subscription data:', error);
      toast.error('Failed to load subscription data');
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh data
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchData();
    setIsRefreshing(false);
    toast.success('Subscription data refreshed');
  };

  // Download invoice (placeholder)
  const handleDownloadInvoice = (reference: string) => {
    toast.info(`Downloading invoice for ${reference}...`);
    // Implement actual invoice download logic
  };

  // Cancel subscription (placeholder)
  const handleCancelSubscription = () => {
    toast.info('Subscription cancellation will be available soon');
    // Implement actual cancellation logic
  };

  useEffect(() => {
    fetchData();
  }, [user]);

  if (!user) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="text-center">
          <p className="text-muted-foreground">Please log in to manage your subscription</p>
          <Link to="/auth">
            <Button className="mt-4">Log In</Button>
          </Link>
        </div>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Subscription Overview */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Subscription Management</span>
          </h2>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {isLoading ? (
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
            <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2" />
          </div>
        ) : subscriptionDetails ? (
          <div className="space-y-4">
            {/* Current Plan */}
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border">
              <div className="flex items-center space-x-3">
                <Crown className="h-6 w-6 text-purple-600" />
                <div>
                  <h3 className="font-medium">
                    {subscriptionDetails.plan_id ? 
                      subscriptionDetails.plan_id.charAt(0).toUpperCase() + subscriptionDetails.plan_id.slice(1) + ' Plan' : 
                      'Premium Plan'
                    }
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Active until {new Date(subscriptionDetails.end_date).toLocaleDateString()}
                  </p>
                </div>
              </div>
              <Badge className="bg-green-500/20 text-green-600 border-green-500/30">
                Active
              </Badge>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Link to="/#pricing">
                <Button variant="outline" className="w-full">
                  <Crown className="h-4 w-4 mr-2" />
                  Upgrade Plan
                </Button>
              </Link>
              
              <Button 
                variant="outline" 
                onClick={handleCancelSubscription}
                className="w-full"
              >
                <AlertTriangle className="h-4 w-4 mr-2" />
                Cancel Plan
              </Button>
              
              <Link to="/payment/troubleshoot">
                <Button variant="outline" className="w-full">
                  <Mail className="h-4 w-4 mr-2" />
                  Get Support
                </Button>
              </Link>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">No active subscription found</p>
            <Link to="/#pricing">
              <Button>
                <Crown className="h-4 w-4 mr-2" />
                View Plans
              </Button>
            </Link>
          </div>
        )}
      </Card>

      {/* Payment History */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center space-x-2">
          <History className="h-5 w-5" />
          <span>Payment History</span>
        </h3>

        {paymentHistory.length > 0 ? (
          <div className="space-y-3">
            {paymentHistory.map((payment) => (
              <motion.div
                key={payment.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <CreditCard className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="font-medium">
                      {payment.plan_id.charAt(0).toUpperCase() + payment.plan_id.slice(1)} Plan
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {new Date(payment.created_at).toLocaleDateString()} • {payment.reference}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="text-right">
                    <p className="font-medium">₦{payment.amount}</p>
                    <Badge 
                      className={
                        payment.status === 'success' ? 'bg-green-500/20 text-green-600 border-green-500/30' :
                        payment.status === 'failed' ? 'bg-red-500/20 text-red-600 border-red-500/30' :
                        'bg-yellow-500/20 text-yellow-600 border-yellow-500/30'
                      }
                    >
                      {payment.status}
                    </Badge>
                  </div>
                  
                  {payment.status === 'success' && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDownloadInvoice(payment.reference)}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <History className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-muted-foreground">No payment history found</p>
          </div>
        )}
      </Card>

      {/* Support & Contact */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Need Help?</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 border rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Mail className="h-5 w-5 text-blue-500" />
              <span className="font-medium">Email Support</span>
            </div>
            <p className="text-sm text-muted-foreground mb-3">
              Get help with billing and subscription issues
            </p>
            <a 
              href="mailto:<EMAIL>" 
              className="text-blue-600 hover:underline text-sm flex items-center space-x-1"
            >
              <span><EMAIL></span>
              <ExternalLink className="h-3 w-3" />
            </a>
          </div>
          
          <div className="p-4 border rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Phone className="h-5 w-5 text-green-500" />
              <span className="font-medium">WhatsApp Support</span>
            </div>
            <p className="text-sm text-muted-foreground mb-3">
              Quick help for urgent subscription issues
            </p>
            <a 
              href="https://wa.me/2348123456789" 
              className="text-green-600 hover:underline text-sm flex items-center space-x-1"
            >
              <span>+234 ************</span>
              <ExternalLink className="h-3 w-3" />
            </a>
          </div>
        </div>

        <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-blue-800">
                Billing Issues?
              </p>
              <p className="text-sm text-blue-700">
                If you were charged but your subscription isn't active, or if you need a refund, 
                contact our support team with your payment reference number.
              </p>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default SubscriptionManagement;