/**
 * Database Access Setup Script
 * Alternative to Supabase CLI for direct database operations
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔧 Setting up database access...\n');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables:');
  console.error('Required: VITE_SUPABASE_URL, VITE_SUPABASE_ANON_KEY');
  console.log('\n📝 Please add these to your .env file:');
  console.log('VITE_SUPABASE_URL=https://agdyycknlxojiwhlqicq.supabase.co');
  console.log('VITE_SUPABASE_ANON_KEY=your_anon_key_here');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function setupDatabaseAccess() {
  try {
    console.log('1. Testing database connection...');
    
    // Test basic connection
    const { data: connectionTest, error: connectionError } = await supabase
      .from('topics')
      .select('id, title')
      .limit(1);

    if (connectionError) {
      console.log('❌ Database connection failed:', connectionError.message);
      return false;
    }

    console.log('✅ Database connection successful');

    // Test quiz_sessions table
    console.log('\n2. Testing quiz_sessions table...');
    const { data: sessionsTest, error: sessionsError } = await supabase
      .from('quiz_sessions')
      .select('id')
      .limit(1);

    if (sessionsError) {
      console.log('❌ quiz_sessions table issue:', sessionsError.message);
      console.log('🔧 Need to create quiz_sessions table');
      return false;
    }

    console.log('✅ quiz_sessions table accessible');

    // Test question_analytics table
    console.log('\n3. Testing question_analytics table...');
    const { data: analyticsTest, error: analyticsError } = await supabase
      .from('question_analytics')
      .select('id')
      .limit(1);

    if (analyticsError) {
      console.log('❌ question_analytics table issue:', analyticsError.message);
      console.log('🔧 Need to create question_analytics table');
      return false;
    }

    console.log('✅ question_analytics table accessible');

    // Test questions table structure
    console.log('\n4. Testing questions table structure...');
    const { data: questionsTest, error: questionsTestError } = await supabase
      .from('questions')
      .select('id, topic_id, question_text, options, correct_answer, explanation')
      .limit(1);

    if (questionsTestError) {
      console.log('❌ Questions table issue:', questionsTestError.message);
      return false;
    }

    console.log('✅ Questions table structure is correct');

    console.log('\n🎉 Database access setup completed successfully!');
    console.log('\n📊 Current setup status:');
    console.log('✅ Database connection working');
    console.log('✅ quiz_sessions table exists');
    console.log('✅ question_analytics table exists');
    console.log('✅ questions table structure correct');
    
    console.log('\n🚀 You can now run the quiz randomization system!');
    
    return true;

  } catch (error) {
    console.error('❌ Setup failed:', error);
    return false;
  }
}

// Check if we need service role key for advanced operations
async function checkServiceRoleAccess() {
  console.log('\n5. Checking for service role access...');
  
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!serviceRoleKey) {
    console.log('⚠️  No service role key found');
    console.log('📝 For advanced database operations, add to .env:');
    console.log('SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here');
    console.log('\n🔍 To get your service role key:');
    console.log('1. Go to Supabase Dashboard → Settings → API');
    console.log('2. Copy the "service_role" key (not the anon key)');
    console.log('3. Add it to your .env file');
    return false;
  }

  try {
    const supabaseAdmin = createClient(supabaseUrl, serviceRoleKey);
    
    // Test admin access
    const { data: adminTest, error: adminError } = await supabaseAdmin
      .from('quiz_sessions')
      .select('id')
      .limit(1);

    if (adminError) {
      console.log('❌ Service role access failed:', adminError.message);
      return false;
    }

    console.log('✅ Service role access working');
    return true;

  } catch (error) {
    console.log('❌ Service role test failed:', error.message);
    return false;
  }
}

// Run setup
async function main() {
  const basicAccess = await setupDatabaseAccess();
  
  if (basicAccess) {
    await checkServiceRoleAccess();
    
    console.log('\n📋 Next steps:');
    console.log('1. Test the quiz randomization system in your app');
    console.log('2. Check browser console for any errors');
    console.log('3. If you see errors, run: node debug-quiz-errors.js');
    
  } else {
    console.log('\n🔧 To fix database issues:');
    console.log('1. Run the SQL script in Supabase SQL editor');
    console.log('2. Check your environment variables');
    console.log('3. Verify your Supabase project is active');
  }
}

main();