# [DEPRECATED] Email Verification Setup Guide

**NOTE: This guide is deprecated. The application now uses Supabase with Resend for email functionality.**

~~This guide will help you set up reliable email verification for your SecQuiz application using EmailJS.~~

## Current Email Configuration

The application now uses **Supabase with Resend** for all email functionality. This provides:

1. ✅ Reliable email delivery for verification emails
2. ✅ Password reset functionality
3. ✅ Customizable email templates through the Supabase dashboard

## Setting Up Supabase with Resend

1. **Create a Resend Account**

   - Go to [Resend](https://resend.com) and create an account
   - Verify your domain for better deliverability
   - Create an API key

2. **Configure Supabase to Use Resend**

   In your Supabase dashboard:

   - Go to Project Settings > Auth > Email Templates
   - Configure Resend as your email provider
   - Add your Resend API key

   ```
   RESEND_API_KEY=your_resend_api_key
   ```

3. **Test the Configuration**

   Try signing up a new user to test if the verification emails are being sent correctly.

## Customizing Email Templates in Supabase

1. **Access Email Templates**

   In your Supabase dashboard:
   - Go to Authentication > Email Templates
   - You'll find templates for:
     - Confirmation (Email Verification)
     - Invite
     - Magic Link
     - Recovery (Password Reset)

2. **Customization Options**

   For each template, you can customize:
   - **Subject Line**: Customize the email subject
   - **HTML Body**: Edit the HTML content of your email
   - **Text Body**: Provide a plain text alternative
   - **Variables**: Use dynamic variables like `{{ .ConfirmationURL }}` for links

3. **Testing Your Templates**

   - Use the "Send test email" feature in the Supabase dashboard
   - Try the sign-up process in your application
   - Check how the emails appear in different email clients

## Benefits of Using Supabase with Resend

1. **Reliability**
   - High deliverability rates
   - Automatic handling of bounces and complaints
   - Email analytics and tracking

2. **Simplicity**
   - No need for additional services or configurations
   - Built-in email templates
   - Easy to customize

3. **Cost-Effective**
   - Resend offers a generous free tier
   - Pay-as-you-go pricing for higher volumes
   - No need for multiple email service providers



## Troubleshooting

If you're still experiencing issues with email delivery:

1. **Check Spam Folders**
   - Ask users to check their spam folders
   - Add instructions in your UI to whitelist your email domain

2. **Verify SPF and DKIM Records**
   - If using a custom domain, ensure proper SPF and DKIM records are set up
   - This significantly improves deliverability

3. **Monitor Delivery Rates**
   - Use the analytics provided by your email service to monitor delivery rates
   - Adjust your email content if you notice high spam rates

4. **Test with Different Email Providers**
   - Test your verification emails with Gmail, Outlook, Yahoo, etc.
   - Some providers have stricter spam filters than others
