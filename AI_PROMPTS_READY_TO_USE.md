# Ready-to-Use AI Prompts for SecQuiz Content Generation

## 🎯 Quick Copy-Paste Prompts

### 1. Network Security Topics Generation

```
You are a cybersecurity education expert creating content for SecQuiz, a Nigerian-focused cybersecurity learning platform. Generate 5 comprehensive topics for the Network Security domain.

Requirements:
- Nigerian/African business context and scenarios
- Reference to local regulations (NDPR, CBN guidelines, NCC regulations)
- Global cybersecurity standards compliance (ISO 27001, NIST)
- Progressive difficulty from beginner to advanced
- Real-world examples from Nigerian companies/sectors

For each topic, provide:
1. Title (concise, professional)
2. Description (2-3 sentences with Nigerian context)
3. Difficulty level (easy/medium/hard)
4. 3-5 key learning objectives
5. Nigerian business scenario example

Domain: Network Security
Target Audience: Nigerian cybersecurity professionals, students, and business owners

Format your response as JSON with this structure:
{
  "topics": [
    {
      "title": "",
      "description": "",
      "difficulty": "",
      "learning_objectives": [],
      "nigerian_scenario": ""
    }
  ]
}
```

### 2. Cloud Security Quiz Questions

```
Generate 10 multiple-choice cybersecurity quiz questions for the topic: "Cloud Security for Nigerian Financial Institutions"

Requirements:
- Nigerian banking context and scenarios
- Reference Nigerian banks: GTBank, Access Bank, Zenith Bank, First Bank, UBA
- Include CBN cybersecurity framework requirements
- Lagos financial district scenarios
- Realistic African business challenges and solutions
- Mix of difficulty levels: 3 easy, 4 medium, 3 hard
- Each question should have 4 options with only one correct answer
- Provide detailed explanations referencing CBN guidelines

Nigerian Banking Context:
- CBN cybersecurity framework compliance
- Data localization requirements
- Cross-border payment security
- Mobile banking security (USSD, apps)
- Agency banking security

Format as JSON:
{
  "questions": [
    {
      "question_text": "",
      "options": {
        "0": "",
        "1": "",
        "2": "",
        "3": ""
      },
      "correct_answer": "0",
      "explanation": "",
      "difficulty": "easy|medium|hard",
      "nigerian_context": "brief description of Nigerian relevance"
    }
  ]
}
```

### 3. Incident Response Learning Material

```
Create comprehensive learning material for the cybersecurity topic: "Incident Response for Nigerian Businesses"

Requirements:
- Nigerian business context throughout
- Reference to NDPR breach notification requirements
- Case studies from Nigerian sectors (banking, telecom, fintech)
- Practical examples relevant to African business environment
- Global best practices adapted for Nigerian context
- Include actionable recommendations for Nigerian SMEs

Structure:
1. Introduction with Nigerian cybersecurity landscape
2. Key concepts with local examples
3. Nigerian regulatory requirements (NDPR, CBN, NCC)
4. Case studies (2-3 Nigerian examples)
5. Best practices for Nigerian businesses
6. Implementation guidelines for SMEs
7. NDPR compliance checklist
8. Additional resources

Nigerian Sectors to Consider:
- Banking: GTBank breach response procedures
- Fintech: Paystack/Flutterwave security practices
- Telecoms: MTN Nigeria incident handling
- E-commerce: Jumia security measures

Format as Markdown with Nigerian focus:

# Incident Response for Nigerian Businesses

## Introduction
[Nigerian cybersecurity threat landscape]

## Key Concepts
[Core incident response concepts with Nigerian examples]

## Nigerian Regulatory Requirements
[NDPR breach notification, CBN guidelines, NCC requirements]

## Case Studies
### Case Study 1: Banking Sector Incident Response
### Case Study 2: Fintech Security Breach Management

## Best Practices for Nigerian SMEs
[Actionable recommendations for small businesses]

## NDPR Compliance for Incident Response
[Step-by-step NDPR breach notification process]

## Implementation Guidelines
[Practical steps for Nigerian businesses]

## Resources for Nigerian Businesses
[Local and international resources]

Provide the content in JSON format:
{
  "title": "Incident Response for Nigerian Businesses",
  "summary": "Comprehensive guide to cybersecurity incident response tailored for Nigerian business environment",
  "content": "[Full markdown content]",
  "is_premium": false,
  "nigerian_focus_areas": ["NDPR compliance", "SME guidance", "Local case studies"]
}
```

### 4. Penetration Testing Scenarios

```
Create 5 scenario-based penetration testing questions focusing on real Nigerian business situations.

Each scenario should:
- Feature a realistic Nigerian business situation
- Include specific Nigerian context (companies, locations, regulations)
- Present a penetration testing challenge or methodology question
- Require application of ethical hacking knowledge
- Reference appropriate testing frameworks (OWASP, NIST)

Scenario Types:
1. Banking penetration test (Lagos financial district)
2. Telecommunications infrastructure test (MTN Nigeria scenario)
3. Fintech startup security assessment (Lagos tech hub)
4. Oil & gas company test (Port Harcourt operations)
5. Government agency assessment (Abuja federal ministry)

Nigerian Business Context:
- Companies: GTBank, MTN Nigeria, Flutterwave, Shell Nigeria, NNPC
- Locations: Lagos (Victoria Island, Ikoyi), Abuja (Central Business District), Port Harcourt
- Regulations: NDPR, CBN cybersecurity framework, NCC guidelines
- Infrastructure: Nigerian internet backbone, submarine cables, data centers

Format each question as:
{
  "scenario": "[Detailed Nigerian business scenario with specific company/location]",
  "question": "[Specific penetration testing question]",
  "options": {
    "0": "[Option A - specific methodology/tool]",
    "1": "[Option B - alternative approach]",
    "2": "[Option C - different technique]",
    "3": "[Option D - incorrect approach]"
  },
  "correct_answer": "[0-3]",
  "explanation": "[Detailed explanation with Nigerian regulatory considerations]",
  "difficulty": "[easy|medium|hard]",
  "sector": "[banking|telecom|fintech|oil_gas|government]",
  "testing_phase": "[reconnaissance|scanning|exploitation|post_exploitation]",
  "regulations_referenced": ["NDPR", "CBN Guidelines", "etc."]
}
```

### 5. Cryptography for Nigerian Context

```
Generate comprehensive content for "Cryptography in Nigerian Digital Payments" topic.

Focus Areas:
- Nigerian payment systems: Interswitch, Paystack, Flutterwave
- CBN guidelines for payment security
- Mobile money security (MTN MoMo, Airtel Money)
- USSD banking security
- Cross-border payment encryption
- Cryptocurrency regulations in Nigeria

Create:
1. 8 quiz questions (mix of difficulties)
2. Learning material (3000+ words)
3. Practical examples from Nigerian fintech

Requirements:
- Reference Nigerian Payment System Vision 2025
- Include CBN cashless policy implications
- Address cryptocurrency ban and CBDC (eNaira) security
- Cover mobile banking encryption standards
- Include practical implementation for Nigerian fintechs

Nigerian Fintech Examples:
- Paystack payment processing
- Flutterwave cross-border payments
- Kuda Bank mobile security
- PiggyVest savings platform security
- Interswitch payment infrastructure

Format as comprehensive JSON:
{
  "topic": {
    "title": "Cryptography in Nigerian Digital Payments",
    "description": "",
    "difficulty": "intermediate",
    "learning_objectives": []
  },
  "questions": [
    {
      "question_text": "",
      "options": {},
      "correct_answer": "",
      "explanation": "",
      "difficulty": ""
    }
  ],
  "learning_material": {
    "title": "",
    "summary": "",
    "content": "[Full markdown content with Nigerian examples]",
    "nigerian_case_studies": []
  }
}
```

### 6. Social Engineering Nigerian Context

```
Create social engineering awareness content specifically for Nigerian business environment.

Focus on Nigerian-specific attack vectors:
- WhatsApp business scams
- Fake bank SMS/calls
- 419 scam evolution
- Fake government agency communications
- Cryptocurrency investment scams
- Fake job recruitment scams
- Romance scams targeting professionals

Nigerian Context Elements:
- Local languages (Pidgin English, Yoruba, Hausa, Igbo)
- Cultural references and trust patterns
- Nigerian business communication styles
- Religious and cultural manipulation tactics
- Economic pressures and vulnerabilities
- Technology adoption patterns

Create:
1. 10 scenario-based questions about Nigerian social engineering
2. Learning material covering Nigerian-specific attack patterns
3. Prevention strategies for Nigerian businesses

Include real examples (anonymized):
- Fake CBN communications
- Fraudulent EFCC/ICPC messages
- Fake NYSC/university communications
- Bogus investment opportunities
- Fake diaspora remittance scams

Format as:
{
  "nigerian_social_engineering_content": {
    "questions": [],
    "learning_material": "",
    "prevention_strategies": [],
    "nigerian_specific_indicators": [],
    "cultural_awareness_points": []
  }
}
```

### 7. Compliance and Governance

```
Generate content for "Cybersecurity Governance for Nigerian Organizations" covering:

Regulatory Framework:
- NDPR implementation and compliance
- CBN cybersecurity framework
- NCC cybersecurity guidelines
- NITDA guidelines
- SEC cybersecurity rules for capital market

Nigerian Organizational Context:
- Federal government agencies
- State government organizations
- Private sector companies
- SMEs and startups
- NGOs and international organizations

Create comprehensive content including:
1. Governance framework questions (12 questions)
2. Compliance checklist learning material
3. Implementation roadmap for Nigerian organizations
4. Cost-effective solutions for SMEs

Focus Areas:
- Board-level cybersecurity oversight
- Risk management frameworks
- Incident response governance
- Third-party risk management
- Employee training and awareness
- Budget allocation for cybersecurity

Nigerian Business Considerations:
- Limited cybersecurity budgets
- Skills shortage in cybersecurity
- Infrastructure challenges
- Regulatory compliance costs
- International business requirements

Format as detailed governance guide:
{
  "governance_content": {
    "framework_questions": [],
    "implementation_guide": "",
    "compliance_checklist": [],
    "sme_specific_guidance": "",
    "cost_optimization_strategies": []
  }
}
```

## 🚀 Usage Instructions

1. **Copy the relevant prompt** for your content type
2. **Paste into your AI tool** (Claude, ChatGPT, DeepSeek, etc.)
3. **Replace placeholders** with specific domain/topic names
4. **Review and refine** the generated content
5. **Insert into database** using the provided SQL examples

## 📝 Content Review Checklist

Before adding content to SecQuiz:
- [ ] Nigerian context is authentic and accurate
- [ ] Regulatory references are current
- [ ] Company examples are realistic
- [ ] Difficulty progression is appropriate
- [ ] Language is clear and professional
- [ ] Cultural sensitivity is maintained
- [ ] Technical accuracy is verified
