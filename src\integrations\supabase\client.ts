import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';
import { supabaseConfig } from '@/config';

// Force cloud Supabase instance - never use local development instance
const FORCE_CLOUD_URL = 'https://agdyycknlxojiwhlqicq.supabase.co';
const FORCE_CLOUD_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFnZHl5Y2tubHhvaml3aGxxaWNxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyMjkzOTgsImV4cCI6MjA1OTgwNTM5OH0.lWZLRByfsyRqkK7XZfi21qSeEuOZHJKkFJGC_2ojQR8';

// Use configuration from the config file, but fallback to forced cloud values
const supabaseUrl = supabaseConfig.url || FORCE_CLOUD_URL;
const supabaseAnonKey = supabaseConfig.anonKey || FORCE_CLOUD_ANON_KEY;

// Debug logging for development
if (import.meta.env.DEV) {
  console.log('🔧 Supabase Client Configuration:');
  console.log('URL from config:', supabaseConfig.url);
  console.log('URL being used:', supabaseUrl);
  console.log('Is using cloud URL:', supabaseUrl.includes('supabase.co'));
  console.log('Environment mode:', import.meta.env.MODE);
}

// Validate that we have valid configuration
if (!supabaseUrl || !supabaseUrl.includes('supabase.co')) {
  console.warn('⚠️ Supabase URL not properly configured, using fallback cloud URL');
  console.log('Current URL:', supabaseUrl);
  console.log('Fallback URL:', FORCE_CLOUD_URL);
}

if (!supabaseAnonKey) {
  console.warn('⚠️ Supabase Anon Key not properly configured, using fallback cloud key');
}

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

// Create Supabase client with proper session persistence configuration
// Always use cloud instance, never local development instance
const finalUrl = supabaseUrl.includes('127.0.0.1') || supabaseUrl.includes('localhost') ? FORCE_CLOUD_URL : supabaseUrl;
const finalKey = supabaseAnonKey || FORCE_CLOUD_ANON_KEY;

if (import.meta.env.DEV) {
  console.log('🚀 Creating Supabase client with:');
  console.log('Final URL:', finalUrl);
  console.log('Final Key:', finalKey ? 'SET' : 'NOT SET');
}

export const supabase = createClient<Database>(finalUrl, finalKey, {
  auth: {
    persistSession: true, // Enable session persistence
    storageKey: 'secquiz-auth', // Custom storage key for better identification
    autoRefreshToken: true, // Automatically refresh the token
    detectSessionInUrl: true, // Detect session in URL for OAuth and magic link flows
  },
});