/**
 * <PERSON><PERSON><PERSON> to apply quiz randomization schema fix
 * This script creates the necessary tables for quiz randomization
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables');
  console.error('Required: VITE_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const createQuizSessionsTable = `
-- Create quiz_sessions table for tracking randomized quiz instances
CREATE TABLE IF NOT EXISTS quiz_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL,
  topic_id UUID,
  questions_data JSONB NOT NULL,
  quiz_length INTEGER NOT NULL DEFAULT 10,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() + INTERVAL '2 hours',
  completed_at TIMESTAMP WITH TIME ZONE,
  score INTEGER,
  total_questions INTEGER,
  time_taken INTEGER
);
`;

const createQuizSessionsIndexes = `
-- Create indexes for quiz_sessions
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_user_id ON quiz_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_topic_id ON quiz_sessions(topic_id);
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_created_at ON quiz_sessions(created_at);
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_expires_at ON quiz_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_completed_at ON quiz_sessions(completed_at);
`;

const enableRLS = `
-- Enable Row Level Security for quiz_sessions
ALTER TABLE quiz_sessions ENABLE ROW LEVEL SECURITY;
`;

const createPolicies = `
-- Create policies for quiz_sessions
DROP POLICY IF EXISTS "Users can manage their own quiz sessions" ON quiz_sessions;
CREATE POLICY "Users can manage their own quiz sessions"
  ON quiz_sessions FOR ALL
  USING (true);
`;

const createQuestionAnalyticsTable = `
-- Create question_analytics table for performance tracking
CREATE TABLE IF NOT EXISTS question_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  question_id UUID NOT NULL,
  user_id UUID NOT NULL,
  quiz_session_id UUID,
  answered_correctly BOOLEAN NOT NULL,
  time_to_answer INTEGER,
  selected_option INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
`;

const createAnalyticsIndexes = `
-- Create indexes for question_analytics
CREATE INDEX IF NOT EXISTS idx_question_analytics_question_id ON question_analytics(question_id);
CREATE INDEX IF NOT EXISTS idx_question_analytics_user_id ON question_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_question_analytics_quiz_session_id ON question_analytics(quiz_session_id);
CREATE INDEX IF NOT EXISTS idx_question_analytics_created_at ON question_analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_question_analytics_answered_correctly ON question_analytics(answered_correctly);
`;

const enableAnalyticsRLS = `
-- Enable Row Level Security for question_analytics
ALTER TABLE question_analytics ENABLE ROW LEVEL SECURITY;
`;

const createAnalyticsPolicies = `
-- Create policies for question_analytics
DROP POLICY IF EXISTS "Users can manage their own question analytics" ON question_analytics;
CREATE POLICY "Users can manage their own question analytics"
  ON question_analytics FOR ALL
  USING (true);
`;

const createCleanupFunction = `
-- Create function to clean up expired quiz sessions
CREATE OR REPLACE FUNCTION cleanup_expired_quiz_sessions()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM quiz_sessions 
  WHERE expires_at < NOW() 
    AND completed_at IS NULL;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
`;

async function applySchemaFix() {
  console.log('Starting quiz randomization schema fix...');

  try {
    // Step 1: Create quiz_sessions table
    console.log('Creating quiz_sessions table...');
    const { error: tableError } = await supabase.rpc('execute_sql', {
      query: createQuizSessionsTable
    });
    
    if (tableError) {
      console.error('Error creating quiz_sessions table:', tableError);
      throw tableError;
    }
    console.log('✓ quiz_sessions table created');

    // Step 2: Create indexes
    console.log('Creating indexes for quiz_sessions...');
    const { error: indexError } = await supabase.rpc('execute_sql', {
      query: createQuizSessionsIndexes
    });
    
    if (indexError) {
      console.error('Error creating indexes:', indexError);
      throw indexError;
    }
    console.log('✓ quiz_sessions indexes created');

    // Step 3: Enable RLS
    console.log('Enabling RLS for quiz_sessions...');
    const { error: rlsError } = await supabase.rpc('execute_sql', {
      query: enableRLS
    });
    
    if (rlsError) {
      console.error('Error enabling RLS:', rlsError);
      throw rlsError;
    }
    console.log('✓ RLS enabled for quiz_sessions');

    // Step 4: Create policies
    console.log('Creating policies for quiz_sessions...');
    const { error: policyError } = await supabase.rpc('execute_sql', {
      query: createPolicies
    });
    
    if (policyError) {
      console.error('Error creating policies:', policyError);
      throw policyError;
    }
    console.log('✓ Policies created for quiz_sessions');

    // Step 5: Create question_analytics table
    console.log('Creating question_analytics table...');
    const { error: analyticsTableError } = await supabase.rpc('execute_sql', {
      query: createQuestionAnalyticsTable
    });
    
    if (analyticsTableError) {
      console.error('Error creating question_analytics table:', analyticsTableError);
      throw analyticsTableError;
    }
    console.log('✓ question_analytics table created');

    // Step 6: Create analytics indexes
    console.log('Creating indexes for question_analytics...');
    const { error: analyticsIndexError } = await supabase.rpc('execute_sql', {
      query: createAnalyticsIndexes
    });
    
    if (analyticsIndexError) {
      console.error('Error creating analytics indexes:', analyticsIndexError);
      throw analyticsIndexError;
    }
    console.log('✓ question_analytics indexes created');

    // Step 7: Enable RLS for analytics
    console.log('Enabling RLS for question_analytics...');
    const { error: analyticsRlsError } = await supabase.rpc('execute_sql', {
      query: enableAnalyticsRLS
    });
    
    if (analyticsRlsError) {
      console.error('Error enabling analytics RLS:', analyticsRlsError);
      throw analyticsRlsError;
    }
    console.log('✓ RLS enabled for question_analytics');

    // Step 8: Create analytics policies
    console.log('Creating policies for question_analytics...');
    const { error: analyticsPolicyError } = await supabase.rpc('execute_sql', {
      query: createAnalyticsPolicies
    });
    
    if (analyticsPolicyError) {
      console.error('Error creating analytics policies:', analyticsPolicyError);
      throw analyticsPolicyError;
    }
    console.log('✓ Policies created for question_analytics');

    // Step 9: Create cleanup function
    console.log('Creating cleanup function...');
    const { error: functionError } = await supabase.rpc('execute_sql', {
      query: createCleanupFunction
    });
    
    if (functionError) {
      console.error('Error creating cleanup function:', functionError);
      throw functionError;
    }
    console.log('✓ Cleanup function created');

    // Verify tables exist
    console.log('Verifying tables exist...');
    const { data: tables, error: verifyError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .in('table_name', ['quiz_sessions', 'question_analytics']);

    if (verifyError) {
      console.error('Error verifying tables:', verifyError);
      throw verifyError;
    }

    const tableNames = tables.map(t => t.table_name);
    console.log('Found tables:', tableNames);

    if (tableNames.includes('quiz_sessions') && tableNames.includes('question_analytics')) {
      console.log('✅ Schema fix completed successfully!');
      console.log('Quiz randomization service should now work properly.');
    } else {
      console.error('❌ Some tables are missing:', {
        quiz_sessions: tableNames.includes('quiz_sessions'),
        question_analytics: tableNames.includes('question_analytics')
      });
    }

  } catch (error) {
    console.error('❌ Schema fix failed:', error);
    process.exit(1);
  }
}

// Run the schema fix
applySchemaFix();