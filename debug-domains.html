<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Domains - SecQuiz</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .domain-card { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 SecQuiz Domains Debug Tool</h1>
        <p>✅ <strong>Database schema has been created successfully!</strong></p>
        <p>This tool helps verify that the domains are loading correctly in your application.</p>
        
        <div id="results"></div>
        
        <button onclick="runAllTests()">🧪 Run All Tests</button>
        <button onclick="testSupabaseConnection()">📡 Test Connection</button>
        <button onclick="fetchDomains()">📋 Fetch Domains</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
        
        <h2>📊 Test Results</h2>
        <div id="test-results"></div>
        
        <h2>📋 Domains Data</h2>
        <div id="domains-data"></div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js';
        
        // Supabase configuration
        const SUPABASE_URL = 'https://agdyycknlxojiwhlqicq.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFnZHl5Y2tubHhvaml3aGxxaWNxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyMjkzOTgsImV4cCI6MjA1OTgwNTM5OH0.lWZLRByfsyRqkK7XZfi21qSeEuOZHJKkFJGC_2ojQR8';
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('domains-data').innerHTML = '';
        }
        
        async function testSupabaseConnection() {
            addResult('🧪 Testing Supabase connection...', 'info');
            
            try {
                const { data, error } = await supabase
                    .from('domains')
                    .select('count')
                    .limit(1);
                
                if (error) {
                    addResult(`❌ Connection failed: ${error.message}`, 'error');
                    console.error('Connection error:', error);
                    return false;
                }
                
                addResult('✅ Supabase connection successful!', 'success');
                return true;
            } catch (error) {
                addResult(`💥 Connection error: ${error.message}`, 'error');
                console.error('Connection error:', error);
                return false;
            }
        }
        
        async function fetchDomains() {
            addResult('📋 Fetching domains...', 'info');
            
            try {
                const { data, error } = await supabase
                    .from('domains')
                    .select('*')
                    .eq('is_active', true)
                    .order('sort_order');
                
                if (error) {
                    addResult(`❌ Failed to fetch domains: ${error.message}`, 'error');
                    console.error('Fetch error:', error);
                    return;
                }
                
                addResult(`✅ Successfully fetched ${data?.length || 0} domains`, 'success');
                
                // Display domains
                const domainsDiv = document.getElementById('domains-data');
                if (data && data.length > 0) {
                    domainsDiv.innerHTML = data.map(domain => `
                        <div class="domain-card">
                            <h3>${domain.name}</h3>
                            <p><strong>Slug:</strong> ${domain.slug}</p>
                            <p><strong>Description:</strong> ${domain.description || 'No description'}</p>
                            <p><strong>Difficulty:</strong> ${domain.difficulty_level}</p>
                            <p><strong>Duration:</strong> ${domain.estimated_duration_weeks} weeks</p>
                            <p><strong>Active:</strong> ${domain.is_active ? 'Yes' : 'No'}</p>
                        </div>
                    `).join('');
                } else {
                    domainsDiv.innerHTML = '<p>No domains found</p>';
                }
                
                console.log('Domains data:', data);
                
            } catch (error) {
                addResult(`💥 Error fetching domains: ${error.message}`, 'error');
                console.error('Fetch error:', error);
            }
        }
        
        async function testPolicies() {
            addResult('🔒 Testing RLS policies...', 'info');
            
            try {
                // Test anonymous access
                const { data, error } = await supabase
                    .from('domains')
                    .select('id, name')
                    .limit(1);
                
                if (error) {
                    addResult(`❌ Policy test failed: ${error.message}`, 'error');
                    return false;
                }
                
                addResult('✅ RLS policies allow access', 'success');
                return true;
            } catch (error) {
                addResult(`💥 Policy test error: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function runAllTests() {
            clearResults();
            addResult('🚀 Starting comprehensive domain tests...', 'info');
            
            const connectionOk = await testSupabaseConnection();
            if (!connectionOk) return;
            
            const policiesOk = await testPolicies();
            if (!policiesOk) return;
            
            await fetchDomains();
            
            addResult('🎉 All tests completed!', 'success');
        }
        
        // Make functions global
        window.testSupabaseConnection = testSupabaseConnection;
        window.fetchDomains = fetchDomains;
        window.runAllTests = runAllTests;
        window.clearResults = clearResults;
        
        // Auto-run tests on load
        setTimeout(runAllTests, 1000);
    </script>
</body>
</html>
