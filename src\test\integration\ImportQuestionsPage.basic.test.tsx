import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { TooltipProvider } from '@/components/ui/tooltip';

// Mock all the hooks and dependencies
vi.mock('@/hooks/use-admin', () => ({
  useAdminTopics: vi.fn(() => ({
    data: [
      { id: '1', title: 'JavaScript Basics', slug: 'javascript-basics' },
      { id: '2', title: 'React Fundamentals', slug: 'react-fundamentals' },
    ],
    isLoading: false,
  })),
}));

vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(() => ({ toast: vi.fn() })),
}));

vi.mock('@/hooks/use-auth', () => ({
  useAuth: vi.fn(() => ({
    user: { id: 'test-user', email: '<EMAIL>' },
    isLoading: false,
    signOut: vi.fn(),
  })),
}));

vi.mock('@/hooks/use-admin-status', () => ({
  useAdminStatus: vi.fn(() => ({ isAdmin: true })),
}));

vi.mock('@/utils/csv-import', () => ({
  generateCSVTemplate: vi.fn(() => 'mock,csv,content'),
  generateMultiTopicCSVTemplate: vi.fn(() => 'topic_name,mock,csv,content'),
  parseQuestionCSVEnhanced: vi.fn(),
}));

vi.mock('@/services/batch-import-service', () => ({
  batchImportService: {
    validateImportResult: vi.fn(() => []),
    executeBatchImport: vi.fn(),
  },
}));

vi.mock('@/services/import-error-reporting-service', () => ({
  importErrorReportingService: {
    generateErrorReport: vi.fn(() => null),
  },
}));

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => vi.fn(),
  };
});

// Mock the Navbar component to avoid auth context issues
vi.mock('@/components/Navbar', () => ({
  default: () => <div data-testid="navbar">Navbar</div>,
}));

import ImportQuestionsPage from '@/pages/ImportQuestionsPage';

const renderImportPage = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return render(
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <BrowserRouter>
          <ImportQuestionsPage />
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

describe('ImportQuestionsPage - Basic Functionality', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the import page with basic elements', () => {
    renderImportPage();
    
    expect(screen.getByRole('heading', { name: 'Import Questions' })).toBeInTheDocument();
    expect(screen.getByText('Upload File')).toBeInTheDocument();
    expect(screen.getByText('Preview Data')).toBeInTheDocument();
    expect(screen.getByText('View Results')).toBeInTheDocument();
  });

  it('displays mode selector', () => {
    renderImportPage();
    
    expect(screen.getByText('Single Topic')).toBeInTheDocument();
    expect(screen.getByText('Multi Topic')).toBeInTheDocument();
    expect(screen.getByText('Import Mode')).toBeInTheDocument();
  });

  it('shows file upload section', () => {
    renderImportPage();
    
    expect(screen.getByText('Upload CSV File')).toBeInTheDocument();
    expect(screen.getByLabelText('CSV File')).toBeInTheDocument();
  });

  it('displays template download options', () => {
    renderImportPage();
    
    expect(screen.getByText('Single Topic Template')).toBeInTheDocument();
    expect(screen.getByText('Multi Topic Templates')).toBeInTheDocument();
  });

  it('shows topic selection in single-topic mode by default', () => {
    renderImportPage();
    
    expect(screen.getByText('Select Topic')).toBeInTheDocument();
    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });
});