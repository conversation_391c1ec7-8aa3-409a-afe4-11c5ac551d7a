/**
 * Tests for Question Pool Analytics Service
 * Comprehensive test suite for analytics calculations and reporting
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { QuestionPoolAnalyticsService } from '../question-pool-analytics-service';
import { supabase } from '@/integrations/supabase/client';

// Mock the supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(),
    rpc: vi.fn()
  }
}));

describe('QuestionPoolAnalyticsService', () => {
  const mockSupabaseFrom = vi.mocked(supabase.from);
  const mockSupabaseRpc = vi.mocked(supabase.rpc);

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('recordQuestionAnalytics', () => {
    it('should successfully record question analytics', async () => {
      const mockInsert = vi.fn().mockResolvedValue({ error: null });
      mockSupabaseFrom.mockReturnValue({
        insert: mockInsert
      } as unknown);

      const result = await QuestionPoolAnalyticsService.recordQuestionAnalytics(
        'question-1',
        'user-1',
        'session-1',
        true,
        2,
        30
      );

      expect(result).toBe(true);
      expect(mockSupabaseFrom).toHaveBeenCalledWith('question_analytics');
      expect(mockInsert).toHaveBeenCalledWith({
        question_id: 'question-1',
        user_id: 'user-1',
        quiz_session_id: 'session-1',
        answered_correctly: true,
        selected_option: 2,
        time_to_answer: 30
      });
    });

    it('should handle database errors gracefully', async () => {
      const mockInsert = vi.fn().mockResolvedValue({ 
        error: { message: 'Database error' } 
      });
      mockSupabaseFrom.mockReturnValue({
        insert: mockInsert
      } as unknown);

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const result = await QuestionPoolAnalyticsService.recordQuestionAnalytics(
        'question-1',
        'user-1',
        'session-1',
        true,
        2
      );

      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith('Error recording question analytics:', { message: 'Database error' });
      
      consoleSpy.mockRestore();
    });

    it('should handle exceptions gracefully', async () => {
      mockSupabaseFrom.mockImplementation(() => {
        throw new Error('Connection error');
      });

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const result = await QuestionPoolAnalyticsService.recordQuestionAnalytics(
        'question-1',
        'user-1',
        'session-1',
        true,
        2
      );

      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith('Error in recordQuestionAnalytics:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });
  });

  describe('getTopicQuestionStats', () => {
    it('should return topic statistics successfully', async () => {
      const mockStats = {
        total_questions: 25,
        avg_usage_count: 5.2,
        avg_correct_rate: 72.5,
        questions_never_used: 3,
        questions_low_performance: 2
      };

      mockSupabaseRpc.mockResolvedValue({
        data: [mockStats],
        error: null
      });

      const result = await QuestionPoolAnalyticsService.getTopicQuestionStats('topic-1');

      expect(result).toEqual(mockStats);
      expect(mockSupabaseRpc).toHaveBeenCalledWith('get_topic_question_stats', { topic_uuid: 'topic-1' });
    });

    it('should return default stats when no data found', async () => {
      mockSupabaseRpc.mockResolvedValue({
        data: null,
        error: null
      });

      const result = await QuestionPoolAnalyticsService.getTopicQuestionStats('topic-1');

      expect(result).toEqual({
        total_questions: 0,
        avg_usage_count: 0,
        avg_correct_rate: 0,
        questions_never_used: 0,
        questions_low_performance: 0
      });
    });

    it('should handle database errors', async () => {
      mockSupabaseRpc.mockResolvedValue({
        data: null,
        error: { message: 'Function not found' }
      });

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      await expect(QuestionPoolAnalyticsService.getTopicQuestionStats('topic-1'))
        .rejects.toThrow('Failed to fetch topic stats: Function not found');

      consoleSpy.mockRestore();
    });
  });

  describe('analyzeQuestionDifficulty', () => {
    it('should analyze question difficulty correctly', async () => {
      const mockQuestion = {
        question_text: 'What is cybersecurity?',
        usage_count: 10,
        correct_answer_rate: 65
      };

      const mockAnalytics = [
        { answered_correctly: true, time_to_answer: 25 },
        { answered_correctly: false, time_to_answer: 45 },
        { answered_correctly: true, time_to_answer: 30 },
        { answered_correctly: true, time_to_answer: 20 }
      ];

      // Mock question fetch
      const mockQuestionChain = {
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: mockQuestion,
              error: null
            })
          })
        })
      };

      // Mock analytics fetch
      const mockAnalyticsChain = {
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockResolvedValue({
            data: mockAnalytics,
            error: null
          })
        })
      };

      mockSupabaseFrom
        .mockReturnValueOnce(mockQuestionChain as unknown)
        .mockReturnValueOnce(mockAnalyticsChain as unknown);

      const result = await QuestionPoolAnalyticsService.analyzeQuestionDifficulty('question-1');

      expect(result).toEqual({
        questionId: 'question-1',
        questionText: 'What is cybersecurity?',
        totalAttempts: 4,
        correctAttempts: 3,
        correctRate: 75,
        averageTimeToAnswer: 30,
        difficultyLevel: 'medium',
        needsReview: false
      });
    });

    it('should classify difficulty levels correctly', async () => {
      const testCases = [
        { correctRate: 85, expected: 'easy' },
        { correctRate: 65, expected: 'medium' },
        { correctRate: 45, expected: 'hard' },
        { correctRate: 25, expected: 'very_hard' }
      ];

      for (const testCase of testCases) {
        const mockQuestion = {
          question_text: 'Test question',
          usage_count: 10,
          correct_answer_rate: testCase.correctRate
        };

        const mockAnalytics = Array.from({ length: 100 }, (_, i) => ({
          answered_correctly: i < testCase.correctRate,
          time_to_answer: 30
        }));

        const mockQuestionChain = {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({
                data: mockQuestion,
                error: null
              })
            })
          })
        };

        const mockAnalyticsChain = {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({
              data: mockAnalytics,
              error: null
            })
          })
        };

        mockSupabaseFrom
          .mockReturnValueOnce(mockQuestionChain as unknown)
          .mockReturnValueOnce(mockAnalyticsChain as unknown);

        const result = await QuestionPoolAnalyticsService.analyzeQuestionDifficulty('question-1');
        expect(result.difficultyLevel).toBe(testCase.expected);
      }
    });

    it('should identify questions needing review', async () => {
      const mockQuestion = {
        question_text: 'Very difficult question',
        usage_count: 15,
        correct_answer_rate: 25
      };

      const mockAnalytics = Array.from({ length: 15 }, (_, i) => ({
        answered_correctly: i < 4, // 4 out of 15 correct = 26.7%
        time_to_answer: 60
      }));

      const mockQuestionChain = {
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: mockQuestion,
              error: null
            })
          })
        })
      };

      const mockAnalyticsChain = {
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockResolvedValue({
            data: mockAnalytics,
            error: null
          })
        })
      };

      mockSupabaseFrom
        .mockReturnValueOnce(mockQuestionChain as unknown)
        .mockReturnValueOnce(mockAnalyticsChain as unknown);

      const result = await QuestionPoolAnalyticsService.analyzeQuestionDifficulty('question-1');

      expect(result.needsReview).toBe(true);
      expect(result.difficultyLevel).toBe('very_hard');
    });
  });

  describe('getTopicInsights', () => {
    it('should generate comprehensive topic insights', async () => {
      const mockTopic = { title: 'Cybersecurity Fundamentals' };
      const mockStats = {
        total_questions: 15,
        avg_usage_count: 3.2,
        avg_correct_rate: 45,
        questions_never_used: 8,
        questions_low_performance: 5
      };

      // Mock topic fetch
      const mockTopicSelect = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: mockTopic,
            error: null
          })
        })
      });

      mockSupabaseFrom.mockReturnValue({ select: mockTopicSelect } as unknown);

      // Mock stats RPC
      mockSupabaseRpc.mockResolvedValue({
        data: [mockStats],
        error: null
      });

      const result = await QuestionPoolAnalyticsService.getTopicInsights('topic-1');

      expect(result.topicId).toBe('topic-1');
      expect(result.topicTitle).toBe('Cybersecurity Fundamentals');
      expect(result.questionVariety).toBe('minimal');
      expect(result.alerts).toHaveLength(3); // low performance, high difficulty, unused questions
      expect(result.recommendations).toContain('Add 15 more questions to improve quiz variety');
    });

    it('should classify question variety correctly', async () => {
      const varietyTests = [
        { questions: 10, expected: 'insufficient' },
        { questions: 25, expected: 'minimal' },
        { questions: 40, expected: 'good' },
        { questions: 60, expected: 'excellent' }
      ];

      for (const test of varietyTests) {
        const mockTopic = { title: 'Test Topic' };
        const mockStats = {
          total_questions: test.questions,
          avg_usage_count: 5,
          avg_correct_rate: 70,
          questions_never_used: 2,
          questions_low_performance: 1
        };

        const mockTopicSelect = vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: mockTopic,
              error: null
            })
          })
        });

        mockSupabaseFrom.mockReturnValue({ select: mockTopicSelect } as unknown);
        mockSupabaseRpc.mockResolvedValue({
          data: [mockStats],
          error: null
        });

        const result = await QuestionPoolAnalyticsService.getTopicInsights('topic-1');
        expect(result.questionVariety).toBe(test.expected);
      }
    });
  });

  describe('getContentRecommendations', () => {
    it('should generate content recommendations', async () => {
      const mockTopics = [
        {
          id: 'topic-1',
          title: 'Small Topic',
          questions: Array.from({ length: 8 }, (_, i) => ({
            id: `q${i}`,
            usage_count: 5,
            correct_answer_rate: 70
          }))
        },
        {
          id: 'topic-2',
          title: 'Topic with Low Performance',
          questions: Array.from({ length: 25 }, (_, i) => ({
            id: `q${i}`,
            usage_count: 10,
            correct_answer_rate: i < 5 ? 20 : 70 // 5 low-performing questions
          }))
        }
      ];

      const mockSelect = vi.fn().mockResolvedValue({
        data: mockTopics,
        error: null
      });

      mockSupabaseFrom.mockReturnValue({ select: mockSelect } as any);

      const result = await QuestionPoolAnalyticsService.getContentRecommendations();

      expect(result).toHaveLength(6); // 1 add_questions + 5 review_question
      expect(result[0].type).toBe('add_questions');
      expect(result[0].priority).toBe('high');
      expect(result.filter(r => r.type === 'review_question')).toHaveLength(5);
    });

    it('should prioritize recommendations correctly', async () => {
      const mockTopics = [
        {
          id: 'topic-1',
          title: 'Very Small Topic',
          questions: Array.from({ length: 5 }, () => ({
            id: 'q1',
            usage_count: 1,
            correct_answer_rate: 80
          }))
        },
        {
          id: 'topic-2',
          title: 'Small Topic',
          questions: Array.from({ length: 15 }, () => ({
            id: 'q2',
            usage_count: 3,
            correct_answer_rate: 75
          }))
        }
      ];

      const mockSelect = vi.fn().mockResolvedValue({
        data: mockTopics,
        error: null
      });

      mockSupabaseFrom.mockReturnValue({ select: mockSelect } as any);

      const result = await QuestionPoolAnalyticsService.getContentRecommendations();

      expect(result[0].priority).toBe('high'); // Very small topic should be high priority
      expect(result[1].priority).toBe('medium'); // Small topic should be medium priority
    });
  });

  describe('getAnalyticsSummary', () => {
    it('should generate comprehensive analytics summary', async () => {
      const mockQuestions = [
        { id: 'q1', usage_count: 5, correct_answer_rate: 80, topic_id: 'topic-1' },
        { id: 'q2', usage_count: 0, correct_answer_rate: null, topic_id: 'topic-1' },
        { id: 'q3', usage_count: 10, correct_answer_rate: 25, topic_id: 'topic-2' },
        { id: 'q4', usage_count: 3, correct_answer_rate: 90, topic_id: 'topic-3' }
      ];

      const mockRecentActivity = [
        { created_at: new Date().toISOString(), answered_correctly: true },
        { created_at: new Date().toISOString(), answered_correctly: false },
        { created_at: new Date().toISOString(), answered_correctly: true }
      ];

      // Mock questions fetch
      const mockQuestionsSelect = vi.fn().mockResolvedValue({
        data: mockQuestions,
        error: null
      });

      // Mock recent activity fetch
      const mockActivitySelect = vi.fn().mockReturnValue({
        gte: vi.fn().mockReturnValue({
          order: vi.fn().mockResolvedValue({
            data: mockRecentActivity,
            error: null
          })
        })
      });

      mockSupabaseFrom
        .mockReturnValueOnce({ select: mockQuestionsSelect } as any)
        .mockReturnValueOnce({ select: mockActivitySelect } as any);

      const result = await QuestionPoolAnalyticsService.getAnalyticsSummary();

      expect(result).toEqual({
        totalQuestions: 4,
        questionsUsed: 3,
        questionsNeverUsed: 1,
        questionsNeedingReview: 1,
        topicsWithInsufficientQuestions: 3, // All topics have < 20 questions
        recentActivity: {
          weeklyAttempts: 3,
          weeklyCorrectRate: 66.67
        },
        averageUsageCount: 6, // (5 + 10 + 3) / 3
        averageCorrectRate: 65 // (80 + 25 + 90) / 3
      });
    });

    it('should handle empty data gracefully', async () => {
      const mockQuestionsSelect = vi.fn().mockResolvedValue({
        data: [],
        error: null
      });

      const mockActivitySelect = vi.fn().mockReturnValue({
        gte: vi.fn().mockReturnValue({
          order: vi.fn().mockResolvedValue({
            data: [],
            error: null
          })
        })
      });

      mockSupabaseFrom
        .mockReturnValueOnce({ select: mockQuestionsSelect } as any)
        .mockReturnValueOnce({ select: mockActivitySelect } as any);

      const result = await QuestionPoolAnalyticsService.getAnalyticsSummary();

      expect(result).toEqual({
        totalQuestions: 0,
        questionsUsed: 0,
        questionsNeverUsed: 0,
        questionsNeedingReview: 0,
        topicsWithInsufficientQuestions: 0,
        recentActivity: {
          weeklyAttempts: 0,
          weeklyCorrectRate: 0
        },
        averageUsageCount: 0,
        averageCorrectRate: 0
      });
    });
  });
});