import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import BottomNavigation from "@/components/BottomNavigation";
import Navbar from "@/components/Navbar";
import SecurePaystackButton from "@/components/SecurePaystackButton";
import PaystackButton from "@/components/PaystackButton";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/components/ui/use-toast";
import { 
  ArrowLeft, 
  Clock, 
  Users, 
  BookOpen, 
  PlayCircle,
  CheckCircle,
  Lock,
  Star,
  Trophy,
  Target,
  Zap
} from "lucide-react";
import { DomainWithDetails, DomainEnrollment } from "@/types/domain";
import { fetchDomainBySlug, getDomainEnrollmentStatus, checkDomainPrerequisites, getDifficultyColor, getDurationBadge } from "@/utils/domain-utils";
import { domainSpecificPlans } from "@/utils/paystack";
import { motion } from "framer-motion";

const DomainDetailPage = () => {
  const { domainSlug } = useParams<{ domainSlug: string }>();
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  
  const [domain, setDomain] = useState<DomainWithDetails | null>(null);
  const [enrollment, setEnrollment] = useState<DomainEnrollment | null>(null);
  const [prerequisites, setPrerequisites] = useState<{ met: boolean; missing: string[] }>({ met: true, missing: [] });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (domainSlug) {
      loadDomainData();
    }
  }, [domainSlug, user]);

  const loadDomainData = async () => {
    if (!domainSlug) return;
    
    try {
      setLoading(true);
      
      // Load domain details
      const domainData = await fetchDomainBySlug(domainSlug);
      if (!domainData) {
        toast({
          title: "Domain not found",
          description: "The requested domain could not be found.",
          variant: "destructive",
        });
        navigate("/domains");
        return;
      }
      
      setDomain(domainData);

      // Load user-specific data if authenticated
      if (user) {
        const [enrollmentData, prereqCheck] = await Promise.all([
          getDomainEnrollmentStatus(user.id, domainData.id),
          checkDomainPrerequisites(user.id, domainData)
        ]);
        
        setEnrollment(enrollmentData);
        setPrerequisites(prereqCheck);
      }
    } catch (error) {
      console.error('Error loading domain data:', error);
      toast({
        title: "Error",
        description: "Failed to load domain information.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getSubscriptionPlan = () => {
    if (!domain) return null;
    
    // Find matching subscription plan
    const planKey = Object.keys(domainSpecificPlans).find(key => 
      domainSpecificPlans[key].id.includes(domain.slug.replace('-', ''))
    );
    
    return planKey ? domainSpecificPlans[planKey] : null;
  };

  const canAccessDomain = () => {
    if (!user) return false;
    if (!prerequisites.met) return false;
    return enrollment?.hasActiveSubscription || domain?.topics.some(t => !t.is_premium);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="text-center"
            >
              <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <div className="text-blue-900 text-lg font-medium">Loading domain...</div>
            </motion.div>
          </div>
        </div>
        <BottomNavigation />
      </div>
    );
  }

  if (!domain) {
    return (
      <div className="min-h-screen bg-white">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-12"
          >
            <div className="bg-gray-50 border border-gray-200 rounded-xl p-8 max-w-md mx-auto shadow-lg">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">Domain Not Found</h1>
              <p className="text-gray-600 mb-6">The requested cybersecurity domain could not be found.</p>
              <Link to="/domains">
                <Button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Domains
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
        <BottomNavigation />
      </div>
    );
  }

  const subscriptionPlan = getSubscriptionPlan();
  const difficultyColor = getDifficultyColor(domain.difficultyLevel);
  const durationBadge = getDurationBadge(domain.estimatedDurationWeeks);

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      <div className="container mx-auto px-4 py-8">
        {/* Back Button */}
        <div className="mb-6">
          <Link to="/domains">
            <Button variant="ghost" className="text-gray-700 bg-green-200 hover:bg-blue-600">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Domains
            </Button>
          </Link>
        </div>

        {/* Domain Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <Card className="bg-white border border-gray-200 p-6 sm:p-8 shadow-lg">
            <div className="flex flex-col lg:flex-row gap-6 lg:gap-8">
              {/* Domain Info */}
              <div className="flex-1">
                <div className="flex items-start gap-4 mb-6">
                  <div
                    className="p-3 sm:p-4 rounded-xl flex-shrink-0"
                    style={{ backgroundColor: `${domain.colorTheme}15` }}
                  >
                    <div
                      className="h-6 w-6 sm:h-8 sm:w-8 rounded"
                      style={{ backgroundColor: domain.colorTheme }}
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
                      {domain.name}
                    </h1>
                    <p className="text-gray-600 text-base sm:text-lg mb-4 font-medium">
                      {domain.description}
                    </p>
                    
                    {/* Badges */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      <Badge className={`${difficultyColor} text-xs px-2 py-1 font-medium`}>
                        {domain.difficultyLevel}
                      </Badge>
                      <Badge className={`${durationBadge.color} text-xs px-2 py-1 font-medium`}>
                        <Clock className="h-3 w-3 mr-1" />
                        {durationBadge.text}
                      </Badge>
                      <Badge className="text-blue-600 bg-blue-100 text-xs px-2 py-1 font-medium">
                        <BookOpen className="h-3 w-3 mr-1" />
                        {domain.topics.length} Topics
                      </Badge>
                      <Badge className="text-purple-600 bg-purple-100 text-xs px-2 py-1 font-medium">
                        <Target className="h-3 w-3 mr-1" />
                        {domain.learningPaths.length} Learning Paths
                      </Badge>
                    </div>

                    {/* Prerequisites Warning */}
                    {!prerequisites.met && (
                      <div className="bg-green-500/20 border border-green-600/30 rounded-lg p-4 mb-4">
                        <div className="flex items-center gap-2 text-red-900 mb-2">
                          <Lock className="h-4 w-4" />
                          <span className="font-medium">Prerequisites Required</span>
                        </div>
                        <p className="text-lime-950 text-sm">
                          Complete these domains first: {prerequisites.missing.join(', ')}
                        </p>
                      </div>
                    )}

                    {/* Progress */}
                    {enrollment?.progress && (
                      <div className="mb-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-gray-900 font-medium">Your Progress</span>
                          <span className="text-gray-600">
                            {enrollment.progress.completion_percentage.toFixed(0)}%
                          </span>
                        </div>
                        <Progress
                          value={enrollment.progress.completion_percentage}
                          className="h-2"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Subscription Card */}
              {subscriptionPlan && (
                <div className="lg:w-80">
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    <Card className="bg-gray-50 border border-gray-200 p-6 shadow-lg">
                      <div className="text-center mb-4">
                        <div className="text-2xl font-bold text-gray-900 mb-1">
                          ₦{subscriptionPlan.amount}
                        </div>
                        <div className="text-gray-600 text-sm uppercase font-medium">
                          per {subscriptionPlan.interval}
                        </div>
                      </div>

                      <div className="space-y-2 mb-6">
                        {subscriptionPlan.features.map((feature, index) => (
                          <div key={index} className="flex items-center gap-2 text-sm">
                            <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                            <span className="text-gray-700 font-medium">{feature}</span>
                          </div>
                        ))}
                      </div>

                      {user ? (
                        enrollment?.hasActiveSubscription ? (
                          <Button className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3" disabled>
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Subscribed
                          </Button>
                        ) : prerequisites.met ? (
                          <PaystackButton
                            plan={subscriptionPlan}
                            className="w-full font-medium py-3"
                            buttonText="Buy Domain Pass"
                          />
                        ) : (
                          <Button className="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-3" disabled>
                            <Lock className="h-4 w-4 mr-2" />
                            Prerequisites Required
                          </Button>
                        )
                      ) : (
                        <Link to="/auth?tab=login">
                          <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3">
                            Login to Subscribe
                          </Button>
                        </Link>
                      )}
                    </Card>
                  </motion.div>
                </div>
              )}
            </div>
          </Card>
        </motion.div>

        {/* Content Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="bg-gray-100 border border-gray-200 shadow-lg w-full sm:w-auto">
              <TabsTrigger value="overview" className="data-[state=active]:bg-white data-[state=active]:text-gray-900 text-gray-600 font-medium text-xs sm:text-sm">
                Overview
              </TabsTrigger>
              <TabsTrigger value="learning-paths" className="data-[state=active]:bg-white data-[state=active]:text-gray-900 text-gray-600 font-medium text-xs sm:text-sm">
                Learning Paths
              </TabsTrigger>
              <TabsTrigger value="topics" className="data-[state=active]:bg-white data-[state=active]:text-gray-900 text-gray-600 font-medium text-xs sm:text-sm">
                Topics
              </TabsTrigger>
            </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4">
              {[
                { icon: BookOpen, label: "Topics", value: domain.topics.length, color: "text-blue-600" },
                { icon: Users, label: "Learning Paths", value: domain.learningPaths.length, color: "text-green-600" },
                { icon: Trophy, label: "Questions", value: domain.topics.reduce((sum, t) => sum + (t.question_count || 0), 0), color: "text-yellow-600" },
                { icon: Star, label: "Difficulty", value: domain.difficultyLevel, color: "text-purple-600" }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="bg-gray-50 border border-gray-200 p-4 hover:bg-gray-100 transition-all duration-200 shadow-sm">
                    <div className="flex items-center gap-3">
                      <stat.icon className={`h-6 w-6 sm:h-8 sm:w-8 ${stat.color}`} />
                      <div>
                        <div className="text-xl sm:text-2xl font-bold text-gray-900">{stat.value}</div>
                        <div className="text-gray-600 text-xs sm:text-sm font-medium">{stat.label}</div>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>

            {/* Action Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="flex flex-col sm:flex-row gap-3 sm:gap-4"
            >
              <Link to={`/domains/${domain.slug}/learn`} className="flex-1">
                <Button
                  className="w-full text-white font-medium hover:opacity-90 transition-all duration-200 py-2.5 sm:py-3 text-sm sm:text-base shadow-lg"
                  disabled={!canAccessDomain()}
                  style={{ backgroundColor: domain.colorTheme }}
                >
                  <PlayCircle className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                  Start Learning
                </Button>
              </Link>
              <Link to={`/domains/${domain.slug}/quizzes`} className="flex-1">
                <Button
                  variant="outline"
                  className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 font-medium transition-all duration-200 py-2.5 sm:py-3 text-sm sm:text-base shadow-lg"
                  disabled={!canAccessDomain()}
                >
                  <Zap className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                  Take Quizzes
                </Button>
              </Link>
            </motion.div>
          </TabsContent>

          <TabsContent value="learning-paths" className="space-y-4">
            {domain.learningPaths.map((path, index) => (
              <motion.div
                key={path.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="bg-gray-50 border border-gray-200 p-6 hover:bg-gray-100 transition-all duration-200 shadow-sm">
                  <div className="flex flex-col gap-4">
                    <div className="flex items-start gap-3 sm:gap-4">
                      <div
                        className="p-2 sm:p-3 rounded-lg flex-shrink-0"
                        style={{ backgroundColor: `${domain.colorTheme}15` }}
                      >
                        <Users
                          className="h-4 w-4 sm:h-5 sm:w-5"
                          style={{ color: domain.colorTheme }}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">{path.name}</h3>
                        <p className="text-gray-600 mb-3 sm:mb-4 font-medium text-sm sm:text-base">{path.description}</p>
                        <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3 sm:h-4 sm:w-4" />
                            <span className="font-medium">{path.estimated_hours} hours</span>
                          </div>
                          <Badge className={`${getDifficultyColor(path.difficulty_level)} text-xs px-2 py-1 font-medium`}>
                            {path.difficulty_level}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <Button
                      disabled={!canAccessDomain()}
                      style={{ backgroundColor: domain.colorTheme }}
                      className="text-white font-medium hover:opacity-90 transition-opacity w-full sm:w-auto"
                    >
                      <PlayCircle className="h-4 w-4 mr-2" />
                      Start Path
                    </Button>
                  </div>
                </Card>
              </motion.div>
            ))}
          </TabsContent>

          <TabsContent value="topics" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {domain.topics.map((topic, index) => (
                <motion.div
                  key={topic.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="bg-gray-50 border border-gray-200 p-6 hover:bg-gray-100 transition-all duration-200 shadow-sm h-full">
                    <div className="flex items-start gap-4 mb-4">
                      <div
                        className="p-3 rounded-lg flex-shrink-0"
                        style={{ backgroundColor: `${domain.colorTheme}15` }}
                      >
                        <Target
                          className="h-5 w-5"
                          style={{ color: domain.colorTheme }}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="text-lg font-semibold text-gray-900 mb-2">{topic.title}</h4>
                        <p className="text-gray-600 text-sm mb-4 line-clamp-2 font-medium">{topic.description}</p>
                        <div className="flex flex-wrap items-center gap-2">
                          <Badge className={`${getDifficultyColor(topic.difficulty || 'medium')} text-xs px-2 py-1 font-medium`}>
                            {topic.difficulty}
                          </Badge>
                          <Badge className="text-blue-600 bg-blue-100 text-xs px-2 py-1 font-medium">
                            <Trophy className="h-3 w-3 mr-1" />
                            {topic.question_count} questions
                          </Badge>
                          {topic.is_premium && (
                            <Badge className="text-yellow-600 bg-yellow-100 text-xs px-2 py-1 font-medium">
                              Premium
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          </TabsContent>
          </Tabs>
        </motion.div>
      </div>

      <BottomNavigation />
    </div>
  );
};

export default DomainDetailPage;
