const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('Please ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function applyDomainReorganization() {
  console.log('🚀 Starting domain reorganization...');
  
  try {
    // Read the reorganization SQL file
    const sqlFile = path.join(__dirname, 'reorganize-domains-and-topics.sql');
    const sqlContent = fs.readFileSync(sqlFile, 'utf8');
    
    // Split the SQL into individual statements
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      // Skip SELECT statements (they're for verification)
      if (statement.trim().toUpperCase().startsWith('SELECT')) {
        console.log(`⏭️  Skipping SELECT statement: ${statement.substring(0, 50)}...`);
        continue;
      }
      
      console.log(`🔄 Executing statement ${i + 1}/${statements.length}...`);
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql_query: statement });
        
        if (error) {
          console.error(`❌ Error executing statement ${i + 1}:`, error);
          console.log('Statement:', statement);
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`);
        }
      } catch (err) {
        console.error(`❌ Exception executing statement ${i + 1}:`, err.message);
        console.log('Statement:', statement);
      }
    }
    
    // Verify the changes
    console.log('\n🔍 Verifying reorganization...');
    
    const { data: domainStats, error: statsError } = await supabase
      .from('domains')
      .select(`
        name,
        slug,
        topics(count),
        domain_learning_paths(count)
      `)
      .order('sort_order');
    
    if (statsError) {
      console.error('❌ Error fetching domain statistics:', statsError);
    } else {
      console.log('\n📊 Domain Statistics:');
      domainStats.forEach(domain => {
        console.log(`  ${domain.name} (${domain.slug}):`);
        console.log(`    - Topics: ${domain.topics?.[0]?.count || 0}`);
        console.log(`    - Learning Paths: ${domain.domain_learning_paths?.[0]?.count || 0}`);
      });
    }
    
    // Show topics by domain
    const { data: topicsByDomain, error: topicsError } = await supabase
      .from('topics')
      .select(`
        title,
        difficulty,
        is_premium,
        domains(name, slug)
      `)
      .order('domains.sort_order, title');
    
    if (topicsError) {
      console.error('❌ Error fetching topics:', topicsError);
    } else {
      console.log('\n📚 Topics by Domain:');
      let currentDomain = '';
      topicsByDomain.forEach(topic => {
        const domainName = topic.domains?.name || 'Unassigned';
        if (domainName !== currentDomain) {
          currentDomain = domainName;
          console.log(`\n  ${domainName}:`);
        }
        console.log(`    - ${topic.title} (${topic.difficulty}${topic.is_premium ? ', Premium' : ''})`);
      });
    }
    
    console.log('\n✅ Domain reorganization completed successfully!');
    console.log('\n📋 Summary of changes:');
    console.log('  ✅ Created "Cybersecurity Foundations" domain');
    console.log('  ✅ Renamed "Penetration Testing" to "Ethical Hacking"');
    console.log('  ✅ Moved GDPR/NDPR to Governance & Compliance');
    console.log('  ✅ Moved Network Security topics to Network Security domain');
    console.log('  ✅ Moved Linux Fundamentals and Web Security to Ethical Hacking');
    console.log('  ✅ Moved CISSP topics to Cybersecurity Foundations');
    console.log('  ✅ Fixed Security Awareness domain topic count');
    
  } catch (error) {
    console.error('❌ Fatal error during reorganization:', error);
    process.exit(1);
  }
}

// Run the reorganization
applyDomainReorganization();
