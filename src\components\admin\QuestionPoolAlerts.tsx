import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  Bell,
  BellOff
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import QuestionPoolAnalyticsService, {
  TopicInsights,
  ContentRecommendation
} from '@/services/question-pool-analytics-service';
import { supabase } from '@/integrations/supabase/client';

interface AlertSettings {
  enabled: boolean;
  minQuestionsPerTopic: number;
  minCorrectRate: number;
  maxUnusedPercentage: number;
}

interface TopicAlert {
  topicId: string;
  topicTitle: string;
  alertType: 'insufficient_questions' | 'low_performance' | 'unused_questions' | 'high_difficulty';
  severity: 'low' | 'medium' | 'high';
  message: string;
  count?: number;
  timestamp: Date;
}

const QuestionPoolAlerts: React.FC = () => {
  const { toast } = useToast();
  const [alerts, setAlerts] = useState<TopicAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [alertSettings, setAlertSettings] = useState<AlertSettings>({
    enabled: true,
    minQuestionsPerTopic: 20,
    minCorrectRate: 40,
    maxUnusedPercentage: 30
  });

  const loadAlerts = async () => {
    try {
      setLoading(true);
      
      // Get all topics
      const { data: topics, error: topicsError } = await supabase
        .from('topics')
        .select('id, title');

      if (topicsError) {
        throw new Error(`Failed to fetch topics: ${topicsError.message}`);
      }

      const newAlerts: TopicAlert[] = [];

      // Check each topic for issues
      for (const topic of topics || []) {
        try {
          const insights = await QuestionPoolAnalyticsService.getTopicInsights(topic.id);
          
          // Convert insights alerts to our alert format
          for (const alert of insights.alerts) {
            newAlerts.push({
              topicId: topic.id,
              topicTitle: topic.title,
              alertType: alert.type,
              severity: alert.severity,
              message: alert.message,
              count: alert.count,
              timestamp: new Date()
            });
          }
        } catch (error) {
          console.warn(`Failed to get insights for topic ${topic.id}:`, error);
        }
      }

      // Sort alerts by severity (high -> medium -> low)
      const severityOrder = { high: 3, medium: 2, low: 1 };
      newAlerts.sort((a, b) => severityOrder[b.severity] - severityOrder[a.severity]);

      setAlerts(newAlerts);

    } catch (error) {
      console.error('Error loading alerts:', error);
      toast({
        title: "Error loading alerts",
        description: "Failed to load question pool alerts. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const dismissAlert = (alertIndex: number) => {
    setAlerts(prev => prev.filter((_, index) => index !== alertIndex));
  };

  const toggleAlerts = () => {
    setAlertSettings(prev => ({
      ...prev,
      enabled: !prev.enabled
    }));
  };

  const getSeverityIcon = (severity: 'low' | 'medium' | 'high') => {
    switch (severity) {
      case 'high':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'medium':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'low':
        return <Clock className="h-4 w-4 text-blue-600" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getSeverityColor = (severity: 'low' | 'medium' | 'high') => {
    switch (severity) {
      case 'high': return 'border-red-200 bg-red-50';
      case 'medium': return 'border-yellow-200 bg-yellow-50';
      case 'low': return 'border-blue-200 bg-blue-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  const getAlertTypeLabel = (type: string) => {
    switch (type) {
      case 'insufficient_questions': return 'Insufficient Questions';
      case 'low_performance': return 'Low Performance';
      case 'unused_questions': return 'Unused Questions';
      case 'high_difficulty': return 'High Difficulty';
      default: return type.replace('_', ' ');
    }
  };

  useEffect(() => {
    if (alertSettings.enabled) {
      loadAlerts();
    }
  }, [alertSettings.enabled]);

  // Auto-refresh alerts every 5 minutes
  useEffect(() => {
    if (!alertSettings.enabled) return;

    const interval = setInterval(() => {
      loadAlerts();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [alertSettings.enabled]);

  if (!alertSettings.enabled) {
    return (
      <Card className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <BellOff className="h-5 w-5 text-muted-foreground" />
            <span className="text-muted-foreground">Question Pool Alerts Disabled</span>
          </div>
          <Button onClick={toggleAlerts} variant="outline" size="sm">
            <Bell className="h-4 w-4 mr-2" />
            Enable Alerts
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <Card className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Bell className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold">Question Pool Alerts</h3>
            {alerts.length > 0 && (
              <Badge variant="destructive">{alerts.length}</Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button onClick={loadAlerts} variant="outline" size="sm" disabled={loading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button onClick={toggleAlerts} variant="outline" size="sm">
              <BellOff className="h-4 w-4 mr-2" />
              Disable
            </Button>
          </div>
        </div>
      </Card>

      {/* Alert Settings */}
      <Card className="p-4">
        <h4 className="font-medium mb-3">Alert Thresholds</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <label className="block text-muted-foreground mb-1">Min Questions per Topic</label>
            <input
              type="number"
              value={alertSettings.minQuestionsPerTopic}
              onChange={(e) => setAlertSettings(prev => ({
                ...prev,
                minQuestionsPerTopic: parseInt(e.target.value) || 20
              }))}
              className="w-full p-2 border rounded"
              min="1"
            />
          </div>
          <div>
            <label className="block text-muted-foreground mb-1">Min Correct Rate (%)</label>
            <input
              type="number"
              value={alertSettings.minCorrectRate}
              onChange={(e) => setAlertSettings(prev => ({
                ...prev,
                minCorrectRate: parseInt(e.target.value) || 40
              }))}
              className="w-full p-2 border rounded"
              min="0"
              max="100"
            />
          </div>
          <div>
            <label className="block text-muted-foreground mb-1">Max Unused (%)</label>
            <input
              type="number"
              value={alertSettings.maxUnusedPercentage}
              onChange={(e) => setAlertSettings(prev => ({
                ...prev,
                maxUnusedPercentage: parseInt(e.target.value) || 30
              }))}
              className="w-full p-2 border rounded"
              min="0"
              max="100"
            />
          </div>
        </div>
      </Card>

      {/* Alerts List */}
      {loading ? (
        <Card className="p-4">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-5 w-5 animate-spin mr-2" />
            <span>Loading alerts...</span>
          </div>
        </Card>
      ) : alerts.length === 0 ? (
        <Card className="p-4">
          <div className="flex items-center justify-center text-muted-foreground">
            <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
            <span>No alerts at this time. All topics are healthy!</span>
          </div>
        </Card>
      ) : (
        <div className="space-y-3">
          {alerts.map((alert, index) => (
            <Alert key={index} className={getSeverityColor(alert.severity)}>
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3 flex-1">
                  {getSeverityIcon(alert.severity)}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant="outline" className="text-xs">
                        {getAlertTypeLabel(alert.alertType)}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {alert.severity}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {alert.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                    <h5 className="font-medium text-sm">{alert.topicTitle}</h5>
                    <AlertDescription className="text-sm mt-1">
                      {alert.message}
                    </AlertDescription>
                  </div>
                </div>
                <Button
                  onClick={() => dismissAlert(index)}
                  variant="ghost"
                  size="sm"
                  className="ml-2"
                >
                  <XCircle className="h-4 w-4" />
                </Button>
              </div>
            </Alert>
          ))}
        </div>
      )}

      {/* Summary Stats */}
      {alerts.length > 0 && (
        <Card className="p-4">
          <h4 className="font-medium mb-3">Alert Summary</h4>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-red-600">
                {alerts.filter(a => a.severity === 'high').length}
              </p>
              <p className="text-sm text-muted-foreground">High Priority</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-yellow-600">
                {alerts.filter(a => a.severity === 'medium').length}
              </p>
              <p className="text-sm text-muted-foreground">Medium Priority</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-blue-600">
                {alerts.filter(a => a.severity === 'low').length}
              </p>
              <p className="text-sm text-muted-foreground">Low Priority</p>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default QuestionPoolAlerts;