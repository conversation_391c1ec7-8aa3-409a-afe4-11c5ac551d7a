import React, { useState, useEffect, useCallback } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import {
  BarChart3,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  BookOpen,
  Target,
  Lightbulb,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import QuestionPoolAnalyticsService, {
  TopicInsights,
  QuestionDifficultyAnalysis,
  ContentRecommendation,
  QuestionWithStats
} from '@/services/question-pool-analytics-service';
import QuestionPoolAlerts from './QuestionPoolAlerts';

interface AnalyticsSummary {
  totalQuestions: number;
  questionsUsed: number;
  questionsNeverUsed: number;
  questionsNeedingReview: number;
  topicsWithInsufficientQuestions: number;
  recentActivity: {
    weeklyAttempts: number;
    weeklyCorrectRate: number;
  };
  averageUsageCount: number;
  averageCorrectRate: number;
}

const QuestionPoolAnalyticsDashboard: React.FC = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [analyticsSummary, setAnalyticsSummary] = useState<AnalyticsSummary | null>(null);
  const [topicInsights, setTopicInsights] = useState<TopicInsights[]>([]);
  const [questionsNeedingReview, setQuestionsNeedingReview] = useState<QuestionDifficultyAnalysis[]>([]);
  const [contentRecommendations, setContentRecommendations] = useState<ContentRecommendation[]>([]);
  const [selectedTopicId, setSelectedTopicId] = useState<string>('');
  const [topicQuestionUsage, setTopicQuestionUsage] = useState<QuestionWithStats[]>([]);
  const [availableTopics, setAvailableTopics] = useState<Array<{id: string, title: string}>>([]);

  const loadAnalyticsData = useCallback(async () => {
    try {
      setLoading(true);
      
      // Load analytics summary with error handling
      try {
        const summary = await QuestionPoolAnalyticsService.getAnalyticsSummary();
        setAnalyticsSummary(summary);
      } catch (summaryError) {
        console.error('Error loading analytics summary:', summaryError);
        // Set default values if summary fails
        setAnalyticsSummary({
          totalQuestions: 0,
          questionsUsed: 0,
          questionsNeverUsed: 0,
          questionsNeedingReview: 0,
          topicsWithInsufficientQuestions: 0,
          recentActivity: { weeklyAttempts: 0, weeklyCorrectRate: 0 },
          averageUsageCount: 0,
          averageCorrectRate: 0
        });
      }

      // Load content recommendations with error handling
      try {
        const recommendations = await QuestionPoolAnalyticsService.getContentRecommendations();
        setContentRecommendations(recommendations);
      } catch (recError) {
        console.error('Error loading recommendations:', recError);
        setContentRecommendations([]);
      }

      // Load questions needing review with error handling
      try {
        const reviewQuestions = await QuestionPoolAnalyticsService.getQuestionsNeedingReview();
        setQuestionsNeedingReview(reviewQuestions);
      } catch (reviewError) {
        console.error('Error loading questions needing review:', reviewError);
        setQuestionsNeedingReview([]);
      }

      // Load available topics with error handling
      try {
        const { data: topics, error: topicsError } = await supabase
          .from('topics')
          .select('id, title')
          .order('title');

        if (topicsError) {
          console.error('Error loading topics:', topicsError);
          setAvailableTopics([]);
        } else {
          setAvailableTopics(topics || []);
        }
      } catch (topicsError) {
        console.error('Error loading topics:', topicsError);
        setAvailableTopics([]);
      }

      toast({
        title: "Analytics loaded",
        description: "Question pool analytics data has been refreshed.",
      });

    } catch (error) {
      console.error('Error loading analytics:', error);
      toast({
        title: "Error loading analytics",
        description: "Failed to load analytics data. Some features may not work properly.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const loadTopicInsights = useCallback(async (topicId: string) => {
    try {
      const insights = await QuestionPoolAnalyticsService.getTopicInsights(topicId);
      setTopicInsights([insights]);
      
      const usage = await QuestionPoolAnalyticsService.getTopicQuestionUsage(topicId);
      setTopicQuestionUsage(usage);
      
    } catch (error) {
      console.error('Error loading topic insights:', error);
      toast({
        title: "Error loading topic insights",
        description: "Failed to load topic insights. Please try again.",
        variant: "destructive",
      });
    }
  }, [toast]);

  useEffect(() => {
    loadAnalyticsData();
  }, [loadAnalyticsData]);

  useEffect(() => {
    if (selectedTopicId) {
      loadTopicInsights(selectedTopicId);
    }
  }, [selectedTopicId, loadTopicInsights]);

  const getSeverityColor = (severity: 'low' | 'medium' | 'high') => {
    switch (severity) {
      case 'high': return 'text-red-600 bg-red-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-blue-600 bg-blue-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getPriorityColor = (priority: 'low' | 'medium' | 'high') => {
    switch (priority) {
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'outline';
    }
  };

  const getVarietyColor = (variety: 'insufficient' | 'minimal' | 'good' | 'excellent') => {
    switch (variety) {
      case 'excellent': return 'text-green-600 bg-green-50';
      case 'good': return 'text-blue-600 bg-blue-50';
      case 'minimal': return 'text-yellow-600 bg-yellow-50';
      case 'insufficient': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        <span>Loading analytics...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Question Pool Analytics</h2>
          <p className="text-muted-foreground">
            Monitor question usage, performance, and content quality
          </p>
        </div>
        <Button onClick={loadAnalyticsData} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh Data
        </Button>
      </div>

      {/* Analytics Summary Cards */}
      {analyticsSummary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="flex items-center">
              <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                <BookOpen className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Questions</p>
                <p className="text-2xl font-bold">{analyticsSummary.totalQuestions}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center">
              <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Questions Used</p>
                <p className="text-2xl font-bold">{analyticsSummary.questionsUsed}</p>
                <p className="text-xs text-muted-foreground">
                  {analyticsSummary.totalQuestions > 0 
                    ? Math.round((analyticsSummary.questionsUsed / analyticsSummary.totalQuestions) * 100)
                    : 0}% of total
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center">
              <div className="h-10 w-10 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Need Review</p>
                <p className="text-2xl font-bold">{analyticsSummary.questionsNeedingReview}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center">
              <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                <Target className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Avg Correct Rate</p>
                <p className="text-2xl font-bold">{analyticsSummary.averageCorrectRate}%</p>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Recent Activity */}
      {analyticsSummary?.recentActivity && (
        <Card className="p-4">
          <h3 className="text-lg font-semibold mb-3 flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Recent Activity (Last 7 Days)
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">Quiz Attempts</p>
              <p className="text-xl font-bold">{analyticsSummary.recentActivity.weeklyAttempts}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Success Rate</p>
              <p className="text-xl font-bold">{analyticsSummary.recentActivity.weeklyCorrectRate}%</p>
            </div>
          </div>
        </Card>
      )}

      <Tabs defaultValue="alerts" className="space-y-4">
        <TabsList>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          <TabsTrigger value="review">Questions to Review</TabsTrigger>
          <TabsTrigger value="topics">Topic Insights</TabsTrigger>
        </TabsList>

        {/* Alerts Tab */}
        <TabsContent value="alerts" className="space-y-4">
          <QuestionPoolAlerts />
        </TabsContent>

        {/* Content Recommendations Tab */}
        <TabsContent value="recommendations" className="space-y-4">
          <Card className="p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <Lightbulb className="h-5 w-5 mr-2" />
              Content Improvement Recommendations
            </h3>
            
            {contentRecommendations.length === 0 ? (
              <p className="text-muted-foreground">No recommendations at this time. Great job!</p>
            ) : (
              <div className="space-y-3">
                {contentRecommendations.map((rec, index) => (
                  <Alert key={index}>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <Badge variant={getPriorityColor(rec.priority)}>
                              {rec.priority} priority
                            </Badge>
                            <Badge variant="outline">{rec.type.replace('_', ' ')}</Badge>
                          </div>
                          <p className="font-medium">{rec.message}</p>
                          <p className="text-sm text-muted-foreground mt-1">{rec.details}</p>
                        </div>
                      </div>
                    </AlertDescription>
                  </Alert>
                ))}
              </div>
            )}
          </Card>
        </TabsContent>

        {/* Questions to Review Tab */}
        <TabsContent value="review" className="space-y-4">
          <Card className="p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <XCircle className="h-5 w-5 mr-2" />
              Questions Needing Review
            </h3>
            
            {questionsNeedingReview.length === 0 ? (
              <p className="text-muted-foreground">No questions need review at this time.</p>
            ) : (
              <div className="space-y-4">
                {questionsNeedingReview.map((question) => (
                  <Card key={question.questionId} className="p-4 border-l-4 border-l-yellow-500">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Badge variant="outline">{question.difficultyLevel}</Badge>
                        <Badge variant="destructive">
                          {question.correctRate}% correct rate
                        </Badge>
                      </div>
                      <p className="font-medium">{question.questionText.substring(0, 100)}...</p>
                      <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                        <div>Total Attempts: {question.totalAttempts}</div>
                        <div>Avg Time: {question.averageTimeToAnswer}s</div>
                      </div>
                      <Progress value={question.correctRate} className="h-2" />
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </Card>
        </TabsContent>

        {/* Topic Insights Tab */}
        <TabsContent value="topics" className="space-y-4">
          <Card className="p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Topic Analysis
            </h3>
            
            {/* Topic Selection */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">Select Topic for Analysis</label>
              <select
                value={selectedTopicId}
                onChange={(e) => setSelectedTopicId(e.target.value)}
                className="w-full p-2 border rounded-md"
              >
                <option value="">Select a topic...</option>
                {availableTopics.map((topic) => (
                  <option key={topic.id} value={topic.id}>
                    {topic.title}
                  </option>
                ))}
              </select>
            </div>

            {/* Topic Insights Display */}
            {topicInsights.map((insight) => (
              <div key={insight.topicId} className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-medium">{insight.topicTitle}</h4>
                  <Badge className={getVarietyColor(insight.questionVariety)}>
                    {insight.questionVariety} variety
                  </Badge>
                </div>

                {/* Topic Stats */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold">{insight.stats.total_questions}</p>
                    <p className="text-sm text-muted-foreground">Total Questions</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">{insight.stats.avg_usage_count.toFixed(1)}</p>
                    <p className="text-sm text-muted-foreground">Avg Usage</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">{insight.stats.avg_correct_rate.toFixed(1)}%</p>
                    <p className="text-sm text-muted-foreground">Avg Correct Rate</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">{insight.stats.questions_never_used}</p>
                    <p className="text-sm text-muted-foreground">Never Used</p>
                  </div>
                </div>

                {/* Alerts */}
                {insight.alerts.length > 0 && (
                  <div className="space-y-2">
                    <h5 className="font-medium">Alerts</h5>
                    {insight.alerts.map((alert, index) => (
                      <Alert key={index}>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                          <Badge className={getSeverityColor(alert.severity)} variant="outline">
                            {alert.severity}
                          </Badge>
                          <span className="ml-2">{alert.message}</span>
                        </AlertDescription>
                      </Alert>
                    ))}
                  </div>
                )}

                {/* Recommendations */}
                {insight.recommendations.length > 0 && (
                  <div className="space-y-2">
                    <h5 className="font-medium">Recommendations</h5>
                    <ul className="list-disc list-inside space-y-1">
                      {insight.recommendations.map((rec, index) => (
                        <li key={index} className="text-sm text-muted-foreground">{rec}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Question Usage Details */}
                {topicQuestionUsage.length > 0 && (
                  <div className="space-y-2">
                    <h5 className="font-medium">Question Usage Details</h5>
                    <div className="max-h-64 overflow-y-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left p-2">Question</th>
                            <th className="text-left p-2">Usage Count</th>
                            <th className="text-left p-2">Correct Rate</th>
                            <th className="text-left p-2">Last Used</th>
                          </tr>
                        </thead>
                        <tbody>
                          {topicQuestionUsage.slice(0, 10).map((question) => (
                            <tr key={question.id} className="border-b">
                              <td className="p-2">
                                {question.question_text.substring(0, 50)}...
                              </td>
                              <td className="p-2">{question.usage_count || 0}</td>
                              <td className="p-2">
                                {question.correct_answer_rate ? `${question.correct_answer_rate}%` : 'N/A'}
                              </td>
                              <td className="p-2">
                                {question.last_used 
                                  ? new Date(question.last_used).toLocaleDateString()
                                  : 'Never'
                                }
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default QuestionPoolAnalyticsDashboard;