-- Reorganize Domains and Topics Script
-- This script will reorganize the domain structure and topic assignments
-- Note: This script assumes domains already exist from initial-domains-data.sql

-- 1. Rename "Penetration Testing" to "Ethical Hacking" (only if Penetration Testing exists and Ethical Hacking doesn't)
UPDATE public.domains 
SET name = 'Ethical Hacking', 
    slug = 'ethical-hacking',
    description = 'Ethical hacking, vulnerability assessment, and penetration testing methodologies'
WHERE slug = 'penetration-testing'
  AND NOT EXISTS (SELECT 1 FROM public.domains WHERE name = 'Ethical Hacking');

-- 2. Update domain subscription plans for the renamed domain (only if the old plan exists)
UPDATE public.domain_subscription_plans 
SET plan_id = 'domain-ethical-hacking',
    name = 'Ethical Hacking Focus'
WHERE plan_id = 'domain-penetration-testing'
  AND EXISTS (SELECT 1 FROM public.domains WHERE slug = 'ethical-hacking');

-- 3. Update learning paths for the renamed domain (only if they exist)
UPDATE public.domain_learning_paths 
SET name = 'Ethical Hacking Basics'
WHERE domain_id = (SELECT id FROM public.domains WHERE slug = 'ethical-hacking' LIMIT 1) 
  AND name = 'Ethical Hacking Basics'
  AND EXISTS (SELECT 1 FROM public.domains WHERE slug = 'ethical-hacking');

UPDATE public.domain_learning_paths 
SET name = 'Advanced Ethical Hacking'
WHERE domain_id = (SELECT id FROM public.domains WHERE slug = 'ethical-hacking' LIMIT 1) 
  AND name = 'Advanced Penetration Testing'
  AND EXISTS (SELECT 1 FROM public.domains WHERE slug = 'ethical-hacking');

-- 3b. Create CPN Certification domain if it doesn't exist
INSERT INTO public.domains (name, slug, description, icon, color_theme, difficulty_level, estimated_duration_weeks, is_active, sort_order)
SELECT 'CPN Certification', 'cpn-certification', 'Preparation materials and exams for CPN Cybersecurity Certification', 'award', '#0EA5E9', 'intermediate', 4, true, 12
WHERE NOT EXISTS (SELECT 1 FROM public.domains WHERE slug = 'cpn-certification');

-- 4. Move specific Cybersecurity Foundation topics from Security Awareness to Cybersecurity Foundations domain
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'cybersecurity-foundations'
  AND title IN (
    'Cybersecurity Foundation - Hard',
    'Cybersecurity Foundation - Easy',
    'Cybersecurity Foundation - Medium'
  );

-- 5. Move other topics to Cybersecurity Foundations domain
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'cybersecurity-foundations'
  AND title IN (
    'Cybersecurity Awareness Skill',
    'Security Best Practices',
    'Phishing Awareness',
    'Password Security',
    'Social Engineering Awareness',
    'Mobile Device Security',
    'Home Office Security',
    'ISC2 Certification',
    'CISSP Fundamentals'
  );

-- 6. Move GDPR and NDPR to Governance & Compliance
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'governance-compliance'
  AND title IN (
    'GDPR',
    'NDPR'
  );

-- 7. Move Network Security topics to Network Security domain
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'network-security'
  AND title IN (
    'Network Security',
    'Network Security Fundamentals',
    'Advanced Network Security',
    'Firewall Management',
    'Network Protocols',
    'VPN Technologies',
    'Network Monitoring',
    'Intrusion Detection Systems',
    'Network Access Control'
  );

-- 8. Move Linux Fundamentals and Web Security to Ethical Hacking domain
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'ethical-hacking'
  AND title IN (
    'Linux Fundamentals',
    'Web Security',
    'Web Application Security'
  );

-- 9. Move remaining CISSP topics to Cybersecurity Foundations
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'cybersecurity-foundations'
  AND title IN (
    'Security Architecture',
    'Asset Security',
    'Security Engineering',
    'Communication and Network Security',
    'Identity and Access Management',
    'Security Assessment and Testing',
    'Security Operations',
    'Software Development Security'
  );

-- 9b. Move CISSP Prep to CISSP Preparation domain
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'cissp-preparation'
  AND title IN (
    'CISSP Prep'
  );

-- 10. Create learning paths for Cybersecurity Foundations (only if they don't exist)
INSERT INTO public.domain_learning_paths (domain_id, name, description, difficulty_level, estimated_hours, sort_order)
SELECT d.id, 'Security Fundamentals', 'Basic cybersecurity concepts and principles', 'beginner', 20, 1
FROM public.domains d
WHERE d.slug = 'cybersecurity-foundations'
  AND NOT EXISTS (SELECT 1 FROM public.domain_learning_paths WHERE domain_id = d.id AND name = 'Security Fundamentals');

INSERT INTO public.domain_learning_paths (domain_id, name, description, difficulty_level, estimated_hours, sort_order)
SELECT d.id, 'Security Awareness & Best Practices', 'Security awareness and best practices for everyone', 'beginner', 15, 2
FROM public.domains d
WHERE d.slug = 'cybersecurity-foundations'
  AND NOT EXISTS (SELECT 1 FROM public.domain_learning_paths WHERE domain_id = d.id AND name = 'Security Awareness & Best Practices');

INSERT INTO public.domain_learning_paths (domain_id, name, description, difficulty_level, estimated_hours, sort_order)
SELECT d.id, 'CISSP Preparation', 'CISSP certification preparation materials', 'advanced', 60, 3
FROM public.domains d
WHERE d.slug = 'cybersecurity-foundations'
  AND NOT EXISTS (SELECT 1 FROM public.domain_learning_paths WHERE domain_id = d.id AND name = 'CISSP Preparation');

-- 11. Update Security Awareness domain to have proper topics
-- Move basic awareness topics back to Security Awareness
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'security-awareness'
  AND title IN (
    'Cybersecurity Awareness Skill',
    'Security Best Practices',
    'Phishing Awareness',
    'Password Security',
    'Social Engineering Awareness',
    'Mobile Device Security',
    'Home Office Security'
  );

-- 12. Update sort order for all domains to reflect new structure
UPDATE public.domains SET sort_order = 1 WHERE slug = 'cybersecurity-foundations';
UPDATE public.domains SET sort_order = 2 WHERE slug = 'network-security';
UPDATE public.domains SET sort_order = 3 WHERE slug = 'cloud-security';
UPDATE public.domains SET sort_order = 4 WHERE slug = 'incident-response';
UPDATE public.domains SET sort_order = 5 WHERE slug = 'ethical-hacking';
UPDATE public.domains SET sort_order = 6 WHERE slug = 'governance-compliance';
UPDATE public.domains SET sort_order = 7 WHERE slug = 'cryptography';
UPDATE public.domains SET sort_order = 8 WHERE slug = 'security-awareness';
UPDATE public.domains SET sort_order = 9 WHERE slug = 'cissp-preparation';
UPDATE public.domains SET sort_order = 10 WHERE slug = 'digital-forensics';
UPDATE public.domains SET sort_order = 11 WHERE slug = 'application-security';
UPDATE public.domains SET sort_order = 12 WHERE slug = 'cpn-certification';

-- 13. Create domain subscription plan for Cybersecurity Foundations (only if it doesn't exist)
INSERT INTO public.domain_subscription_plans (domain_id, plan_id, name, amount, interval, features)
SELECT d.id, 'domain-cybersecurity-foundations', 'Cybersecurity Foundations Focus', 49900, 'weekly', 
 ARRAY['Cybersecurity fundamentals content', 'Security awareness materials', 'CISSP preparation content', 'Basic security concepts']
FROM public.domains d
WHERE d.slug = 'cybersecurity-foundations'
  AND NOT EXISTS (SELECT 1 FROM public.domain_subscription_plans WHERE plan_id = 'domain-cybersecurity-foundations');

-- 14. Verify the reorganization with topic counts
SELECT 
  d.name as domain_name,
  d.slug as domain_slug,
  COUNT(t.id) as topic_count,
  COUNT(DISTINCT dlp.id) as learning_path_count
FROM public.domains d
LEFT JOIN public.topics t ON d.id = t.domain_id
LEFT JOIN public.domain_learning_paths dlp ON d.id = dlp.domain_id
GROUP BY d.id, d.name, d.slug, d.sort_order
ORDER BY d.sort_order;

-- 15. Show topics by domain for verification
SELECT 
  d.name as domain_name,
  t.title as topic_title,
  t.difficulty,
  t.is_premium
FROM public.domains d
LEFT JOIN public.topics t ON d.id = t.domain_id
ORDER BY d.sort_order, t.title;

-- 16. Show specific Cybersecurity Foundation topics that were moved
SELECT 
  d.name as domain_name,
  t.title as topic_title,
  t.difficulty,
  t.is_premium
FROM public.domains d
JOIN public.topics t ON d.id = t.domain_id
WHERE d.slug = 'cybersecurity-foundations'
  AND t.title IN (
    'Cybersecurity Foundation - Hard',
    'Cybersecurity Foundation - Easy',
    'Cybersecurity Foundation - Medium'
  )
ORDER BY t.title;

-- 17. Show specific GDPR and NDPR topics that were moved to Governance & Compliance
SELECT 
  d.name as domain_name,
  t.title as topic_title,
  t.difficulty,
  t.is_premium
FROM public.domains d
JOIN public.topics t ON d.id = t.domain_id
WHERE d.slug = 'governance-compliance'
  AND t.title IN (
    'GDPR',
    'NDPR'
  )
ORDER BY t.title;

-- 19. Move CPN Cybersecurity Certification Exam to CPN Certification domain
UPDATE public.topics 
SET domain_id = d.id
FROM public.domains d
WHERE d.slug = 'cpn-certification'
  AND title IN (
    'CPN Cybersecurity Certification Exam'
  );

-- 18. Show final topic counts for all domains (for domain cards display)
SELECT 
  d.name as domain_name,
  d.slug as domain_slug,
  d.difficulty_level,
  d.estimated_duration_weeks,
  COUNT(t.id) as topic_count,
  COUNT(DISTINCT dlp.id) as learning_path_count
FROM public.domains d
LEFT JOIN public.topics t ON d.id = t.domain_id
LEFT JOIN public.domain_learning_paths dlp ON d.id = dlp.domain_id
WHERE d.is_active = true
GROUP BY d.id, d.name, d.slug, d.difficulty_level, d.estimated_duration_weeks, d.sort_order
ORDER BY d.sort_order;
