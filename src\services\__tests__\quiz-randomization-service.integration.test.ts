/**
 * Integration tests for Quiz Randomization Service
 * Tests the service with actual database interactions (mocked)
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { QuizRandomizationService } from '../quiz-randomization-service';

// Mock the supabase client with more realistic responses
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(),
    rpc: vi.fn()
  }
}));

vi.mock('@/utils/answer-validation', () => ({
  parseCorrectAnswer: (answer: any, optionsCount = 4) => ({
    correctIndex: typeof answer === 'number' ? answer : parseInt(answer) || 0,
    isValid: true,
    originalValue: answer
  }),
  parseQuestionOptions: (options: any) => {
    if (Array.isArray(options)) return options;
    if (typeof options === 'object' && options !== null) {
      return Object.keys(options).sort().map(key => options[key]);
    }
    return ['Option A', 'Option B', 'Option C', 'Option D'];
  }
}));

import { supabase } from '@/integrations/supabase/client';

describe('QuizRandomizationService Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('End-to-End Quiz Session Flow', () => {
    it('should create, retrieve, and complete a quiz session', async () => {
      const mockQuestions = [
        {
          id: 'q1',
          topic_id: 'topic1',
          question_text: 'What is 2+2?',
          options: { '0': '3', '1': '4', '2': '5', '3': '6' },
          correct_answer: '1',
          explanation: '2+2 equals 4',
          difficulty: 'easy',
          usage_count: 0,
          last_used: null,
          correct_answer_rate: null,
          created_at: '2024-01-01',
          updated_at: '2024-01-01',
          created_by: null,
          is_premium: false
        },
        {
          id: 'q2',
          topic_id: 'topic1',
          question_text: 'What is 3+3?',
          options: { '0': '5', '1': '6', '2': '7', '3': '8' },
          correct_answer: '1',
          explanation: '3+3 equals 6',
          difficulty: 'easy',
          usage_count: 2,
          last_used: '2024-01-01',
          correct_answer_rate: 85.5,
          created_at: '2024-01-01',
          updated_at: '2024-01-01',
          created_by: null,
          is_premium: false
        }
      ];

      const mockSessionData = {
        id: 'session123',
        user_id: 'user1',
        topic_id: 'topic1',
        quiz_length: 2,
        expires_at: new Date(Date.now() + 3600000).toISOString(),
        questions_data: {
          questions: [
            {
              id: 'q1',
              originalCorrectIndex: 1,
              shuffledCorrectIndex: 2,
              optionMapping: [2, 0, 1, 3]
            },
            {
              id: 'q2',
              originalCorrectIndex: 1,
              shuffledCorrectIndex: 0,
              optionMapping: [1, 3, 0, 2]
            }
          ]
        }
      };

      // Mock question selection for session creation
      (supabase.from as any)
        .mockReturnValueOnce({
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              data: mockQuestions,
              error: null
            })
          })
        })
        // Mock session insertion
        .mockReturnValueOnce({
          insert: vi.fn().mockReturnValue({
            select: vi.fn().mockReturnValue({
              single: vi.fn().mockReturnValue({
                data: mockSessionData,
                error: null
              })
            })
          })
        })
        // Mock session retrieval
        .mockReturnValueOnce({
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                single: vi.fn().mockReturnValue({
                  data: mockSessionData,
                  error: null
                })
              })
            })
          })
        })
        // Mock question retrieval for session reconstruction
        .mockReturnValueOnce({
          select: vi.fn().mockReturnValue({
            in: vi.fn().mockReturnValue({
              data: mockQuestions,
              error: null
            })
          })
        })
        // Mock session completion
        .mockReturnValueOnce({
          update: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                error: null
              })
            })
          })
        });

      // Step 1: Generate quiz session
      const sessionData = await QuizRandomizationService.generateQuizSession('topic1', 'user1', 2);
      
      expect(sessionData.sessionId).toBe('session123');
      expect(sessionData.questions).toHaveLength(2);
      expect(sessionData.questions[0]).toHaveProperty('shuffledOptions');
      expect(sessionData.questions[0]).toHaveProperty('optionMapping');

      // Step 2: Retrieve quiz session
      const retrievedSession = await QuizRandomizationService.getQuizSession('session123', 'user1');
      
      expect(retrievedSession).not.toBeNull();
      expect(retrievedSession?.sessionId).toBe('session123');
      expect(retrievedSession?.questions).toHaveLength(2);

      // Step 3: Complete quiz session
      const completed = await QuizRandomizationService.completeQuizSession('session123', 'user1', 85, 120);
      
      expect(completed).toBe(true);
    });

    it('should handle question analytics recording', async () => {
      // Mock analytics insertion
      (supabase.from as any).mockReturnValue({
        insert: vi.fn().mockReturnValue({
          error: null
        })
      });

      const result = await QuizRandomizationService.recordQuestionAnalytics(
        'q1',
        'user1',
        'session123',
        true,
        2,
        15
      );

      expect(result).toBe(true);
      expect(supabase.from).toHaveBeenCalledWith('question_analytics');
    });

    it('should fetch topic question statistics', async () => {
      const mockStats = {
        total_questions: 50,
        avg_usage_count: 4.2,
        avg_correct_rate: 78.5,
        questions_never_used: 8,
        questions_low_performance: 3
      };

      (supabase.rpc as any).mockResolvedValue({
        data: [mockStats],
        error: null
      });

      const stats = await QuizRandomizationService.getTopicQuestionStats('topic1');

      expect(stats).toEqual(mockStats);
      expect(supabase.rpc).toHaveBeenCalledWith('get_topic_question_stats', {
        topic_uuid: 'topic1'
      });
    });

    it('should cleanup expired sessions', async () => {
      (supabase.rpc as any).mockResolvedValue({
        data: 12,
        error: null
      });

      const cleanedCount = await QuizRandomizationService.cleanupExpiredSessions();

      expect(cleanedCount).toBe(12);
      expect(supabase.rpc).toHaveBeenCalledWith('cleanup_expired_quiz_sessions');
    });
  });

  describe('Question Selection Strategies', () => {
    it('should prioritize unused questions when preferUnused is true', async () => {
      const mockQuestions = [
        { id: 'q1', usage_count: 0, last_used: null },
        { id: 'q2', usage_count: 5, last_used: '2024-01-01' },
        { id: 'q3', usage_count: 0, last_used: null },
        { id: 'q4', usage_count: 2, last_used: '2024-01-02' }
      ].map(q => ({
        ...q,
        topic_id: 'topic1',
        question_text: `Question ${q.id}`,
        options: { '0': 'A', '1': 'B', '2': 'C', '3': 'D' },
        correct_answer: '0',
        explanation: 'Test explanation',
        difficulty: 'medium',
        correct_answer_rate: null,
        created_at: '2024-01-01',
        updated_at: '2024-01-01',
        created_by: null,
        is_premium: false
      }));

      (supabase.from as any).mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            data: mockQuestions,
            error: null
          })
        })
      });

      const result = await QuizRandomizationService.selectRandomQuestions({
        topicId: 'topic1',
        count: 2,
        preferUnused: true
      });

      // Should prefer unused questions (q1 and q3)
      const unusedQuestions = result.filter(q => !q.usage_count || q.usage_count === 0);
      expect(unusedQuestions.length).toBeGreaterThan(0);
    });

    it('should handle insufficient questions gracefully', async () => {
      const mockQuestions = [
        {
          id: 'q1',
          topic_id: 'topic1',
          question_text: 'Only question',
          options: { '0': 'A', '1': 'B', '2': 'C', '3': 'D' },
          correct_answer: '0',
          explanation: 'Test explanation',
          difficulty: 'medium',
          usage_count: 0,
          last_used: null,
          correct_answer_rate: null,
          created_at: '2024-01-01',
          updated_at: '2024-01-01',
          created_by: null,
          is_premium: false
        }
      ];

      (supabase.from as any).mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            data: mockQuestions,
            error: null
          })
        })
      });

      const result = await QuizRandomizationService.selectRandomQuestions({
        topicId: 'topic1',
        count: 5 // Request more than available
      });

      expect(result).toHaveLength(1); // Should return all available
    });
  });

  describe('Error Scenarios', () => {
    it('should handle database connection errors', async () => {
      (supabase.from as any).mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            data: null,
            error: { message: 'Connection timeout' }
          })
        })
      });

      await expect(
        QuizRandomizationService.selectRandomQuestions({
          topicId: 'topic1',
          count: 5
        })
      ).rejects.toThrow('Failed to fetch questions: Connection timeout');
    });

    it('should handle session creation failures', async () => {
      const mockQuestions = [{
        id: 'q1',
        topic_id: 'topic1',
        question_text: 'Question 1',
        options: { '0': 'A', '1': 'B', '2': 'C', '3': 'D' },
        correct_answer: '0',
        explanation: 'Test explanation',
        difficulty: 'medium',
        usage_count: 0,
        last_used: null,
        correct_answer_rate: null,
        created_at: '2024-01-01',
        updated_at: '2024-01-01',
        created_by: null,
        is_premium: false
      }];

      (supabase.from as any)
        .mockReturnValueOnce({
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              data: mockQuestions,
              error: null
            })
          })
        })
        .mockReturnValueOnce({
          insert: vi.fn().mockReturnValue({
            select: vi.fn().mockReturnValue({
              single: vi.fn().mockReturnValue({
                data: null,
                error: { message: 'Insert failed' }
              })
            })
          })
        });

      await expect(
        QuizRandomizationService.generateQuizSession('topic1', 'user1', 1)
      ).rejects.toThrow('Failed to create quiz session: Insert failed');
    });

    it('should handle expired session retrieval', async () => {
      const expiredSession = {
        id: 'session123',
        user_id: 'user1',
        topic_id: 'topic1',
        expires_at: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        questions_data: { questions: [] }
      };

      (supabase.from as any).mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              single: vi.fn().mockReturnValue({
                data: expiredSession,
                error: null
              })
            })
          })
        })
      });

      const result = await QuizRandomizationService.getQuizSession('session123', 'user1');

      expect(result).toBeNull();
    });
  });
});