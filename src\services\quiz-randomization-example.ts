/**
 * Example usage of Quiz Randomization Service
 * Demonstrates the complete workflow for implementing randomized quizzes
 */

import { QuizRandomizationService } from './quiz-randomization-service';
import type { QuizSessionData, RandomizedQuestion } from './quiz-randomization-service';

/**
 * Example: Complete Quiz Flow
 * This demonstrates how to use the randomization service in a real application
 */
export class QuizFlowExample {
  /**
   * Step 1: Start a new randomized quiz session
   */
  static async startQuiz(topicId: string, userId: string, quizLength: number = 10): Promise<QuizSessionData> {
    try {
      console.log(`Starting quiz for topic ${topicId} with ${quizLength} questions`);
      
      // Generate a new randomized quiz session
      const sessionData = await QuizRandomizationService.generateQuizSession(
        topicId,
        userId,
        quizLength
      );

      console.log(`Quiz session created: ${sessionData.sessionId}`);
      console.log(`Questions randomized: ${sessionData.questions.length}`);
      
      return sessionData;
    } catch (error) {
      console.error('Failed to start quiz:', error);
      throw error;
    }
  }

  /**
   * Step 2: Get a question from the session
   */
  static getQuestion(sessionData: QuizSessionData, questionIndex: number): RandomizedQuestion | null {
    if (questionIndex < 0 || questionIndex >= sessionData.questions.length) {
      return null;
    }

    const question = sessionData.questions[questionIndex];
    
    console.log(`Question ${questionIndex + 1}/${sessionData.questions.length}:`);
    console.log(`Text: ${question.question_text}`);
    console.log('Options:', question.shuffledOptions);
    
    return question;
  }

  /**
   * Step 3: Validate an answer and record analytics
   */
  static async submitAnswer(
    sessionData: QuizSessionData,
    questionIndex: number,
    selectedOptionIndex: number,
    timeToAnswer?: number
  ): Promise<{ isCorrect: boolean; explanation: string }> {
    const question = sessionData.questions[questionIndex];
    if (!question) {
      throw new Error('Invalid question index');
    }

    // Check if the answer is correct
    const isCorrect = selectedOptionIndex === question.shuffledCorrectIndex;
    
    console.log(`Answer submitted: Option ${selectedOptionIndex}`);
    console.log(`Correct answer: Option ${question.shuffledCorrectIndex}`);
    console.log(`Result: ${isCorrect ? 'CORRECT' : 'INCORRECT'}`);

    // Record analytics for this question
    try {
      await QuizRandomizationService.recordQuestionAnalytics(
        question.id,
        sessionData.userId,
        sessionData.sessionId,
        isCorrect,
        selectedOptionIndex,
        timeToAnswer
      );
    } catch (error) {
      console.warn('Failed to record analytics:', error);
    }

    return {
      isCorrect,
      explanation: question.explanation || 'No explanation available'
    };
  }

  /**
   * Step 4: Complete the quiz and calculate final score
   */
  static async completeQuiz(
    sessionData: QuizSessionData,
    answers: { questionIndex: number; selectedOption: number; timeToAnswer?: number }[],
    totalTimeSeconds?: number
  ): Promise<{ score: number; percentage: number; results: any[] }> {
    let correctAnswers = 0;
    const results = [];

    // Process all answers
    for (const answer of answers) {
      const question = sessionData.questions[answer.questionIndex];
      const isCorrect = answer.selectedOption === question.shuffledCorrectIndex;
      
      if (isCorrect) {
        correctAnswers++;
      }

      results.push({
        questionId: question.id,
        questionText: question.question_text,
        selectedOption: answer.selectedOption,
        correctOption: question.shuffledCorrectIndex,
        isCorrect,
        explanation: question.explanation
      });

      // Record analytics for each question
      try {
        await QuizRandomizationService.recordQuestionAnalytics(
          question.id,
          sessionData.userId,
          sessionData.sessionId,
          isCorrect,
          answer.selectedOption,
          answer.timeToAnswer
        );
      } catch (error) {
        console.warn(`Failed to record analytics for question ${question.id}:`, error);
      }
    }

    const percentage = Math.round((correctAnswers / sessionData.questions.length) * 100);

    // Complete the session in the database
    try {
      await QuizRandomizationService.completeQuizSession(
        sessionData.sessionId,
        sessionData.userId,
        percentage,
        totalTimeSeconds
      );
    } catch (error) {
      console.warn('Failed to complete session:', error);
    }

    console.log(`Quiz completed: ${correctAnswers}/${sessionData.questions.length} (${percentage}%)`);

    return {
      score: correctAnswers,
      percentage,
      results
    };
  }

  /**
   * Step 5: Resume an existing quiz session
   */
  static async resumeQuiz(sessionId: string, userId: string): Promise<QuizSessionData | null> {
    try {
      const sessionData = await QuizRandomizationService.getQuizSession(sessionId, userId);
      
      if (!sessionData) {
        console.log('Quiz session not found or expired');
        return null;
      }

      console.log(`Resumed quiz session: ${sessionId}`);
      console.log(`Questions: ${sessionData.questions.length}`);
      
      return sessionData;
    } catch (error) {
      console.error('Failed to resume quiz:', error);
      return null;
    }
  }

  /**
   * Utility: Get topic statistics for admin dashboard
   */
  static async getTopicStats(topicId: string) {
    try {
      const stats = await QuizRandomizationService.getTopicQuestionStats(topicId);
      
      console.log(`Topic ${topicId} Statistics:`);
      console.log(`- Total questions: ${stats.total_questions}`);
      console.log(`- Average usage: ${stats.avg_usage_count}`);
      console.log(`- Average correct rate: ${stats.avg_correct_rate}%`);
      console.log(`- Never used: ${stats.questions_never_used}`);
      console.log(`- Low performance: ${stats.questions_low_performance}`);
      
      return stats;
    } catch (error) {
      console.error('Failed to get topic stats:', error);
      throw error;
    }
  }

  /**
   * Utility: Cleanup expired sessions (for maintenance)
   */
  static async cleanupExpiredSessions(): Promise<number> {
    try {
      const cleanedCount = await QuizRandomizationService.cleanupExpiredSessions();
      console.log(`Cleaned up ${cleanedCount} expired quiz sessions`);
      return cleanedCount;
    } catch (error) {
      console.error('Failed to cleanup expired sessions:', error);
      return 0;
    }
  }
}

/**
 * Example usage in a React component or service:
 * 
 * // Start a new quiz
 * const sessionData = await QuizFlowExample.startQuiz('topic-123', 'user-456', 15);
 * 
 * // Get first question
 * const firstQuestion = QuizFlowExample.getQuestion(sessionData, 0);
 * 
 * // Submit answer
 * const result = await QuizFlowExample.submitAnswer(sessionData, 0, 2, 30);
 * 
 * // Complete quiz
 * const finalResults = await QuizFlowExample.completeQuiz(sessionData, answers, 900);
 * 
 * // Resume existing quiz
 * const resumedSession = await QuizFlowExample.resumeQuiz('session-id', 'user-456');
 */

export default QuizFlowExample;