#!/bin/bash

# Supabase CLI Installation Script
# This script automatically installs Supabase CLI based on your operating system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Detect operating system
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    elif [[ "$OSTYPE" == "cygwin" ]] || [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
        OS="windows"
    else
        print_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
    print_status "Detected OS: $OS"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Install on macOS
install_macos() {
    print_status "Installing Supabase CLI on macOS..."
    
    if command_exists brew; then
        print_status "Using Homebrew to install Supabase CLI..."
        brew install supabase/tap/supabase
    elif command_exists npm; then
        print_status "Using npm to install Supabase CLI..."
        npm install -g supabase
    else
        print_error "Neither Homebrew nor npm found. Please install one of them first."
        print_status "Install Homebrew: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        print_status "Or install Node.js/npm from: https://nodejs.org/"
        exit 1
    fi
}

# Install on Linux
install_linux() {
    print_status "Installing Supabase CLI on Linux..."
    
    # Try package manager first
    if command_exists apt-get; then
        print_status "Using apt to install Supabase CLI..."
        curl -fsSL https://supabase.com/dist/gpg | sudo gpg --dearmor -o /usr/share/keyrings/supabase-archive-keyring.gpg
        echo "deb [signed-by=/usr/share/keyrings/supabase-archive-keyring.gpg] https://supabase.com/dist/apt/ stable main" | sudo tee /etc/apt/sources.list.d/supabase.list
        sudo apt update
        sudo apt install -y supabase
    elif command_exists npm; then
        print_status "Using npm to install Supabase CLI..."
        npm install -g supabase
    else
        print_status "Using direct download method..."
        curl -fsSL https://github.com/supabase/cli/releases/latest/download/supabase_linux_amd64.tar.gz | tar -xz
        sudo mv supabase /usr/local/bin/
        sudo chmod +x /usr/local/bin/supabase
    fi
}

# Install on Windows (Git Bash/WSL)
install_windows() {
    print_status "Installing Supabase CLI on Windows..."
    
    if command_exists npm; then
        print_status "Using npm to install Supabase CLI..."
        npm install -g supabase
    else
        print_error "npm not found. Please install Node.js first."
        print_status "Download Node.js from: https://nodejs.org/"
        print_status "Or use Scoop: scoop install supabase"
        print_status "Or use Chocolatey: choco install supabase"
        exit 1
    fi
}

# Check Docker installation
check_docker() {
    print_status "Checking Docker installation..."
    
    if command_exists docker; then
        print_success "Docker is installed"
        docker --version
        
        # Check if Docker is running
        if docker info >/dev/null 2>&1; then
            print_success "Docker is running"
        else
            print_warning "Docker is installed but not running"
            print_status "Please start Docker and try again"
        fi
    else
        print_warning "Docker is not installed"
        print_status "Supabase CLI requires Docker to run local instances"
        print_status "Install Docker from: https://www.docker.com/products/docker-desktop"
    fi
}

# Verify installation
verify_installation() {
    print_status "Verifying Supabase CLI installation..."
    
    if command_exists supabase; then
        print_success "Supabase CLI installed successfully!"
        supabase --version
        
        print_status "Available commands:"
        supabase --help | head -20
        
        return 0
    else
        print_error "Supabase CLI installation failed"
        print_status "Please check the installation manually or try a different method"
        return 1
    fi
}

# Add to PATH (for manual installations)
add_to_path() {
    local install_dir="$1"
    
    if [[ ":$PATH:" != *":$install_dir:"* ]]; then
        print_status "Adding $install_dir to PATH..."
        
        # Determine shell config file
        if [[ "$SHELL" == *"zsh"* ]]; then
            config_file="$HOME/.zshrc"
        else
            config_file="$HOME/.bashrc"
        fi
        
        echo "export PATH=\"$install_dir:\$PATH\"" >> "$config_file"
        print_status "Added to $config_file"
        print_warning "Please restart your terminal or run: source $config_file"
    fi
}

# Main installation function
main() {
    print_status "🚀 Supabase CLI Installation Script"
    print_status "======================================"
    
    # Detect OS
    detect_os
    
    # Check if already installed
    if command_exists supabase; then
        print_warning "Supabase CLI is already installed"
        supabase --version
        print_status "Use --force to reinstall"
        if [[ "$1" != "--force" ]]; then
            exit 0
        fi
    fi
    
    # Install based on OS
    case $OS in
        "macos")
            install_macos
            ;;
        "linux")
            install_linux
            ;;
        "windows")
            install_windows
            ;;
        *)
            print_error "Unsupported OS: $OS"
            exit 1
            ;;
    esac
    
    # Verify installation
    if verify_installation; then
        print_success "✅ Installation completed successfully!"
        
        # Check Docker
        check_docker
        
        print_status "🎉 Next steps:"
        print_status "1. Navigate to your project: cd /path/to/your/project"
        print_status "2. Initialize Supabase: supabase init"
        print_status "3. Start local instance: supabase start"
        print_status "4. Open Supabase Studio: http://localhost:54323"
        
    else
        print_error "❌ Installation failed"
        print_status "Please try manual installation or check the documentation"
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    "--help" | "-h")
        echo "Supabase CLI Installation Script"
        echo ""
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --force        Force reinstallation"
        echo "  --check        Check current installation"
        echo ""
        exit 0
        ;;
    "--check")
        print_status "Checking current installation..."
        if command_exists supabase; then
            print_success "Supabase CLI is installed"
            supabase --version
        else
            print_error "Supabase CLI is not installed"
        fi
        check_docker
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
