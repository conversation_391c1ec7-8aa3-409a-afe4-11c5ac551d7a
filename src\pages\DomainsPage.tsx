import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import BottomNavigation from "@/components/BottomNavigation";
import Navbar from "@/components/Navbar";
import { Link } from "react-router-dom";
import { useAuth } from "@/hooks/use-auth";
import { 
  Search, 
  Clock, 
  Users, 
  BookOpen, 
  Shield, 
  Cloud, 
  AlertTriangle,
  ShieldCheck,
  FileText,
  Key,
  Award,
  Code,
  SearchIcon
} from "lucide-react";
import { DomainForUI, DomainFilters } from "@/types/domain";
import { fetchAllDomains, convertDomainsToUIFormat, filterDomains, getDifficultyColor, getDurationBadge, markOwnedDomains, enrichWithTopicCounts } from "@/utils/domain-utils";
import { runDomainDiagnostics } from "@/utils/test-supabase-connection";
import { motion } from "framer-motion";

// Icon mapping for domains
const domainIcons: Record<string, any> = {
  'network': Shield,
  'cloud': Cloud,
  'alert-triangle': AlertTriangle,
  'shield-check': ShieldCheck,
  'file-text': FileText,
  'key': Key,
  'users': Users,
  'award': Award,
  'search': SearchIcon,
  'code': Code
};

const DomainsPage = () => {
  const { user } = useAuth();
  const [domains, setDomains] = useState<DomainForUI[]>([]);
  const [filteredDomains, setFilteredDomains] = useState<DomainForUI[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<DomainFilters>({});

  useEffect(() => {
    loadDomains();
  }, [user?.id]);

  useEffect(() => {
    setFilteredDomains(filterDomains(domains, filters));
  }, [domains, filters]);

  const loadDomains = async () => {
    try {
      console.log('🚀 DomainsPage: Starting to load domains...');
      setLoading(true);

      const domainsData = await fetchAllDomains();
      console.log('📦 DomainsPage: Received domains data:', domainsData);

      let uiDomains = convertDomainsToUIFormat(domainsData);
      
      // Enrich with topic counts
      uiDomains = await enrichWithTopicCounts(uiDomains);
      console.log('🔢 DomainsPage: Enriched with topic counts:', uiDomains);
      
      // Enrich ownership if logged in
      uiDomains = await markOwnedDomains(uiDomains, user?.id || null);
      console.log('🎨 DomainsPage: Converted to UI format:', uiDomains);

      setDomains(uiDomains);
      setFilteredDomains(uiDomains);

      console.log('✅ DomainsPage: Successfully loaded', uiDomains.length, 'domains');
    } catch (error) {
      console.error('💥 DomainsPage: Error loading domains:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (value: string) => {
    setFilters(prev => ({ ...prev, searchQuery: value }));
  };

  const handleDifficultyFilter = (value: string) => {
    setFilters(prev => ({ 
      ...prev, 
      difficulty: value === 'all' ? undefined : value as any 
    }));
  };

  const handleDurationFilter = (value: string) => {
    setFilters(prev => ({ 
      ...prev, 
      duration: value === 'all' ? undefined : value as any 
    }));
  };

  const getDomainIcon = (iconName: string) => {
    const IconComponent = domainIcons[iconName] || Shield;
    return IconComponent;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-blue-900 text-lg">Loading domains...</div>
          </div>
        </div>
        <BottomNavigation />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-blue-900 mb-4">
            Cybersecurity Learning Domains
          </h1>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto mb-6">
            Master cybersecurity through specialized domain-focused learning paths.
            Choose your area of expertise and advance your skills with targeted content.
          </p>

          {/* Domain Count Badge */}
          <div className="inline-flex items-center gap-2 bg-blue-50 text-blue-700 px-4 py-2 rounded-full text-sm font-medium">
            <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
            {domains.length} Cybersecurity Domains Available
          </div>
        </div>

        {/* Filters */}
        <div className="mb-8">
          <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Find Your Perfect Domain</h3>
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4" />
                <Input
                  placeholder="Search domains (e.g., Network Security, Cloud Security)..."
                  className="pl-10 bg-white border-gray-300 text-gray-900 placeholder:text-gray-500 focus:border-blue-500 focus:ring-blue-500"
                  onChange={(e) => handleSearchChange(e.target.value)}
                />
              </div>

              {/* Difficulty Filter */}
              <Select onValueChange={handleDifficultyFilter}>
                <SelectTrigger className="w-full md:w-48 bg-white border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500">
                  <SelectValue placeholder="Difficulty Level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Levels</SelectItem>
                  <SelectItem value="beginner">Beginner</SelectItem>
                  <SelectItem value="intermediate">Intermediate</SelectItem>
                  <SelectItem value="advanced">Advanced</SelectItem>
                  <SelectItem value="expert">Expert</SelectItem>
                </SelectContent>
              </Select>

              {/* Duration Filter */}
              <Select onValueChange={handleDurationFilter}>
                <SelectTrigger className="w-full md:w-48 bg-white border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500">
                  <SelectValue placeholder="Duration" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Durations</SelectItem>
                  <SelectItem value="short">Short (1-4 weeks)</SelectItem>
                  <SelectItem value="medium">Medium (5-8 weeks)</SelectItem>
                  <SelectItem value="long">Long (9+ weeks)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Quick Filter Buttons */}
            <div className="flex flex-wrap gap-2 mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDifficultyFilter('beginner')}
                className="border-green-300 text-green-700 hover:bg-green-50"
              >
                Beginner Friendly
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDurationFilter('short')}
                className="border-blue-300 text-blue-700 hover:bg-blue-50"
              >
                Quick Start (1-4 weeks)
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setFilters({})}
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                Show All Domains
              </Button>
            </div>
          </div>
        </div>

        {/* Popular Domains Section */}
        {filteredDomains.length === domains.length && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Popular Domains</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mb-6">
              {domains.slice(0, 3).map((domain) => {
                const IconComponent = getDomainIcon(domain.icon);
                return (
                  <Link key={domain.id} to={`/domains/${domain.slug}`}>
                    <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 hover:border-blue-300 hover:shadow-md transition-all duration-300 p-3 sm:p-4">
                      <div className="flex items-center gap-2 sm:gap-3">
                        <div
                          className="p-2 rounded-lg flex-shrink-0"
                          style={{ backgroundColor: `${domain.colorTheme}20` }}
                        >
                          <IconComponent
                            className="h-4 w-4 sm:h-5 sm:w-5"
                            style={{ color: domain.colorTheme }}
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-gray-900 text-sm sm:text-base truncate">{domain.name}</h3>
                          <p className="text-xs sm:text-sm text-gray-600 truncate">{domain.difficultyLevel} • {domain.estimatedDurationWeeks} weeks</p>
                        </div>
                      </div>
                    </Card>
                  </Link>
                );
              })}
            </div>
          </div>
        )}

        {/* All Domains Grid */}
        <div className="mb-4">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            {filteredDomains.length === domains.length ? 'All Domains' : `Found ${filteredDomains.length} Domain${filteredDomains.length !== 1 ? 's' : ''}`}
          </h2>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          {filteredDomains.map((domain, index) => {
            const IconComponent = getDomainIcon(domain.icon);
            const difficultyColor = getDifficultyColor(domain.difficultyLevel);
            const durationBadge = getDurationBadge(domain.estimatedDurationWeeks);

            return (
              <motion.div
                key={domain.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="w-full"
              >
                <Card className="bg-white border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-300 h-full shadow-sm">
                  <div className="p-4 sm:p-6 h-full flex flex-col">
                    {/* Domain Header */}
                    <div className="flex items-start gap-3 sm:gap-4 mb-4">
                      <div
                        className="p-2 sm:p-3 rounded-lg flex-shrink-0"
                        style={{ backgroundColor: `${domain.colorTheme}15` }}
                      >
                        <IconComponent
                          className="h-5 w-5 sm:h-6 sm:w-6"
                          style={{ color: domain.colorTheme }}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2 leading-tight">
                          {domain.name}
                        </h3>
                        <p className="text-gray-600 text-sm line-clamp-2">
                          {domain.description}
                        </p>
                      </div>
                    </div>

                    {/* Badges */}
                    <div className="flex flex-wrap gap-1.5 sm:gap-2 mb-4">
                      <Badge className={`${difficultyColor} text-xs px-2 py-1`}>
                        {domain.difficultyLevel}
                      </Badge>
                      <Badge className={`${durationBadge.color} text-xs px-2 py-1`}>
                        <Clock className="h-3 w-3 mr-1" />
                        {durationBadge.text}
                      </Badge>
                      {domain.prerequisites.length > 0 && (
                        <Badge className="text-yellow-600 bg-yellow-100 text-xs px-2 py-1">
                          Prerequisites
                        </Badge>
                      )}
                    </div>

                    {/* Stats + Ownership */}
                    <div className="flex items-center gap-3 sm:gap-4 text-sm text-gray-600 mb-4 sm:mb-6">
                      <div className="flex items-center gap-1">
                        <BookOpen className="h-4 w-4 flex-shrink-0" />
                        <span className="truncate">{domain.topicCount || 0} Topics</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4 flex-shrink-0" />
                        <span className="truncate">Learning Paths</span>
                      </div>
                      {domain.userHasDomainPass && (
                        <Badge className="bg-green-600 text-white">Owned</Badge>
                      )}
                    </div>

                    {/* Action Button */}
                    <div className="mt-auto">
                      <Link to={`/domains/${domain.slug}`} className="block">
                        <Button
                          className="w-full text-white font-medium hover:opacity-90 transition-opacity py-2.5 sm:py-3 text-sm sm:text-base"
                          style={{
                            backgroundColor: domain.colorTheme,
                            borderColor: domain.colorTheme
                          }}
                        >
                          Explore Domain
                        </Button>
                      </Link>
                    </div>
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Empty State */}
        {filteredDomains.length === 0 && !loading && (
          <div className="text-center py-12">
            <div className="text-gray-600 text-lg mb-4">
              {domains.length === 0
                ? "No domains available. There might be a database connection issue."
                : "No domains found matching your criteria"
              }
            </div>
            {domains.length === 0 ? (
              <div className="space-y-4">
                <Button
                  variant="outline"
                  onClick={loadDomains}
                  className="border-blue-300 text-blue-600 hover:bg-blue-50"
                >
                  Retry Loading Domains
                </Button>
                <div className="text-sm text-gray-500">
                  Check the browser console for error details
                </div>
              </div>
            ) : (
              <Button
                variant="outline"
                onClick={() => setFilters({})}
                className="border-blue-300 text-blue-600 hover:bg-blue-50"
              >
                Clear Filters
              </Button>
            )}
          </div>
        )}
      </div>

      <BottomNavigation />
    </div>
  );
};

export default DomainsPage;
