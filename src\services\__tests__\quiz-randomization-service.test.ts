/**
 * Unit tests for Quiz Randomization Service
 * Tests question selection, shuffling algorithms, and session management
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock the supabase client before importing the service
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          not: vi.fn(() => ({ data: [], error: null })),
          data: [],
          error: null
        })),
        in: vi.fn(() => ({ data: [], error: null })),
        data: [],
        error: null
      })),
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(() => ({ data: null, error: null }))
        }))
      })),
      update: vi.fn(() => ({
        eq: vi.fn(() => ({
          eq: vi.fn(() => ({ error: null }))
        }))
      }))
    })),
    rpc: vi.fn(() => ({ data: null, error: null }))
  }
}));

vi.mock('@/utils/answer-validation', () => ({
  parseCorrectAnswer: vi.fn((answer, optionsCount = 4) => ({
    correctIndex: typeof answer === 'number' ? answer : parseInt(answer) || 0,
    isValid: true,
    originalValue: answer
  })),
  parseQuestionOptions: vi.fn((options) => {
    if (Array.isArray(options)) return options;
    if (typeof options === 'object' && options !== null) {
      return Object.keys(options).sort().map(key => options[key]);
    }
    return ['Option A', 'Option B', 'Option C', 'Option D'];
  })
}));

import { QuizRandomizationService } from '../quiz-randomization-service';
import type { Question } from '../quiz-randomization-service';
import { supabase } from '@/integrations/supabase/client';

describe('QuizRandomizationService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('shuffleAnswerOptions', () => {
    const mockQuestion: Question = {
      id: '1',
      topic_id: 'topic1',
      question_text: 'Test Question',
      options: { '0': 'Option A', '1': 'Option B', '2': 'Option C', '3': 'Option D' },
      correct_answer: '2',
      explanation: 'Test explanation',
      difficulty: 'medium',
      usage_count: 0,
      last_used: null,
      correct_answer_rate: null,
      created_at: '2024-01-01',
      updated_at: '2024-01-01',
      created_by: null,
      is_premium: false
    };

    it('should shuffle answer options while maintaining correct mapping', () => {
      const result = QuizRandomizationService.shuffleAnswerOptions(mockQuestion);

      expect(result.id).toBe(mockQuestion.id);
      expect(result.originalCorrectIndex).toBe(2);
      expect(result.optionMapping).toHaveLength(4);
      expect(result.shuffledOptions).toBeDefined();
      expect(Object.keys(result.shuffledOptions)).toHaveLength(4);
      
      // Verify that the correct answer mapping is maintained
      const originalCorrectOption = 'Option C';
      const shuffledCorrectOption = result.shuffledOptions[result.shuffledCorrectIndex.toString()];
      expect(shuffledCorrectOption).toBe(originalCorrectOption);
    });

    it('should handle questions with different numbers of options', () => {
      const questionWith3Options = {
        ...mockQuestion,
        options: { '0': 'Option A', '1': 'Option B', '2': 'Option C' },
        correct_answer: '1'
      };

      const result = QuizRandomizationService.shuffleAnswerOptions(questionWith3Options);

      expect(result.optionMapping).toHaveLength(3);
      expect(Object.keys(result.shuffledOptions)).toHaveLength(3);
      expect(result.originalCorrectIndex).toBe(1);
    });

    it('should handle invalid correct answer gracefully', () => {
      const questionWithInvalidAnswer = {
        ...mockQuestion,
        correct_answer: 'invalid'
      };

      const result = QuizRandomizationService.shuffleAnswerOptions(questionWithInvalidAnswer);

      expect(result.originalCorrectIndex).toBe(0); // Should fallback to 0
      expect(result.optionMapping).toHaveLength(4);
      expect(result.shuffledOptions).toBeDefined();
    });

    it('should produce different shuffles on multiple calls', () => {
      // Mock Math.random to produce different sequences
      const originalRandom = Math.random;
      let callCount = 0;
      Math.random = vi.fn(() => {
        callCount++;
        return callCount * 0.1; // Different values each call
      });

      const result1 = QuizRandomizationService.shuffleAnswerOptions(mockQuestion);
      const result2 = QuizRandomizationService.shuffleAnswerOptions(mockQuestion);

      // Results should be different (with high probability)
      const mapping1 = result1.optionMapping.join(',');
      const mapping2 = result2.optionMapping.join(',');
      
      // Reset Math.random
      Math.random = originalRandom;

      // Note: There's a small chance they could be the same, but very unlikely
      expect(mapping1).not.toBe(mapping2);
    });

    it('should maintain all original options in shuffled result', () => {
      const result = QuizRandomizationService.shuffleAnswerOptions(mockQuestion);
      
      // All original options should be present in shuffled options
      const originalOptions = ['Option A', 'Option B', 'Option C', 'Option D'];
      const shuffledOptionValues = Object.values(result.shuffledOptions);
      
      originalOptions.forEach(option => {
        expect(shuffledOptionValues).toContain(option);
      });
    });

    it('should have valid option mapping indices', () => {
      const result = QuizRandomizationService.shuffleAnswerOptions(mockQuestion);
      
      // Option mapping should contain all indices from 0 to 3
      const sortedMapping = [...result.optionMapping].sort();
      expect(sortedMapping).toEqual([0, 1, 2, 3]);
      
      // Each index should be unique
      const uniqueIndices = new Set(result.optionMapping);
      expect(uniqueIndices.size).toBe(4);
    });
  });

  describe('Input Validation', () => {
    it('should validate quiz session parameters', async () => {
      await expect(
        QuizRandomizationService.generateQuizSession('', 'user1', 10)
      ).rejects.toThrow('Topic ID and User ID are required');

      await expect(
        QuizRandomizationService.generateQuizSession('topic1', '', 10)
      ).rejects.toThrow('Topic ID and User ID are required');

      await expect(
        QuizRandomizationService.generateQuizSession('topic1', 'user1', 0)
      ).rejects.toThrow('Quiz length must be between 1 and 50 questions');

      await expect(
        QuizRandomizationService.generateQuizSession('topic1', 'user1', 51)
      ).rejects.toThrow('Quiz length must be between 1 and 50 questions');
    });
  });

  describe('Randomization Quality Tests', () => {
    it('should produce reasonably distributed option shuffling', () => {
      const mockQuestion: Question = {
        id: '1',
        topic_id: 'topic1',
        question_text: 'Test Question',
        options: { '0': 'Option A', '1': 'Option B', '2': 'Option C', '3': 'Option D' },
        correct_answer: '0',
        explanation: 'Test explanation',
        difficulty: 'medium',
        usage_count: 0,
        last_used: null,
        correct_answer_rate: null,
        created_at: '2024-01-01',
        updated_at: '2024-01-01',
        created_by: null,
        is_premium: false
      };

      // Run multiple shuffles and check distribution
      const shuffles: number[][] = [];
      for (let i = 0; i < 20; i++) {
        const result = QuizRandomizationService.shuffleAnswerOptions(mockQuestion);
        shuffles.push(result.optionMapping);
      }

      // Check that we get different shuffles
      const uniqueShuffles = new Set(shuffles.map(s => s.join(',')));
      expect(uniqueShuffles.size).toBeGreaterThan(1);

      // Check that all shuffles contain all original indices
      shuffles.forEach(shuffle => {
        expect(shuffle.sort()).toEqual([0, 1, 2, 3]);
      });
    });

    it('should maintain correct answer position tracking across shuffles', () => {
      const testCases = [
        { correct_answer: '0', expectedOriginal: 0 },
        { correct_answer: '1', expectedOriginal: 1 },
        { correct_answer: '2', expectedOriginal: 2 },
        { correct_answer: '3', expectedOriginal: 3 }
      ];

      testCases.forEach(({ correct_answer, expectedOriginal }) => {
        const mockQuestion: Question = {
          id: '1',
          topic_id: 'topic1',
          question_text: 'Test Question',
          options: { '0': 'Option A', '1': 'Option B', '2': 'Option C', '3': 'Option D' },
          correct_answer,
          explanation: 'Test explanation',
          difficulty: 'medium',
          usage_count: 0,
          last_used: null,
          correct_answer_rate: null,
          created_at: '2024-01-01',
          updated_at: '2024-01-01',
          created_by: null,
          is_premium: false
        };

        const result = QuizRandomizationService.shuffleAnswerOptions(mockQuestion);
        
        expect(result.originalCorrectIndex).toBe(expectedOriginal);
        
        // The shuffled correct index should point to the same option text
        const originalCorrectOption = mockQuestion.options[correct_answer];
        const shuffledCorrectOption = result.shuffledOptions[result.shuffledCorrectIndex.toString()];
        expect(shuffledCorrectOption).toBe(originalCorrectOption);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed question options gracefully', () => {
      const malformedQuestion: Question = {
        id: '1',
        topic_id: 'topic1',
        question_text: 'Test Question',
        options: null as any, // Malformed options
        correct_answer: '0',
        explanation: 'Test explanation',
        difficulty: 'medium',
        usage_count: 0,
        last_used: null,
        correct_answer_rate: null,
        created_at: '2024-01-01',
        updated_at: '2024-01-01',
        created_by: null,
        is_premium: false
      };

      // Should not throw an error
      expect(() => {
        QuizRandomizationService.shuffleAnswerOptions(malformedQuestion);
      }).not.toThrow();
    });

    it('should handle questions with empty options', () => {
      const emptyOptionsQuestion: Question = {
        id: '1',
        topic_id: 'topic1',
        question_text: 'Test Question',
        options: {},
        correct_answer: '0',
        explanation: 'Test explanation',
        difficulty: 'medium',
        usage_count: 0,
        last_used: null,
        correct_answer_rate: null,
        created_at: '2024-01-01',
        updated_at: '2024-01-01',
        created_by: null,
        is_premium: false
      };

      const result = QuizRandomizationService.shuffleAnswerOptions(emptyOptionsQuestion);
      
      // Should handle gracefully and provide fallback options
      expect(result.optionMapping).toBeDefined();
      expect(result.shuffledOptions).toBeDefined();
    });
  });

  describe('Fisher-Yates Shuffle Algorithm', () => {
    it('should properly shuffle arrays using Fisher-Yates algorithm', () => {
      // Test the private shuffleArray method indirectly through shuffleAnswerOptions
      const mockQuestion: Question = {
        id: '1',
        topic_id: 'topic1',
        question_text: 'Test Question',
        options: { '0': 'A', '1': 'B', '2': 'C', '3': 'D', '4': 'E', '5': 'F' },
        correct_answer: '0',
        explanation: 'Test explanation',
        difficulty: 'medium',
        usage_count: 0,
        last_used: null,
        correct_answer_rate: null,
        created_at: '2024-01-01',
        updated_at: '2024-01-01',
        created_by: null,
        is_premium: false
      };

      // Run multiple shuffles to test distribution
      const results: number[][] = [];
      for (let i = 0; i < 100; i++) {
        const result = QuizRandomizationService.shuffleAnswerOptions(mockQuestion);
        results.push(result.optionMapping);
      }

      // Check that each position gets different values across runs
      for (let position = 0; position < 6; position++) {
        const valuesAtPosition = results.map(mapping => mapping[position]);
        const uniqueValues = new Set(valuesAtPosition);
        
        // Each position should have seen multiple different values
        expect(uniqueValues.size).toBeGreaterThan(1);
      }
    });
  });
});