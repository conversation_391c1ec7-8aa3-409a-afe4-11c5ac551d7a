/**
 * Test script for Free Tier Implementation
 * This script tests the free tier activation functionality
 */

const API_URL = process.env.VITE_API_URL || 'http://localhost:3001';

async function testFreeTierActivation() {
  console.log('🧪 Testing Free Tier Activation...\n');

  const testEmail = `test-${Date.now()}@example.com`;
  
  try {
    console.log(`📧 Testing with email: ${testEmail}`);
    
    // Test the free tier activation endpoint
    const response = await fetch(`${API_URL}/api/subscriptions/activate-free-tier`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email: testEmail }),
    });

    const result = await response.json();
    
    console.log(`📊 Response Status: ${response.status}`);
    console.log(`📋 Response Body:`, JSON.stringify(result, null, 2));

    if (response.ok && result.success) {
      console.log('✅ Free tier activation test PASSED');
      return true;
    } else {
      console.log('❌ Free tier activation test FAILED');
      console.log('Error:', result.message || result.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Free tier activation test FAILED with exception');
    console.error('Error:', error.message);
    return false;
  }
}

async function testInvalidEmail() {
  console.log('\n🧪 Testing Invalid Email Validation...\n');
  
  try {
    const response = await fetch(`${API_URL}/api/subscriptions/activate-free-tier`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email: 'invalid-email' }),
    });

    const result = await response.json();
    
    console.log(`📊 Response Status: ${response.status}`);
    console.log(`📋 Response Body:`, JSON.stringify(result, null, 2));

    if (response.status === 400 && !result.success) {
      console.log('✅ Invalid email validation test PASSED');
      return true;
    } else {
      console.log('❌ Invalid email validation test FAILED');
      return false;
    }
  } catch (error) {
    console.log('❌ Invalid email validation test FAILED with exception');
    console.error('Error:', error.message);
    return false;
  }
}

async function testMissingEmail() {
  console.log('\n🧪 Testing Missing Email Validation...\n');
  
  try {
    const response = await fetch(`${API_URL}/api/subscriptions/activate-free-tier`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({}),
    });

    const result = await response.json();
    
    console.log(`📊 Response Status: ${response.status}`);
    console.log(`📋 Response Body:`, JSON.stringify(result, null, 2));

    if (response.status === 400 && !result.success) {
      console.log('✅ Missing email validation test PASSED');
      return true;
    } else {
      console.log('❌ Missing email validation test FAILED');
      return false;
    }
  } catch (error) {
    console.log('❌ Missing email validation test FAILED with exception');
    console.error('Error:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting Free Tier Implementation Tests\n');
  console.log(`🌐 API URL: ${API_URL}\n`);
  
  const results = [];
  
  // Test 1: Valid free tier activation
  results.push(await testFreeTierActivation());
  
  // Test 2: Invalid email validation
  results.push(await testInvalidEmail());
  
  // Test 3: Missing email validation
  results.push(await testMissingEmail());
  
  // Summary
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log('\n📊 Test Summary:');
  console.log(`✅ Passed: ${passed}/${total}`);
  console.log(`❌ Failed: ${total - passed}/${total}`);
  
  if (passed === total) {
    console.log('\n🎉 All tests PASSED! Free tier implementation is working correctly.');
  } else {
    console.log('\n⚠️  Some tests FAILED. Please check the implementation.');
  }
  
  return passed === total;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testFreeTierActivation,
  testInvalidEmail,
  testMissingEmail,
  runAllTests
};
