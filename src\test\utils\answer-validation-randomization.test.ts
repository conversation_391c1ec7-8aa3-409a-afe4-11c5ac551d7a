/**
 * Unit tests for answer validation with randomized questions
 * Tests the answer validation logic specifically for randomized quiz sessions
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { 
  parseCorrectAnswer, 
  validateAnswer, 
  parseQuestionOptions,
  safeValidateAnswer,
  logAnswerValidation
} from '@/utils/answer-validation';
import type { AnswerValidationResult } from '@/utils/answer-validation';

describe('Answer Validation with Randomization', () => {
  describe('parseCorrectAnswer', () => {
    it('should parse numeric correct answers correctly', () => {
      const result = parseCorrectAnswer(2, 4);
      expect(result.correctIndex).toBe(2);
      expect(result.isValid).toBe(true);
      expect(result.originalValue).toBe(2);
    });

    it('should parse string numeric correct answers correctly', () => {
      const result = parseCorrectAnswer('1', 4);
      expect(result.correctIndex).toBe(1);
      expect(result.isValid).toBe(true);
      expect(result.originalValue).toBe('1');
    });

    it('should handle out-of-range indices by clamping', () => {
      const result = parseCorrectAnswer(5, 4);
      expect(result.correctIndex).toBe(3); // Clamped to max valid index
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toContain('out of range');
    });

    it('should handle negative indices by clamping to 0', () => {
      const result = parseCorrectAnswer(-1, 4);
      expect(result.correctIndex).toBe(0); // Clamped to min valid index
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toContain('out of range');
    });

    it('should handle null/undefined by defaulting to 0', () => {
      const nullResult = parseCorrectAnswer(null, 4);
      expect(nullResult.correctIndex).toBe(0);
      expect(nullResult.isValid).toBe(false);

      const undefinedResult = parseCorrectAnswer(undefined, 4);
      expect(undefinedResult.correctIndex).toBe(0);
      expect(undefinedResult.isValid).toBe(false);
    });

    it('should handle invalid string formats', () => {
      const result = parseCorrectAnswer('invalid', 4);
      expect(result.correctIndex).toBe(0);
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toContain('Cannot parse');
    });

    it('should handle boolean values', () => {
      const trueResult = parseCorrectAnswer(true, 4);
      expect(trueResult.correctIndex).toBe(1);
      expect(trueResult.isValid).toBe(true);

      const falseResult = parseCorrectAnswer(false, 4);
      expect(falseResult.correctIndex).toBe(0);
      expect(falseResult.isValid).toBe(true);
    });
  });

  describe('validateAnswer with randomized options', () => {
    it('should validate correct answers in randomized positions', () => {
      // Simulate a question where correct answer was originally index 1
      // but after shuffling is now at index 3
      const originalCorrectAnswer = '1';
      const shuffledCorrectIndex = 3;
      const selectedIndex = 3; // User selected the correct shuffled position

      const result = validateAnswer(selectedIndex, shuffledCorrectIndex, 4);
      expect(result.isCorrect).toBe(true);
      expect(result.selectedIndex).toBe(3);
      expect(result.correctIndex).toBe(3);
      expect(result.confidence).toBe('high');
    });

    it('should validate incorrect answers in randomized positions', () => {
      // User selected wrong option in shuffled quiz
      const shuffledCorrectIndex = 2;
      const selectedIndex = 0; // User selected wrong option

      const result = validateAnswer(selectedIndex, shuffledCorrectIndex, 4);
      expect(result.isCorrect).toBe(false);
      expect(result.selectedIndex).toBe(0);
      expect(result.correctIndex).toBe(2);
      expect(result.confidence).toBe('high');
    });

    it('should handle null selection', () => {
      const result = validateAnswer(null, 2, 4);
      expect(result.isCorrect).toBe(false);
      expect(result.selectedIndex).toBe(-1);
      expect(result.correctIndex).toBe(2);
      expect(result.warnings).toContain('No answer selected');
    });

    it('should handle out-of-range selections', () => {
      const result = validateAnswer(5, 2, 4);
      expect(result.isCorrect).toBe(false);
      expect(result.selectedIndex).toBe(5);
      expect(result.correctIndex).toBe(2);
      expect(result.confidence).toBe('low');
      expect(result.warnings.length).toBeGreaterThan(0);
      expect(result.warnings[0]).toContain('out of range');
    });
  });

  describe('parseQuestionOptions', () => {
    it('should parse object format options correctly', () => {
      const options = {
        '0': 'Option A',
        '1': 'Option B',
        '2': 'Option C',
        '3': 'Option D'
      };

      const result = parseQuestionOptions(options);
      expect(result).toEqual(['Option A', 'Option B', 'Option C', 'Option D']);
    });

    it('should parse array format options correctly', () => {
      const options = ['First', 'Second', 'Third', 'Fourth'];
      const result = parseQuestionOptions(options);
      expect(result).toEqual(['First', 'Second', 'Third', 'Fourth']);
    });

    it('should handle JSON string format', () => {
      const options = '{"0": "Alpha", "1": "Beta", "2": "Gamma", "3": "Delta"}';
      const result = parseQuestionOptions(options);
      expect(result).toEqual(['Alpha', 'Beta', 'Gamma', 'Delta']);
    });

    it('should handle null/undefined options', () => {
      const nullResult = parseQuestionOptions(null);
      expect(nullResult).toEqual(['Option not available']);

      const undefinedResult = parseQuestionOptions(undefined);
      expect(undefinedResult).toEqual(['Option not available']);
    });

    it('should handle malformed JSON strings', () => {
      const options = 'invalid json';
      const result = parseQuestionOptions(options);
      expect(result).toEqual(['invalid json']);
    });

    it('should sort numeric keys correctly', () => {
      const options = {
        '10': 'Ten',
        '2': 'Two',
        '1': 'One',
        '11': 'Eleven'
      };

      const result = parseQuestionOptions(options);
      expect(result).toEqual(['One', 'Two', 'Ten', 'Eleven']);
    });
  });

  describe('safeValidateAnswer', () => {
    it('should use robust validation when possible', () => {
      const result = safeValidateAnswer(2, '2', 2, 4);
      expect(result.isCorrect).toBe(true);
      expect(result.confidence).toBe('high');
      expect(result.warnings).toHaveLength(0);
    });

    it('should fall back to simple comparison on validation issues', () => {
      // Simulate a scenario where robust validation has issues
      const result = safeValidateAnswer(1, 'invalid-format', 1, 4);
      expect(result.isCorrect).toBe(true); // Should still work with fallback
      expect(result.confidence).toBe('medium');
      expect(result.warnings.length).toBeGreaterThan(0);
      expect(result.warnings.some(w => w.includes('fallback'))).toBe(true);
    });

    it('should handle emergency fallback scenarios', () => {
      const result = safeValidateAnswer(2, null, 2, 4);
      expect(result.isCorrect).toBe(true);
      expect(result.selectedIndex).toBe(2);
      expect(result.correctIndex).toBe(2);
    });
  });

  describe('Randomization-specific scenarios', () => {
    it('should handle complete option shuffling scenario', () => {
      // Original question: correct answer is index 1
      // After shuffling: [original_1, original_2, original_3, original_0]
      // So correct answer is now at index 0
      
      const originalOptions = ['Wrong A', 'Correct', 'Wrong B', 'Wrong C'];
      const shuffledOptions = ['Correct', 'Wrong B', 'Wrong C', 'Wrong A'];
      const originalCorrectIndex = 1;
      const shuffledCorrectIndex = 0;
      
      // User selects index 0 (which is correct in shuffled version)
      const userSelection = 0;
      
      const result = validateAnswer(userSelection, shuffledCorrectIndex, 4);
      expect(result.isCorrect).toBe(true);
      expect(result.selectedIndex).toBe(0);
      expect(result.correctIndex).toBe(0);
    });

    it('should handle partial shuffling with option mapping', () => {
      // Test scenario where options are partially shuffled
      const optionMapping = [2, 0, 1, 3]; // Maps shuffled positions to original positions
      const originalCorrectIndex = 1;
      
      // Find where original correct answer ended up after shuffling
      const shuffledCorrectIndex = optionMapping.indexOf(originalCorrectIndex);
      expect(shuffledCorrectIndex).toBe(2); // Original index 1 is now at position 2
      
      // User selects the correct shuffled position
      const result = validateAnswer(2, shuffledCorrectIndex, 4);
      expect(result.isCorrect).toBe(true);
    });

    it('should maintain validation accuracy across multiple shuffles', () => {
      // Test multiple randomization scenarios
      const testCases = [
        { original: 0, shuffled: 3, userChoice: 3, expected: true },
        { original: 1, shuffled: 0, userChoice: 1, expected: false },
        { original: 2, shuffled: 1, userChoice: 1, expected: true },
        { original: 3, shuffled: 2, userChoice: 0, expected: false },
      ];

      testCases.forEach(({ original, shuffled, userChoice, expected }, index) => {
        const result = validateAnswer(userChoice, shuffled, 4);
        expect(result.isCorrect).toBe(expected);
        expect(result.selectedIndex).toBe(userChoice);
        expect(result.correctIndex).toBe(shuffled);
      });
    });

    it('should handle edge case with single option', () => {
      const result = validateAnswer(0, 0, 1);
      expect(result.isCorrect).toBe(true);
      expect(result.selectedIndex).toBe(0);
      expect(result.correctIndex).toBe(0);
    });

    it('should handle maximum options scenario', () => {
      // Test with many options (some quizzes might have more than 4)
      const maxOptions = 10;
      const correctIndex = 7;
      const userSelection = 7;

      const result = validateAnswer(userSelection, correctIndex, maxOptions);
      expect(result.isCorrect).toBe(true);
      expect(result.selectedIndex).toBe(7);
      expect(result.correctIndex).toBe(7);
    });
  });

  describe('logAnswerValidation', () => {
    it('should log validation results without errors', () => {
      // Mock console methods
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      const validationResult: AnswerValidationResult = {
        isCorrect: true,
        selectedIndex: 2,
        correctIndex: 2,
        confidence: 'high',
        warnings: []
      };

      logAnswerValidation('test-question-id', '2', 2, validationResult);

      expect(consoleSpy).toHaveBeenCalledWith(
        'Answer validation successful:',
        expect.objectContaining({
          questionId: 'test-question-id',
          originalCorrectAnswer: '2',
          selectedIndex: 2,
          validationResult
        })
      );

      consoleSpy.mockRestore();
      consoleWarnSpy.mockRestore();
    });

    it('should log warnings for validation issues', () => {
      const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      const validationResult: AnswerValidationResult = {
        isCorrect: false,
        selectedIndex: 1,
        correctIndex: 2,
        confidence: 'low',
        warnings: ['Parsing error detected']
      };

      logAnswerValidation('test-question-id', 'invalid', 1, validationResult);

      expect(consoleWarnSpy).toHaveBeenCalledWith(
        'Answer validation issues detected:',
        expect.objectContaining({
          questionId: 'test-question-id',
          validationResult
        })
      );

      consoleWarnSpy.mockRestore();
    });
  });
});