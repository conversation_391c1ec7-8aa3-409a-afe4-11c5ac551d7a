# Multi-Topic CSV Import Guide

This guide explains how to use the multi-topic CSV import feature to efficiently import quiz questions for multiple topics in a single operation.

## Overview

The multi-topic CSV import feature allows you to:
- Import questions for multiple topics in one CSV file
- Automatically create new topics during import
- Use different topic identification formats
- Preview imports before committing changes

## CSV Format Options

### 1. Name-Based Format (Recommended)

Use topic names to identify topics. This is the most user-friendly format.

**Headers:**
```csv
topic_name,question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty
```

**Example:**
```csv
topic_name,question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty
Security Fundamentals,What is the primary purpose of a firewall?,To prevent physical access to a network,To control network traffic based on security rules,To encrypt data during transmission,To detect malware on endpoints,B,A firewall controls network traffic flow based on predetermined security rules,medium
Network Security,What does VPN stand for?,Virtual Private Network,Very Personal Network,Verified Public Network,Variable Protocol Network,A,VPN stands for Virtual Private Network which creates a secure tunnel over the internet,easy
Incident Response,What is the first step in incident response?,Containment,Identification,Recovery,Lessons learned,B,The first step in incident response is identification - recognizing that a security incident has occurred,hard
```

### 2. ID-Based Format

Use existing topic IDs to identify topics. Useful when you know the exact topic IDs.

**Headers:**
```csv
topic_id,question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty
```

**Example:**
```csv
topic_id,question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty
security-fundamentals-uuid,What is the primary purpose of a firewall?,To prevent physical access to a network,To control network traffic based on security rules,To encrypt data during transmission,To detect malware on endpoints,B,A firewall controls network traffic flow based on predetermined security rules,medium
network-security-uuid,What does VPN stand for?,Virtual Private Network,Very Personal Network,Verified Public Network,Variable Protocol Network,A,VPN stands for Virtual Private Network which creates a secure tunnel over the internet,easy
incident-response-uuid,What is the first step in incident response?,Containment,Identification,Recovery,Lessons learned,B,The first step in incident response is identification - recognizing that a security incident has occurred,hard
```

### 3. Both Columns Format

Include both topic_name and topic_id columns. The topic_name takes precedence for topic resolution.

**Headers:**
```csv
topic_name,topic_id,question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty
```

**Example:**
```csv
topic_name,topic_id,question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty
Security Fundamentals,security-fundamentals-uuid,What is the primary purpose of a firewall?,To prevent physical access to a network,To control network traffic based on security rules,To encrypt data during transmission,To detect malware on endpoints,B,A firewall controls network traffic flow based on predetermined security rules,medium
Network Security,network-security-uuid,What does VPN stand for?,Virtual Private Network,Very Personal Network,Verified Public Network,Variable Protocol Network,A,VPN stands for Virtual Private Network which creates a secure tunnel over the internet,easy
Incident Response,incident-response-uuid,What is the first step in incident response?,Containment,Identification,Recovery,Lessons learned,B,The first step in incident response is identification - recognizing that a security incident has occurred,hard
```

## Required Columns

All formats must include these columns:

| Column | Description | Required | Format |
|--------|-------------|----------|---------|
| `question_text` | The question text | Yes | String (minimum 10 characters) |
| `option_a` | First answer option | Yes | String |
| `option_b` | Second answer option | Yes | String |
| `option_c` | Third answer option | Yes | String |
| `option_d` | Fourth answer option | Yes | String |
| `correct_answer` | Correct answer | Yes | A, B, C, or D |
| `explanation` | Explanation of the correct answer | Yes | String (minimum 10 characters) |
| `difficulty` | Question difficulty | No | easy, medium, or hard (defaults to medium) |

## Topic Identification Columns

Choose one of these approaches:

| Column | Description | Format |
|--------|-------------|---------|
| `topic_name` | Topic name (will be created if doesn't exist) | String |
| `topic_id` | Existing topic UUID | UUID string |
| Both | Include both columns (topic_name takes precedence) | String + UUID |

## Import Modes

### Single-Topic Mode
- Import all questions to one selected topic
- Ignore any topic columns in the CSV
- Use existing single-topic workflow

### Multi-Topic Mode
- Import questions to multiple topics based on CSV data
- Automatically detect topic columns
- Option to auto-create missing topics

## Auto-Create Topics

When enabled, the system will:
- Create new topics for topic names that don't exist
- Use default topic settings for new topics
- Validate topic names (no special characters, reasonable length)
- Report which topics were created

When disabled, the system will:
- Only import questions for existing topics
- Report errors for missing topics
- Skip questions with unresolved topic references

## Import Process

1. **Upload CSV File**: Select your multi-topic CSV file
2. **Choose Mode**: Select single-topic or multi-topic mode
3. **Configure Options**: Enable/disable auto-create topics
4. **Preview Import**: Review questions grouped by topic
5. **Confirm Import**: Execute the import after reviewing
6. **View Results**: See import summary with success/error counts

## Validation Rules

### Topic Validation
- Topic names must be 1-100 characters
- No special characters except spaces, hyphens, and underscores
- Topic IDs must be valid UUIDs if using ID-based format

### Question Validation
- Question text minimum 10 characters
- All four options must be provided and non-empty
- Correct answer must be A, B, C, or D
- Explanation minimum 10 characters
- Difficulty must be easy, medium, or hard (if provided)

### Cross-Topic Validation
- Duplicate question detection across topics
- Consistent answer format validation
- Topic reference consistency checks

## Error Handling

### Global Errors
- CSV parsing errors
- Invalid file format
- Missing required columns
- Topic resolution failures

### Topic-Level Errors
- Invalid topic names
- Topic creation failures
- Permission errors

### Question-Level Errors
- Missing required fields
- Invalid answer formats
- Validation rule violations
- Grouped by topic for easy identification

## Best Practices

1. **Start Small**: Test with a few questions first
2. **Use Templates**: Download format-specific templates
3. **Validate Data**: Check your CSV in a spreadsheet program first
4. **Preview First**: Always preview before confirming import
5. **Backup Data**: Export existing questions before large imports
6. **Consistent Naming**: Use consistent topic naming conventions
7. **Quality Content**: Ensure questions and explanations are well-written

## Troubleshooting

### Common Issues

**"Topic not found" errors:**
- Enable auto-create topics, or
- Create topics manually first, or
- Check topic name spelling

**"Invalid CSV format" errors:**
- Ensure proper CSV encoding (UTF-8)
- Check for missing commas or quotes
- Verify column headers match expected format

**"Duplicate question" warnings:**
- Review questions for actual duplicates
- Consider if duplicates are intentional
- Modify questions to be unique if needed

**"Validation failed" errors:**
- Check question text length (minimum 10 characters)
- Verify correct_answer is A, B, C, or D
- Ensure all required fields are filled

### Getting Help

If you encounter issues:
1. Check the error messages for specific guidance
2. Download and examine the template files
3. Verify your CSV format matches the examples
4. Test with a smaller subset of data first
5. Contact support with specific error messages

## Template Downloads

Use the template download feature in the import interface to get properly formatted CSV files:

- **Single-Topic Template**: Basic format for single topic imports
- **Name-Based Multi-Topic**: Use topic names for identification
- **ID-Based Multi-Topic**: Use topic IDs for identification  
- **Both Columns Multi-Topic**: Include both name and ID columns

Each template includes sample data to help you understand the expected format.

## Troubleshooting Multi-Topic Imports

### Common Error Messages and Solutions

#### "Topic not found" Errors

**Error:** `Topic 'Security Basics' not found`

**Causes:**
- Topic name doesn't exist in the system
- Typo in topic name
- Extra spaces or special characters

**Solutions:**
1. **Enable Auto-Create Topics**: Turn on the auto-create option to create missing topics automatically
2. **Create Topics Manually**: Add the topics in the admin interface before importing
3. **Check Spelling**: Verify topic names match existing topics exactly
4. **Remove Extra Characters**: Ensure no leading/trailing spaces or special characters

#### "Invalid CSV Format" Errors

**Error:** `CSV parsing failed` or `Invalid file format`

**Causes:**
- File is not in CSV format
- Incorrect encoding
- Missing or malformed headers
- Empty file or rows

**Solutions:**
1. **Save as CSV**: Ensure file is saved as .csv format, not .xlsx or .txt
2. **Use UTF-8 Encoding**: Save with UTF-8 encoding to support special characters
3. **Check Headers**: Verify column headers match template exactly (case-sensitive)
4. **Remove Empty Rows**: Delete any empty rows at the end of the file
5. **Validate Structure**: Open in a text editor to check comma separation

#### "Validation Failed" Errors

**Error:** `Question validation failed` or `Invalid field format`

**Causes:**
- Missing required fields
- Incorrect answer format
- Text too short
- Invalid difficulty level

**Solutions:**
1. **Check Required Fields**: Ensure all required columns have values
2. **Verify Answer Format**: Correct answer must be exactly A, B, C, or D
3. **Text Length**: Question text and explanation must be at least 10 characters
4. **Difficulty Values**: Use only "easy", "medium", or "hard" (lowercase)

#### "Permission Denied" Errors

**Error:** `Access denied` or `Insufficient permissions`

**Causes:**
- Not logged in as admin
- Insufficient user permissions
- Session expired

**Solutions:**
1. **Check Admin Access**: Ensure you're logged in with admin privileges
2. **Refresh Session**: Log out and log back in
3. **Contact Administrator**: Request proper permissions if needed

### Topic-Specific Issues

#### Invalid Topic Names

**Symptoms:**
- "Invalid topic name" errors
- Topics not being created

**Requirements:**
- Length: 1-100 characters
- Allowed characters: letters, numbers, spaces, hyphens (-), underscores (_)
- No special characters: @, #, $, %, &, *, etc.

**Examples:**
- ✅ Good: "Security Fundamentals", "Network-Security", "Web_Security"
- ❌ Bad: "Security@Work", "Network Security!", "Web Security (Advanced)"

#### Duplicate Topic References

**Symptoms:**
- Same topic appears multiple times with different names
- Questions scattered across similar topic names

**Solutions:**
1. **Standardize Names**: Use consistent topic naming throughout your CSV
2. **Check for Variations**: Look for "Security", "Security ", "security" variations
3. **Use Find/Replace**: Standardize topic names before importing

### Performance Issues

#### Slow Import Processing

**Symptoms:**
- Import takes very long time
- Browser becomes unresponsive
- Timeout errors

**Solutions:**
1. **Reduce File Size**: Split large files into smaller batches (50-100 questions)
2. **Optimize Browser**: Close other tabs, use latest browser version
3. **Check Connection**: Ensure stable internet connection
4. **Import During Off-Peak**: Avoid high-traffic times

#### Memory Issues

**Symptoms:**
- "Out of memory" errors
- Browser crashes during import
- Very slow preview generation

**Solutions:**
1. **Smaller Batches**: Import 25-50 questions at a time
2. **Restart Browser**: Clear cache and restart browser
3. **Simplify Content**: Reduce text length if extremely long
4. **Use Different Device**: Try on a device with more memory

### Data Quality Issues

#### Duplicate Questions

**Symptoms:**
- Warning about duplicate questions
- Same question appears multiple times

**Solutions:**
1. **Review Duplicates**: Check if duplicates are intentional
2. **Modify Slightly**: Change question text slightly if unintentional
3. **Remove Duplicates**: Delete duplicate rows from CSV
4. **Cross-Topic Duplicates**: Consider if same question in different topics is acceptable

#### Inconsistent Formatting

**Symptoms:**
- Some questions import, others fail
- Inconsistent validation errors

**Solutions:**
1. **Use Templates**: Start with provided templates
2. **Batch Validation**: Test small batches first
3. **Standardize Format**: Ensure consistent formatting throughout
4. **Check Encoding**: Verify UTF-8 encoding for special characters

### Advanced Troubleshooting

#### Browser Console Errors

**How to Check:**
1. Press F12 to open developer tools
2. Go to Console tab
3. Look for red error messages during import

**Common Console Errors:**
- Network errors: Check internet connection
- CORS errors: Contact system administrator
- JavaScript errors: Try different browser or clear cache

#### CSV File Analysis

**Manual Inspection:**
1. Open CSV in text editor (not Excel)
2. Check for proper comma separation
3. Verify quote handling for text with commas
4. Look for hidden characters or encoding issues

**Validation Tools:**
1. Use online CSV validators
2. Import small test file first
3. Compare with working template files

### Getting Help

#### Before Contacting Support

1. **Try Small Test File**: Import 5-10 questions first
2. **Use Templates**: Download and modify provided templates
3. **Check Documentation**: Review this guide and CSV format requirements
4. **Clear Browser Cache**: Try in incognito/private mode

#### Information to Provide

When contacting support, include:

1. **Exact Error Messages**: Copy the full error text
2. **Sample Data**: Provide 2-3 rows of your CSV (remove sensitive data)
3. **Import Settings**: Specify mode (single/multi-topic) and options used
4. **Browser Information**: Browser name and version
5. **File Details**: File size, number of rows, encoding used
6. **Steps to Reproduce**: Exact steps that led to the error

#### Support Channels

- **Documentation**: Check the main CSV Import Guide
- **Help Section**: Use the "Show Help" button in the import interface
- **System Administrator**: Contact your SecQuiz administrator
- **Technical Support**: Provide detailed information as listed above

### Prevention Tips

#### Before Creating Your CSV

1. **Plan Topic Structure**: Decide on topic names and hierarchy
2. **Use Consistent Naming**: Establish naming conventions
3. **Start Small**: Begin with a few questions to test the process
4. **Keep Backups**: Save copies of your CSV files

#### During CSV Creation

1. **Use Templates**: Always start with provided templates
2. **Validate as You Go**: Check formatting regularly
3. **Test Incrementally**: Import small batches to catch issues early
4. **Document Conventions**: Keep notes on your naming and formatting decisions

#### Before Importing

1. **Preview First**: Always use the preview feature
2. **Check File Size**: Keep files under 10MB for best performance
3. **Verify Encoding**: Ensure UTF-8 encoding
4. **Review Error Messages**: Fix all validation errors before importing