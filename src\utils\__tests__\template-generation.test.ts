import { describe, it, expect, vi, beforeEach } from 'vitest';
import { 
  generateCSVTemplate, 
  generateMultiTopicCSVTemplate,
  type MultiTopicTemplateFormat
} from '../csv-import';

// Mock Papa Parse
vi.mock('papaparse', () => ({
  default: {
    unparse: vi.fn(),
  },
}));

describe('CSV Template Generation', () => {
  let mockPapa: any;

  beforeEach(async () => {
    const Papa = (await import('papaparse')).default;
    mockPapa = Papa;
    vi.clearAllMocks();
  });

  describe('generateCSVTemplate', () => {
    it('should generate a single-topic CSV template with correct headers', () => {
      mockPapa.unparse.mockReturnValue('question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty\nWhat is a firewall?,Physical barrier,Network traffic controller,Data encryptor,Malware detector,B,A firewall controls network traffic flow,medium');
      
      const template = generateCSVTemplate();
      
      expect(template).toBeTruthy();
      expect(mockPapa.unparse).toHaveBeenCalledWith({
        fields: [
          'question_text',
          'option_a',
          'option_b',
          'option_c',
          'option_d',
          'correct_answer',
          'explanation',
          'difficulty',
        ],
        data: expect.arrayContaining([
          expect.arrayContaining([
            'What is the primary purpose of a firewall?',
            'To prevent physical access to a network',
            'To control network traffic based on security rules',
            'To encrypt data during transmission',
            'To detect malware on endpoints',
            'B',
            expect.any(String), // explanation
            'medium',
          ])
        ]),
      });
    });
  });

  describe('generateMultiTopicCSVTemplate', () => {
    it('should generate name-based format by default', () => {
      mockPapa.unparse.mockReturnValue('topic_name,question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty');
      
      const template = generateMultiTopicCSVTemplate();
      
      expect(template).toBeTruthy();
      expect(mockPapa.unparse).toHaveBeenCalledWith({
        fields: expect.arrayContaining(['topic_name', 'question_text']),
        data: expect.any(Array),
      });
      
      const call = mockPapa.unparse.mock.calls[0][0];
      expect(call.fields[0]).toBe('topic_name');
      expect(call.fields).not.toContain('topic_id');
    });

    it('should generate id-based format when specified', () => {
      mockPapa.unparse.mockReturnValue('topic_id,question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty');
      
      const template = generateMultiTopicCSVTemplate('id-based');
      
      expect(template).toBeTruthy();
      expect(mockPapa.unparse).toHaveBeenCalledWith({
        fields: expect.arrayContaining(['topic_id', 'question_text']),
        data: expect.any(Array),
      });
      
      const call = mockPapa.unparse.mock.calls[0][0];
      expect(call.fields[0]).toBe('topic_id');
      expect(call.fields).not.toContain('topic_name');
    });

    it('should generate both columns format when specified', () => {
      mockPapa.unparse.mockReturnValue('topic_name,topic_id,question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty');
      
      const template = generateMultiTopicCSVTemplate('both');
      
      expect(template).toBeTruthy();
      expect(mockPapa.unparse).toHaveBeenCalledWith({
        fields: expect.arrayContaining(['topic_name', 'topic_id', 'question_text']),
        data: expect.any(Array),
      });
      
      const call = mockPapa.unparse.mock.calls[0][0];
      expect(call.fields[0]).toBe('topic_name');
      expect(call.fields[1]).toBe('topic_id');
    });

    it('should include sample data for all formats', () => {
      const formats: MultiTopicTemplateFormat[] = ['name-based', 'id-based', 'both'];
      
      formats.forEach(format => {
        mockPapa.unparse.mockReturnValue(`sample,${format},csv,content`);
        
        const template = generateMultiTopicCSVTemplate(format);
        
        expect(template).toBeTruthy();
        
        const call = mockPapa.unparse.mock.calls[mockPapa.unparse.mock.calls.length - 1][0];
        expect(call.data).toHaveLength(3); // Three sample questions
        
        // Check that each row has the correct number of columns
        call.data.forEach((row: string[]) => {
          expect(row).toHaveLength(call.fields.length);
        });
      });
    });

    it('should include diverse sample topics and questions', () => {
      mockPapa.unparse.mockReturnValue('sample,csv,content');
      
      generateMultiTopicCSVTemplate('name-based');
      
      const call = mockPapa.unparse.mock.calls[0][0];
      const sampleData = call.data;
      
      // Check for diverse topics
      const topics = sampleData.map((row: string[]) => row[0]);
      expect(topics).toContain('Security Fundamentals');
      expect(topics).toContain('Network Security');
      expect(topics).toContain('Incident Response');
      
      // Check for diverse difficulty levels
      const difficulties = sampleData.map((row: string[]) => row[row.length - 1]);
      expect(difficulties).toContain('easy');
      expect(difficulties).toContain('medium');
      expect(difficulties).toContain('hard');
      
      // Check for proper answer format
      const correctAnswers = sampleData.map((row: string[]) => row[row.length - 3]);
      correctAnswers.forEach(answer => {
        expect(['A', 'B', 'C', 'D']).toContain(answer);
      });
    });

    it('should handle invalid format gracefully', () => {
      mockPapa.unparse.mockReturnValue('fallback,csv,content');
      
      // @ts-expect-error Testing invalid format
      const template = generateMultiTopicCSVTemplate('invalid-format');
      
      expect(template).toBeTruthy();
      
      // Should fall back to name-based format
      const call = mockPapa.unparse.mock.calls[0][0];
      expect(call.fields[0]).toBe('topic_name');
    });
  });

  describe('Template Content Quality', () => {
    it('should generate realistic and educational sample questions', () => {
      mockPapa.unparse.mockReturnValue('sample,csv,content');
      
      generateMultiTopicCSVTemplate('name-based');
      
      const call = mockPapa.unparse.mock.calls[0][0];
      const sampleData = call.data;
      
      sampleData.forEach((row: string[]) => {
        const questionText = row[1]; // question_text column
        const explanation = row[row.length - 2]; // explanation column
        
        // Questions should be meaningful
        expect(questionText.length).toBeGreaterThan(10);
        expect(questionText).toMatch(/\?$/); // Should end with question mark
        
        // Explanations should be informative
        expect(explanation.length).toBeGreaterThan(20);
        expect(explanation).not.toMatch(/^(test|sample|example)/i); // Should not be placeholder text
      });
    });

    it('should provide distinct options for each question', () => {
      mockPapa.unparse.mockReturnValue('sample,csv,content');
      
      generateMultiTopicCSVTemplate('name-based');
      
      const call = mockPapa.unparse.mock.calls[0][0];
      const sampleData = call.data;
      
      sampleData.forEach((row: string[]) => {
        const options = [row[2], row[3], row[4], row[5]]; // option_a through option_d
        
        // All options should be unique within the question
        const uniqueOptions = new Set(options);
        expect(uniqueOptions.size).toBe(4);
        
        // Options should not be empty
        options.forEach(option => {
          expect(option.trim().length).toBeGreaterThan(0);
        });
      });
    });
  });
});