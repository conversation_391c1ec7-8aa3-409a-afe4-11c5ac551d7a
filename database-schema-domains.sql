-- Enhanced Database Schema for Multi-Domain Cybersecurity Platform

-- 1. Create domains table (new top-level organization)
CREATE TABLE IF NOT EXISTS public.domains (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  slug TEXT NOT NULL UNIQUE, -- URL-friendly identifier
  description TEXT,
  icon TEXT, -- Icon identifier for UI
  color_theme TEXT, -- Hex color for domain branding
  difficulty_level TEXT CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced', 'expert')),
  estimated_duration_weeks INTEGER DEFAULT 4, -- Estimated completion time
  prerequisites TEXT[], -- Array of prerequisite domain slugs
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Add domain_id to existing topics table
ALTER TABLE public.topics 
ADD COLUMN IF NOT EXISTS domain_id UUID REFERENCES public.domains(id) ON DELETE CASCADE;

-- 3. Create domain_subscription_plans table
CREATE TABLE IF NOT EXISTS public.domain_subscription_plans (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  domain_id UUID REFERENCES public.domains(id) ON DELETE CASCADE,
  plan_id TEXT NOT NULL, -- e.g., 'domain-network-security'
  name TEXT NOT NULL, -- e.g., 'Network Security Focus'
  amount INTEGER NOT NULL, -- Amount in kobo (₦599 = 59900)
  interval TEXT DEFAULT 'weekly' CHECK (interval IN ('weekly', 'monthly', 'one-time')),
  features TEXT[], -- Array of features
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(domain_id, plan_id)
);

-- 4. Create domain_learning_paths table (structured learning progression)
CREATE TABLE IF NOT EXISTS public.domain_learning_paths (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  domain_id UUID REFERENCES public.domains(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  difficulty_level TEXT CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
  estimated_hours INTEGER,
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create path_topics table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS public.path_topics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  learning_path_id UUID REFERENCES public.domain_learning_paths(id) ON DELETE CASCADE,
  topic_id UUID REFERENCES public.topics(id) ON DELETE CASCADE,
  sort_order INTEGER DEFAULT 0,
  is_required BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(learning_path_id, topic_id)
);

-- 6. Create user_domain_progress table
CREATE TABLE IF NOT EXISTS public.user_domain_progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  domain_id UUID REFERENCES public.domains(id) ON DELETE CASCADE,
  learning_path_id UUID REFERENCES public.domain_learning_paths(id) ON DELETE CASCADE,
  completed_topics INTEGER DEFAULT 0,
  total_topics INTEGER DEFAULT 0,
  completion_percentage DECIMAL(5,2) DEFAULT 0,
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, domain_id, learning_path_id)
);

-- 7. Indexes for performance
CREATE INDEX IF NOT EXISTS domains_slug_idx ON public.domains(slug);
CREATE INDEX IF NOT EXISTS topics_domain_id_idx ON public.topics(domain_id);
CREATE INDEX IF NOT EXISTS domain_subscription_plans_domain_id_idx ON public.domain_subscription_plans(domain_id);
CREATE INDEX IF NOT EXISTS path_topics_learning_path_id_idx ON public.path_topics(learning_path_id);
CREATE INDEX IF NOT EXISTS user_domain_progress_user_id_idx ON public.user_domain_progress(user_id);
CREATE INDEX IF NOT EXISTS user_domain_progress_domain_id_idx ON public.user_domain_progress(domain_id);

-- 8. Row Level Security Policies
ALTER TABLE public.domains ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.domain_subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.domain_learning_paths ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.path_topics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_domain_progress ENABLE ROW LEVEL SECURITY;

-- Everyone can read domains and learning paths
CREATE POLICY "Domains are viewable by everyone" ON public.domains FOR SELECT USING (true);
CREATE POLICY "Domain learning paths are viewable by everyone" ON public.domain_learning_paths FOR SELECT USING (true);
CREATE POLICY "Path topics are viewable by everyone" ON public.path_topics FOR SELECT USING (true);
CREATE POLICY "Domain subscription plans are viewable by everyone" ON public.domain_subscription_plans FOR SELECT USING (true);

-- Users can view and manage their own progress
CREATE POLICY "Users can view their own domain progress" ON public.user_domain_progress FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own domain progress" ON public.user_domain_progress FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own domain progress" ON public.user_domain_progress FOR UPDATE USING (auth.uid() = user_id);

-- Admins can manage all domain-related content
CREATE POLICY "Admins can manage domains" ON public.domains FOR ALL USING (
  EXISTS (SELECT 1 FROM public.user_profiles WHERE user_id = auth.uid() AND is_admin = true)
);
CREATE POLICY "Admins can manage domain subscription plans" ON public.domain_subscription_plans FOR ALL USING (
  EXISTS (SELECT 1 FROM public.user_profiles WHERE user_id = auth.uid() AND is_admin = true)
);
CREATE POLICY "Admins can manage domain learning paths" ON public.domain_learning_paths FOR ALL USING (
  EXISTS (SELECT 1 FROM public.user_profiles WHERE user_id = auth.uid() AND is_admin = true)
);
CREATE POLICY "Admins can manage path topics" ON public.path_topics FOR ALL USING (
  EXISTS (SELECT 1 FROM public.user_profiles WHERE user_id = auth.uid() AND is_admin = true)
);
