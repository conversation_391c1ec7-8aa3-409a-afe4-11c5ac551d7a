/**
 * ProfileLayoutTest Component
 * 
 * This component is for testing the profile layout fixes.
 * It demonstrates different subscription statuses and screen sizes.
 * Remove this file after testing is complete.
 */

import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { UserCircle, Crown, Clock, AlertTriangle, Lock } from 'lucide-react';

type TestStatus = 'Free' | 'Premium' | 'Expiring Soon' | 'Expired';

const ProfileLayoutTest = () => {
  const [currentStatus, setCurrentStatus] = useState<TestStatus>('Free');

  const getStatusConfig = (status: TestStatus) => {
    switch (status) {
      case 'Premium':
        return {
          color: 'bg-green-500/20 text-green-600 border-green-500/30',
          icon: <Crown className="h-4 w-4" />,
          label: 'Premium Active',
          shortLabel: 'Pro'
        };
      case 'Expiring Soon':
        return {
          color: 'bg-amber-500/20 text-amber-600 border-amber-500/30',
          icon: <Clock className="h-4 w-4" />,
          label: 'Expiring Soon',
          shortLabel: 'Exp'
        };
      case 'Expired':
        return {
          color: 'bg-red-500/20 text-red-600 border-red-500/30',
          icon: <AlertTriangle className="h-4 w-4" />,
          label: 'Expired',
          shortLabel: 'Exp'
        };
      default:
        return {
          color: 'bg-gray-500/20 text-gray-600 border-gray-500/30',
          icon: <Lock className="h-4 w-4" />,
          label: 'Free Plan',
          shortLabel: 'Free'
        };
    }
  };

  const statusConfig = getStatusConfig(currentStatus);

  return (
    <div className="p-8 space-y-8">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-4">Profile Layout Test</h2>
        <p className="text-muted-foreground mb-6">
          Test the profile avatar and subscription badge layout across different statuses and screen sizes.
        </p>
        
        {/* Status Selector */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          {(['Free', 'Premium', 'Expiring Soon', 'Expired'] as TestStatus[]).map((status) => (
            <Button
              key={status}
              variant={currentStatus === status ? 'default' : 'outline'}
              size="sm"
              onClick={() => setCurrentStatus(status)}
            >
              {status}
            </Button>
          ))}
        </div>
      </div>

      {/* Test Cases */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        
        {/* Desktop View */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Desktop View</h3>
          <div className="flex flex-col items-center">
            <div className="profile-avatar-container mb-3">
              <div className="h-20 w-20 rounded-full bg-cyber-primary/10 flex items-center justify-center border-2 border-cyber-primary/40">
                <UserCircle className="h-12 w-12 text-cyber-primary" />
              </div>
              <div className="subscription-badge-overlay">
                <div className="subscription-badge-compact flex items-center space-x-1">
                  <Badge className={`${statusConfig.color} text-xs px-2 py-1 whitespace-nowrap shadow-sm border`}>
                    {statusConfig.icon}
                    <span className="ml-1">{statusConfig.label}</span>
                  </Badge>
                </div>
              </div>
            </div>
            <h4 className="text-lg font-bold">John Doe</h4>
            <p className="text-sm text-muted-foreground"><EMAIL></p>
          </div>
        </Card>

        {/* Mobile View Simulation */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Mobile View (Simulated)</h3>
          <div className="max-w-[320px] mx-auto">
            <div className="flex flex-col items-center">
              <div className="profile-avatar-container mb-3">
                <div className="h-20 w-20 rounded-full bg-cyber-primary/10 flex items-center justify-center border-2 border-cyber-primary/40">
                  <UserCircle className="h-12 w-12 text-cyber-primary" />
                </div>
                <div className="subscription-badge-overlay">
                  <div className="subscription-badge-compact flex items-center space-x-1">
                    <Badge className={`${statusConfig.color} text-xs px-2 py-1 whitespace-nowrap shadow-sm border`}>
                      {statusConfig.icon}
                      <span className="ml-1">{statusConfig.shortLabel}</span>
                    </Badge>
                  </div>
                </div>
              </div>
              <h4 className="text-lg font-bold">John Doe</h4>
              <p className="text-sm text-muted-foreground"><EMAIL></p>
            </div>
          </div>
        </Card>

        {/* Compact Badge Only */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Badge Variations</h3>
          <div className="space-y-4">
            <div>
              <p className="text-sm font-medium mb-2">Desktop Badge:</p>
              <Badge className={`${statusConfig.color} text-xs px-2 py-1 whitespace-nowrap shadow-sm border`}>
                {statusConfig.icon}
                <span className="ml-1">{statusConfig.label}</span>
              </Badge>
            </div>
            <div>
              <p className="text-sm font-medium mb-2">Mobile Badge:</p>
              <Badge className={`${statusConfig.color} text-xs px-2 py-1 whitespace-nowrap shadow-sm border`}>
                {statusConfig.icon}
                <span className="ml-1">{statusConfig.shortLabel}</span>
              </Badge>
            </div>
          </div>
        </Card>
      </div>

      {/* Layout Guidelines */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Layout Guidelines</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="font-medium mb-2">✅ What to Check:</h4>
            <ul className="space-y-1 text-muted-foreground">
              <li>• Badge doesn't overlap avatar</li>
              <li>• Proper spacing around elements</li>
              <li>• Badge is fully visible</li>
              <li>• Text is readable at all sizes</li>
              <li>• Responsive behavior works</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-2">❌ Issues to Avoid:</h4>
            <ul className="space-y-1 text-muted-foreground">
              <li>• Badge extending outside container</li>
              <li>• Overlapping with other elements</li>
              <li>• Text cutoff on small screens</li>
              <li>• Poor contrast or visibility</li>
              <li>• Inconsistent positioning</li>
            </ul>
          </div>
        </div>
      </Card>

      {/* CSS Classes Reference */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">CSS Classes Used</h3>
        <div className="space-y-2 text-sm font-mono bg-muted p-4 rounded">
          <div><span className="text-blue-600">.profile-avatar-container</span> - Main container with proper padding</div>
          <div><span className="text-blue-600">.subscription-badge-overlay</span> - Badge positioning</div>
          <div><span className="text-blue-600">.subscription-badge-compact</span> - Badge layout</div>
        </div>
      </Card>
    </div>
  );
};

export default ProfileLayoutTest;
