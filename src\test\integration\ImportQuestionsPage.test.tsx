import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { TooltipProvider } from '@/components/ui/tooltip';
import ImportQuestionsPage from '@/pages/ImportQuestionsPage';
import { useAdminTopics } from '@/hooks/use-admin';
import { useToast } from '@/hooks/use-toast';
import { AuthProvider } from '@/hooks/use-auth';
import * as csvImportUtils from '@/utils/csv-import';
import { batchImportService } from '@/services/batch-import-service';
import { importErrorReportingService } from '@/services/import-error-reporting-service';

// Mock dependencies
vi.mock('@/hooks/use-admin');
vi.mock('@/hooks/use-toast');
vi.mock('@/hooks/use-auth', () => ({
  useAuth: vi.fn(() => ({
    user: { id: 'test-user', email: '<EMAIL>' },
    isLoading: false,
    signOut: vi.fn(),
  })),
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
}));
vi.mock('@/hooks/use-admin-status', () => ({
  useAdminStatus: vi.fn(() => ({ isAdmin: true })),
}));
vi.mock('@/utils/csv-import');
vi.mock('@/services/batch-import-service');
vi.mock('@/services/import-error-reporting-service');
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => vi.fn(),
  };
});

const mockTopics = [
  { id: '1', title: 'JavaScript Basics', slug: 'javascript-basics' },
  { id: '2', title: 'React Fundamentals', slug: 'react-fundamentals' },
  { id: '3', title: 'Node.js', slug: 'nodejs' },
];

const mockToast = vi.fn();

const createMockImportResult = (topicCount: number, questionsPerTopic: number) => ({
  success: true,
  totalRows: topicCount * questionsPerTopic,
  globalErrors: [],
  newTopicsCreated: [],
  topicResults: new Map(
    Array.from({ length: topicCount }, (_, i) => [
      `topic-${i + 1}`,
      {
        topicId: `topic-${i + 1}`,
        topicName: `Topic ${i + 1}`,
        isNewTopic: false,
        validQuestions: Array.from({ length: questionsPerTopic }, (_, j) => ({
          question: `Question ${j + 1}`,
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Test explanation',
          difficulty: 'medium' as const,
        })),
        errors: [],
      },
    ])
  ),
});

const renderImportPage = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return render(
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <BrowserRouter>
          <ImportQuestionsPage />
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

describe('ImportQuestionsPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useAdminTopics as any).mockReturnValue({
      data: mockTopics,
      isLoading: false,
    });
    (useToast as any).mockReturnValue({ toast: mockToast });
    (csvImportUtils.generateCSVTemplate as any).mockReturnValue('mock,csv,content');
    (csvImportUtils.generateMultiTopicCSVTemplate as any).mockReturnValue('topic_name,mock,csv,content');
    (batchImportService.validateImportResult as any).mockReturnValue([]);
    (importErrorReportingService.generateErrorReport as any).mockReturnValue(null);
  });

  it('renders the import page with progress indicator', () => {
    renderImportPage();
    
    expect(screen.getByText('Import Questions')).toBeInTheDocument();
    expect(screen.getByText('Upload File')).toBeInTheDocument();
    expect(screen.getByText('Preview Data')).toBeInTheDocument();
    expect(screen.getByText('Import Questions')).toBeInTheDocument();
    expect(screen.getByText('View Results')).toBeInTheDocument();
  });

  it('displays mode selector with single and multi-topic options', () => {
    renderImportPage();
    
    expect(screen.getByText('Single Topic')).toBeInTheDocument();
    expect(screen.getByText('Multi Topic')).toBeInTheDocument();
    expect(screen.getByText('Import Mode')).toBeInTheDocument();
  });

  it('shows topic selection in single-topic mode', () => {
    renderImportPage();
    
    // Should be in single-topic mode by default
    expect(screen.getByText('Select Topic')).toBeInTheDocument();
    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });

  it('switches to multi-topic mode and shows auto-create toggle', () => {
    renderImportPage();
    
    // Switch to multi-topic mode
    const multiTopicButton = screen.getByText('Multi Topic');
    fireEvent.click(multiTopicButton);
    
    expect(screen.getByText('Auto-create Topics')).toBeInTheDocument();
    expect(screen.getByRole('switch')).toBeInTheDocument();
  });

  it('handles file selection', () => {
    renderImportPage();
    
    const fileInput = screen.getByLabelText('CSV File');
    const file = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    expect(screen.getByText('test.csv')).toBeInTheDocument();
  });

  it('downloads single-topic template', () => {
    renderImportPage();
    
    const templateButton = screen.getByText('Single Topic Template');
    fireEvent.click(templateButton);
    
    expect(csvImportUtils.generateCSVTemplate).toHaveBeenCalled();
  });

  it('downloads multi-topic templates', () => {
    renderImportPage();
    
    const multiTemplateButton = screen.getByText('Multi Topic Templates');
    fireEvent.click(multiTemplateButton);
    
    // Should show dropdown options
    expect(screen.getByText('Name-based Template')).toBeInTheDocument();
    expect(screen.getByText('ID-based Template')).toBeInTheDocument();
    expect(screen.getByText('Both Columns Template')).toBeInTheDocument();
  });

  it('validates required fields before preview', async () => {
    renderImportPage();
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Missing file',
        description: 'Please select a CSV file to preview.',
        variant: 'destructive',
      });
    });
  });

  it('validates topic selection in single-topic mode', async () => {
    renderImportPage();
    
    // Add file but don't select topic
    const fileInput = screen.getByLabelText('CSV File');
    const file = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Missing topic',
        description: 'Please select a topic for single-topic import.',
        variant: 'destructive',
      });
    });
  });

  it('processes preview successfully', async () => {
    const mockResult = createMockImportResult(2, 3);
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
    
    renderImportPage();
    
    // Select topic and file
    const topicSelect = screen.getByRole('combobox');
    fireEvent.click(topicSelect);
    fireEvent.click(screen.getByText('JavaScript Basics'));
    
    const fileInput = screen.getByLabelText('CSV File');
    const file = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      expect(csvImportUtils.parseQuestionCSVEnhanced).toHaveBeenCalledWith(
        file,
        expect.objectContaining({
          mode: 'single-topic',
          selectedTopicId: '1',
        })
      );
    });
    
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Preview successful',
        description: expect.stringContaining('Ready to import 6 questions'),
      });
    });
  });

  it('handles preview errors gracefully', async () => {
    const error = new Error('Invalid CSV format');
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockRejectedValue(error);
    
    renderImportPage();
    
    // Select topic and file
    const topicSelect = screen.getByRole('combobox');
    fireEvent.click(topicSelect);
    fireEvent.click(screen.getByText('JavaScript Basics'));
    
    const fileInput = screen.getByLabelText('CSV File');
    const file = new File(['invalid,csv'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Preview error',
        description: 'Invalid CSV format',
        variant: 'destructive',
      });
    });
  });

  it('displays preview results with topic breakdown', async () => {
    const mockResult = createMockImportResult(2, 3);
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
    
    renderImportPage();
    
    // Select topic and file, then preview
    const topicSelect = screen.getByRole('combobox');
    fireEvent.click(topicSelect);
    fireEvent.click(screen.getByText('JavaScript Basics'));
    
    const fileInput = screen.getByLabelText('CSV File');
    const file = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      expect(screen.getByText('Import Preview')).toBeInTheDocument();
      expect(screen.getByText('📊 Import Summary')).toBeInTheDocument();
      expect(screen.getByText('6')).toBeInTheDocument(); // Total valid questions
    });
  });

  it('executes batch import successfully', async () => {
    const mockPreviewResult = createMockImportResult(2, 3);
    const mockBatchResult = {
      success: true,
      totalQuestionsImported: 6,
      totalTopicsProcessed: 2,
      topicsCreated: [],
      errors: [],
      duration: 1500,
      topicResults: new Map(),
    };
    
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockPreviewResult);
    (batchImportService.executeBatchImport as any).mockResolvedValue(mockBatchResult);
    
    renderImportPage();
    
    // Go through preview first
    const topicSelect = screen.getByRole('combobox');
    fireEvent.click(topicSelect);
    fireEvent.click(screen.getByText('JavaScript Basics'));
    
    const fileInput = screen.getByLabelText('CSV File');
    const file = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      expect(screen.getByText('Confirm Import')).toBeInTheDocument();
    });
    
    // Now confirm import
    const confirmButton = screen.getByText('Confirm Import');
    fireEvent.click(confirmButton);
    
    await waitFor(() => {
      expect(batchImportService.executeBatchImport).toHaveBeenCalledWith(
        mockPreviewResult,
        expect.objectContaining({
          mode: 'single-topic',
          batchSize: 10,
          maxRetries: 3,
        })
      );
    });
    
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Import completed successfully',
        description: expect.stringContaining('Imported 6 questions'),
        variant: 'default',
      });
    });
  });

  it('handles batch import errors', async () => {
    const mockPreviewResult = createMockImportResult(2, 3);
    const error = new Error('Database connection failed');
    
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockPreviewResult);
    (batchImportService.executeBatchImport as any).mockRejectedValue(error);
    
    renderImportPage();
    
    // Go through preview and confirm
    const topicSelect = screen.getByRole('combobox');
    fireEvent.click(topicSelect);
    fireEvent.click(screen.getByText('JavaScript Basics'));
    
    const fileInput = screen.getByLabelText('CSV File');
    const file = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      const confirmButton = screen.getByText('Confirm Import');
      fireEvent.click(confirmButton);
    });
    
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Import error',
        description: 'Database connection failed',
        variant: 'destructive',
      });
    });
  });

  it('allows starting over after completion', async () => {
    const mockPreviewResult = createMockImportResult(1, 2);
    const mockBatchResult = {
      success: true,
      totalQuestionsImported: 2,
      totalTopicsProcessed: 1,
      topicsCreated: [],
      errors: [],
      duration: 1000,
      topicResults: new Map(),
    };
    
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockPreviewResult);
    (batchImportService.executeBatchImport as any).mockResolvedValue(mockBatchResult);
    
    renderImportPage();
    
    // Complete the import process
    const topicSelect = screen.getByRole('combobox');
    fireEvent.click(topicSelect);
    fireEvent.click(screen.getByText('JavaScript Basics'));
    
    const fileInput = screen.getByLabelText('CSV File');
    const file = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      const confirmButton = screen.getByText('Confirm Import');
      fireEvent.click(confirmButton);
    });
    
    await waitFor(() => {
      expect(screen.getByText('Import More Questions')).toBeInTheDocument();
    });
    
    // Click start over
    const startOverButton = screen.getByText('Import More Questions');
    fireEvent.click(startOverButton);
    
    // Should be back to upload step
    expect(screen.getByText('Upload CSV File')).toBeInTheDocument();
  });

  it('shows help section when requested', () => {
    renderImportPage();
    
    const helpButton = screen.getByText('Help');
    fireEvent.click(helpButton);
    
    expect(screen.getByText('CSV Format Requirements')).toBeInTheDocument();
    expect(screen.getByText('• Use UTF-8 encoding')).toBeInTheDocument();
  });

  it('handles loading state for topics', () => {
    (useAdminTopics as unknown).mockReturnValue({
      data: [],
      isLoading: true,
    });
    
    renderImportPage();
    
    // Should show loading spinner
    expect(screen.getByRole('status')).toBeInTheDocument();
  });
});