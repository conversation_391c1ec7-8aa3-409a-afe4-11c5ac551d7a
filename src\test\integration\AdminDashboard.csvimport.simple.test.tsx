import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { type MultiTopicImportResult } from '@/utils/csv-import';

// Test the enhanced CSV import success handler function directly
describe('AdminDashboard CSV Import Integration - Success Handler', () => {
  let mockToast: ReturnType<typeof vi.fn>;
  let mockRefreshQuestions: ReturnType<typeof vi.fn>;
  let mockRefreshTopics: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    mockToast = vi.fn();
    mockRefreshQuestions = vi.fn();
    mockRefreshTopics = vi.fn();
  });

  // Simulate the enhanced success handler logic
  const simulateCSVImportSuccess = (result?: MultiTopicImportResult) => {
    // This simulates the logic from the enhanced handleCSVImportSuccess function
    if (result) {
      console.log('Multi-topic import completed:', result);
      
      // Enhanced success handling for multi-topic results
      const totalQuestions = Array.from(result.topicResults.values())
        .reduce((sum, topicResult) => sum + topicResult.validQuestions.length, 0);
      
      const topicCount = result.topicResults.size;
      const newTopicsCount = result.newTopicsCreated.length;
      
      let successMessage = `Successfully imported ${totalQuestions} question${totalQuestions !== 1 ? 's' : ''}`;
      
      if (topicCount > 1) {
        successMessage += ` across ${topicCount} topic${topicCount !== 1 ? 's' : ''}`;
      }
      
      if (newTopicsCount > 0) {
        successMessage += `. Created ${newTopicsCount} new topic${newTopicsCount !== 1 ? 's' : ''}: ${result.newTopicsCreated.join(', ')}`;
      }
      
      mockToast({
        title: "Import completed successfully",
        description: successMessage,
      });
      
      // Refresh topics if new ones were created
      if (newTopicsCount > 0) {
        mockRefreshTopics();
      }
    }
    
    mockRefreshQuestions();
  };

  it('should handle single-topic import success correctly', () => {
    const singleTopicResult: MultiTopicImportResult = {
      success: true,
      totalRows: 5,
      topicResults: new Map([
        ['topic-1', {
          topicId: 'topic-1',
          topicName: 'Security Fundamentals',
          validQuestions: Array(5).fill(null).map((_, i) => ({
            id: `q-${i}`,
            topic_id: 'topic-1',
            question_text: `Question ${i + 1}`,
            options: { A: 'Option A', B: 'Option B', C: 'Option C', D: 'Option D' },
            correct_answer: 'A',
            explanation: 'Test explanation',
            difficulty: 'medium',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })),
          errors: [],
          isNewTopic: false,
        }]
      ]),
      globalErrors: [],
      newTopicsCreated: [],
    };

    simulateCSVImportSuccess(singleTopicResult);

    // Check that success message is correct for single topic
    expect(mockToast).toHaveBeenCalledWith({
      title: "Import completed successfully",
      description: "Successfully imported 5 questions",
    });

    // Check that questions are refreshed but not topics (no new topics created)
    expect(mockRefreshQuestions).toHaveBeenCalled();
    expect(mockRefreshTopics).not.toHaveBeenCalled();
  });

  it('should handle multi-topic import success with new topics correctly', () => {
    const multiTopicResult: MultiTopicImportResult = {
      success: true,
      totalRows: 10,
      topicResults: new Map([
        ['topic-1', {
          topicId: 'topic-1',
          topicName: 'Security Fundamentals',
          validQuestions: Array(3).fill(null).map((_, i) => ({
            id: `q1-${i}`,
            topic_id: 'topic-1',
            question_text: `Security Question ${i + 1}`,
            options: { A: 'Option A', B: 'Option B', C: 'Option C', D: 'Option D' },
            correct_answer: 'A',
            explanation: 'Security explanation',
            difficulty: 'medium',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })),
          errors: [],
          isNewTopic: false,
        }],
        ['topic-2', {
          topicId: 'topic-2',
          topicName: 'Network Security',
          validQuestions: Array(4).fill(null).map((_, i) => ({
            id: `q2-${i}`,
            topic_id: 'topic-2',
            question_text: `Network Question ${i + 1}`,
            options: { A: 'Option A', B: 'Option B', C: 'Option C', D: 'Option D' },
            correct_answer: 'B',
            explanation: 'Network explanation',
            difficulty: 'hard',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })),
          errors: [],
          isNewTopic: true,
        }],
        ['topic-3', {
          topicId: 'topic-3',
          topicName: 'Incident Response',
          validQuestions: Array(3).fill(null).map((_, i) => ({
            id: `q3-${i}`,
            topic_id: 'topic-3',
            question_text: `Incident Question ${i + 1}`,
            options: { A: 'Option A', B: 'Option B', C: 'Option C', D: 'Option D' },
            correct_answer: 'C',
            explanation: 'Incident explanation',
            difficulty: 'easy',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })),
          errors: [],
          isNewTopic: true,
        }]
      ]),
      globalErrors: [],
      newTopicsCreated: ['Network Security', 'Incident Response'],
    };

    simulateCSVImportSuccess(multiTopicResult);

    // Check that success message is correct for multi-topic import
    expect(mockToast).toHaveBeenCalledWith({
      title: "Import completed successfully",
      description: "Successfully imported 10 questions across 3 topics. Created 2 new topics: Network Security, Incident Response",
    });

    // Check that both questions and topics are refreshed (new topics were created)
    expect(mockRefreshQuestions).toHaveBeenCalled();
    expect(mockRefreshTopics).toHaveBeenCalled();
  });

  it('should handle single question import correctly', () => {
    const singleQuestionResult: MultiTopicImportResult = {
      success: true,
      totalRows: 1,
      topicResults: new Map([
        ['topic-1', {
          topicId: 'topic-1',
          topicName: 'Security Fundamentals',
          validQuestions: [{
            id: 'q-1',
            topic_id: 'topic-1',
            question_text: 'What is security?',
            options: { A: 'Option A', B: 'Option B', C: 'Option C', D: 'Option D' },
            correct_answer: 'A',
            explanation: 'Security explanation',
            difficulty: 'medium',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }],
          errors: [],
          isNewTopic: false,
        }]
      ]),
      globalErrors: [],
      newTopicsCreated: [],
    };

    simulateCSVImportSuccess(singleQuestionResult);

    // Check that singular form is used for single question
    expect(mockToast).toHaveBeenCalledWith({
      title: "Import completed successfully",
      description: "Successfully imported 1 question",
    });
  });

  it('should handle import with single new topic correctly', () => {
    const singleNewTopicResult: MultiTopicImportResult = {
      success: true,
      totalRows: 2,
      topicResults: new Map([
        ['topic-1', {
          topicId: 'topic-1',
          topicName: 'New Topic',
          validQuestions: Array(2).fill(null).map((_, i) => ({
            id: `q-${i}`,
            topic_id: 'topic-1',
            question_text: `Question ${i + 1}`,
            options: { A: 'Option A', B: 'Option B', C: 'Option C', D: 'Option D' },
            correct_answer: 'A',
            explanation: 'Test explanation',
            difficulty: 'medium',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })),
          errors: [],
          isNewTopic: true,
        }]
      ]),
      globalErrors: [],
      newTopicsCreated: ['New Topic'],
    };

    simulateCSVImportSuccess(singleNewTopicResult);

    // Check that singular form is used for single new topic
    expect(mockToast).toHaveBeenCalledWith({
      title: "Import completed successfully",
      description: "Successfully imported 2 questions. Created 1 new topic: New Topic",
    });

    // Check that topics are refreshed when new topic is created
    expect(mockRefreshTopics).toHaveBeenCalled();
  });

  it('should handle import result logging correctly', () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    
    const testResult: MultiTopicImportResult = {
      success: true,
      totalRows: 1,
      topicResults: new Map([
        ['topic-1', {
          topicId: 'topic-1',
          topicName: 'Test Topic',
          validQuestions: [{
            id: 'q-1',
            topic_id: 'topic-1',
            question_text: 'Test question',
            options: { A: 'Option A', B: 'Option B', C: 'Option C', D: 'Option D' },
            correct_answer: 'A',
            explanation: 'Test explanation',
            difficulty: 'medium',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }],
          errors: [],
          isNewTopic: false,
        }]
      ]),
      globalErrors: [],
      newTopicsCreated: [],
    };

    simulateCSVImportSuccess(testResult);

    // Check that import result is logged
    expect(consoleSpy).toHaveBeenCalledWith('Multi-topic import completed:', testResult);

    consoleSpy.mockRestore();
  });

  it('should handle undefined result gracefully', () => {
    simulateCSVImportSuccess(undefined);

    // Should still refresh questions even with undefined result
    expect(mockRefreshQuestions).toHaveBeenCalled();
    expect(mockRefreshTopics).not.toHaveBeenCalled();
    expect(mockToast).not.toHaveBeenCalled();
  });
});