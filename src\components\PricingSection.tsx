import { motion } from "framer-motion";
import { Check } from "lucide-react";
import SecurePaystackButton from "./SecurePaystackButton";
import { subscriptionPlans, topicSpecificPlans } from "@/utils/paystack";
import { useAuth } from "@/hooks/use-auth";
import { Link } from "react-router-dom";
import { useEffect, useRef } from "react";
import { useLocation } from "react-router-dom";

const PricingSection = () => {
  const { user } = useAuth();
  const location = useLocation();
  const sectionRef = useRef<HTMLElement>(null);

  // Handle direct scrolling to this section when the component mounts
  useEffect(() => {
    if (location.hash === '#pricing' && sectionRef.current) {
      setTimeout(() => {
        sectionRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    }
  }, [location.hash]);

  return (
    <section
      id="pricing"
      ref={sectionRef}
      className="relative py-20 overflow-hidden">

      <div className="container relative z-10 mx-auto px-4 bg-transparent">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">Choose Your Plan</h2>
          <p className="text-lg text-white max-w-2xl mx-auto">
            Select the perfect plan to accelerate your cybersecurity learning journey
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-6 justify-center items-center lg:items-stretch max-w-5xl mx-auto">
          {/* Basic Plan */}
          <motion.div
            className="w-full max-w-sm bg-white rounded-3xl shadow-xl overflow-hidden relative"
            initial={{ opacity: 0, y: 50, scale: 0.9 }}
            whileInView={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.1, type: "spring", stiffness: 100 }}
            viewport={{ once: true }}
            whileHover={{ y: -10, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
          >
            <div className="p-8 pb-20">
              <div className="text-center mb-8">
                <span className="text-blue-500 font-medium uppercase text-4xl tracking-wider">Basic</span>
                <div className="relative mt-6">
                  <motion.div
                    className="w-24 h-24 rounded-full bg-blue-100/50 mx-auto flex items-center justify-center"
                    whileHover={{ scale: 1.05, backgroundColor: "rgba(219, 234, 254, 0.8)" }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div className="flex items-baseline justify-center">
                      <span className="text-6xl font-bold text-green-600">Free</span>
                    </div>
                  </motion.div>
                  <div className="mt-2 text-gray-500 font-medium uppercase text-sm">REQUIRES LOGIN</div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">Access to 4 quiz domains</span>
                </div>
                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">400 questions weekly</span>
                </div>
                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">Requires authentication</span>
                </div>
              </div>
            </div>

            <div className="absolute bottom-0 left-0 right-0 p-4">
              {user ? (
                <motion.button
                  className="w-full py-3 px-6 rounded-full bg-green-500 hover:bg-green-600 text-white font-medium transition-colors duration-200 uppercase text-sm tracking-wider"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  disabled
                >
                  Already Active
                </motion.button>
              ) : (
                <Link to="/auth?tab=login" className="block">
                  <motion.button
                    className="w-full py-3 px-6 rounded-full bg-blue-500 hover:bg-blue-600 text-white font-medium transition-colors duration-200 uppercase text-sm tracking-wider"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Sign Up for Free
                  </motion.button>
                </Link>
              )}
            </div>
          </motion.div>

          {/* Starter Plan */}
          <motion.div
            className="w-full max-w-sm bg-white rounded-3xl shadow-xl overflow-hidden relative"
            initial={{ opacity: 0, y: 50, scale: 0.9 }}
            whileInView={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2, type: "spring", stiffness: 100 }}
            viewport={{ once: true }}
            whileHover={{ y: -10, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
          >
            <div className="p-8 pb-20">
              <div className="text-center mb-8">
                <span className="text-green-500 font-medium uppercase text-4xl tracking-wider">Starter</span>
                <p className="text-sm text-gray-500 uppercase tracking-wider mt-2">BEGINNER FOCUSED</p>
                <div className="relative mt-6">
                  <motion.div
                    className="w-24 h-24 rounded-full bg-green-100/50 mx-auto flex items-center justify-center"
                    whileHover={{ scale: 1.05, backgroundColor: "rgba(220, 252, 231, 0.8)" }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div className="flex items-baseline justify-center">
                      <span className="text-6xl font-bold text-green-600">₦999</span>
                    </div>
                  </motion.div>
                  <div className="mt-2 text-gray-500 font-medium uppercase text-sm">PER WEEK</div>
                </div>
              </div>

              <div className="space-y-4">
                {subscriptionPlans.starter.features.map((feature, index) => (
                  <div key={index} className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-gray-600">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="absolute bottom-0 left-0 right-0 p-4">
              {user ? (
                <SecurePaystackButton
                  plan={subscriptionPlans.starter}
                  className="bg-gradient-to-r from-green-500 to-emerald-400 hover:from-green-600 hover:to-emerald-500 text-white"
                  buttonText="Start Learning"
                />
              ) : (
                <Link to="/auth?tab=login" className="block">
                  <motion.button
                    className="w-full py-3 px-6 rounded-full bg-gradient-to-r from-green-500 to-emerald-400 hover:from-green-600 hover:to-emerald-500 text-white font-medium transition-colors duration-200 uppercase text-sm tracking-wider"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Login to Start
                  </motion.button>
                </Link>
              )}
            </div>
          </motion.div>

          {/* Pro Plan (Featured) */}
          <motion.div
            className="w-full max-w-sm bg-white rounded-3xl shadow-xl overflow-hidden relative lg:scale-110 z-10"
            initial={{ opacity: 0, y: 50, scale: 0.9 }}
            whileInView={{ opacity: 1, y: 0, scale: 1.1 }}
            transition={{ duration: 0.6, type: "spring", stiffness: 100 }}
            viewport={{ once: true }}
            whileHover={{ y: -10, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
          >
            <div className="bg-gradient-to-r from-indigo-500 to-cyan-400 py-2 text-center">
              <span className="text-white font-medium">MOST POPULAR</span>
            </div>
            <div className="p-8 pb-20">
              <div className="text-center mb-8">
                <span className="text-green-500 font-medium uppercase text-4xl tracking-wider">Pro</span>
                <div className="relative mt-6">
                  <motion.div
                    className="w-24 h-24 rounded-full bg-green-100/50 mx-auto flex items-center justify-center"
                    whileHover={{ scale: 1.05, backgroundColor: "rgba(220, 252, 231, 0.8)" }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div className="flex items-baseline justify-center">
                      <span className="text-6xl font-bold text-green-600">₦1,799</span>
                    </div>
                  </motion.div>
                  <div className="mt-2 text-gray-500 font-medium uppercase text-sm">PER WEEK</div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">Access to all quiz domains</span>
                </div>
                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">Unlimited questions</span>
                </div>

                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">Free international Certification</span>
                </div>
              </div>
            </div>

            <div className="absolute bottom-0 left-0 right-0 p-4">
              {user ? (
                <SecurePaystackButton
                  plan={subscriptionPlans.pro}
                  className="bg-gradient-to-r from-indigo-500 to-cyan-400 hover:from-indigo-600 hover:to-cyan-500 text-white shadow-md"
                  buttonText="Subscribe"
                />
              ) : (
                <Link to="/auth?tab=login" className="block">
                  <motion.button
                    className="w-full py-3 px-6 rounded-full bg-gradient-to-r from-indigo-500 to-cyan-400 hover:from-indigo-600 hover:to-cyan-500 text-white font-medium transition-colors duration-200 uppercase text-sm tracking-wider shadow-md"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Login to Subscribe
                  </motion.button>
                </Link>
              )}
            </div>
          </motion.div>

          {/* Elite Plan */}
          <motion.div
            className="w-full max-w-sm bg-white rounded-3xl shadow-xl overflow-hidden relative"
            initial={{ opacity: 0, y: 50, scale: 0.9 }}
            whileInView={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2, type: "spring", stiffness: 100 }}
            viewport={{ once: true }}
            whileHover={{ y: -10, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
          >
            <div className="p-8 pb-20">
              <div className="text-center mb-8">
                <span className="text-pink-500 font-medium uppercase text-4xl tracking-wider">Elite</span>
                <div className="relative mt-6">
                  <motion.div
                    className="w-24 h-24 rounded-full bg-pink-100/50 mx-auto flex items-center justify-center"
                    whileHover={{ scale: 1.05, backgroundColor: "rgba(252, 231, 243, 0.8)" }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div className="flex items-baseline justify-center">
                      <span className="text-6xl font-bold text-blue-900">₦4,500</span>
                    </div>
                  </motion.div>
                  <div className="mt-2 text-gray-500 font-medium uppercase text-sm">ONE-TIME</div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">Everything in Pro</span>
                </div>
                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">Community Access</span>
                </div>
                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">Free international Certification</span>
                </div>

                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">24/7 Priority Mentorship & Support</span>
                </div>

                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">Daily Cybersecurity Job Alert</span>
                </div>

              </div>
            </div>

            <div className="absolute bottom-0 left-0 right-0 p-4">
              {user ? (
                <SecurePaystackButton
                  plan={subscriptionPlans.elite}
                  className="bg-pink-400 hover:bg-pink-500 text-white"
                  buttonText="Purchase"
                />
              ) : (
                <Link to="/auth?tab=login" className="block">
                  <motion.button
                    className="w-full py-3 px-6 rounded-full bg-pink-400 hover:bg-pink-500 text-white font-medium transition-colors duration-200 uppercase text-sm tracking-wider"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Login to Purchase
                  </motion.button>
                </Link>
              )}
            </div>
          </motion.div>
        </div>

        {/* Topic-Specific Plans Section */}
        <div className="mt-20">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold mb-4 text-white">Topic-Specific Learning</h3>
            <p className="text-lg text-white max-w-2xl mx-auto">
              Focus on specific cybersecurity domains with targeted learning paths
            </p>
          </div>

          <div className="flex flex-col lg:flex-row gap-6 justify-center items-center lg:items-stretch max-w-4xl mx-auto">
            {/* CISSP Focus Plan */}
            <motion.div
              className="w-full max-w-sm bg-white rounded-3xl shadow-xl overflow-hidden relative"
              initial={{ opacity: 0, y: 50, scale: 0.9 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.1, type: "spring", stiffness: 100 }}
              viewport={{ once: true }}
              whileHover={{ y: -10, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
            >
              <div className="p-8 pb-20">
                <div className="text-center mb-8">
                  <span className="text-purple-500 font-medium uppercase text-3xl tracking-wider">CISSP Focus</span>
                  <div className="relative mt-6">
                    <motion.div
                      className="w-24 h-24 rounded-full bg-purple-100/50 mx-auto flex items-center justify-center"
                      whileHover={{ scale: 1.05, backgroundColor: "rgba(243, 232, 255, 0.8)" }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <div className="flex items-baseline justify-center">
                        <span className="text-5xl font-bold text-purple-600">₦599</span>
                      </div>
                    </motion.div>
                    <div className="mt-2 text-gray-500 font-medium uppercase text-sm">PER WEEK</div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-gray-600">CISSP-specific content only</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-gray-600">Unlimited CISSP questions</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-gray-600">CISSP study roadmap</span>
                  </div>
                </div>
              </div>

              <div className="absolute bottom-0 left-0 right-0 p-4">
                {user ? (
                  <SecurePaystackButton
                    plan={topicSpecificPlans.cissp}
                    className="bg-gradient-to-r from-purple-500 to-purple-400 hover:from-purple-600 hover:to-purple-500 text-white"
                    buttonText="Focus on CISSP"
                  />
                ) : (
                  <Link to="/auth?tab=login" className="block">
                    <motion.button
                      className="w-full py-3 px-6 rounded-full bg-gradient-to-r from-purple-500 to-purple-400 hover:from-purple-600 hover:to-purple-500 text-white font-medium transition-colors duration-200 uppercase text-sm tracking-wider"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      Login to Focus
                    </motion.button>
                  </Link>
                )}
              </div>
            </motion.div>

            {/* Security Awareness Plan */}
            <motion.div
              className="w-full max-w-sm bg-white rounded-3xl shadow-xl overflow-hidden relative"
              initial={{ opacity: 0, y: 50, scale: 0.9 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2, type: "spring", stiffness: 100 }}
              viewport={{ once: true }}
              whileHover={{ y: -10, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
            >
              <div className="p-8 pb-20">
                <div className="text-center mb-8">
                  <span className="text-orange-500 font-medium uppercase text-3xl tracking-wider">Security Awareness</span>
                  <div className="relative mt-6">
                    <motion.div
                      className="w-24 h-24 rounded-full bg-orange-100/50 mx-auto flex items-center justify-center"
                      whileHover={{ scale: 1.05, backgroundColor: "rgba(255, 237, 213, 0.8)" }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <div className="flex items-baseline justify-center">
                        <span className="text-5xl font-bold text-orange-600">₦399</span>
                      </div>
                    </motion.div>
                    <div className="mt-2 text-gray-500 font-medium uppercase text-sm">PER WEEK</div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-gray-600">Security awareness content</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-gray-600">Basic cybersecurity concepts</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-gray-600">Beginner-friendly materials</span>
                  </div>
                </div>
              </div>

              <div className="absolute bottom-0 left-0 right-0 p-4">
                {user ? (
                  <SecurePaystackButton
                    plan={topicSpecificPlans.awareness}
                    className="bg-gradient-to-r from-orange-500 to-orange-400 hover:from-orange-600 hover:to-orange-500 text-white"
                    buttonText="Start Awareness"
                  />
                ) : (
                  <Link to="/auth?tab=login" className="block">
                    <motion.button
                      className="w-full py-3 px-6 rounded-full bg-gradient-to-r from-orange-500 to-orange-400 hover:from-orange-600 hover:to-orange-500 text-white font-medium transition-colors duration-200 uppercase text-sm tracking-wider"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      Login to Start
                    </motion.button>
                  </Link>
                )}
              </div>
            </motion.div>
          </div>
        </div>

        <div className="text-center mt-12">
          <p className="text-white text-sm">
          Don't know where to start?
            <br />
            Need help choosing? <a href="/contact" className="text-indigo-200 font-medium hover:underline">Contact us</a> for assistance.
          </p>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
