/**
 * Question Pool Analytics Service
 * Provides analytics and insights for question pools, usage tracking, and performance monitoring
 */

import { supabase } from '@/integrations/supabase/client';
import type { Tables } from '@/types/supabase';

// Type definitions for analytics
export interface QuestionAnalytics extends Tables<'question_analytics'> {}

export interface QuestionWithStats extends Tables<'questions'> {
  usage_count: number;
  last_used: string | null;
  correct_answer_rate: number;
}

export interface TopicQuestionStats {
  total_questions: number;
  avg_usage_count: number;
  avg_correct_rate: number;
  questions_never_used: number;
  questions_low_performance: number;
}

export interface QuestionDifficultyAnalysis {
  questionId: string;
  questionText: string;
  totalAttempts: number;
  correctAttempts: number;
  correctRate: number;
  averageTimeToAnswer: number;
  difficultyLevel: 'easy' | 'medium' | 'hard' | 'very_hard';
  needsReview: boolean;
}

export interface TopicInsights {
  topicId: string;
  topicTitle: string;
  stats: TopicQuestionStats;
  questionVariety: 'insufficient' | 'minimal' | 'good' | 'excellent';
  alerts: TopicAlert[];
  recommendations: string[];
}

export interface TopicAlert {
  type: 'insufficient_questions' | 'low_performance' | 'unused_questions' | 'high_difficulty';
  severity: 'low' | 'medium' | 'high';
  message: string;
  count?: number;
}

export interface ContentRecommendation {
  type: 'add_questions' | 'review_question' | 'improve_explanation' | 'adjust_difficulty';
  priority: 'low' | 'medium' | 'high';
  topicId: string;
  questionId?: string;
  message: string;
  details: string;
}

/**
 * Question Pool Analytics Service Class
 * Provides comprehensive analytics for question pools and user performance
 */
export class QuestionPoolAnalyticsService {
  
  /**
   * Records analytics data for a question answer
   * @param questionId - ID of the question
   * @param userId - ID of the user
   * @param sessionId - ID of the quiz session
   * @param answeredCorrectly - Whether the answer was correct
   * @param selectedOption - The option index selected by user
   * @param timeToAnswer - Time taken to answer in seconds
   * @returns Promise<boolean> - Success status
   */
  static async recordQuestionAnalytics(
    questionId: string,
    userId: string,
    sessionId: string,
    answeredCorrectly: boolean,
    selectedOption: number,
    timeToAnswer?: number
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('question_analytics')
        .insert({
          question_id: questionId,
          user_id: userId,
          quiz_session_id: sessionId,
          answered_correctly: answeredCorrectly,
          selected_option: selectedOption,
          time_to_answer: timeToAnswer
        });

      if (error) {
        console.error('Error recording question analytics:', error);
        return false;
      }

      return true;

    } catch (error) {
      console.error('Error in recordQuestionAnalytics:', error);
      return false;
    }
  }

  /**
   * Gets comprehensive statistics for a topic's question pool
   * @param topicId - ID of the topic
   * @returns Promise<TopicQuestionStats> - Topic statistics
   */
  static async getTopicQuestionStats(topicId: string): Promise<TopicQuestionStats> {
    try {
      const { data, error } = await supabase
        .rpc('get_topic_question_stats', { topic_uuid: topicId });

      if (error) {
        console.error('Error fetching topic question stats:', error);
        throw new Error(`Failed to fetch topic stats: ${error.message}`);
      }

      return data?.[0] || {
        total_questions: 0,
        avg_usage_count: 0,
        avg_correct_rate: 0,
        questions_never_used: 0,
        questions_low_performance: 0
      };

    } catch (error) {
      console.error('Error in getTopicQuestionStats:', error);
      throw error;
    }
  }

  /**
   * Analyzes question difficulty based on user responses
   * @param questionId - ID of the question to analyze
   * @returns Promise<QuestionDifficultyAnalysis> - Difficulty analysis
   */
  static async analyzeQuestionDifficulty(questionId: string): Promise<QuestionDifficultyAnalysis> {
    try {
      // Get question details
      const { data: question, error: questionError } = await supabase
        .from('questions')
        .select('question_text, usage_count, correct_answer_rate')
        .eq('id', questionId)
        .single();

      if (questionError || !question) {
        throw new Error(`Question not found: ${questionError?.message}`);
      }

      // Get analytics data for this question
      const { data: analytics, error: analyticsError } = await supabase
        .from('question_analytics')
        .select('answered_correctly, time_to_answer')
        .eq('question_id', questionId);

      if (analyticsError) {
        throw new Error(`Failed to fetch analytics: ${analyticsError.message}`);
      }

      const totalAttempts = analytics?.length || 0;
      const correctAttempts = analytics?.filter(a => a.answered_correctly).length || 0;
      const correctRate = totalAttempts > 0 ? (correctAttempts / totalAttempts) * 100 : 0;
      
      // Calculate average time to answer (excluding null values)
      const validTimes = analytics?.filter(a => a.time_to_answer !== null).map(a => a.time_to_answer!) || [];
      const averageTimeToAnswer = validTimes.length > 0 
        ? validTimes.reduce((sum, time) => sum + time, 0) / validTimes.length 
        : 0;

      // Determine difficulty level based on correct rate and time
      let difficultyLevel: 'easy' | 'medium' | 'hard' | 'very_hard';
      if (correctRate >= 80) {
        difficultyLevel = 'easy';
      } else if (correctRate >= 60) {
        difficultyLevel = 'medium';
      } else if (correctRate >= 30) {
        difficultyLevel = 'hard';
      } else {
        difficultyLevel = 'very_hard';
      }

      // Determine if question needs review
      const needsReview = correctRate < 30 || (totalAttempts >= 10 && correctRate < 40);

      return {
        questionId,
        questionText: question.question_text,
        totalAttempts,
        correctAttempts,
        correctRate: Math.round(correctRate * 100) / 100,
        averageTimeToAnswer: Math.round(averageTimeToAnswer),
        difficultyLevel,
        needsReview
      };

    } catch (error) {
      console.error('Error in analyzeQuestionDifficulty:', error);
      throw error;
    }
  }

  /**
   * Gets comprehensive insights for a topic including alerts and recommendations
   * @param topicId - ID of the topic
   * @returns Promise<TopicInsights> - Complete topic insights
   */
  static async getTopicInsights(topicId: string): Promise<TopicInsights> {
    try {
      // Get topic details
      const { data: topic, error: topicError } = await supabase
        .from('topics')
        .select('title')
        .eq('id', topicId)
        .single();

      if (topicError || !topic) {
        throw new Error(`Topic not found: ${topicError?.message}`);
      }

      // Get topic statistics
      const stats = await this.getTopicQuestionStats(topicId);

      // Determine question variety level
      let questionVariety: 'insufficient' | 'minimal' | 'good' | 'excellent';
      if (stats.total_questions < 15) {
        questionVariety = 'insufficient';
      } else if (stats.total_questions < 30) {
        questionVariety = 'minimal';
      } else if (stats.total_questions < 50) {
        questionVariety = 'good';
      } else {
        questionVariety = 'excellent';
      }

      // Generate alerts
      const alerts: TopicAlert[] = [];

      if (stats.total_questions < 20) {
        alerts.push({
          type: 'insufficient_questions',
          severity: 'high',
          message: `Topic has only ${stats.total_questions} questions. Minimum 20 recommended for good randomization.`,
          count: stats.total_questions
        });
      }

      if (stats.questions_low_performance > 0) {
        alerts.push({
          type: 'low_performance',
          severity: 'medium',
          message: `${stats.questions_low_performance} questions have low performance (< 30% correct rate).`,
          count: stats.questions_low_performance
        });
      }

      if (stats.questions_never_used > stats.total_questions * 0.3) {
        alerts.push({
          type: 'unused_questions',
          severity: 'low',
          message: `${stats.questions_never_used} questions have never been used in quizzes.`,
          count: stats.questions_never_used
        });
      }

      if (stats.avg_correct_rate < 40) {
        alerts.push({
          type: 'high_difficulty',
          severity: 'medium',
          message: `Average correct rate is ${stats.avg_correct_rate.toFixed(1)}%. Consider reviewing question difficulty.`
        });
      }

      // Generate recommendations
      const recommendations: string[] = [];

      if (stats.total_questions < 30) {
        recommendations.push(`Add ${30 - stats.total_questions} more questions to improve quiz variety`);
      }

      if (stats.questions_low_performance > 0) {
        recommendations.push(`Review ${stats.questions_low_performance} low-performing questions for clarity and accuracy`);
      }

      if (stats.avg_correct_rate < 50) {
        recommendations.push('Consider adding easier questions or improving explanations to balance difficulty');
      }

      if (stats.questions_never_used > 5) {
        recommendations.push('Promote unused questions or review their relevance to the topic');
      }

      return {
        topicId,
        topicTitle: topic.title,
        stats,
        questionVariety,
        alerts,
        recommendations
      };

    } catch (error) {
      console.error('Error in getTopicInsights:', error);
      throw error;
    }
  }

  /**
   * Gets content improvement recommendations across all topics
   * @returns Promise<ContentRecommendation[]> - Array of recommendations
   */
  static async getContentRecommendations(): Promise<ContentRecommendation[]> {
    try {
      const recommendations: ContentRecommendation[] = [];

      // Get all topics with their question counts
      const { data: topics, error: topicsError } = await supabase
        .from('topics')
        .select(`
          id,
          title,
          questions!inner(id, usage_count, correct_answer_rate)
        `);

      if (topicsError) {
        throw new Error(`Failed to fetch topics: ${topicsError.message}`);
      }

      for (const topic of topics || []) {
        const questions = topic.questions as any[];
        const questionCount = questions.length;

        // Recommendation: Add more questions
        if (questionCount < 20) {
          recommendations.push({
            type: 'add_questions',
            priority: questionCount < 10 ? 'high' : 'medium',
            topicId: topic.id,
            message: `Add more questions to ${topic.title}`,
            details: `Currently has ${questionCount} questions. Recommended: 20+ for good variety.`
          });
        }

        // Recommendation: Review low-performing questions
        const lowPerformingQuestions = questions.filter(q => 
          q.correct_answer_rate !== null && q.correct_answer_rate < 30
        );

        for (const question of lowPerformingQuestions) {
          recommendations.push({
            type: 'review_question',
            priority: 'medium',
            topicId: topic.id,
            questionId: question.id,
            message: `Review low-performing question in ${topic.title}`,
            details: `Question has ${question.correct_answer_rate}% correct rate. Consider improving clarity or difficulty.`
          });
        }

        // Recommendation: Improve explanations for difficult questions
        const difficultQuestions = questions.filter(q => 
          q.correct_answer_rate !== null && q.correct_answer_rate < 50 && q.correct_answer_rate >= 30
        );

        if (difficultQuestions.length > questionCount * 0.4) {
          recommendations.push({
            type: 'improve_explanation',
            priority: 'low',
            topicId: topic.id,
            message: `Improve explanations in ${topic.title}`,
            details: `${difficultQuestions.length} questions have moderate difficulty. Better explanations could help.`
          });
        }
      }

      // Sort by priority
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      recommendations.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority]);

      return recommendations;

    } catch (error) {
      console.error('Error in getContentRecommendations:', error);
      throw error;
    }
  }

  /**
   * Gets questions that need review based on performance metrics
   * @param topicId - Optional topic ID to filter by
   * @param limit - Maximum number of questions to return
   * @returns Promise<QuestionDifficultyAnalysis[]> - Questions needing review
   */
  static async getQuestionsNeedingReview(
    topicId?: string,
    limit: number = 20
  ): Promise<QuestionDifficultyAnalysis[]> {
    try {
      let query = supabase
        .from('questions')
        .select('id, question_text, usage_count, correct_answer_rate, topic_id')
        .or('correct_answer_rate.lt.30,usage_count.gte.10')
        .order('correct_answer_rate', { ascending: true })
        .limit(limit);

      if (topicId) {
        query = query.eq('topic_id', topicId);
      }

      const { data: questions, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch questions: ${error.message}`);
      }

      const analyses: QuestionDifficultyAnalysis[] = [];

      for (const question of questions || []) {
        try {
          const analysis = await this.analyzeQuestionDifficulty(question.id);
          if (analysis.needsReview) {
            analyses.push(analysis);
          }
        } catch (error) {
          console.warn(`Failed to analyze question ${question.id}:`, error);
        }
      }

      return analyses;

    } catch (error) {
      console.error('Error in getQuestionsNeedingReview:', error);
      throw error;
    }
  }

  /**
   * Gets usage statistics for all questions in a topic
   * @param topicId - ID of the topic
   * @returns Promise<QuestionWithStats[]> - Questions with usage statistics
   */
  static async getTopicQuestionUsage(topicId: string): Promise<QuestionWithStats[]> {
    try {
      const { data: questions, error } = await supabase
        .from('questions')
        .select('*, usage_count, last_used, correct_answer_rate')
        .eq('topic_id', topicId)
        .order('usage_count', { ascending: false });

      if (error) {
        throw new Error(`Failed to fetch questions: ${error.message}`);
      }

      return questions || [];

    } catch (error) {
      console.error('Error in getTopicQuestionUsage:', error);
      throw error;
    }
  }

  /**
   * Gets analytics summary for administrative dashboard
   * @returns Promise with overall analytics summary
   */
  static async getAnalyticsSummary() {
    try {
      // Get overall statistics with timeout and retry logic
      const { data: overallStats, error: statsError } = await supabase
        .from('questions')
        .select('id, usage_count, correct_answer_rate, topic_id')
        .limit(1000); // Limit to prevent timeout on large datasets

      if (statsError) {
        console.error('Database error in getAnalyticsSummary:', statsError);
        // Return default values instead of throwing
        return {
          totalQuestions: 0,
          questionsUsed: 0,
          questionsNeverUsed: 0,
          questionsNeedingReview: 0,
          topicsWithInsufficientQuestions: 0,
          recentActivity: {
            weeklyAttempts: 0,
            weeklyCorrectRate: 0
          },
          averageUsageCount: 0,
          averageCorrectRate: 0
        };
      }

      const questions = overallStats || [];
      const totalQuestions = questions.length;
      const questionsWithUsage = questions.filter(q => q.usage_count && q.usage_count > 0);
      const questionsWithLowPerformance = questions.filter(q => 
        q.correct_answer_rate !== null && q.correct_answer_rate < 30
      );

      // Get topic counts
      const topicCounts = questions.reduce((acc, q) => {
        if (q.topic_id) {
          acc[q.topic_id] = (acc[q.topic_id] || 0) + 1;
        }
        return acc;
      }, {} as Record<string, number>);

      const topicsWithInsufficientQuestions = Object.values(topicCounts).filter(count => count < 20).length;

      // Get recent analytics activity with error handling
      let recentActivity = null;
      try {
        const { data, error: activityError } = await supabase
          .from('question_analytics')
          .select('created_at, answered_correctly')
          .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
          .order('created_at', { ascending: false })
          .limit(1000); // Limit to prevent timeout

        if (activityError) {
          console.warn('Failed to fetch recent activity:', activityError);
        } else {
          recentActivity = data;
        }
      } catch (activityError) {
        console.warn('Error fetching recent activity:', activityError);
      }

      const recentAttempts = recentActivity?.length || 0;
      const recentCorrectAttempts = recentActivity?.filter(a => a.answered_correctly).length || 0;
      const weeklyCorrectRate = recentAttempts > 0 ? (recentCorrectAttempts / recentAttempts) * 100 : 0;

      return {
        totalQuestions,
        questionsUsed: questionsWithUsage.length,
        questionsNeverUsed: totalQuestions - questionsWithUsage.length,
        questionsNeedingReview: questionsWithLowPerformance.length,
        topicsWithInsufficientQuestions,
        recentActivity: {
          weeklyAttempts: recentAttempts,
          weeklyCorrectRate: Math.round(weeklyCorrectRate * 100) / 100
        },
        averageUsageCount: questionsWithUsage.length > 0 
          ? Math.round(questionsWithUsage.reduce((sum, q) => sum + (q.usage_count || 0), 0) / questionsWithUsage.length)
          : 0,
        averageCorrectRate: questions.filter(q => q.correct_answer_rate !== null).length > 0
          ? Math.round(questions.filter(q => q.correct_answer_rate !== null)
              .reduce((sum, q) => sum + (q.correct_answer_rate || 0), 0) / 
              questions.filter(q => q.correct_answer_rate !== null).length * 100) / 100
          : 0
      };

    } catch (error) {
      console.error('Error in getAnalyticsSummary:', error);
      throw error;
    }
  }
}

export default QuestionPoolAnalyticsService;