/**
 * QuizSession Model
 * Represents a quiz session with randomized questions and provides session management operations
 */

import { supabase } from '@/integrations/supabase/client';
import type { Tables } from '@/types/supabase';
import type { RandomizedQuestion } from '@/services/quiz-randomization-service';

export interface QuizSessionData extends Tables<'quiz_sessions'> {}

export interface QuizSessionCreateParams {
  userId: string;
  topicId: string;
  questionsData: {
    questions: Array<{
      id: string;
      originalCorrectIndex: number;
      shuffledCorrectIndex: number;
      optionMapping: number[];
    }>;
    metadata: {
      totalQuestions: number;
      topicId: string;
      createdAt: string;
    };
  };
  quizLength: number;
}

export interface QuizSessionUpdateParams {
  completedAt?: string;
  score?: number;
  timeTaken?: number;
}

export interface QuizSessionValidationResult {
  isValid: boolean;
  isExpired: boolean;
  isCompleted: boolean;
  message?: string;
}

/**
 * QuizSession Model Class
 * Provides CRUD operations and validation for quiz sessions
 */
export class QuizSession {
  private data: QuizSessionData;

  constructor(data: QuizSessionData) {
    this.data = data;
  }

  // Getters for session properties
  get id(): string {
    return this.data.id;
  }

  get userId(): string {
    return this.data.user_id;
  }

  get topicId(): string | null {
    return this.data.topic_id;
  }

  get questionsData(): any {
    return this.data.questions_data;
  }

  get quizLength(): number {
    return this.data.quiz_length;
  }

  get createdAt(): string {
    return this.data.created_at;
  }

  get expiresAt(): string {
    return this.data.expires_at;
  }

  get completedAt(): string | null {
    return this.data.completed_at;
  }

  get score(): number | null {
    return this.data.score;
  }

  get totalQuestions(): number | null {
    return this.data.total_questions;
  }

  get timeTaken(): number | null {
    return this.data.time_taken;
  }

  get isCompleted(): boolean {
    return this.data.completed_at !== null;
  }

  get isExpired(): boolean {
    return new Date(this.data.expires_at) < new Date();
  }

  /**
   * Creates a new quiz session in the database
   * @param params - Session creation parameters
   * @returns Promise<QuizSession> - The created session
   */
  static async create(params: QuizSessionCreateParams): Promise<QuizSession> {
    try {
      const { data, error } = await supabase
        .from('quiz_sessions')
        .insert({
          user_id: params.userId,
          topic_id: params.topicId,
          questions_data: params.questionsData,
          quiz_length: params.quizLength,
          total_questions: params.questionsData.questions.length,
          // expires_at will be set by database default (2 hours from now)
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating quiz session:', error);
        throw new Error(`Failed to create quiz session: ${error.message}`);
      }

      return new QuizSession(data);

    } catch (error) {
      console.error('Error in QuizSession.create:', error);
      throw error;
    }
  }

  /**
   * Retrieves a quiz session by ID
   * @param sessionId - ID of the session
   * @param userId - ID of the user (for security validation)
   * @returns Promise<QuizSession | null> - The session or null if not found
   */
  static async findById(sessionId: string, userId?: string): Promise<QuizSession | null> {
    try {
      let query = supabase
        .from('quiz_sessions')
        .select('*')
        .eq('id', sessionId);

      // Add user filter if provided for security
      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query.single();

      if (error || !data) {
        if (error.code !== 'PGRST116') { // Not found error
          console.error('Error fetching quiz session:', error);
        }
        return null;
      }

      return new QuizSession(data);

    } catch (error) {
      console.error('Error in QuizSession.findById:', error);
      return null;
    }
  }

  /**
   * Retrieves all quiz sessions for a user
   * @param userId - ID of the user
   * @param limit - Maximum number of sessions to return
   * @param includeCompleted - Whether to include completed sessions
   * @returns Promise<QuizSession[]> - Array of sessions
   */
  static async findByUserId(
    userId: string, 
    limit: number = 50, 
    includeCompleted: boolean = true
  ): Promise<QuizSession[]> {
    try {
      let query = supabase
        .from('quiz_sessions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (!includeCompleted) {
        query = query.is('completed_at', null);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching user quiz sessions:', error);
        throw new Error(`Failed to fetch quiz sessions: ${error.message}`);
      }

      return (data || []).map(sessionData => new QuizSession(sessionData));

    } catch (error) {
      console.error('Error in QuizSession.findByUserId:', error);
      throw error;
    }
  }

  /**
   * Retrieves active (non-expired, non-completed) sessions for a user
   * @param userId - ID of the user
   * @returns Promise<QuizSession[]> - Array of active sessions
   */
  static async findActiveSessions(userId: string): Promise<QuizSession[]> {
    try {
      const { data, error } = await supabase
        .from('quiz_sessions')
        .select('*')
        .eq('user_id', userId)
        .is('completed_at', null)
        .gt('expires_at', new Date().toISOString())
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching active quiz sessions:', error);
        throw new Error(`Failed to fetch active sessions: ${error.message}`);
      }

      return (data || []).map(sessionData => new QuizSession(sessionData));

    } catch (error) {
      console.error('Error in QuizSession.findActiveSessions:', error);
      throw error;
    }
  }

  /**
   * Updates the quiz session
   * @param updates - Fields to update
   * @returns Promise<boolean> - Success status
   */
  async update(updates: QuizSessionUpdateParams): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('quiz_sessions')
        .update({
          completed_at: updates.completedAt,
          score: updates.score,
          time_taken: updates.timeTaken,
        })
        .eq('id', this.data.id);

      if (error) {
        console.error('Error updating quiz session:', error);
        return false;
      }

      // Update local data
      if (updates.completedAt !== undefined) {
        this.data.completed_at = updates.completedAt;
      }
      if (updates.score !== undefined) {
        this.data.score = updates.score;
      }
      if (updates.timeTaken !== undefined) {
        this.data.time_taken = updates.timeTaken;
      }

      return true;

    } catch (error) {
      console.error('Error in QuizSession.update:', error);
      return false;
    }
  }

  /**
   * Completes the quiz session with score and time
   * @param score - Final score achieved
   * @param timeTaken - Time taken in seconds
   * @returns Promise<boolean> - Success status
   */
  async complete(score: number, timeTaken?: number): Promise<boolean> {
    return this.update({
      completedAt: new Date().toISOString(),
      score,
      timeTaken,
    });
  }

  /**
   * Validates the session state
   * @returns QuizSessionValidationResult - Validation result with details
   */
  validate(): QuizSessionValidationResult {
    const now = new Date();
    const expiresAt = new Date(this.data.expires_at);
    const isExpired = expiresAt < now;
    const isCompleted = this.data.completed_at !== null;

    if (isCompleted) {
      return {
        isValid: false,
        isExpired,
        isCompleted: true,
        message: 'Session is already completed'
      };
    }

    if (isExpired) {
      return {
        isValid: false,
        isExpired: true,
        isCompleted: false,
        message: 'Session has expired'
      };
    }

    return {
      isValid: true,
      isExpired: false,
      isCompleted: false,
      message: 'Session is valid and active'
    };
  }

  /**
   * Extends the session expiration time
   * @param additionalMinutes - Minutes to add to expiration
   * @returns Promise<boolean> - Success status
   */
  async extendExpiration(additionalMinutes: number = 60): Promise<boolean> {
    try {
      const currentExpiry = new Date(this.data.expires_at);
      const newExpiry = new Date(currentExpiry.getTime() + (additionalMinutes * 60 * 1000));

      const { error } = await supabase
        .from('quiz_sessions')
        .update({ expires_at: newExpiry.toISOString() })
        .eq('id', this.data.id);

      if (error) {
        console.error('Error extending session expiration:', error);
        return false;
      }

      this.data.expires_at = newExpiry.toISOString();
      return true;

    } catch (error) {
      console.error('Error in QuizSession.extendExpiration:', error);
      return false;
    }
  }

  /**
   * Deletes the quiz session
   * @returns Promise<boolean> - Success status
   */
  async delete(): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('quiz_sessions')
        .delete()
        .eq('id', this.data.id);

      if (error) {
        console.error('Error deleting quiz session:', error);
        return false;
      }

      return true;

    } catch (error) {
      console.error('Error in QuizSession.delete:', error);
      return false;
    }
  }

  /**
   * Cleans up expired sessions for all users
   * @returns Promise<number> - Number of sessions cleaned up
   */
  static async cleanupExpired(): Promise<number> {
    try {
      const { data, error } = await supabase
        .rpc('cleanup_expired_quiz_sessions');

      if (error) {
        console.error('Error cleaning up expired sessions:', error);
        return 0;
      }

      return data || 0;

    } catch (error) {
      console.error('Error in QuizSession.cleanupExpired:', error);
      return 0;
    }
  }

  /**
   * Gets session statistics for a user
   * @param userId - ID of the user
   * @returns Promise with user session statistics
   */
  static async getUserSessionStats(userId: string) {
    try {
      const { data, error } = await supabase
        .from('quiz_sessions')
        .select('completed_at, score, total_questions, time_taken, topic_id')
        .eq('user_id', userId);

      if (error) {
        console.error('Error fetching user session stats:', error);
        throw error;
      }

      const sessions = data || [];
      const completedSessions = sessions.filter(s => s.completed_at);
      
      const stats = {
        totalSessions: sessions.length,
        completedSessions: completedSessions.length,
        averageScore: 0,
        averageTime: 0,
        totalQuestionsAnswered: 0,
        uniqueTopics: new Set(sessions.map(s => s.topic_id).filter(Boolean)).size
      };

      if (completedSessions.length > 0) {
        const totalScore = completedSessions.reduce((sum, s) => sum + (s.score || 0), 0);
        const totalTime = completedSessions.reduce((sum, s) => sum + (s.time_taken || 0), 0);
        const totalQuestions = completedSessions.reduce((sum, s) => sum + (s.total_questions || 0), 0);

        stats.averageScore = Math.round((totalScore / completedSessions.length) * 100) / 100;
        stats.averageTime = Math.round(totalTime / completedSessions.length);
        stats.totalQuestionsAnswered = totalQuestions;
      }

      return stats;

    } catch (error) {
      console.error('Error in QuizSession.getUserSessionStats:', error);
      throw error;
    }
  }

  /**
   * Converts the session to a plain object
   * @returns Plain object representation of the session
   */
  toJSON(): QuizSessionData {
    return { ...this.data };
  }
}

export default QuizSession;