# Supabase CLI Setup Guide

## Overview

The Supabase CLI is a command-line tool that allows you to run a local Supabase instance for development. This is different from the VS Code extension, which is primarily for database management and doesn't provide local instance functionality.

## VS Code Extension vs CLI Tool

### VS Code Extension
- **Purpose**: Database management, schema editing, SQL queries
- **Limitations**: Cannot start local Supabase instances
- **Use Case**: Managing remote Supabase projects

### Supabase CLI
- **Purpose**: Local development, project scaffolding, migrations
- **Capabilities**: Start local instances, run migrations, deploy functions
- **Use Case**: Full local development workflow

## 1. Installing Supabase CLI

### Windows Installation

#### Method 1: Using Scoop (Recommended)
```bash
# Install Scoop if you don't have it
Set-ExecutionPolicy RemoteSigned -Scope CurrentUser
irm get.scoop.sh | iex

# Install Supabase CLI
scoop bucket add supabase https://github.com/supabase/scoop-bucket.git
scoop install supabase
```

#### Method 2: Using Chocolatey
```bash
# Install Chocolatey if you don't have it
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# Install Supabase CLI
choco install supabase
```

#### Method 3: Direct Download
1. Go to [Supabase CLI Releases](https://github.com/supabase/cli/releases)
2. Download the Windows binary (`supabase_windows_amd64.tar.gz`)
3. Extract to a folder (e.g., `C:\supabase`)
4. Add the folder to your PATH environment variable

### macOS Installation

#### Method 1: Using Homebrew (Recommended)
```bash
# Install Homebrew if you don't have it
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install Supabase CLI
brew install supabase/tap/supabase
```

#### Method 2: Using npm
```bash
npm install -g supabase
```

### Linux Installation

#### Method 1: Using Package Manager (Ubuntu/Debian)
```bash
# Add Supabase repository
curl -fsSL https://supabase.com/dist/gpg | sudo gpg --dearmor -o /usr/share/keyrings/supabase-archive-keyring.gpg
echo "deb [signed-by=/usr/share/keyrings/supabase-archive-keyring.gpg] https://supabase.com/dist/apt/ stable main" | sudo tee /etc/apt/sources.list.d/supabase.list

# Update and install
sudo apt update
sudo apt install supabase
```

#### Method 2: Using npm
```bash
npm install -g supabase
```

#### Method 3: Direct Download
```bash
# Download and install
curl -fsSL https://github.com/supabase/cli/releases/latest/download/supabase_linux_amd64.tar.gz | tar -xz
sudo mv supabase /usr/local/bin/
```

## 2. Verify CLI Installation

### Check Installation
```bash
supabase --version
```

**Expected Output:**
```
supabase version 1.x.x
```

### Check Available Commands
```bash
supabase --help
```

**Expected Output:**
```
Supabase CLI

Usage:
  supabase [command]

Available Commands:
  completion  Generate the autocompletion script for the specified shell
  db          Manage local Postgres databases
  functions   Manage Supabase Edge Functions
  gen         Generate code and types from your Supabase schema
  help        Help about any command
  init        Initialize a new Supabase project
  link        Link to a Supabase project
  login       Authenticate using an access token
  logout      Log out of the Supabase CLI
  migration   Manage database migration scripts
  orgs        Manage Supabase organizations
  projects    Manage Supabase projects
  secrets     Manage Supabase secrets
  start       Start containers for Supabase local development
  status      Show status of local Supabase containers
  stop        Stop all local Supabase containers
  test        Run tests on local Supabase containers

Flags:
      --create-ticket   create a support ticket for any CLI error
      --debug           output debug logs to stderr
  -h, --help            help for supabase
      --version         version for supabase

Use "supabase [command] --help" for more information about a command.
```

## 3. Troubleshooting Installation Issues

### Common Error: "bash: supabase: command not found"

#### Windows Solutions:
1. **Check PATH Environment Variable:**
   ```cmd
   echo %PATH%
   ```
   Ensure the Supabase installation directory is in PATH.

2. **Add to PATH manually:**
   - Open System Properties → Advanced → Environment Variables
   - Add Supabase installation directory to PATH
   - Restart terminal

3. **Reinstall with different method:**
   Try a different installation method from above.

#### macOS/Linux Solutions:
1. **Check PATH:**
   ```bash
   echo $PATH
   ```

2. **Add to shell profile:**
   ```bash
   # For bash
   echo 'export PATH="/usr/local/bin:$PATH"' >> ~/.bashrc
   source ~/.bashrc

   # For zsh
   echo 'export PATH="/usr/local/bin:$PATH"' >> ~/.zshrc
   source ~/.zshrc
   ```

3. **Check installation location:**
   ```bash
   which supabase
   ls -la /usr/local/bin/supabase
   ```

### Permission Issues (Linux/macOS)
```bash
# Make executable
sudo chmod +x /usr/local/bin/supabase

# Or install to user directory
mkdir -p ~/.local/bin
mv supabase ~/.local/bin/
echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc
```

## 4. Docker Requirement

Supabase CLI requires Docker to run local instances.

### Install Docker

#### Windows:
- Download Docker Desktop from [docker.com](https://www.docker.com/products/docker-desktop)
- Install and start Docker Desktop

#### macOS:
```bash
brew install --cask docker
```

#### Linux (Ubuntu):
```bash
sudo apt update
sudo apt install docker.io docker-compose
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER
```

### Verify Docker Installation
```bash
docker --version
docker-compose --version
```

**Expected Output:**
```
Docker version 20.x.x, build xxxxx
Docker Compose version v2.x.x
```

## 5. Initialize Supabase Project

### Navigate to Your Project
```bash
cd /path/to/your/secquiz/project
```

### Initialize Supabase
```bash
supabase init
```

**Expected Output:**
```
Generate VS Code settings for Deno? [y/N] N
Finished supabase init.
```

This creates a `supabase` folder with configuration files.

### Project Structure After Init
```
your-project/
├── supabase/
│   ├── config.toml
│   ├── seed.sql
│   └── migrations/
├── .gitignore (updated)
└── ... (your existing files)
```

## 6. Start Local Supabase Instance

### Start All Services
```bash
supabase start
```

**Expected Output:**
```
Starting supabase local development setup.

         API URL: http://localhost:54321
          DB URL: postgresql://postgres:postgres@localhost:54322/postgres
      Studio URL: http://localhost:54323
    Inbucket URL: http://localhost:54324
        anon key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
service_role key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Check Status
```bash
supabase status
```

### Stop Services
```bash
supabase stop
```

## 7. Common Issues and Solutions

### Issue: "Docker daemon not running"
**Solution:**
```bash
# Start Docker
sudo systemctl start docker  # Linux
# Or start Docker Desktop on Windows/macOS
```

### Issue: "Port already in use"
**Solution:**
```bash
# Check what's using the port
netstat -tulpn | grep :54321

# Kill the process or change ports in config.toml
```

### Issue: "Permission denied"
**Solution:**
```bash
# Add user to docker group (Linux)
sudo usermod -aG docker $USER
newgrp docker
```

### Issue: "Cannot connect to Docker daemon"
**Solution:**
```bash
# Check Docker service
sudo systemctl status docker

# Restart Docker
sudo systemctl restart docker
```

## 8. Alternative Installation Methods

### Using npm (Cross-platform)
```bash
npm install -g supabase
```

### Using yarn
```bash
yarn global add supabase
```

### Using pnpm
```bash
pnpm add -g supabase
```

### Manual Installation
1. Download binary from GitHub releases
2. Extract to desired location
3. Add to PATH
4. Make executable (Linux/macOS)

## 9. Verification Script

Create a verification script to test your installation:

```bash
#!/bin/bash
echo "🔍 Checking Supabase CLI installation..."

# Check if supabase command exists
if command -v supabase &> /dev/null; then
    echo "✅ Supabase CLI is installed"
    supabase --version
else
    echo "❌ Supabase CLI not found"
    exit 1
fi

# Check Docker
if command -v docker &> /dev/null; then
    echo "✅ Docker is installed"
    docker --version
else
    echo "❌ Docker not found"
    exit 1
fi

# Check if Docker is running
if docker info &> /dev/null; then
    echo "✅ Docker is running"
else
    echo "❌ Docker is not running"
    exit 1
fi

echo "🎉 All prerequisites are met!"
```

Save as `check-supabase.sh` and run:
```bash
chmod +x check-supabase.sh
./check-supabase.sh
```

## 10. SecQuiz Project Integration

### Current Project Structure
Your SecQuiz project likely has this structure:
```
secquiz/
├── src/
├── server/
├── supabase/
├── .env.example
└── package.json
```

### Step 1: Initialize Supabase in SecQuiz
```bash
cd /path/to/secquiz
supabase init
```

### Step 2: Configure Local Environment
Update your `.env` file for local development:

```env
# Local Supabase Configuration
VITE_SUPABASE_URL=http://localhost:54321
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Keep your production values for deployment
# VITE_SUPABASE_URL_PROD=https://your-project.supabase.co
# VITE_SUPABASE_ANON_KEY_PROD=your-production-key
```

### Step 3: Start Local Development
```bash
# Start Supabase
supabase start

# Start your development server
npm run dev

# Start your backend server (in another terminal)
cd server && npm run dev
```

### Step 4: Access Local Services
- **Supabase Studio**: http://localhost:54323
- **Your App**: http://localhost:5173
- **API**: http://localhost:54321
- **Database**: postgresql://postgres:postgres@localhost:54322/postgres

### Step 5: Import Existing Schema (if needed)
If you have existing migrations or schema:

```bash
# Link to your remote project (optional)
supabase link --project-ref your-project-id

# Pull existing schema
supabase db pull

# Or create new migrations
supabase migration new initial_schema
```

## 11. Development Workflow

### Daily Development
```bash
# Start local Supabase
supabase start

# Check status
supabase status

# Work on your project...

# Stop when done
supabase stop
```

### Database Changes
```bash
# Create migration
supabase migration new add_new_table

# Apply migrations
supabase migration up

# Reset database (careful!)
supabase db reset
```

### Testing
```bash
# Run with local database
npm run test

# Seed test data
supabase seed
```

## 12. Troubleshooting SecQuiz Integration

### Issue: "Invalid JWT" or Auth Errors
**Solution:** Ensure you're using the local anon key from `supabase start` output.

### Issue: "Connection refused"
**Solution:**
```bash
# Check if Supabase is running
supabase status

# Restart if needed
supabase stop && supabase start
```

### Issue: "Schema not found"
**Solution:**
```bash
# Apply migrations
supabase migration up

# Or reset and reseed
supabase db reset
```

### Issue: "Port conflicts"
**Solution:** Edit `supabase/config.toml`:
```toml
[api]
port = 54321

[db]
port = 54322

[studio]
port = 54323
```

## 13. Production vs Development

### Environment Switching
Create separate environment files:

**`.env.local`** (for local development):
```env
VITE_SUPABASE_URL=http://localhost:54321
VITE_SUPABASE_ANON_KEY=local-anon-key
```

**`.env.production`** (for production):
```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=production-anon-key
```

### Package.json Scripts
Add these scripts to your `package.json`:
```json
{
  "scripts": {
    "dev:local": "supabase start && npm run dev",
    "dev:remote": "npm run dev",
    "supabase:start": "supabase start",
    "supabase:stop": "supabase stop",
    "supabase:status": "supabase status",
    "supabase:reset": "supabase db reset"
  }
}
```

## 14. Quick Start Commands

### First Time Setup
```bash
# 1. Install CLI (choose your method from above)
# 2. Install Docker
# 3. Navigate to project
cd secquiz

# 4. Initialize Supabase
supabase init

# 5. Start local instance
supabase start

# 6. Update .env with local credentials
# 7. Start your app
npm run dev
```

### Daily Development
```bash
supabase start    # Start local Supabase
npm run dev       # Start your app
# Work on your project
supabase stop     # Stop when done
```

This comprehensive guide should help you get the Supabase CLI installed and integrated with your SecQuiz project. The key is ensuring both the CLI and Docker are properly installed and accessible in your PATH.
