/**
 * Quiz Session Management Usage Examples
 * Demonstrates how to use the QuizSession model and QuizRandomizationService
 * for session lifecycle management
 */

import { QuizRandomizationService } from '@/services/quiz-randomization-service';
import { QuizSession } from '@/models/QuizSession';

/**
 * Example 1: Creating a new quiz session
 */
export async function createQuizSessionExample() {
  try {
    const topicId = 'cybersecurity-basics';
    const userId = 'user-123';
    const quizLength = 15;

    // Generate a new quiz session with randomized questions
    const sessionResult = await QuizRandomizationService.generateQuizSession(
      topicId,
      userId,
      quizLength
    );

    console.log('Quiz session created:', {
      sessionId: sessionResult.sessionId,
      questionCount: sessionResult.questions.length,
      topicId: sessionResult.topicId,
      expiresAt: sessionResult.session.expiresAt
    });

    // Access individual randomized questions
    sessionResult.questions.forEach((question, index) => {
      console.log(`Question ${index + 1}:`, {
        id: question.id,
        text: question.question_text,
        shuffledOptions: question.shuffledOptions,
        correctAnswerIndex: question.shuffledCorrectIndex
      });
    });

    return sessionResult;

  } catch (error) {
    console.error('Error creating quiz session:', error);
    throw error;
  }
}

/**
 * Example 2: Retrieving an existing quiz session
 */
export async function retrieveQuizSessionExample(sessionId: string, userId: string) {
  try {
    // Retrieve the session with reconstructed questions
    const sessionResult = await QuizRandomizationService.getQuizSession(sessionId, userId);

    if (!sessionResult) {
      console.log('Session not found or expired');
      return null;
    }

    // Validate session state
    const validation = sessionResult.session.validate();
    console.log('Session validation:', validation);

    if (!validation.isValid) {
      console.log('Session is not valid:', validation.message);
      return null;
    }

    console.log('Retrieved quiz session:', {
      sessionId: sessionResult.sessionId,
      questionCount: sessionResult.questions.length,
      isCompleted: sessionResult.session.isCompleted,
      score: sessionResult.session.score
    });

    return sessionResult;

  } catch (error) {
    console.error('Error retrieving quiz session:', error);
    throw error;
  }
}

/**
 * Example 3: Recording question answers and analytics
 */
export async function recordQuestionAnswerExample(
  sessionId: string,
  userId: string,
  questionId: string,
  selectedOptionIndex: number,
  correctAnswerIndex: number,
  timeToAnswer: number
) {
  try {
    const answeredCorrectly = selectedOptionIndex === correctAnswerIndex;

    // Record the analytics for this question
    const success = await QuizRandomizationService.recordQuestionAnalytics(
      questionId,
      userId,
      sessionId,
      answeredCorrectly,
      selectedOptionIndex,
      timeToAnswer
    );

    console.log('Question analytics recorded:', {
      questionId,
      answeredCorrectly,
      selectedOption: selectedOptionIndex,
      timeToAnswer,
      success
    });

    return success;

  } catch (error) {
    console.error('Error recording question analytics:', error);
    return false;
  }
}

/**
 * Example 4: Completing a quiz session
 */
export async function completeQuizSessionExample(
  sessionId: string,
  userId: string,
  finalScore: number,
  totalTimeTaken: number
) {
  try {
    // Complete the session with final results
    const success = await QuizRandomizationService.completeQuizSession(
      sessionId,
      userId,
      finalScore,
      totalTimeTaken
    );

    if (success) {
      console.log('Quiz session completed successfully:', {
        sessionId,
        finalScore,
        totalTimeTaken
      });

      // Get updated session data
      const sessionResult = await QuizRandomizationService.getQuizSession(sessionId, userId);
      if (sessionResult) {
        console.log('Final session state:', {
          isCompleted: sessionResult.session.isCompleted,
          score: sessionResult.session.score,
          timeTaken: sessionResult.session.timeTaken
        });
      }
    } else {
      console.error('Failed to complete quiz session');
    }

    return success;

  } catch (error) {
    console.error('Error completing quiz session:', error);
    return false;
  }
}

/**
 * Example 5: Managing user sessions
 */
export async function manageUserSessionsExample(userId: string) {
  try {
    // Get active sessions for the user
    const activeSessions = await QuizRandomizationService.getActiveSessions(userId);
    console.log(`User has ${activeSessions.length} active sessions`);

    activeSessions.forEach(session => {
      console.log('Active session:', {
        id: session.id,
        topicId: session.topicId,
        createdAt: session.createdAt,
        expiresAt: session.expiresAt,
        quizLength: session.quizLength
      });
    });

    // Get user session statistics
    const stats = await QuizRandomizationService.getUserSessionStats(userId);
    console.log('User session statistics:', stats);

    return { activeSessions, stats };

  } catch (error) {
    console.error('Error managing user sessions:', error);
    throw error;
  }
}

/**
 * Example 6: Extending session expiration
 */
export async function extendSessionExample(sessionId: string, userId: string) {
  try {
    // Extend session by 30 minutes
    const success = await QuizRandomizationService.extendSessionExpiration(
      sessionId,
      userId,
      30
    );

    if (success) {
      console.log('Session expiration extended by 30 minutes');
      
      // Verify the extension
      const session = await QuizSession.findById(sessionId, userId);
      if (session) {
        console.log('New expiration time:', session.expiresAt);
      }
    } else {
      console.error('Failed to extend session expiration');
    }

    return success;

  } catch (error) {
    console.error('Error extending session:', error);
    return false;
  }
}

/**
 * Example 7: Session cleanup and maintenance
 */
export async function sessionMaintenanceExample() {
  try {
    // Clean up expired sessions
    const cleanedCount = await QuizRandomizationService.cleanupExpiredSessions();
    console.log(`Cleaned up ${cleanedCount} expired sessions`);

    return cleanedCount;

  } catch (error) {
    console.error('Error during session maintenance:', error);
    return 0;
  }
}

/**
 * Example 8: Direct QuizSession model usage
 */
export async function directSessionModelExample() {
  try {
    const userId = 'user-456';

    // Find all sessions for a user
    const userSessions = await QuizSession.findByUserId(userId, 10);
    console.log(`Found ${userSessions.length} sessions for user`);

    // Find only active sessions
    const activeSessions = await QuizSession.findActiveSessions(userId);
    console.log(`Found ${activeSessions.length} active sessions`);

    // Work with individual session
    if (userSessions.length > 0) {
      const session = userSessions[0];
      
      // Check session state
      const validation = session.validate();
      console.log('Session validation:', validation);

      // Update session if needed
      if (validation.isValid && !session.isCompleted) {
        await session.update({
          score: 8,
          timeTaken: 300
        });
        console.log('Session updated with partial results');
      }
    }

    return { userSessions, activeSessions };

  } catch (error) {
    console.error('Error with direct session model usage:', error);
    throw error;
  }
}

/**
 * Complete workflow example: Create, take, and complete a quiz
 */
export async function completeQuizWorkflowExample() {
  try {
    const topicId = 'network-security';
    const userId = 'user-789';

    console.log('=== Starting Complete Quiz Workflow ===');

    // Step 1: Create quiz session
    console.log('Step 1: Creating quiz session...');
    const sessionResult = await createQuizSessionExample();
    if (!sessionResult) {
      throw new Error('Failed to create session');
    }

    // Step 2: Simulate taking the quiz
    console.log('Step 2: Simulating quiz taking...');
    let correctAnswers = 0;
    const startTime = Date.now();

    for (let i = 0; i < sessionResult.questions.length; i++) {
      const question = sessionResult.questions[i];
      
      // Simulate user selecting an answer (randomly for demo)
      const selectedOption = Math.floor(Math.random() * 4);
      const isCorrect = selectedOption === question.shuffledCorrectIndex;
      
      if (isCorrect) correctAnswers++;

      // Record the answer
      await recordQuestionAnswerExample(
        sessionResult.sessionId,
        userId,
        question.id,
        selectedOption,
        question.shuffledCorrectIndex,
        Math.floor(Math.random() * 30) + 10 // 10-40 seconds per question
      );

      console.log(`Question ${i + 1}: ${isCorrect ? 'Correct' : 'Incorrect'}`);
    }

    // Step 3: Complete the session
    console.log('Step 3: Completing quiz session...');
    const totalTime = Math.floor((Date.now() - startTime) / 1000);
    const finalScore = correctAnswers;

    const completed = await completeQuizSessionExample(
      sessionResult.sessionId,
      userId,
      finalScore,
      totalTime
    );

    if (completed) {
      console.log('=== Quiz Workflow Completed Successfully ===');
      console.log(`Final Score: ${finalScore}/${sessionResult.questions.length}`);
      console.log(`Total Time: ${totalTime} seconds`);
    }

    return {
      sessionId: sessionResult.sessionId,
      finalScore,
      totalQuestions: sessionResult.questions.length,
      totalTime,
      completed
    };

  } catch (error) {
    console.error('Error in complete quiz workflow:', error);
    throw error;
  }
}

// Export all examples for easy usage
export const QuizSessionExamples = {
  createSession: createQuizSessionExample,
  retrieveSession: retrieveQuizSessionExample,
  recordAnswer: recordQuestionAnswerExample,
  completeSession: completeQuizSessionExample,
  manageUserSessions: manageUserSessionsExample,
  extendSession: extendSessionExample,
  sessionMaintenance: sessionMaintenanceExample,
  directModelUsage: directSessionModelExample,
  completeWorkflow: completeQuizWorkflowExample
};