# Web Application Security - Intermediate Level Questions

## 50 Multiple Choice Questions

**Question 1:** Which of the following is the most effective way to prevent SQL injection attacks?
A) Using input validation only
B) Using parameterized queries/prepared statements
C) Hiding error messages
D) Using strong passwords
**Correct Answer:** B
**Explanation:** Parameterized queries/prepared statements separate SQL code from data, making it impossible for attackers to inject malicious SQL commands through user input.

**Question 2:** What is the primary difference between stored and reflected XSS attacks?
A) Stored XSS is less dangerous
B) Stored XSS persists on the server, reflected XSS is immediate
C) Reflected XSS affects more users
D) There is no difference
**Correct Answer:** B
**Explanation:** Stored XSS attacks save malicious scripts on the server (like in a database), affecting all users who view the content, while reflected XSS attacks are immediate and typically affect only the targeted user.

**Question 3:** Which HTTP header helps prevent clickjacking attacks?
A) Content-Security-Policy
B) X-Frame-Options
C) X-XSS-Protection
D) Strict-Transport-Security
**Correct Answer:** B
**Explanation:** X-Frame-Options header prevents a web page from being embedded in frames or iframes, which is the primary technique used in clickjacking attacks.

**Question 4:** What is CSRF (Cross-Site Request Forgery)?
A) A type of XSS attack
B) An attack that tricks users into performing unwanted actions on authenticated sites
C) A SQL injection variant
D) A password cracking method
**Correct Answer:** B
**Explanation:** CSRF attacks trick authenticated users into unknowingly performing actions on web applications where they're logged in, by exploiting the trust that a site has in the user's browser.

**Question 5:** Which of these is NOT part of the OWASP Top 10?
A) Injection
B) Broken Authentication
C) Buffer Overflow
D) Security Misconfiguration
**Correct Answer:** C
**Explanation:** Buffer Overflow is primarily a system-level vulnerability, not a web application vulnerability. The OWASP Top 10 focuses specifically on web application security risks.

**Question 6:** What is the purpose of input sanitization?
A) To make input look prettier
B) To remove or encode potentially dangerous characters from user input
C) To speed up data processing
D) To compress input data
**Correct Answer:** B
**Explanation:** Input sanitization removes or encodes potentially dangerous characters from user input to prevent injection attacks and other security vulnerabilities.

**Question 7:** Which authentication method is most vulnerable to session hijacking?
A) Multi-factor authentication
B) Session tokens transmitted over HTTP
C) Certificate-based authentication
D) Biometric authentication
**Correct Answer:** B
**Explanation:** Session tokens transmitted over unencrypted HTTP connections can be easily intercepted by attackers, allowing them to hijack user sessions.

**Question 8:** What does the Content-Security-Policy (CSP) header primarily protect against?
A) SQL injection
B) Cross-Site Scripting (XSS)
C) CSRF attacks
D) Authentication bypass
**Correct Answer:** B
**Explanation:** CSP header helps prevent XSS attacks by controlling which resources (scripts, stylesheets, images) can be loaded and executed on a web page.

**Question 9:** Which of these represents a secure way to store passwords in a database?
A) Plain text
B) MD5 hash
C) bcrypt with salt
D) Base64 encoding
**Correct Answer:** C
**Explanation:** bcrypt with salt is a secure password hashing method that includes built-in salting and is computationally expensive, making it resistant to brute force attacks.

**Question 10:** What is the main security risk of using GET requests for sensitive operations?
A) GET requests are slower
B) Parameters appear in URL and server logs
C) GET requests don't work with HTTPS
D) GET requests can't carry data
**Correct Answer:** B
**Explanation:** GET request parameters appear in URLs, browser history, and server logs, making sensitive data visible and potentially exposing it to unauthorized parties.

**Question 11:** Which HTTP status code should be returned for both valid and invalid login attempts to prevent user enumeration?
A) 200 OK for valid, 401 Unauthorized for invalid
B) 200 OK for both
C) 302 Redirect for both
D) 404 Not Found for both
**Correct Answer:** C
**Explanation:** Returning the same response (like 302 redirect) for both valid and invalid attempts prevents attackers from determining which usernames exist in the system.

**Question 12:** What is the primary purpose of session timeout?
A) To save server resources
B) To reduce the window of opportunity for session hijacking
C) To force users to log in frequently
D) To improve application performance
**Correct Answer:** B
**Explanation:** Session timeout limits the time window during which a hijacked session can be used, reducing the risk of unauthorized access if a session is compromised.

**Question 13:** Which of these is a characteristic of a secure API design?
A) All endpoints are publicly accessible
B) Authentication is required for sensitive operations
C) Error messages reveal system details
D) All data is returned in every response
**Correct Answer:** B
**Explanation:** Secure APIs require proper authentication and authorization for sensitive operations, ensuring only authorized users can access protected resources.

**Question 14:** What is the main security concern with file upload functionality?
A) Files take up storage space
B) Uploaded files might contain malware or executable code
C) File uploads are always slow
D) Users might upload large files
**Correct Answer:** B
**Explanation:** File uploads can be used to upload malicious files, including malware, web shells, or executable code that could compromise the server or other users.

**Question 15:** Which technique helps prevent automated attacks on login forms?
A) Using longer passwords
B) Implementing CAPTCHA
C) Hiding the login form
D) Using GET instead of POST
**Correct Answer:** B
**Explanation:** CAPTCHA challenges help distinguish between human users and automated bots, making it harder for attackers to perform automated brute force attacks.

**Question 16:** What is the security risk of exposing detailed error messages to users?
A) Users get confused
B) Error messages can reveal system information useful to attackers
C) Error messages slow down the application
D) There is no security risk
**Correct Answer:** B
**Explanation:** Detailed error messages can reveal sensitive information about the system architecture, database structure, or file paths that attackers can use to plan further attacks.

**Question 17:** Which of these is the most secure way to handle user logout?
A) Just redirect to login page
B) Invalidate session on server and clear client-side tokens
C) Only clear browser cookies
D) Set session timeout to 1 second
**Correct Answer:** B
**Explanation:** Proper logout requires invalidating the session on the server side and clearing any client-side authentication tokens to prevent session reuse.

**Question 18:** What is the purpose of rate limiting in web applications?
A) To make applications faster
B) To prevent abuse and automated attacks
C) To reduce server costs
D) To improve user experience
**Correct Answer:** B
**Explanation:** Rate limiting restricts the number of requests a user can make in a given time period, helping prevent brute force attacks, DDoS attacks, and API abuse.

**Question 19:** Which of these is a sign that a web application might be vulnerable to injection attacks?
A) Fast response times
B) User input is directly concatenated into queries
C) Strong password requirements
D) HTTPS is used
**Correct Answer:** B
**Explanation:** When user input is directly concatenated into SQL queries or other commands without proper sanitization, it creates opportunities for injection attacks.

**Question 20:** What is the main security benefit of using HTTPS over HTTP?
A) Faster data transmission
B) Encryption of data in transit
C) Better search engine ranking
D) Reduced server load
**Correct Answer:** B
**Explanation:** HTTPS encrypts data transmitted between the client and server, protecting it from interception and tampering by attackers monitoring network traffic.

**Question 21:** Which vulnerability allows attackers to access files outside the intended directory?
A) SQL injection
B) Directory traversal
C) XSS
D) CSRF
**Correct Answer:** B
**Explanation:** Directory traversal (path traversal) attacks use sequences like "../" to access files and directories outside the intended application directory structure.

**Question 22:** What is the primary security concern with using third-party libraries in web applications?
A) They make applications slower
B) They may contain known vulnerabilities
C) They are always expensive
D) They don't work with modern browsers
**Correct Answer:** B
**Explanation:** Third-party libraries may contain security vulnerabilities that could be exploited by attackers, making it important to keep them updated and monitor for security advisories.

**Question 23:** Which HTTP method should be used for operations that modify data on the server?
A) GET
B) POST, PUT, or DELETE
C) HEAD
D) OPTIONS
**Correct Answer:** B
**Explanation:** GET requests should be idempotent and not modify server state. Data-modifying operations should use POST, PUT, or DELETE methods with proper CSRF protection.

**Question 24:** What is the security risk of using predictable session IDs?
A) Sessions expire too quickly
B) Attackers can guess valid session IDs
C) Sessions use too much memory
D) Users can't remember them
**Correct Answer:** B
**Explanation:** Predictable session IDs allow attackers to guess valid session tokens and potentially hijack other users' sessions, gaining unauthorized access.

**Question 25:** Which of these is a secure practice for handling sensitive data in web applications?
A) Store everything in cookies
B) Encrypt sensitive data at rest and in transit
C) Display all data in error messages
D) Cache sensitive data indefinitely
**Correct Answer:** B
**Explanation:** Sensitive data should be encrypted both when stored (at rest) and when transmitted (in transit) to protect it from unauthorized access.

**Question 26:** What is the main purpose of input validation?
A) To make forms look better
B) To ensure data meets expected format and constraints
C) To speed up data processing
D) To reduce database size
**Correct Answer:** B
**Explanation:** Input validation ensures that user-provided data meets expected format, type, and constraint requirements, helping prevent various attacks and data corruption.

**Question 27:** Which of these is a characteristic of a secure password reset mechanism?
A) Passwords are sent via email
B) Reset tokens expire after a short time
C) The same reset link works forever
D) No verification is required
**Correct Answer:** B
**Explanation:** Secure password reset mechanisms use time-limited tokens that expire quickly to minimize the window of opportunity if reset emails are intercepted.

**Question 28:** What is the security risk of allowing unrestricted file types in uploads?
A) Storage space issues
B) Executable files could be uploaded and run on the server
C) Slow upload speeds
D) User confusion
**Correct Answer:** B
**Explanation:** Allowing unrestricted file types can enable attackers to upload executable files, web shells, or malicious scripts that could compromise the server.

**Question 29:** Which technique helps prevent XSS attacks in user-generated content?
A) Using longer passwords
B) Output encoding/escaping
C) Hiding the content
D) Using GET requests
**Correct Answer:** B
**Explanation:** Output encoding/escaping converts potentially dangerous characters into safe representations when displaying user content, preventing XSS attacks.

**Question 30:** What is the main security concern with using HTTP cookies for authentication?
A) Cookies are too small
B) Cookies can be stolen via XSS or transmitted over insecure connections
C) Cookies expire too quickly
D) Cookies don't work in all browsers
**Correct Answer:** B
**Explanation:** Authentication cookies can be stolen through XSS attacks or intercepted if transmitted over insecure connections, potentially allowing session hijacking.

**Question 31:** Which of these is a secure way to implement user authorization?
A) Check permissions on the client side only
B) Verify permissions on the server for every request
C) Trust all authenticated users
D) Use the same permissions for all users
**Correct Answer:** B
**Explanation:** Authorization checks must be performed on the server side for every request to ensure users can only access resources they're permitted to use.

**Question 32:** What is the purpose of the Strict-Transport-Security header?
A) To prevent XSS attacks
B) To force browsers to use HTTPS
C) To prevent SQL injection
D) To improve page load speed
**Correct Answer:** B
**Explanation:** The Strict-Transport-Security (HSTS) header tells browsers to only communicate with the server over HTTPS, preventing downgrade attacks.

**Question 33:** Which of these represents a secure approach to handling user sessions?
A) Store session data in URLs
B) Use secure, random session tokens with proper expiration
C) Never expire sessions
D) Share sessions between users
**Correct Answer:** B
**Explanation:** Secure session management uses cryptographically random session tokens, proper expiration times, and secure transmission and storage practices.

**Question 34:** What is the main security risk of using client-side validation only?
A) It's slower than server-side validation
B) Attackers can bypass client-side validation
C) It doesn't work in all browsers
D) It uses more bandwidth
**Correct Answer:** B
**Explanation:** Client-side validation can be easily bypassed by attackers who can modify requests or disable JavaScript, so server-side validation is essential for security.

**Question 35:** Which HTTP header helps prevent MIME type sniffing attacks?
A) X-Content-Type-Options
B) X-Frame-Options
C) Content-Security-Policy
D) X-XSS-Protection
**Correct Answer:** A
**Explanation:** The X-Content-Type-Options header with "nosniff" value prevents browsers from MIME type sniffing, which could lead to security vulnerabilities.

**Question 36:** What is the security benefit of implementing proper logging in web applications?
A) Faster application performance
B) Detection and investigation of security incidents
C) Reduced storage requirements
D) Better user experience
**Correct Answer:** B
**Explanation:** Proper logging helps detect security incidents, investigate breaches, and understand attack patterns, making it crucial for security monitoring.

**Question 37:** Which of these is a secure practice for API endpoints?
A) Return all available data in every response
B) Implement proper input validation and output filtering
C) Allow unlimited requests from any source
D) Include sensitive data in error messages
**Correct Answer:** B
**Explanation:** Secure APIs implement thorough input validation to prevent attacks and output filtering to ensure only necessary data is returned to clients.

**Question 38:** What is the main security concern with using default configurations in web applications?
A) They are too complex
B) Default settings often have known vulnerabilities or weak security
C) They don't work properly
D) They are too secure
**Correct Answer:** B
**Explanation:** Default configurations often prioritize functionality over security and may include known vulnerabilities, weak passwords, or unnecessary features that increase attack surface.

**Question 39:** Which technique helps prevent timing attacks on authentication systems?
A) Using faster servers
B) Implementing constant-time comparison functions
C) Reducing password complexity
D) Disabling authentication
**Correct Answer:** B
**Explanation:** Constant-time comparison functions take the same amount of time regardless of input, preventing attackers from using timing differences to guess passwords or tokens.

**Question 40:** What is the security risk of exposing internal system information in HTTP headers?
A) Headers take up bandwidth
B) Attackers can learn about system architecture and plan targeted attacks
C) Headers slow down requests
D) There is no security risk
**Correct Answer:** B
**Explanation:** Exposing system information like server versions, frameworks, or internal paths in headers gives attackers valuable intelligence for planning targeted attacks.

**Question 41:** Which of these is a characteristic of secure error handling?
A) Show detailed stack traces to users
B) Log detailed errors but show generic messages to users
C) Hide all errors from logs
D) Show database connection strings in errors
**Correct Answer:** B
**Explanation:** Secure error handling logs detailed information for developers while showing only generic, non-revealing error messages to users to prevent information disclosure.

**Question 42:** What is the main purpose of implementing CORS (Cross-Origin Resource Sharing) policies?
A) To speed up web requests
B) To control which domains can access resources from a web application
C) To compress data
D) To improve SEO rankings
**Correct Answer:** B
**Explanation:** CORS policies control which external domains are allowed to make requests to a web application, helping prevent unauthorized cross-origin requests.

**Question 43:** Which of these is a secure approach to handling user passwords during registration?
A) Store passwords in plain text
B) Hash passwords with a strong algorithm and unique salt
C) Encrypt passwords with a shared key
D) Store passwords in cookies
**Correct Answer:** B
**Explanation:** Passwords should be hashed using strong algorithms like bcrypt with unique salts for each password, making them extremely difficult to reverse.

**Question 44:** What is the security benefit of implementing proper access controls in web applications?
A) Faster page loading
B) Users can only access resources they're authorized to use
C) Reduced server costs
D) Better search engine optimization
**Correct Answer:** B
**Explanation:** Proper access controls ensure that users can only access resources and perform actions they're specifically authorized for, preventing unauthorized access.

**Question 45:** Which of these is a sign that a web application has poor session management?
A) Fast login process
B) Sessions never expire or use predictable tokens
C) Strong password requirements
D) Multi-factor authentication
**Correct Answer:** B
**Explanation:** Poor session management includes sessions that never expire, use predictable tokens, or aren't properly invalidated, creating security vulnerabilities.

**Question 46:** What is the main security concern with using JavaScript for sensitive operations?
A) JavaScript is too slow
B) Client-side code can be viewed and modified by users
C) JavaScript doesn't work in all browsers
D) JavaScript uses too much memory
**Correct Answer:** B
**Explanation:** JavaScript code runs on the client side where users can view, modify, or bypass it, so sensitive security operations must be performed on the server.

**Question 47:** Which HTTP method is most appropriate for retrieving data without side effects?
A) POST
B) GET
C) PUT
D) DELETE
**Correct Answer:** B
**Explanation:** GET requests should be used for retrieving data without causing side effects or modifying server state, making them safe and idempotent.

**Question 48:** What is the security risk of not implementing proper data validation on file uploads?
A) Files might be too large
B) Malicious files could be uploaded and executed
C) Upload process might be slow
D) Users might get confused
**Correct Answer:** B
**Explanation:** Without proper validation, attackers could upload malicious files including web shells, malware, or executable code that could compromise the system.

**Question 49:** Which of these is a secure practice for handling sensitive URLs?
A) Include sensitive data in URL parameters
B) Use POST requests and session-based authorization
C) Make URLs as long as possible
D) Share URLs publicly
**Correct Answer:** B
**Explanation:** Sensitive operations should use POST requests to avoid exposing data in URLs, and rely on session-based authorization rather than URL parameters.

**Question 50:** What is the main benefit of implementing security headers in web applications?
A) Faster page loading
B) Additional layers of protection against various attacks
C) Better search engine rankings
D) Reduced bandwidth usage
**Correct Answer:** B
**Explanation:** Security headers like CSP, HSTS, and X-Frame-Options provide additional layers of protection against various attacks including XSS, clickjacking, and protocol downgrade attacks.