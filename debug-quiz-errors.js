/**
 * Quiz Error Debugging Script
 * Comprehensive error detection and fixing for the quiz randomization system
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function debugQuizErrors() {
  console.log('🔍 Starting comprehensive quiz error debugging...\n');

  const errors = [];
  const warnings = [];
  const fixes = [];

  try {
    // 1. Check database schema
    console.log('1. Checking database schema...');

    const schemaChecks = [
      { table: 'topics', required: true },
      { table: 'questions', required: true },
      { table: 'quiz_sessions', required: true },
      { table: 'question_analytics', required: true },
      { table: 'quiz_attempts', required: false }
    ];

    for (const check of schemaChecks) {
      try {
        const { data, error } = await supabase
          .from(check.table)
          .select('*')
          .limit(1);

        if (error) {
          if (check.required) {
            errors.push(`❌ Required table '${check.table}' is missing or inaccessible: ${error.message}`);
            fixes.push(`🔧 Create table '${check.table}' using the provided SQL script`);
          } else {
            warnings.push(`⚠️  Optional table '${check.table}' is missing: ${error.message}`);
          }
        } else {
          console.log(`✅ Table '${check.table}' is accessible`);
        }
      } catch (tableError) {
        errors.push(`❌ Error checking table '${check.table}': ${tableError.message}`);
      }
    }

    // 2. Check question data integrity
    console.log('\n2. Checking question data integrity...');

    try {
      const { data: questions, error: questionsError } = await supabase
        .from('questions')
        .select('id, topic_id, question_text, options, correct_answer, explanation')
        .limit(10);

      if (questionsError) {
        errors.push(`❌ Cannot fetch questions: ${questionsError.message}`);
      } else if (!questions || questions.length === 0) {
        warnings.push('⚠️  No questions found in database');
        fixes.push('🔧 Import questions using the admin interface');
      } else {
        console.log(`✅ Found ${questions.length} sample questions`);

        // Check question data quality
        let invalidQuestions = 0;
        for (const question of questions) {
          const issues = [];

          if (!question.question_text || question.question_text.trim() === '') {
            issues.push('missing question_text');
          }

          if (!question.options) {
            issues.push('missing options');
          } else {
            try {
              const options = typeof question.options === 'string'
                ? JSON.parse(question.options)
                : question.options;

              const optionValues = Object.values(options);
              if (optionValues.length < 2) {
                issues.push('insufficient options');
              }
            } catch (parseError) {
              issues.push('invalid options format');
            }
          }

          if (question.correct_answer === null || question.correct_answer === undefined) {
            issues.push('missing correct_answer');
          }

          if (issues.length > 0) {
            invalidQuestions++;
            warnings.push(`⚠️  Question ${question.id}: ${issues.join(', ')}`);
          }
        }

        if (invalidQuestions > 0) {
          warnings.push(`⚠️  Found ${invalidQuestions} questions with data issues`);
          fixes.push('🔧 Review and fix question data in the admin interface');
        } else {
          console.log('✅ Question data integrity looks good');
        }
      }
    } catch (questionCheckError) {
      errors.push(`❌ Error checking questions: ${questionCheckError.message}`);
    }

    // 3. Check topics and question distribution
    console.log('\n3. Checking topics and question distribution...');

    try {
      const { data: topics, error: topicsError } = await supabase
        .from('topics')
        .select('id, title');

      if (topicsError) {
        errors.push(`❌ Cannot fetch topics: ${topicsError.message}`);
      } else if (!topics || topics.length === 0) {
        warnings.push('⚠️  No topics found in database');
        fixes.push('🔧 Create topics using the admin interface');
      } else {
        console.log(`✅ Found ${topics.length} topics`);

        // Check question distribution per topic
        for (const topic of topics.slice(0, 5)) { // Check first 5 topics
          const { data: topicQuestions, error: topicQuestionsError } = await supabase
            .from('questions')
            .select('id', { count: 'exact' })
            .eq('topic_id', topic.id);

          if (topicQuestionsError) {
            warnings.push(`⚠️  Cannot count questions for topic '${topic.title}': ${topicQuestionsError.message}`);
          } else {
            const questionCount = topicQuestions?.length || 0;
            console.log(`  - ${topic.title}: ${questionCount} questions`);

            if (questionCount === 0) {
              warnings.push(`⚠️  Topic '${topic.title}' has no questions`);
            } else if (questionCount < 10) {
              warnings.push(`⚠️  Topic '${topic.title}' has only ${questionCount} questions (recommended: 20+)`);
            }
          }
        }
      }
    } catch (topicCheckError) {
      errors.push(`❌ Error checking topics: ${topicCheckError.message}`);
    }

    // 4. Test quiz session creation
    console.log('\n4. Testing quiz session creation...');

    try {
      // Find a topic with questions for testing
      const { data: testTopics, error: testTopicsError } = await supabase
        .from('topics')
        .select('id, title')
        .limit(3);

      if (testTopicsError || !testTopics || testTopics.length === 0) {
        warnings.push('⚠️  Cannot test quiz session creation - no topics available');
      } else {
        let testTopic = null;

        for (const topic of testTopics) {
          const { data: topicQuestions, error: topicQuestionsError } = await supabase
            .from('questions')
            .select('id')
            .eq('topic_id', topic.id)
            .limit(1);

          if (!topicQuestionsError && topicQuestions && topicQuestions.length > 0) {
            testTopic = topic;
            break;
          }
        }

        if (!testTopic) {
          warnings.push('⚠️  Cannot test quiz session creation - no topics with questions');
        } else {
          // Test session creation
          const testSessionData = {
            user_id: crypto.randomUUID(),
            topic_id: testTopic.id,
            questions_data: {
              questions: [{
                id: 'test_question',
                originalCorrectIndex: 0,
                shuffledCorrectIndex: 0,
                optionMapping: [0, 1, 2, 3]
              }],
              metadata: {
                totalQuestions: 1,
                topicId: testTopic.id,
                createdAt: new Date().toISOString()
              }
            },
            quiz_length: 1,
            total_questions: 1
          };

          const { data: sessionResult, error: sessionError } = await supabase
            .from('quiz_sessions')
            .insert(testSessionData)
            .select()
            .single();

          if (sessionError) {
            errors.push(`❌ Quiz session creation failed: ${sessionError.message}`);
            fixes.push('🔧 Check quiz_sessions table structure and permissions');
          } else {
            console.log('✅ Quiz session creation test passed');

            // Clean up test data
            await supabase
              .from('quiz_sessions')
              .delete()
              .eq('id', sessionResult.id);
          }
        }
      }
    } catch (sessionTestError) {
      errors.push(`❌ Error testing quiz session creation: ${sessionTestError.message}`);
    }

    // 5. Test question analytics
    console.log('\n5. Testing question analytics...');

    try {
      const testAnalyticsData = {
        question_id: crypto.randomUUID(),
        user_id: crypto.randomUUID(),
        quiz_session_id: crypto.randomUUID(),
        answered_correctly: true,
        selected_option: 1,
        time_to_answer: 30
      };

      const { data: analyticsResult, error: analyticsError } = await supabase
        .from('question_analytics')
        .insert(testAnalyticsData)
        .select()
        .single();

      if (analyticsError) {
        errors.push(`❌ Question analytics creation failed: ${analyticsError.message}`);
        fixes.push('🔧 Check question_analytics table structure and permissions');
      } else {
        console.log('✅ Question analytics test passed');

        // Clean up test data
        await supabase
          .from('question_analytics')
          .delete()
          .eq('id', analyticsResult.id);
      }
    } catch (analyticsTestError) {
      errors.push(`❌ Error testing question analytics: ${analyticsTestError.message}`);
    }

    // 6. Check environment configuration
    console.log('\n6. Checking environment configuration...');

    const envChecks = [
      { key: 'VITE_SUPABASE_URL', value: process.env.VITE_SUPABASE_URL, required: true },
      { key: 'VITE_SUPABASE_ANON_KEY', value: process.env.VITE_SUPABASE_ANON_KEY, required: true },
      { key: 'SUPABASE_SERVICE_ROLE_KEY', value: process.env.SUPABASE_SERVICE_ROLE_KEY, required: false },
      { key: 'VITE_ENABLE_DEBUG_MODE', value: process.env.VITE_ENABLE_DEBUG_MODE, required: false }
    ];

    for (const check of envChecks) {
      if (!check.value) {
        if (check.required) {
          errors.push(`❌ Missing required environment variable: ${check.key}`);
          fixes.push(`🔧 Add ${check.key} to your .env file`);
        } else {
          warnings.push(`⚠️  Optional environment variable missing: ${check.key}`);
        }
      } else {
        console.log(`✅ ${check.key} is configured`);
      }
    }

    // Generate report
    console.log('\n' + '='.repeat(60));
    console.log('📊 DEBUGGING REPORT');
    console.log('='.repeat(60));

    if (errors.length === 0 && warnings.length === 0) {
      console.log('🎉 NO ISSUES FOUND!');
      console.log('✅ Your quiz randomization system appears to be working correctly.');
    } else {
      if (errors.length > 0) {
        console.log('\n❌ CRITICAL ERRORS:');
        errors.forEach(error => console.log(error));
      }

      if (warnings.length > 0) {
        console.log('\n⚠️  WARNINGS:');
        warnings.forEach(warning => console.log(warning));
      }

      if (fixes.length > 0) {
        console.log('\n🔧 RECOMMENDED FIXES:');
        fixes.forEach(fix => console.log(fix));
      }
    }

    console.log('\n📋 NEXT STEPS:');
    if (errors.length > 0) {
      console.log('1. Fix the critical errors listed above');
      console.log('2. Run this script again to verify fixes');
      console.log('3. Test the quiz system in your application');
    } else if (warnings.length > 0) {
      console.log('1. Address the warnings for optimal performance');
      console.log('2. Test the quiz system in your application');
      console.log('3. Monitor for any runtime errors');
    } else {
      console.log('1. Your system is ready to use!');
      console.log('2. Test creating and taking quizzes');
      console.log('3. Monitor performance and user feedback');
    }

  } catch (error) {
    console.error('❌ Debugging script failed:', error);
    console.log('\n🔧 Basic troubleshooting:');
    console.log('1. Check your internet connection');
    console.log('2. Verify your Supabase project is active');
    console.log('3. Check your environment variables');
    console.log('4. Try running: node setup-database-access.js');
  }
}

// Run debugging
debugQuizErrors();