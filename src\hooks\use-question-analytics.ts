/**
 * Hook for managing question pool analytics data
 * Provides state management and data fetching for analytics components
 */

import { useState, useEffect, useCallback } from 'react';
import { QuestionPoolAnalyticsService } from '@/services/question-pool-analytics-service';
import type { 
  TopicInsights, 
  ContentRecommendation, 
  QuestionDifficultyAnalysis,
  TopicQuestionStats,
  QuestionWithStats
} from '@/services/question-pool-analytics-service';

interface AnalyticsSummary {
  totalQuestions: number;
  questionsUsed: number;
  questionsNeverUsed: number;
  questionsNeedingReview: number;
  topicsWithInsufficientQuestions: number;
  recentActivity: {
    weeklyAttempts: number;
    weeklyCorrectRate: number;
  };
  averageUsageCount: number;
  averageCorrectRate: number;
}

interface UseQuestionAnalyticsReturn {
  // Data
  summary: AnalyticsSummary | null;
  topicInsights: TopicInsights | null;
  recommendations: ContentRecommendation[];
  questionsNeedingReview: QuestionDifficultyAnalysis[];
  topicQuestionUsage: QuestionWithStats[];
  
  // State
  loading: boolean;
  error: string | null;
  
  // Actions
  loadSummary: () => Promise<void>;
  loadTopicInsights: (topicId: string) => Promise<void>;
  loadRecommendations: () => Promise<void>;
  loadQuestionsNeedingReview: (topicId?: string, limit?: number) => Promise<void>;
  loadTopicQuestionUsage: (topicId: string) => Promise<void>;
  recordQuestionAnalytics: (
    questionId: string,
    userId: string,
    sessionId: string,
    answeredCorrectly: boolean,
    selectedOption: number,
    timeToAnswer?: number
  ) => Promise<boolean>;
  refresh: () => Promise<void>;
}

export const useQuestionAnalytics = (): UseQuestionAnalyticsReturn => {
  const [summary, setSummary] = useState<AnalyticsSummary | null>(null);
  const [topicInsights, setTopicInsights] = useState<TopicInsights | null>(null);
  const [recommendations, setRecommendations] = useState<ContentRecommendation[]>([]);
  const [questionsNeedingReview, setQuestionsNeedingReview] = useState<QuestionDifficultyAnalysis[]>([]);
  const [topicQuestionUsage, setTopicQuestionUsage] = useState<QuestionWithStats[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleError = useCallback((err: unknown, context: string) => {
    const message = err instanceof Error ? err.message : `Failed to ${context}`;
    console.error(`Analytics error (${context}):`, err);
    setError(message);
  }, []);

  const loadSummary = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await QuestionPoolAnalyticsService.getAnalyticsSummary();
      setSummary(data);
    } catch (err) {
      handleError(err, 'load analytics summary');
    } finally {
      setLoading(false);
    }
  }, [handleError]);

  const loadTopicInsights = useCallback(async (topicId: string) => {
    try {
      setLoading(true);
      setError(null);
      const data = await QuestionPoolAnalyticsService.getTopicInsights(topicId);
      setTopicInsights(data);
    } catch (err) {
      handleError(err, 'load topic insights');
    } finally {
      setLoading(false);
    }
  }, [handleError]);

  const loadRecommendations = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await QuestionPoolAnalyticsService.getContentRecommendations();
      setRecommendations(data);
    } catch (err) {
      handleError(err, 'load recommendations');
    } finally {
      setLoading(false);
    }
  }, [handleError]);

  const loadQuestionsNeedingReview = useCallback(async (topicId?: string, limit: number = 20) => {
    try {
      setLoading(true);
      setError(null);
      const data = await QuestionPoolAnalyticsService.getQuestionsNeedingReview(topicId, limit);
      setQuestionsNeedingReview(data);
    } catch (err) {
      handleError(err, 'load questions needing review');
    } finally {
      setLoading(false);
    }
  }, [handleError]);

  const loadTopicQuestionUsage = useCallback(async (topicId: string) => {
    try {
      setLoading(true);
      setError(null);
      const data = await QuestionPoolAnalyticsService.getTopicQuestionUsage(topicId);
      setTopicQuestionUsage(data);
    } catch (err) {
      handleError(err, 'load topic question usage');
    } finally {
      setLoading(false);
    }
  }, [handleError]);

  const recordQuestionAnalytics = useCallback(async (
    questionId: string,
    userId: string,
    sessionId: string,
    answeredCorrectly: boolean,
    selectedOption: number,
    timeToAnswer?: number
  ): Promise<boolean> => {
    try {
      return await QuestionPoolAnalyticsService.recordQuestionAnalytics(
        questionId,
        userId,
        sessionId,
        answeredCorrectly,
        selectedOption,
        timeToAnswer
      );
    } catch (err) {
      handleError(err, 'record question analytics');
      return false;
    }
  }, [handleError]);

  const refresh = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Reload all current data
      const promises: Promise<void>[] = [];
      
      if (summary) {
        promises.push(loadSummary());
      }
      
      if (recommendations.length > 0) {
        promises.push(loadRecommendations());
      }
      
      if (questionsNeedingReview.length > 0) {
        promises.push(loadQuestionsNeedingReview());
      }
      
      await Promise.all(promises);
    } catch (err) {
      handleError(err, 'refresh analytics data');
    } finally {
      setLoading(false);
    }
  }, [summary, recommendations.length, questionsNeedingReview.length, loadSummary, loadRecommendations, loadQuestionsNeedingReview, handleError]);

  return {
    // Data
    summary,
    topicInsights,
    recommendations,
    questionsNeedingReview,
    topicQuestionUsage,
    
    // State
    loading,
    error,
    
    // Actions
    loadSummary,
    loadTopicInsights,
    loadRecommendations,
    loadQuestionsNeedingReview,
    loadTopicQuestionUsage,
    recordQuestionAnalytics,
    refresh
  };
};

export default useQuestionAnalytics;