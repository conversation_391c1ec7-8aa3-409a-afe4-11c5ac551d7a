import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ImportErrorReporting, type ImportErrorReport } from '../ImportErrorReporting';
import type { ImportError, ImportTopicErrors, ImportStatistics, ImportSuggestion, RecoveryOption } from '../ImportErrorReporting';

// Mock the UI components
vi.mock('@/components/ui/alert', () => ({
  Alert: ({ children, variant, className }: any) => (
    <div data-testid="alert" data-variant={variant} className={className}>
      {children}
    </div>
  ),
  AlertDescription: ({ children }: any) => <div data-testid="alert-description">{children}</div>,
  AlertTitle: ({ children }: any) => <div data-testid="alert-title">{children}</div>,
}));

vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children, variant }: any) => (
    <span data-testid="badge" data-variant={variant}>
      {children}
    </span>
  ),
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, variant, disabled }: any) => (
    <button
      data-testid="button"
      onClick={onClick}
      data-variant={variant}
      disabled={disabled}
    >
      {children}
    </button>
  ),
}));

vi.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: any) => (
    <div data-testid="card" className={className}>
      {children}
    </div>
  ),
  CardContent: ({ children }: any) => <div data-testid="card-content">{children}</div>,
  CardDescription: ({ children }: any) => <div data-testid="card-description">{children}</div>,
  CardHeader: ({ children }: any) => <div data-testid="card-header">{children}</div>,
  CardTitle: ({ children }: any) => <div data-testid="card-title">{children}</div>,
}));

vi.mock('@/components/ui/collapsible', () => ({
  Collapsible: ({ children }: any) => <div data-testid="collapsible">{children}</div>,
  CollapsibleContent: ({ children }: any) => <div data-testid="collapsible-content">{children}</div>,
  CollapsibleTrigger: ({ children, asChild }: any) => (
    <div data-testid="collapsible-trigger">{children}</div>
  ),
}));

vi.mock('@/components/ui/separator', () => ({
  Separator: () => <hr data-testid="separator" />,
}));

vi.mock('@/components/ui/tooltip', () => ({
  Tooltip: ({ children }: any) => <div data-testid="tooltip">{children}</div>,
  TooltipContent: ({ children }: any) => <div data-testid="tooltip-content">{children}</div>,
  TooltipTrigger: ({ children, asChild }: any) => (
    <div data-testid="tooltip-trigger">{children}</div>
  ),
}));

describe('ImportErrorReporting', () => {
  const mockOnRetry = vi.fn();
  const mockOnDownloadErrorReport = vi.fn();
  const mockOnFixAndRetry = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  const createMockError = (overrides: Partial<ImportError> = {}): ImportError => ({
    id: 'error-1',
    row: 1,
    field: 'question_text',
    message: 'Test error message',
    severity: 'error',
    category: 'question',
    suggestion: 'Test suggestion',
    actionable: true,
    canRetry: true,
    ...overrides,
  });

  const createMockTopicErrors = (overrides: Partial<ImportTopicErrors> = {}): ImportTopicErrors => ({
    topicId: 'topic-1',
    topicName: 'Test Topic',
    isNewTopic: false,
    errors: [createMockError()],
    warnings: [],
    questionsProcessed: 10,
    questionsSuccessful: 8,
    questionsFailed: 2,
    ...overrides,
  });

  const createMockStatistics = (overrides: Partial<ImportStatistics> = {}): ImportStatistics => ({
    totalRows: 10,
    totalTopics: 2,
    newTopicsCreated: 1,
    questionsImported: 8,
    questionsFailed: 2,
    errorsCount: 2,
    warningsCount: 1,
    duration: 5000,
    successRate: 0.8,
    ...overrides,
  });

  const createMockSuggestion = (overrides: Partial<ImportSuggestion> = {}): ImportSuggestion => ({
    id: 'suggestion-1',
    title: 'Test Suggestion',
    description: 'Test suggestion description',
    priority: 'high',
    category: 'fix-required',
    actionText: 'Fix Now',
    onAction: vi.fn(),
    ...overrides,
  });

  const createMockRecoveryOption = (overrides: Partial<RecoveryOption> = {}): RecoveryOption => ({
    id: 'recovery-1',
    title: 'Test Recovery',
    description: 'Test recovery description',
    actionText: 'Recover',
    onAction: vi.fn(),
    ...overrides,
  });

  const createMockReport = (overrides: Partial<ImportErrorReport> = {}): ImportErrorReport => {
    const topicErrors = new Map<string, ImportTopicErrors>();
    topicErrors.set('topic-1', createMockTopicErrors());

    return {
      errorsByTopic: topicErrors,
      globalErrors: [createMockError({ id: 'global-error-1', message: 'Global error message' })],
      statistics: createMockStatistics(),
      suggestions: [createMockSuggestion()],
      recoveryOptions: [createMockRecoveryOption()],
      ...overrides,
    };
  };

  it('renders overall status with success', () => {
    const report = createMockReport({
      statistics: createMockStatistics({ errorsCount: 0, warningsCount: 0, successRate: 1.0 }),
      globalErrors: [],
      errorsByTopic: new Map(),
    });

    render(<ImportErrorReporting report={report} />);

    expect(screen.getByText('Import Completed Successfully')).toBeInTheDocument();
    expect(screen.getByText('Questions Imported')).toBeInTheDocument();
    expect(screen.getByText('100%')).toBeInTheDocument(); // Success rate
  });

  it('renders overall status with errors', () => {
    const report = createMockReport({
      statistics: createMockStatistics({ errorsCount: 2, successRate: 0.8 }),
    });

    render(<ImportErrorReporting report={report} />);

    expect(screen.getByText('Import Completed with Errors')).toBeInTheDocument();
    expect(screen.getByText('Questions Imported')).toBeInTheDocument();
    expect(screen.getByText('Questions Failed')).toBeInTheDocument();
    expect(screen.getByText('80%')).toBeInTheDocument(); // Success rate
  });

  it('renders overall status with warnings only', () => {
    const report = createMockReport({
      statistics: createMockStatistics({ errorsCount: 0, warningsCount: 2, successRate: 1.0 }),
      globalErrors: [],
    });

    render(<ImportErrorReporting report={report} />);

    expect(screen.getByText('Import Completed with Warnings')).toBeInTheDocument();
  });

  it('displays import statistics', () => {
    const report = createMockReport();

    render(<ImportErrorReporting report={report} />);

    expect(screen.getByText('Import Statistics')).toBeInTheDocument();
    expect(screen.getByText('Total Rows')).toBeInTheDocument();
    expect(screen.getByText('Imported')).toBeInTheDocument();
    expect(screen.getByText('Failed')).toBeInTheDocument();
    expect(screen.getByText('Topics')).toBeInTheDocument();
    expect(screen.getByText('New Topics')).toBeInTheDocument();
    expect(screen.getByText('Success Rate')).toBeInTheDocument();
  });

  it('displays global errors', () => {
    const report = createMockReport();

    render(<ImportErrorReporting report={report} />);

    expect(screen.getByText('Global Issues (1)')).toBeInTheDocument();
    expect(screen.getByText('Global error message')).toBeInTheDocument();
  });

  it('displays topic-specific errors', () => {
    const report = createMockReport();

    render(<ImportErrorReporting report={report} />);

    expect(screen.getByText('Topic-Specific Issues')).toBeInTheDocument();
    expect(screen.getByText('Test Topic')).toBeInTheDocument();
    expect(screen.getByText('8/10 questions')).toBeInTheDocument();
  });

  it('expands and collapses topic errors', async () => {
    const report = createMockReport();

    render(<ImportErrorReporting report={report} />);

    // Find the topic button by looking for the topic name
    const topicButton = screen.getByText('Test Topic').closest('button');
    expect(topicButton).toBeInTheDocument();
    
    fireEvent.click(topicButton!);

    await waitFor(() => {
      expect(screen.getByText('Successful')).toBeInTheDocument();
      expect(screen.getByText('Failed')).toBeInTheDocument();
    });
  });

  it('displays suggestions', () => {
    const report = createMockReport();

    render(<ImportErrorReporting report={report} />);

    expect(screen.getByText('Suggestions for Improvement')).toBeInTheDocument();
    expect(screen.getByText('Test Suggestion')).toBeInTheDocument();
    expect(screen.getByText('Test suggestion description')).toBeInTheDocument();
  });

  it('displays recovery options', () => {
    const report = createMockReport();

    render(<ImportErrorReporting report={report} />);

    expect(screen.getByText('Recovery Options')).toBeInTheDocument();
    expect(screen.getByText('Test Recovery')).toBeInTheDocument();
    expect(screen.getByText('Test recovery description')).toBeInTheDocument();
  });

  it('calls onRetry when retry button is clicked', () => {
    const report = createMockReport();

    render(
      <ImportErrorReporting
        report={report}
        onRetry={mockOnRetry}
      />
    );

    const retryButton = screen.getByText('Retry Import');
    fireEvent.click(retryButton);

    expect(mockOnRetry).toHaveBeenCalledTimes(1);
  });

  it('calls onDownloadErrorReport when download button is clicked', () => {
    const report = createMockReport();

    render(
      <ImportErrorReporting
        report={report}
        onDownloadErrorReport={mockOnDownloadErrorReport}
      />
    );

    const downloadButton = screen.getByText('Download Error Report');
    fireEvent.click(downloadButton);

    expect(mockOnDownloadErrorReport).toHaveBeenCalledTimes(1);
  });

  it('calls onFixAndRetry when fix and retry button is clicked', () => {
    const report = createMockReport();

    render(
      <ImportErrorReporting
        report={report}
        onFixAndRetry={mockOnFixAndRetry}
      />
    );

    const fixButton = screen.getByText('Fix & Retry');
    fireEvent.click(fixButton);

    expect(mockOnFixAndRetry).toHaveBeenCalledWith(null);
  });

  it('displays error suggestions with actionable indicators', () => {
    const report = createMockReport({
      globalErrors: [
        createMockError({
          id: 'actionable-error',
          message: 'Actionable error',
          actionable: true,
          suggestion: 'This is how to fix it',
        }),
      ],
    });

    render(<ImportErrorReporting report={report} />);

    expect(screen.getByText('Actionable error')).toBeInTheDocument();
    expect(screen.getByText('This is how to fix it')).toBeInTheDocument();
    expect(screen.getByText('Actionable')).toBeInTheDocument();
  });

  it('handles empty error report gracefully', () => {
    const report = createMockReport({
      errorsByTopic: new Map(),
      globalErrors: [],
      suggestions: [],
      recoveryOptions: [],
      statistics: createMockStatistics({ errorsCount: 0, warningsCount: 0 }),
    });

    render(<ImportErrorReporting report={report} />);

    expect(screen.getByText('Import Completed Successfully')).toBeInTheDocument();
    expect(screen.queryByText('Global Issues')).not.toBeInTheDocument();
    expect(screen.queryByText('Topic-Specific Issues')).not.toBeInTheDocument();
  });

  it('displays different severity icons correctly', () => {
    const report = createMockReport({
      globalErrors: [
        createMockError({ id: 'error-1', severity: 'error', message: 'Error message' }),
        createMockError({ id: 'warning-1', severity: 'warning', message: 'Warning message' }),
        createMockError({ id: 'info-1', severity: 'info', message: 'Info message' }),
      ],
    });

    render(<ImportErrorReporting report={report} />);

    expect(screen.getByText('Error message')).toBeInTheDocument();
    expect(screen.getByText('Warning message')).toBeInTheDocument();
    expect(screen.getByText('Info message')).toBeInTheDocument();
  });

  it('displays suggestion priorities correctly', () => {
    const report = createMockReport({
      suggestions: [
        createMockSuggestion({ id: 'high-1', priority: 'high', title: 'High Priority' }),
        createMockSuggestion({ id: 'medium-1', priority: 'medium', title: 'Medium Priority' }),
        createMockSuggestion({ id: 'low-1', priority: 'low', title: 'Low Priority' }),
      ],
    });

    render(<ImportErrorReporting report={report} />);

    expect(screen.getByText('High Priority')).toBeInTheDocument();
    expect(screen.getByText('Medium Priority')).toBeInTheDocument();
    expect(screen.getByText('Low Priority')).toBeInTheDocument();
  });

  it('handles topic statistics correctly', () => {
    const topicErrors = createMockTopicErrors({
      questionsProcessed: 15,
      questionsSuccessful: 12,
      questionsFailed: 3,
      errors: [createMockError(), createMockError()],
      warnings: [createMockError({ severity: 'warning' })],
    });

    const report = createMockReport({
      errorsByTopic: new Map([['topic-1', topicErrors]]),
    });

    render(<ImportErrorReporting report={report} />);

    expect(screen.getByText('12/15 questions')).toBeInTheDocument();
    expect(screen.getByText('2 errors')).toBeInTheDocument();
    expect(screen.getByText('1 warnings')).toBeInTheDocument();
  });

  it('shows new topic indicators', () => {
    const topicErrors = createMockTopicErrors({
      isNewTopic: true,
      topicName: 'New Topic',
    });

    const report = createMockReport({
      errorsByTopic: new Map([['topic-1', topicErrors]]),
    });

    render(<ImportErrorReporting report={report} />);

    expect(screen.getByText('New Topic')).toBeInTheDocument();
    expect(screen.getByText('New')).toBeInTheDocument();
  });

  it('displays duration when available', () => {
    const report = createMockReport({
      statistics: createMockStatistics({ duration: 12500 }), // 12.5 seconds
    });

    render(<ImportErrorReporting report={report} />);

    expect(screen.getByText('13s')).toBeInTheDocument(); // Rounded up
  });
});