# Environment Setup Guide

## Quick Fix for Current Errors

If you're seeing these errors:
- `Missing Supabase URL. Check your environment variables: VITE_SUPABASE_URL is not set.`
- `process is not defined` in register-sw.js

**Follow these steps:**

### 1. Create Environment File (Required)

Run the setup script:
```bash
node setup-env.js
```

Or manually create a `.env` file in your project root with:

```env
# Supabase Configuration (REQUIRED)
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key

# Application Settings
VITE_APP_NAME=SecQuiz
VITE_APP_DESCRIPTION=A cybersecurity education platform
VITE_APP_URL=http://localhost:5173

# Feature Flags
VITE_ENABLE_ADMIN_FEATURES=true
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_DEBUG_MODE=true

# API Configuration
VITE_API_TIMEOUT=30000
VITE_MAX_UPLOAD_SIZE=5242880
VITE_API_URL=http://localhost:3001

# Paystack Configuration
VITE_PAYSTACK_PUBLIC_KEY=your_paystack_public_key_here

# Authentication Settings
VITE_AUTH_REDIRECT_URL=http://localhost:5173/auth/verify
```

### 2. Get Your Supabase Credentials

1. Go to [Supabase Dashboard](https://app.supabase.com)
2. Select your project (or create a new one)
3. Go to **Settings** → **API**
4. Copy the **Project URL** and **anon/public key**
5. Replace the placeholder values in your `.env` file

### 3. Restart Development Server

After creating the `.env` file:
```bash
# Stop the current server (Ctrl+C)
# Then restart
npm run dev
```

## Detailed Setup Instructions

### Creating a New Supabase Project

If you don't have a Supabase project yet:

1. **Sign up/Login** to [Supabase](https://app.supabase.com)
2. **Create New Project**
   - Choose organization
   - Enter project name: "SecQuiz"
   - Enter database password (save this!)
   - Select region closest to your users
3. **Wait for setup** (takes 1-2 minutes)
4. **Get API credentials** from Settings → API

### Environment Variables Explained

| Variable | Description | Required | Example |
|----------|-------------|----------|---------|
| `VITE_SUPABASE_URL` | Your Supabase project URL | ✅ Yes | `https://abc123.supabase.co` |
| `VITE_SUPABASE_ANON_KEY` | Supabase anonymous/public key | ✅ Yes | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |
| `VITE_APP_NAME` | Application name | ❌ No | `SecQuiz` |
| `VITE_APP_URL` | Your app's URL | ❌ No | `http://localhost:5173` |
| `VITE_API_URL` | Backend API URL | ❌ No | `http://localhost:3001` |
| `VITE_PAYSTACK_PUBLIC_KEY` | Paystack public key for payments | ❌ No | `pk_test_...` |

### Common Issues and Solutions

#### Issue: "Missing Supabase URL"
**Solution:** Create `.env` file with correct `VITE_SUPABASE_URL`

#### Issue: "process is not defined"
**Solution:** This has been fixed in the latest code. Restart your dev server.

#### Issue: Environment variables not loading
**Solutions:**
1. Ensure `.env` file is in project root (same level as `package.json`)
2. Restart development server after creating/modifying `.env`
3. Check file is named exactly `.env` (not `.env.txt`)
4. Ensure no spaces around `=` in environment variables

#### Issue: Supabase connection fails
**Solutions:**
1. Verify URL format: `https://your-project.supabase.co`
2. Check anon key is complete (very long string)
3. Ensure project is active in Supabase dashboard
4. Check network connectivity

### Development vs Production

#### Development (.env)
```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_APP_URL=http://localhost:5173
VITE_API_URL=http://localhost:3001
VITE_ENABLE_DEBUG_MODE=true
```

#### Production (.env.production)
```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_APP_URL=https://your-domain.com
VITE_API_URL=https://your-api-domain.com
VITE_ENABLE_DEBUG_MODE=false
```

### Security Notes

1. **Never commit `.env` to git** - it's already in `.gitignore`
2. **Use different Supabase projects** for development and production
3. **Rotate keys regularly** in production
4. **Use Row Level Security (RLS)** in Supabase for data protection

### Verification Steps

After setup, verify everything works:

1. **Start development server:**
   ```bash
   npm run dev
   ```

2. **Check browser console** - should see no environment variable errors

3. **Test authentication** - try signing up/logging in

4. **Check network tab** - API calls should go to correct URLs

### Getting Help

If you're still having issues:

1. **Check the browser console** for specific error messages
2. **Verify `.env` file contents** match the template
3. **Ensure Supabase project is active** in the dashboard
4. **Try the automated setup script:** `node setup-env.js`

### Automated Setup Script

Use the included setup script for guided configuration:

```bash
node setup-env.js
```

This script will:
- Check for existing `.env` file
- Prompt for all required values
- Create properly formatted `.env` file
- Provide next steps

### File Structure

Your project should look like this:
```
secquiz/
├── .env                 # ← Your environment variables (create this)
├── .env.example         # ← Template file
├── package.json
├── src/
├── public/
└── server/
```

## Next Steps

After fixing the environment setup:

1. **Test the free tier implementation** using the testing checklist
2. **Configure Paystack** for payment processing (optional)
3. **Set up your database schema** in Supabase
4. **Deploy to production** when ready

## Support

- **Supabase Docs:** https://supabase.com/docs
- **Vite Environment Variables:** https://vitejs.dev/guide/env-and-mode.html
- **Project Issues:** Check the GitHub repository issues
