import { Link } from "react-router-dom";
import { useIsMobile } from "@/hooks/use-mobile";

interface MobileHeaderProps {
  title?: string;
  showLogo?: boolean;
  className?: string;
}

const MobileHeader = ({ title, showLogo = true, className = "" }: MobileHeaderProps) => {
  const isMobile = useIsMobile();

  // Only show on mobile devices
  if (!isMobile) return null;

  return (
    <div className={`bg-white border-b border-gray-200 px-4 py-3 ${className}`}>
      <div className="flex items-center justify-between">
        {/* Logo */}
        {showLogo && (
          <Link to="/" className="flex items-center gap-2">
            <img 
              src="/secquiz-logo.svg" 
              alt="SecQuiz Logo" 
              className="h-8 w-8"
              onError={(e) => {
                // Fallback if logo doesn't exist
                e.currentTarget.style.display = 'none';
                e.currentTarget.nextElementSibling?.classList.remove('hidden');
              }}
            />
            <div className="hidden w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">SQ</span>
            </div>
            <span className="text-xl font-bold text-blue-900">SecQuiz</span>
          </Link>
        )}

        {/* Title */}
        {title && !showLogo && (
          <h1 className="text-lg font-semibold text-gray-900">{title}</h1>
        )}

        {/* Spacer for centering when needed */}
        <div className="w-8"></div>
      </div>
    </div>
  );
};

export default MobileHeader;
