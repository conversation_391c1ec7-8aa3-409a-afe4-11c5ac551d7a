import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { AlertCircle, CheckCircle2, Download, Upload, Info, FileText, Plus, ChevronDown, HelpCircle } from "lucide-react";
import { 
  parseQuestionCSVEnhanced, 
  generateCSVTemplate, 
  generateMultiTopicCSVTemplate,
  type MultiTopicImportResult,
  type ImportConfig,
  type Topic,
  type MultiTopicTemplateFormat
} from "@/utils/csv-import";
import { 
  batchImportService,
  type BatchImportProgress,
  type BatchImportResult,
  type BatchImportConfig
} from "@/services/batch-import-service";
import { Progress } from "@/components/ui/progress";
import { Spinner } from "@/components/ui/spinner";
import { useToast } from "@/hooks/use-toast";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { ImportErrorReporting, type ImportErrorReport } from "./ImportErrorReporting";
import { importErrorReportingService } from "@/services/import-error-reporting-service";

interface MultiTopicCSVImportProps {
  topics: Topic[];
  onSuccess?: (result: MultiTopicImportResult) => void;
  onCancel?: () => void;
}

interface ImportState {
  mode: 'single-topic' | 'multi-topic';
  selectedFile: File | null;
  selectedTopic: string;
  autoCreateTopics: boolean;
  previewData: MultiTopicImportResult | null;
  importing: boolean;
  showPreview: boolean;
  progress: number;
  progressMessage: string;
  batchResult: BatchImportResult | null;
  showHelp: boolean;
  errorReport: ImportErrorReport | null;
}

export function MultiTopicCSVImport({ topics, onSuccess, onCancel }: MultiTopicCSVImportProps) {
  const [state, setState] = useState<ImportState>({
    mode: 'single-topic',
    selectedFile: null,
    selectedTopic: "",
    autoCreateTopics: false,
    previewData: null,
    importing: false,
    showPreview: false,
    progress: 0,
    progressMessage: "",
    batchResult: null,
    showHelp: false,
    errorReport: null,
  });
  
  const { toast } = useToast();

  // Reset preview when file or configuration changes
  useEffect(() => {
    setState(prev => ({
      ...prev,
      previewData: null,
      showPreview: false,
      batchResult: null,
      progressMessage: "",
      errorReport: null,
    }));
  }, [state.selectedFile, state.mode, state.selectedTopic, state.autoCreateTopics]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      setState(prev => ({
        ...prev,
        selectedFile: file,
        previewData: null,
        showPreview: false,
        batchResult: null,
        progressMessage: "",
      }));
      
      // Auto-detect mode based on file content (simplified detection)
      // In a real implementation, you might want to peek at the file headers
      // For now, we'll let the user choose or detect during parsing
    }
  };

  const handleTopicChange = (value: string) => {
    setState(prev => ({
      ...prev,
      selectedTopic: value,
      previewData: null,
      showPreview: false,
      batchResult: null,
      progressMessage: "",
    }));
  };

  const handleModeChange = (mode: 'single-topic' | 'multi-topic') => {
    setState(prev => ({
      ...prev,
      mode,
      previewData: null,
      showPreview: false,
      batchResult: null,
      progressMessage: "",
    }));
  };

  const handleAutoCreateToggle = (checked: boolean) => {
    setState(prev => ({
      ...prev,
      autoCreateTopics: checked,
      previewData: null,
      showPreview: false,
      batchResult: null,
      progressMessage: "",
    }));
  };

  const handleDownloadTemplate = (templateType: 'single' | 'multi' = 'single', format?: MultiTopicTemplateFormat) => {
    let csvContent: string;
    let filename: string;
    
    if (templateType === 'multi') {
      csvContent = generateMultiTopicCSVTemplate(format);
      const formatSuffix = format === 'id-based' ? '_id_based' : format === 'both' ? '_both_columns' : '';
      filename = `multi_topic_questions_template${formatSuffix}.csv`;
    } else {
      csvContent = generateCSVTemplate();
      filename = "quiz_questions_template.csv";
    }
    
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", filename);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handlePreview = async () => {
    if (!state.selectedFile) {
      toast({
        title: "Missing file",
        description: "Please select a CSV file to preview.",
        variant: "destructive",
      });
      return;
    }

    if (state.mode === 'single-topic' && !state.selectedTopic) {
      toast({
        title: "Missing topic",
        description: "Please select a topic for single-topic import.",
        variant: "destructive",
      });
      return;
    }

    setState(prev => ({ ...prev, importing: true, progress: 10 }));

    try {
      const config: ImportConfig = {
        mode: state.mode,
        autoCreateTopics: state.autoCreateTopics,
        selectedTopicId: state.mode === 'single-topic' ? state.selectedTopic : undefined,
      };

      const result = await parseQuestionCSVEnhanced(state.selectedFile, config);
      
      // Generate error report for preview if there are errors
      let errorReport: ImportErrorReport | null = null;
      if (result.globalErrors.length > 0 || 
          Array.from(result.topicResults.values()).some(tr => tr.errors.length > 0)) {
        errorReport = importErrorReportingService.generateErrorReport(result);
      }

      setState(prev => ({
        ...prev,
        previewData: result,
        showPreview: true,
        importing: false,
        progress: 100,
        errorReport,
      }));

      if (result.globalErrors.length > 0) {
        toast({
          title: "Preview completed with warnings",
          description: `Found ${result.globalErrors.length} issues. Please review before importing.`,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Preview successful",
          description: `Ready to import ${result.totalRows} questions across ${result.topicResults.size} topic(s).`,
        });
      }
    } catch (error) {
      console.error("Preview error:", error);
      toast({
        title: "Preview error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
      setState(prev => ({ ...prev, importing: false, progress: 0 }));
    }
  };

  const handleConfirmImport = async () => {
    if (!state.previewData) {
      toast({
        title: "No preview data",
        description: "Please preview the import first.",
        variant: "destructive",
      });
      return;
    }

    // Validate import result before processing
    const validationErrors = batchImportService.validateImportResult(state.previewData);
    if (validationErrors.length > 0) {
      toast({
        title: "Validation failed",
        description: `Found ${validationErrors.length} validation errors. Please check your data.`,
        variant: "destructive",
      });
      return;
    }

    setState(prev => ({ 
      ...prev, 
      importing: true, 
      progress: 0,
      progressMessage: "Starting import...",
      batchResult: null,
    }));

    try {
      // Configure batch import with progress tracking
      const batchConfig: BatchImportConfig = {
        mode: state.mode,
        autoCreateTopics: state.autoCreateTopics,
        selectedTopicId: state.mode === 'single-topic' ? state.selectedTopic : undefined,
        batchSize: 10, // Process 10 questions per batch
        maxRetries: 3, // Retry failed operations up to 3 times
        progressCallback: (progress: BatchImportProgress) => {
          setState(prev => ({
            ...prev,
            progress: progress.percentage,
            progressMessage: progress.message,
          }));
        },
      };

      // Execute the batch import
      const batchResult = await batchImportService.executeBatchImport(
        state.previewData,
        batchConfig
      );

      // Generate comprehensive error report
      const errorReport = importErrorReportingService.generateErrorReport(
        state.previewData,
        batchResult
      );

      setState(prev => ({ 
        ...prev, 
        batchResult,
        errorReport,
        progress: 100,
        progressMessage: "Import completed",
      }));

      // Generate success/error messages
      const hasErrors = batchResult.errors.length > 0;
      const partialSuccess = batchResult.totalQuestionsImported > 0 && hasErrors;

      let title: string;
      let description: string;
      let variant: "default" | "destructive" = "default";

      if (batchResult.success && !hasErrors) {
        title = "Import completed successfully";
        description = batchResult.topicsCreated.length > 0
          ? `Imported ${batchResult.totalQuestionsImported} questions across ${batchResult.totalTopicsProcessed} topic(s). Created ${batchResult.topicsCreated.length} new topic(s).`
          : `Imported ${batchResult.totalQuestionsImported} questions across ${batchResult.totalTopicsProcessed} topic(s).`;
      } else if (partialSuccess) {
        title = "Import completed with errors";
        description = `Imported ${batchResult.totalQuestionsImported} questions, but encountered ${batchResult.errors.length} errors. Check the results below for details.`;
        variant = "destructive";
      } else {
        title = "Import failed";
        description = `Failed to import questions. Encountered ${batchResult.errors.length} errors.`;
        variant = "destructive";
      }

      toast({
        title,
        description,
        variant,
      });

      // Call success callback with enhanced result
      if (onSuccess && batchResult.success) {
        const enhancedResult: MultiTopicImportResult = {
          ...state.previewData,
          success: batchResult.success,
        };
        onSuccess(enhancedResult);
      }
    } catch (error) {
      console.error("Batch import error:", error);
      toast({
        title: "Import error",
        description: error instanceof Error ? error.message : "An unknown error occurred during import",
        variant: "destructive",
      });
      setState(prev => ({ 
        ...prev, 
        progress: 0,
        progressMessage: "Import failed",
      }));
    } finally {
      setState(prev => ({ ...prev, importing: false }));
    }
  };

  const handleCancelPreview = () => {
    setState(prev => ({
      ...prev,
      previewData: null,
      showPreview: false,
      progress: 0,
      progressMessage: "",
      batchResult: null,
    }));
  };

  const renderModeSelector = () => (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <Label>Import Mode</Label>
        <Tooltip>
          <TooltipTrigger asChild>
            <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
          </TooltipTrigger>
          <TooltipContent className="max-w-xs">
            <p>Choose how to import your questions:</p>
            <ul className="mt-1 text-xs space-y-1">
              <li>• <strong>Single Topic:</strong> All questions go to one selected topic</li>
              <li>• <strong>Multi Topic:</strong> Questions are distributed to topics specified in the CSV</li>
            </ul>
          </TooltipContent>
        </Tooltip>
      </div>
      <div className="flex gap-2">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant={state.mode === 'single-topic' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleModeChange('single-topic')}
              disabled={state.importing}
            >
              <FileText className="h-4 w-4 mr-2" />
              Single Topic
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            Import all questions to one selected topic. Use standard CSV format without topic columns.
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant={state.mode === 'multi-topic' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleModeChange('multi-topic')}
              disabled={state.importing}
            >
              <Plus className="h-4 w-4 mr-2" />
              Multi Topic
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            Import questions to multiple topics. CSV must include topic_name or topic_id columns.
          </TooltipContent>
        </Tooltip>
      </div>
      <div className="p-3 bg-muted/50 rounded-lg">
        <p className="text-sm text-muted-foreground">
          {state.mode === 'single-topic' 
            ? "📄 Import all questions to a single selected topic. Your CSV should not include topic columns."
            : "📁 Import questions to multiple topics specified in the CSV file. Include topic_name or topic_id columns."
          }
        </p>
      </div>
    </div>
  );

  const renderTopicConfiguration = () => {
    if (state.mode === 'single-topic') {
      return (
        <div className="space-y-2">
          <div c htmlFor="flex items-center gap-2">
            <Label htmlFor="topic">Select Topic</Label>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent>
                All questions from your CSV will be imported to this topic. Make sure you have permission to add questions to the selected topic.
              </TooltipContent>
            </Tooltip>
          </div>
          <Select value={state.selectedTopic} onValueChange={handleTopicChange}>
            <SelectTrigger id="topic">
              <SelectValue placeholder="Select a topic" />
            </SelectTrigger>
            <SelectContent>
              {topics.map((topic) => (
                <SelectItem key={topic.id} value={topic.id}>
                  {topic.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground">
            💡 All questions in your CSV will be added to the selected topic
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Label htmlFor="auto-create">Auto-create Topics</Label>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <p>When enabled:</p>
                <ul className="mt-1 text-xs space-y-1">
                  <li>• New topics will be created automatically if they don't exist</li>
                  <li>• Topic names must be 1-100 characters</li>
                  <li>• Only letters, numbers, spaces, hyphens, and underscores allowed</li>
                </ul>
                <p className="mt-2 text-xs">When disabled, only existing topics will be used.</p>
              </TooltipContent>
            </Tooltip>
          </div>
          <Switch
            id="auto-create"
            checked={state.autoCreateTopics}
            onCheckedChange={handleAutoCreateToggle}
            disabled={state.importing}
          />
        </div>
        <div className="p-3 bg-muted/50 rounded-lg">
          <p className="text-sm text-muted-foreground">
            {state.autoCreateTopics
              ? "✅ New topics will be created automatically if they don't exist in the system"
              : "⚠️ Only questions for existing topics will be imported. Missing topics will cause errors"
            }
          </p>
        </div>
      </div>
    );
  };

  const renderPreview = () => {
    if (!state.showPreview || !state.previewData) return null;

    const { previewData } = state;

    return (
      <div className="space-y-4">
        <Separator />
        <div>
          <h3 className="text-lg font-semibold mb-3">Import Preview</h3>
          
          {/* Global errors */}
          {previewData.globalErrors.length > 0 && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Issues Found ({previewData.globalErrors.length})</AlertTitle>
              <AlertDescription>
                <p className="mb-2 text-sm">Please fix these issues before importing:</p>
                <div className="mt-2 max-h-32 overflow-y-auto">
                  <ul className="list-disc pl-5 text-sm">
                    {previewData.globalErrors.map((error, index) => (
                      <li key={index}>
                        <strong>Row {error.row}:</strong> {error.message}
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="mt-3 p-2 bg-destructive/5 rounded text-xs">
                  <p className="font-medium">💡 Common fixes:</p>
                  <ul className="mt-1 space-y-1">
                    <li>• Check column headers match template exactly</li>
                    <li>• Ensure all required fields are filled</li>
                    <li>• Verify correct_answer is A, B, C, or D</li>
                    <li>• Check topic names don't contain special characters</li>
                  </ul>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* New topics that will be created */}
          {previewData.newTopicsCreated.length > 0 && (
            <Alert className="mb-4 border-blue-200 bg-blue-50">
              <Info className="h-4 w-4 text-blue-600" />
              <AlertTitle className="text-blue-800">New Topics Will Be Created ({previewData.newTopicsCreated.length})</AlertTitle>
              <AlertDescription className="text-blue-700">
                <p className="mb-2">The following topics don't exist and will be created automatically:</p>
                <div className="flex flex-wrap gap-1 mt-2">
                  {previewData.newTopicsCreated.map((topicName) => (
                    <Badge key={topicName} variant="secondary" className="bg-blue-100 text-blue-800">
                      {topicName}
                    </Badge>
                  ))}
                </div>
                <p className="mt-2 text-sm">✅ These topics will use default settings and can be customized later in the admin panel.</p>
              </AlertDescription>
            </Alert>
          )}

          {/* Topic results */}
          <div className="space-y-3">
            {Array.from(previewData.topicResults.entries()).map(([topicId, topicResult]) => (
              <Card key={topicId} className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{topicResult.topicName}</h4>
                    {topicResult.isNewTopic && (
                      <Badge variant="outline" className="text-xs">New</Badge>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Badge variant="default">
                      {topicResult.validQuestions.length} questions
                    </Badge>
                    {topicResult.errors.length > 0 && (
                      <Badge variant="destructive">
                        {topicResult.errors.length} errors
                      </Badge>
                    )}
                  </div>
                </div>
                
                {topicResult.errors.length > 0 && (
                  <div className="mt-2 p-3 bg-destructive/10 rounded text-sm border border-destructive/20">
                    <p className="font-medium text-destructive mb-2">❌ Errors in this topic ({topicResult.errors.length}):</p>
                    <ul className="list-disc pl-5 text-destructive max-h-24 overflow-y-auto space-y-1">
                      {topicResult.errors.map((error, index) => (
                        <li key={index}>
                          <strong>Row {error.row}:</strong> {error.message}
                        </li>
                      ))}
                    </ul>
                    <div className="mt-2 pt-2 border-t border-destructive/20">
                      <p className="text-xs text-destructive/80">
                        💡 Fix these errors in your CSV and re-upload to include these questions.
                      </p>
                    </div>
                  </div>
                )}
              </Card>
            ))}
          </div>

          {/* Summary */}
          <div className="mt-4 p-4 bg-muted/50 rounded-lg border">
            <h4 className="font-medium mb-2">📊 Import Summary</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground">Total Rows</p>
                <p className="font-medium">{previewData.totalRows}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Valid Questions</p>
                <p className="font-medium text-green-600">
                  {Array.from(previewData.topicResults.values()).reduce((sum, tr) => sum + tr.validQuestions.length, 0)}
                </p>
              </div>
              <div>
                <p className="text-muted-foreground">Topics</p>
                <p className="font-medium">{previewData.topicResults.size}</p>
              </div>
              {previewData.newTopicsCreated.length > 0 && (
                <div>
                  <p className="text-muted-foreground">New Topics</p>
                  <p className="font-medium text-blue-600">{previewData.newTopicsCreated.length}</p>
                </div>
              )}
            </div>
            {previewData.globalErrors.length === 0 && (
              <div className="mt-3 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-800">
                ✅ Ready to import! All questions passed validation.
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderBatchResults = () => {
    if (!state.batchResult) return null;

    const { batchResult } = state;

    return (
      <div className="space-y-4">
        <Separator />
        <div>
          <h3 className="text-lg font-semibold mb-3">Import Results</h3>
          
          {/* Overall result summary */}
          <Alert className={`mb-4 ${batchResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
            {batchResult.success ? (
              <CheckCircle2 className="h-4 w-4 text-green-600" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-600" />
            )}
            <AlertTitle className={batchResult.success ? 'text-green-800' : 'text-red-800'}>
              {batchResult.success ? 'Import Completed' : 'Import Failed'}
            </AlertTitle>
            <AlertDescription className={batchResult.success ? 'text-green-700' : 'text-red-700'}>
              <div className="mt-2">
                <p>
                  <strong>Questions imported:</strong> {batchResult.totalQuestionsImported}
                </p>
                <p>
                  <strong>Topics processed:</strong> {batchResult.totalTopicsProcessed}
                </p>
                {batchResult.topicsCreated.length > 0 && (
                  <p>
                    <strong>New topics created:</strong> {batchResult.topicsCreated.length}
                  </p>
                )}
                <p>
                  <strong>Duration:</strong> {Math.round(batchResult.duration / 1000)}s
                </p>
              </div>
            </AlertDescription>
          </Alert>

          {/* Batch errors */}
          {batchResult.errors.length > 0 && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Import Errors ({batchResult.errors.length})</AlertTitle>
              <AlertDescription>
                <div className="mt-2 max-h-32 overflow-y-auto">
                  <ul className="list-disc pl-5 text-sm">
                    {batchResult.errors.map((error, index) => (
                      <li key={index}>
                        <strong>{error.type}:</strong> {error.message}
                        {error.topicName && ` (Topic: ${error.topicName})`}
                      </li>
                    ))}
                  </ul>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Topic-level results */}
          <div className="space-y-3">
            <h4 className="font-medium">Topic Results</h4>
            {Array.from(batchResult.topicResults.entries()).map(([topicId, topicResult]) => (
              <Card key={topicId} className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <h5 className="font-medium">{topicResult.topicName}</h5>
                    {topicResult.isNewTopic && (
                      <Badge variant="outline" className="text-xs">New</Badge>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Badge variant="default">
                      {topicResult.questionsImported} imported
                    </Badge>
                    {topicResult.questionsFailed > 0 && (
                      <Badge variant="destructive">
                        {topicResult.questionsFailed} failed
                      </Badge>
                    )}
                  </div>
                </div>
                
                <div className="text-sm text-muted-foreground">
                  Duration: {Math.round(topicResult.duration / 1000)}s
                </div>
                
                {topicResult.errors.length > 0 && (
                  <div className="mt-2 p-2 bg-destructive/10 rounded text-sm">
                    <p className="font-medium text-destructive mb-1">Topic Errors:</p>
                    <ul className="list-disc pl-5 text-destructive max-h-24 overflow-y-auto">
                      {topicResult.errors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderHelpSection = () => {
    if (!state.showHelp) return null;

    return (
      <div className="space-y-4">
        <Separator />
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Import Help & Troubleshooting</h3>
          
          <div className="grid gap-4 md:grid-cols-2">
            <Card className="p-4">
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <FileText className="h-4 w-4" />
                CSV Format Requirements
              </h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Use UTF-8 encoding</li>
                <li>• Comma-separated values</li>
                <li>• Question text: min 10 characters</li>
                <li>• Explanation: min 10 characters</li>
                <li>• Correct answer: A, B, C, or D</li>
                <li>• Difficulty: easy, medium, or hard</li>
              </ul>
            </Card>

            <Card className="p-4">
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Multi-Topic Tips
              </h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Use consistent topic naming</li>
                <li>• Enable auto-create for new topics</li>
                <li>• Topic names: 1-100 characters</li>
                <li>• No special characters in names</li>
                <li>• Preview before importing</li>
                <li>• Start with small test files</li>
              </ul>
            </Card>

            <Card className="p-4">
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                Common Issues
              </h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• "Topic not found": Enable auto-create</li>
                <li>• "Invalid format": Check column headers</li>
                <li>• "Validation failed": Check required fields</li>
                <li>• "Permission denied": Check admin access</li>
                <li>• Large files: Split into smaller batches</li>
              </ul>
            </Card>

            <Card className="p-4">
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4" />
                Best Practices
              </h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Download and use templates</li>
                <li>• Test with 5-10 questions first</li>
                <li>• Use preview to catch errors</li>
                <li>• Keep backups of your CSV files</li>
                <li>• Import during off-peak hours</li>
                <li>• Review results after import</li>
              </ul>
            </Card>
          </div>

          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Need More Help?</AlertTitle>
            <AlertDescription>
              <p>For detailed documentation and examples, see the CSV Import Guide in the documentation.</p>
              <p className="mt-1">If you encounter persistent issues, contact support with:</p>
              <ul className="mt-1 text-sm list-disc pl-5">
                <li>Exact error messages</li>
                <li>Sample CSV data (anonymized)</li>
                <li>Import settings used</li>
              </ul>
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  };

  const renderErrorReport = () => {
    if (!state.errorReport) return null;

    const handleRetryImport = () => {
      // Reset state and allow user to retry
      setState(prev => ({
        ...prev,
        importing: false,
        progress: 0,
        progressMessage: "",
        batchResult: null,
        errorReport: null,
        showPreview: true, // Show preview again for retry
      }));
    };

    const handleDownloadErrorReport = () => {
      if (!state.errorReport) return;
      
      const csvContent = importErrorReportingService.generateDownloadableErrorReport(state.errorReport);
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `import_error_report_${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    };

    const handleFixAndRetry = () => {
      // This could open a modal or redirect to a fix interface
      toast({
        title: "Fix and Retry",
        description: "This feature will help you fix errors and retry the import.",
      });
    };

    return (
      <div className="mt-6">
        <Separator className="mb-6" />
        <ImportErrorReporting
          report={state.errorReport}
          onRetry={handleRetryImport}
          onDownloadErrorReport={handleDownloadErrorReport}
          onFixAndRetry={handleFixAndRetry}
        />
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle>Import Questions from CSV</CardTitle>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="h-5 w-5 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent className="max-w-md">
                <p>Bulk import quiz questions from CSV files.</p>
                <ul className="mt-2 text-xs space-y-1">
                  <li>• <strong>Single-topic:</strong> Import all questions to one selected topic</li>
                  <li>• <strong>Multi-topic:</strong> Import questions to multiple topics specified in CSV</li>
                  <li>• Always preview before importing to catch errors early</li>
                  <li>• Download templates to ensure correct formatting</li>
                </ul>
              </TooltipContent>
            </Tooltip>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setState(prev => ({ ...prev, showHelp: !prev.showHelp }))}
            disabled={state.importing}
          >
            <Info className="h-4 w-4 mr-2" />
            {state.showHelp ? 'Hide Help' : 'Show Help'}
          </Button>
        </div>
        <CardDescription>
          Efficiently import quiz questions from CSV files. Use single-topic mode for traditional imports to one topic, 
          or multi-topic mode to distribute questions across multiple topics in one operation. 
          <strong> Always preview your import first to verify the data.</strong>
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {renderModeSelector()}
        
        <Separator />
        
        {renderTopicConfiguration()}

        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Label htmlFor="csv-file">Upload CSV File</Label>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent className="max-w-sm">
                <p>Upload a CSV file with your quiz questions.</p>
                <ul className="mt-1 text-xs space-y-1">
                  <li>• File must be in CSV format (.csv extension)</li>
                  <li>• Use UTF-8 encoding for special characters</li>
                  <li>• Maximum recommended size: 10MB</li>
                  <li>• Use templates to ensure correct format</li>
                </ul>
              </TooltipContent>
            </Tooltip>
          </div>
          <div className="flex items-center gap-2">
            <Input
              id="csv-file"
              type="file"
              accept=".csv"
              onChange={handleFileChange}
              disabled={state.importing}
            />
            {state.mode === 'multi-topic' ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={state.importing}
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Template
                        <ChevronDown className="h-4 w-4 ml-2" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      Download CSV templates with sample data for different multi-topic formats
                    </TooltipContent>
                  </Tooltip>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleDownloadTemplate('multi', 'name-based')}>
                    <FileText className="h-4 w-4 mr-2" />
                    <div>
                      <div className="font-medium">Name-based Format</div>
                      <div className="text-xs text-muted-foreground">Use topic names (recommended)</div>
                    </div>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleDownloadTemplate('multi', 'id-based')}>
                    <FileText className="h-4 w-4 mr-2" />
                    <div>
                      <div className="font-medium">ID-based Format</div>
                      <div className="text-xs text-muted-foreground">Use existing topic IDs</div>
                    </div>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleDownloadTemplate('multi', 'both')}>
                    <FileText className="h-4 w-4 mr-2" />
                    <div>
                      <div className="font-medium">Both Columns Format</div>
                      <div className="text-xs text-muted-foreground">Include name and ID columns</div>
                    </div>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => handleDownloadTemplate('single')}
                    disabled={state.importing}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  Download CSV template with sample data for single-topic import
                </TooltipContent>
              </Tooltip>
            )}
          </div>
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start gap-2">
              <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-blue-800">
                {state.mode === 'single-topic' ? (
                  <div>
                    <p className="font-medium mb-1">Single-Topic Format Requirements:</p>
                    <ul className="text-xs space-y-1">
                      <li>• <strong>Required:</strong> question_text, option_a, option_b, option_c, option_d, correct_answer, explanation</li>
                      <li>• <strong>Optional:</strong> difficulty (easy, medium, hard - defaults to medium)</li>
                      <li>• <strong>Note:</strong> All questions will be imported to the selected topic</li>
                    </ul>
                  </div>
                ) : (
                  <div>
                    <p className="font-medium mb-1">Multi-Topic Format Options:</p>
                    <ul className="text-xs space-y-1">
                      <li>• <strong>Name-based:</strong> Include topic_name column (creates topics if needed)</li>
                      <li>• <strong>ID-based:</strong> Include topic_id column (must be existing UUIDs)</li>
                      <li>• <strong>Both columns:</strong> Include both (topic_name takes precedence)</li>
                    </ul>
                    <p className="text-xs mt-2">
                      <strong>Required columns:</strong> question_text, option_a, option_b, option_c, option_d, correct_answer, explanation
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {state.importing && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Spinner size="sm" />
              <span>
                {state.progressMessage || (state.showPreview ? "Importing questions..." : "Processing CSV...")}
              </span>
            </div>
            <Progress value={state.progress} className="h-2" />
          </div>
        )}

        {renderHelpSection()}
        {renderPreview()}
        {renderBatchResults()}
        {renderErrorReport()}
      </CardContent>
      <CardFooter className="flex justify-end gap-2">
        {onCancel && (
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={state.importing}
          >
            Cancel
          </Button>
        )}
        
        {state.showPreview ? (
          <>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  onClick={handleCancelPreview}
                  disabled={state.importing}
                >
                  Back to Edit
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                Return to file selection to choose a different CSV or change settings
              </TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  onClick={handleConfirmImport}
                  disabled={state.importing || !state.previewData?.topicResults.size || (state.previewData?.globalErrors.length ?? 0) > 0}
                >
                  {state.importing ? (
                    <div className="flex items-center gap-2">
                      <Spinner size="sm" />
                      <span>Importing...</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="h-4 w-4" />
                      <span>Confirm Import</span>
                    </div>
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {(state.previewData?.globalErrors.length ?? 0) > 0 
                  ? "Fix validation errors before importing"
                  : "Execute the import with the previewed questions"
                }
              </TooltipContent>
            </Tooltip>
          </>
        ) : (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={handlePreview}
                disabled={!state.selectedFile || (state.mode === 'single-topic' && !state.selectedTopic) || state.importing}
                className={onCancel ? "" : "w-full"}
              >
                {state.importing ? (
                  <div className="flex items-center gap-2">
                    <Spinner size="sm" />
                    <span>Processing...</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Upload className="h-4 w-4" />
                    <span>Preview Import</span>
                  </div>
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {!state.selectedFile 
                ? "Select a CSV file first"
                : state.mode === 'single-topic' && !state.selectedTopic
                ? "Select a topic for single-topic import"
                : "Parse and validate your CSV file before importing"
              }
            </TooltipContent>
          </Tooltip>
        )}
      </CardFooter>
    </Card>
  );
}