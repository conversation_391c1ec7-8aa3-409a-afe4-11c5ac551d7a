#!/bin/bash

# Domain Reorganization Script
# This script will reorganize the domain structure and topic assignments

echo "🚀 Starting domain reorganization..."

# Check if we have the required environment variables
if [ -z "$DATABASE_URL" ]; then
    echo "❌ DATABASE_URL environment variable is not set"
    echo "Please set DAT<PERSON>ASE_URL to your Supabase database connection string"
    exit 1
fi

# Check if the SQL file exists
if [ ! -f "reorganize-domains-and-topics.sql" ]; then
    echo "❌ reorganize-domains-and-topics.sql file not found"
    exit 1
fi

echo "📝 Executing domain reorganization SQL..."

# Execute the SQL file
psql "$DATABASE_URL" -f reorganize-domains-and-topics.sql

if [ $? -eq 0 ]; then
    echo "✅ Domain reorganization completed successfully!"
    echo ""
    echo "📋 Summary of changes:"
    echo "  ✅ Created 'Cybersecurity Foundations' domain"
    echo "  ✅ Renamed 'Penetration Testing' to 'Ethical Hacking'"
    echo "  ✅ Moved GDPR/NDPR to Governance & Compliance"
    echo "  ✅ Moved Network Security topics to Network Security domain"
    echo "  ✅ Moved Linux Fundamentals and Web Security to Ethical Hacking"
    echo "  ✅ Moved CISSP topics to Cybersecurity Foundations"
    echo "  ✅ Fixed Security Awareness domain topic count"
    echo ""
    echo "🔍 You can now verify the changes in your application"
else
    echo "❌ Domain reorganization failed"
    exit 1
fi
