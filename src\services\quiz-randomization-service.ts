/**
 * Quiz Randomization Service
 * Handles question selection, answer option shuffling, and quiz session management
 */

import { supabase } from '@/integrations/supabase/client';
import { parseCorrectAnswer, parseQuestionOptions } from '@/utils/answer-validation';
import { QuizSession, type QuizSessionCreateParams } from '@/models/QuizSession';
import { generateUUID, ensureValidUUID, validateUUIDForDatabase } from '@/utils/uuid-helpers';
import type { Tables } from '@/types/supabase';
import { 
  errorLogger, 
  logRandomizationError, 
  logSessionError, 
  logDatabaseError,
  logValidationError,
  type ErrorContext 
} from './error-logging-service';
import { 
  userFriendlyErrors,
  getRandomizationErrorMessage,
  getSessionErrorMessage,
  getDatabaseErrorMessage,
  getInsufficientQuestionsStrategy,
  type UserFriendlyError,
  type ErrorFallback
} from './user-friendly-errors';

// Type definitions for randomization
export interface Question extends Tables<'questions'> {}

export interface RandomizedQuestion extends Question {
  originalCorrectIndex: number;
  shuffledCorrectIndex: number;
  optionMapping: number[]; // Maps shuffled positions to original positions
  shuffledOptions: Record<string, string>;
}

export interface QuizSessionData extends Tables<'quiz_sessions'> {}

export interface QuizSessionResult {
  questions: RandomizedQuestion[];
  session: QuizSession;
  sessionId: string;
  topicId: string;
  userId: string;
  quizLength: number;
}

export interface QuestionSelectionOptions {
  topicId: string;
  count: number;
  excludeQuestionIds?: string[];
  preferUnused?: boolean;
}

/**
 * Quiz Randomization Service Class
 * Provides methods for question selection, shuffling, and session management
 */
export class QuizRandomizationService {
  /**
   * Selects random questions from the available question pool for a topic
   * @param options - Question selection options
   * @returns Promise<Question[]> - Array of selected questions
   */
  static async selectRandomQuestions(options: QuestionSelectionOptions): Promise<Question[]> {
    const { topicId, count, excludeQuestionIds = [], preferUnused = true } = options;
    const context: ErrorContext = { topicId, operation: 'selectRandomQuestions' };

    try {
      // Validate inputs
      if (!topicId) {
        const error = new Error('Topic ID is required for question selection');
        logRandomizationError('selectRandomQuestions', error, context);
        throw error;
      }

      if (count <= 0 || count > 100) {
        const error = new Error(`Invalid question count: ${count}. Must be between 1 and 100`);
        logRandomizationError('selectRandomQuestions', error, { ...context, metadata: { count } });
        throw error;
      }

      // Build the query with timeout handling
      let query = supabase
        .from('questions')
        .select('*')
        .eq('topic_id', topicId);

      // Exclude specific questions if provided
      if (excludeQuestionIds.length > 0) {
        if (excludeQuestionIds.length > 50) {
          logValidationError('Large exclude list may impact performance', { 
            ...context, 
            metadata: { excludeCount: excludeQuestionIds.length } 
          });
        }
        query = query.not('id', 'in', `(${excludeQuestionIds.join(',')})`);
      }

      // Execute query with proper timeout handling
      const { data: allQuestions, error } = await query;

      if (error) {
        logDatabaseError('selectRandomQuestions', error, context);
        throw new Error(`Failed to fetch questions: ${error.message}`);
      }

      if (!allQuestions || allQuestions.length === 0) {
        const error = new Error(`No questions found for topic ${topicId}`);
        logRandomizationError('selectRandomQuestions', error, context);
        throw error;
      }

      // Log question pool statistics (simplified since usage tracking columns don't exist)
      errorLogger.logError(
        `Question pool stats: ${allQuestions.length} total questions available`,
        undefined,
        { ...context, metadata: { totalQuestions: allQuestions.length } },
        'info',
        'randomization'
      );

      // Handle insufficient questions with graceful fallback
      if (allQuestions.length < count) {
        logValidationError(
          `Topic ${topicId} has only ${allQuestions.length} questions, less than requested ${count}`,
          { ...context, metadata: { available: allQuestions.length, requested: count } },
          'warning'
        );

        // Apply graceful degradation strategy
        const fallbackStrategy = getInsufficientQuestionsStrategy(allQuestions.length, count, topicId);
        
        if (fallbackStrategy.type === 'graceful_degradation') {
          // Return all available questions
          return this.shuffleArray(allQuestions);
        } else if (fallbackStrategy.type === 'fallback_content') {
          // Still return questions but log the recommendation
          logValidationError(
            `Very few questions available (${allQuestions.length}). Consider adding more content.`,
            { ...context, metadata: { recommendedAction: 'add_more_questions' } },
            'warning'
          );
          return this.shuffleArray(allQuestions);
        }
      }

      // Select questions with improved randomization
      let selectedQuestions: Question[] = [];
      
      try {
        // Use Fisher-Yates shuffle for better randomization
        selectedQuestions = this.shuffleArray([...allQuestions]).slice(0, count);
        
        console.log(`Selected ${selectedQuestions.length} random questions from ${allQuestions.length} available`);
        
        errorLogger.logError(
          `Selected ${selectedQuestions.length} random questions from pool of ${allQuestions.length}`,
          undefined,
          { ...context, metadata: { selectionType: 'random', poolSize: allQuestions.length } },
          'info',
          'randomization'
        );

        // Final validation of selected questions
        if (selectedQuestions.length === 0) {
          const error = new Error('No questions could be selected after filtering');
          logRandomizationError('selectRandomQuestions', error, context);
          throw error;
        }

        // Validate question data integrity
        const invalidQuestions = selectedQuestions.filter(q => 
          !q.question_text || !q.options || !q.correct_answer
        );

        if (invalidQuestions.length > 0) {
          logValidationError(
            `Found ${invalidQuestions.length} questions with invalid data`,
            { ...context, metadata: { invalidQuestionIds: invalidQuestions.map(q => q.id) } },
            'warning'
          );
          
          // Filter out invalid questions
          selectedQuestions = selectedQuestions.filter(q => 
            q.question_text && q.options && q.correct_answer !== null && q.correct_answer !== undefined
          );

          if (selectedQuestions.length === 0) {
            const error = new Error('All selected questions have invalid data');
            logRandomizationError('selectRandomQuestions', error, context);
            throw error;
          }
        }

        return this.shuffleArray(selectedQuestions);

      } catch (selectionError) {
        logRandomizationError('selectRandomQuestions', selectionError as Error, context);
        
        // Fallback: try simple random selection
        try {
          const fallbackQuestions = this.shuffleArray(allQuestions).slice(0, Math.min(count, allQuestions.length));
          logValidationError(
            'Used fallback selection due to error in preferred selection',
            { ...context, metadata: { fallbackCount: fallbackQuestions.length } },
            'warning'
          );
          return fallbackQuestions;
        } catch (fallbackError) {
          logRandomizationError('selectRandomQuestions', fallbackError as Error, { ...context, operation: 'fallback_selection' });
          throw selectionError; // Throw original error
        }
      }

    } catch (error) {
      logRandomizationError('selectRandomQuestions', error as Error, context);
      throw error;
    }
  }

  /**
   * Shuffles answer options while maintaining correct answer mapping
   * @param question - The question to shuffle options for
   * @returns RandomizedQuestion with shuffled options and updated mapping
   */
  static shuffleAnswerOptions(question: Question): RandomizedQuestion {
    const context: ErrorContext = { 
      questionId: question.id, 
      operation: 'shuffleAnswerOptions' 
    };

    try {
      // Validate question data
      if (!question.id) {
        logValidationError('Question missing ID', context, 'warning');
      }

      if (!question.options) {
        const error = new Error('Question has no options to shuffle');
        logValidationError(error.message, context, 'error');
        throw error;
      }

      // Parse the original options and correct answer with enhanced error handling
      let originalOptions: string[];
      try {
        originalOptions = parseQuestionOptions(question.options);
      } catch (parseError) {
        logValidationError(
          `Failed to parse question options: ${parseError}`,
          { ...context, metadata: { originalOptions: question.options } },
          'error'
        );
        throw new Error('Invalid question options format');
      }

      if (originalOptions.length < 2) {
        logValidationError(
          `Question has insufficient options (${originalOptions.length})`,
          { ...context, metadata: { optionCount: originalOptions.length } },
          'warning'
        );
        
        // Don't shuffle if there are fewer than 2 options
        const parsedAnswer = parseCorrectAnswer(question.correct_answer, originalOptions.length);
        return {
          ...question,
          originalCorrectIndex: parsedAnswer.correctIndex,
          shuffledCorrectIndex: parsedAnswer.correctIndex,
          optionMapping: Array.from({ length: originalOptions.length }, (_, i) => i),
          shuffledOptions: question.options as Record<string, string>
        };
      }

      const parsedAnswer = parseCorrectAnswer(question.correct_answer, originalOptions.length);
      
      if (!parsedAnswer.isValid) {
        logValidationError(
          `Question ${question.id}: Invalid correct answer (${parsedAnswer.originalValue}), using fallback index ${parsedAnswer.correctIndex}`,
          { 
            ...context, 
            metadata: { 
              originalCorrectAnswer: parsedAnswer.originalValue,
              fallbackIndex: parsedAnswer.correctIndex,
              errorMessage: parsedAnswer.errorMessage
            } 
          },
          'warning'
        );
      }

      const originalCorrectIndex = parsedAnswer.correctIndex;

      // Validate correct index is within bounds
      if (originalCorrectIndex < 0 || originalCorrectIndex >= originalOptions.length) {
        logValidationError(
          `Correct answer index ${originalCorrectIndex} is out of bounds for ${originalOptions.length} options`,
          { ...context, metadata: { correctIndex: originalCorrectIndex, optionCount: originalOptions.length } },
          'error'
        );
        
        // Use clamped index
        const clampedIndex = Math.max(0, Math.min(originalOptions.length - 1, originalCorrectIndex));
        logValidationError(
          `Clamping correct answer index to ${clampedIndex}`,
          { ...context, metadata: { originalIndex: originalCorrectIndex, clampedIndex } },
          'warning'
        );
      }

      // Create array of indices for shuffling
      const indices = Array.from({ length: originalOptions.length }, (_, i) => i);
      
      // Perform shuffling with error handling
      let shuffledIndices: number[];
      try {
        shuffledIndices = this.shuffleArray(indices);
      } catch (shuffleError) {
        logValidationError(
          `Error during shuffling, using original order: ${shuffleError}`,
          context,
          'warning'
        );
        shuffledIndices = indices; // Use original order as fallback
      }

      // Validate shuffled indices
      if (shuffledIndices.length !== originalOptions.length) {
        logValidationError(
          `Shuffled indices length mismatch: expected ${originalOptions.length}, got ${shuffledIndices.length}`,
          context,
          'error'
        );
        shuffledIndices = indices; // Fallback to original order
      }

      // Create the option mapping (shuffled position -> original position)
      const optionMapping = shuffledIndices;

      // Create shuffled options object with validation
      const shuffledOptions: Record<string, string> = {};
      try {
        shuffledIndices.forEach((originalIndex, shuffledPosition) => {
          if (originalIndex >= 0 && originalIndex < originalOptions.length) {
            shuffledOptions[shuffledPosition.toString()] = originalOptions[originalIndex];
          } else {
            logValidationError(
              `Invalid original index ${originalIndex} during shuffling`,
              { ...context, metadata: { originalIndex, shuffledPosition } },
              'warning'
            );
            shuffledOptions[shuffledPosition.toString()] = `Option ${shuffledPosition + 1}`;
          }
        });
      } catch (mappingError) {
        logValidationError(
          `Error creating option mapping: ${mappingError}`,
          context,
          'error'
        );
        throw new Error('Failed to create shuffled options mapping');
      }

      // Find where the correct answer ended up after shuffling
      const shuffledCorrectIndex = shuffledIndices.indexOf(originalCorrectIndex);

      if (shuffledCorrectIndex === -1) {
        logValidationError(
          `Correct answer index ${originalCorrectIndex} not found in shuffled indices`,
          { ...context, metadata: { originalCorrectIndex, shuffledIndices } },
          'error'
        );
        
        // Emergency fallback: use original order
        return {
          ...question,
          originalCorrectIndex,
          shuffledCorrectIndex: originalCorrectIndex,
          optionMapping: Array.from({ length: originalOptions.length }, (_, i) => i),
          shuffledOptions: question.options as Record<string, string>
        };
      }

      // Log successful shuffling for debugging
      errorLogger.logError(
        `Successfully shuffled options for question ${question.id}`,
        undefined,
        { 
          ...context, 
          metadata: { 
            originalCorrectIndex, 
            shuffledCorrectIndex,
            optionCount: originalOptions.length
          } 
        },
        'info',
        'randomization'
      );

      return {
        ...question,
        originalCorrectIndex,
        shuffledCorrectIndex,
        optionMapping,
        shuffledOptions,
        // Update the options and correct_answer to reflect shuffling
        options: shuffledOptions,
        correct_answer: shuffledCorrectIndex.toString()
      };

    } catch (error) {
      logRandomizationError('shuffleAnswerOptions', error as Error, context);
      
      // Comprehensive fallback: return question with original options and safe defaults
      try {
        const originalOptions = parseQuestionOptions(question.options || {});
        const parsedAnswer = parseCorrectAnswer(question.correct_answer, originalOptions.length);
        
        logValidationError(
          `Using fallback for question ${question.id} due to shuffling error`,
          { ...context, metadata: { fallbackReason: 'shuffling_error' } },
          'warning'
        );
        
        return {
          ...question,
          originalCorrectIndex: parsedAnswer.correctIndex,
          shuffledCorrectIndex: parsedAnswer.correctIndex,
          optionMapping: Array.from({ length: originalOptions.length }, (_, i) => i),
          shuffledOptions: question.options as Record<string, string> || {}
        };
      } catch (fallbackError) {
        logRandomizationError('shuffleAnswerOptions', fallbackError as Error, { ...context, operation: 'fallback' });
        
        // Ultimate fallback with minimal data
        return {
          ...question,
          originalCorrectIndex: 0,
          shuffledCorrectIndex: 0,
          optionMapping: [0, 1, 2, 3],
          shuffledOptions: { '0': 'Option A', '1': 'Option B', '2': 'Option C', '3': 'Option D' }
        };
      }
    }
  }

  /**
   * Generates a complete quiz session with randomized questions and options
   * @param topicId - ID of the topic for the quiz
   * @param userId - ID of the user taking the quiz
   * @param quizLength - Number of questions in the quiz
   * @returns Promise<QuizSessionResult> - Complete quiz session data
   */
  static async generateQuizSession(
    topicId: string,
    userId: string,
    quizLength: number = 15
  ): Promise<QuizSessionResult> {
    const context: ErrorContext = { 
      topicId, 
      userId, 
      operation: 'generateQuizSession',
      metadata: { requestedLength: quizLength }
    };

    try {
      console.log('Starting quiz session generation:', { topicId, userId, quizLength });

      // Comprehensive input validation
      if (!topicId || typeof topicId !== 'string' || topicId.trim() === '') {
        const error = new Error('Valid Topic ID is required');
        console.error('Invalid topic ID:', topicId);
        logSessionError('generateQuizSession', error, context);
        throw error;
      }

      if (!userId || typeof userId !== 'string' || userId.trim() === '') {
        const error = new Error('Valid User ID is required');
        console.error('Invalid user ID:', userId);
        logSessionError('generateQuizSession', error, context);
        throw error;
      }

      if (typeof quizLength !== 'number' || quizLength < 1 || quizLength > 50 || !Number.isInteger(quizLength)) {
        const error = new Error(`Invalid quiz length: ${quizLength}. Must be an integer between 1 and 50`);
        console.error('Invalid quiz length:', quizLength);
        logSessionError('generateQuizSession', error, { ...context, metadata: { ...context.metadata, invalidLength: quizLength } });
        throw error;
      }

      // Get topic statistics with simplified approach (no database functions needed)
      let stats;
      try {
        console.log('Getting topic question count for:', topicId);
        const { data: questionCount, error: countError } = await supabase
          .from('questions')
          .select('id', { count: 'exact' })
          .eq('topic_id', topicId);

        if (countError) {
          throw countError;
        }

        stats = {
          total_questions: questionCount?.length || 0,
          avg_usage_count: 0,
          avg_correct_rate: 0,
          questions_never_used: 0,
          questions_low_performance: 0
        };

        console.log('Topic stats retrieved:', stats);
      } catch (statsError) {
        console.error('Error getting topic stats:', statsError);
        logDatabaseError('getTopicQuestionStats', statsError as Error, context);
        throw new Error('Unable to determine question availability for this topic');
      }
      
      if (stats.total_questions === 0) {
        const error = new Error(`No questions available for topic ${topicId}`);
        logSessionError('generateQuizSession', error, context);
        throw error;
      }

      // Apply intelligent quiz length adjustment with user-friendly messaging
      const effectiveQuizLength = Math.min(quizLength, stats.total_questions);
      
      if (effectiveQuizLength < quizLength) {
        console.log(`Requested ${quizLength} questions but only ${stats.total_questions} available. Adjusting to ${effectiveQuizLength}`);
      }

      // Check if we have sufficient variety for a good quiz experience
      if (stats.total_questions < 10) {
        console.log(`Topic has very few questions (${stats.total_questions}). Quiz experience may be limited`);
      }

      // Select random questions with comprehensive error handling
      let selectedQuestions: Question[];
      try {
        selectedQuestions = await this.selectRandomQuestions({
          topicId,
          count: effectiveQuizLength
        });
      } catch (selectionError) {
        logRandomizationError('generateQuizSession', selectionError as Error, context);
        
        // Try with a smaller count as fallback
        if (effectiveQuizLength > 5) {
          try {
            const fallbackLength = Math.min(5, stats.total_questions);
            console.log(`Retrying with reduced quiz length: ${fallbackLength}`);
            
            selectedQuestions = await this.selectRandomQuestions({
              topicId,
              count: fallbackLength
            });
          } catch (fallbackError) {
            logRandomizationError('generateQuizSession', fallbackError as Error, { ...context, operation: 'fallback_selection' });
            throw selectionError; // Throw original error
          }
        } else {
          throw selectionError;
        }
      }

      if (selectedQuestions.length === 0) {
        const error = new Error(`No questions could be selected for topic ${topicId}`);
        logSessionError('generateQuizSession', error, context);
        throw error;
      }

      // Shuffle answer options for each question with error tracking
      const randomizedQuestions: RandomizedQuestion[] = [];
      const shufflingErrors: string[] = [];

      for (let i = 0; i < selectedQuestions.length; i++) {
        try {
          const randomizedQuestion = this.shuffleAnswerOptions(selectedQuestions[i]);
          randomizedQuestions.push(randomizedQuestion);
        } catch (shuffleError) {
          shufflingErrors.push(selectedQuestions[i].id);
          console.warn(`Failed to shuffle options for question ${selectedQuestions[i].id}, using original order`);
          
          // Add question with original options as fallback
          const originalOptions = parseQuestionOptions(selectedQuestions[i].options);
          const parsedAnswer = parseCorrectAnswer(selectedQuestions[i].correct_answer, originalOptions.length);
          
          randomizedQuestions.push({
            ...selectedQuestions[i],
            originalCorrectIndex: parsedAnswer.correctIndex,
            shuffledCorrectIndex: parsedAnswer.correctIndex,
            optionMapping: Array.from({ length: originalOptions.length }, (_, idx) => idx),
            shuffledOptions: selectedQuestions[i].options as Record<string, string>
          });
        }
      }

      if (shufflingErrors.length > 0) {
        console.warn(`Shuffling failed for ${shufflingErrors.length} questions, used fallback`);
      }

      if (randomizedQuestions.length === 0) {
        const error = new Error('No questions could be processed for the quiz session');
        logSessionError('generateQuizSession', error, context);
        throw error;
      }

      // Create a simplified session object (without database storage for now)
      const sessionId = generateUUID();
      const session = {
        id: sessionId,
        userId,
        topicId,
        questionsData: {
          questions: randomizedQuestions.map(q => ({
            id: q.id,
            originalCorrectIndex: q.originalCorrectIndex,
            shuffledCorrectIndex: q.shuffledCorrectIndex,
            optionMapping: q.optionMapping
          })),
          metadata: {
            totalQuestions: randomizedQuestions.length,
            topicId,
            createdAt: new Date().toISOString(),
            requestedLength: quizLength,
            effectiveLength: randomizedQuestions.length,
            shufflingErrors: shufflingErrors.length
          }
        },
        quizLength: randomizedQuestions.length,
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
        completedAt: null,
        score: null,
        totalQuestions: randomizedQuestions.length,
        timeTaken: null
      };

      console.log(`Successfully created quiz session with ${randomizedQuestions.length} questions`);

      return {
        questions: randomizedQuestions,
        session: session as any,
        sessionId: sessionId,
        topicId,
        userId,
        quizLength: randomizedQuestions.length
      };

    } catch (error) {
      logSessionError('generateQuizSession', error as Error, context);
      
      // Provide user-friendly error message
      const friendlyError = getSessionErrorMessage(error as Error, context);
      
      // Re-throw with enhanced error information
      const enhancedError = new Error(friendlyError.message);
      (enhancedError as any).userFriendly = friendlyError;
      (enhancedError as any).originalError = error;
      
      throw enhancedError;
    }
  }

  /**
   * Retrieves an existing quiz session with reconstructed questions
   * @param sessionId - ID of the quiz session
   * @param userId - ID of the user (for security)
   * @returns Promise<QuizSessionResult | null> - Quiz session data or null if not found
   */
  static async getQuizSession(sessionId: string, userId: string): Promise<QuizSessionResult | null> {
    try {
      // Use QuizSession model to retrieve session
      const session = await QuizSession.findById(sessionId, userId);
      
      if (!session) {
        console.warn(`Quiz session ${sessionId} not found for user ${userId}`);
        return null;
      }

      // Validate session state
      const validation = session.validate();
      if (!validation.isValid) {
        console.warn(`Quiz session ${sessionId} is invalid: ${validation.message}`);
        return null;
      }

      // Reconstruct the randomized questions from stored data
      const questionsData = session.questionsData as any;
      const questionIds = questionsData.questions.map((q: any) => q.id);

      // Fetch the actual question data
      const { data: questions, error: questionsError } = await supabase
        .from('questions')
        .select('*')
        .in('id', questionIds);

      if (questionsError || !questions) {
        throw new Error(`Failed to fetch questions for session: ${questionsError?.message}`);
      }

      // Reconstruct randomized questions with proper order and shuffling
      const randomizedQuestions: RandomizedQuestion[] = questionsData.questions.map((sessionQ: any) => {
        const originalQuestion = questions.find(q => q.id === sessionQ.id);
        if (!originalQuestion) {
          throw new Error(`Question ${sessionQ.id} not found`);
        }

        // Reconstruct shuffled options
        const originalOptions = parseQuestionOptions(originalQuestion.options);
        const shuffledOptions: Record<string, string> = {};
        
        sessionQ.optionMapping.forEach((originalIndex: number, shuffledPosition: number) => {
          shuffledOptions[shuffledPosition.toString()] = originalOptions[originalIndex];
        });

        return {
          ...originalQuestion,
          originalCorrectIndex: sessionQ.originalCorrectIndex,
          shuffledCorrectIndex: sessionQ.shuffledCorrectIndex,
          optionMapping: sessionQ.optionMapping,
          shuffledOptions,
          options: shuffledOptions,
          correct_answer: sessionQ.shuffledCorrectIndex.toString()
        };
      });

      return {
        questions: randomizedQuestions,
        session,
        sessionId: session.id,
        topicId: session.topicId || '',
        userId: session.userId,
        quizLength: session.quizLength
      };

    } catch (error) {
      console.error('Error retrieving quiz session:', error);
      throw error;
    }
  }

  /**
   * Completes a quiz session and records the results
   * @param sessionId - ID of the quiz session
   * @param userId - ID of the user
   * @param score - Final score achieved
   * @param timeTaken - Time taken in seconds
   * @returns Promise<boolean> - Success status
   */
  static async completeQuizSession(
    sessionId: string,
    userId: string,
    score: number,
    timeTaken?: number
  ): Promise<boolean> {
    try {
      // Use QuizSession model for completion
      const session = await QuizSession.findById(sessionId, userId);
      
      if (!session) {
        console.error(`Quiz session ${sessionId} not found for user ${userId}`);
        return false;
      }

      return await session.complete(score, timeTaken);

    } catch (error) {
      console.error('Error in completeQuizSession:', error);
      return false;
    }
  }

  /**
   * Records analytics for a question answer
   * @param questionId - ID of the question
   * @param userId - ID of the user
   * @param sessionId - ID of the quiz session
   * @param answeredCorrectly - Whether the answer was correct
   * @param selectedOption - The option index selected by user
   * @param timeToAnswer - Time taken to answer in seconds
   * @returns Promise<boolean> - Success status
   */
  static async recordQuestionAnalytics(
    questionId: string,
    userId: string,
    sessionId: string,
    answeredCorrectly: boolean,
    selectedOption: number,
    timeToAnswer?: number
  ): Promise<boolean> {
    try {
      // Ensure sessionId is a proper UUID, generate one if not
      const validSessionId = ensureValidUUID(sessionId);

      const { error } = await supabase
        .from('question_analytics')
        .insert({
          question_id: questionId,
          user_id: userId,
          quiz_session_id: validSessionId,
          answered_correctly: answeredCorrectly,
          selected_option: selectedOption,
          time_to_answer: timeToAnswer
        });

      if (error) {
        console.error('Error recording question analytics:', error);
        return false;
      }

      return true;

    } catch (error) {
      console.error('Error in recordQuestionAnalytics:', error);
      return false;
    }
  }

  /**
   * Utility method to shuffle an array using Fisher-Yates algorithm
   * @param array - Array to shuffle
   * @returns Shuffled copy of the array
   */
  private static shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  /**
   * Gets statistics about question pool for a topic
   * @param topicId - ID of the topic
   * @returns Promise with topic question statistics
   */
  static async getTopicQuestionStats(topicId: string) {
    try {
      // Use direct query with only existing columns
      const { data: questions, error } = await supabase
        .from('questions')
        .select('id')
        .eq('topic_id', topicId);

      if (error) {
        console.error('Error fetching topic questions:', error);
        throw error;
      }

      if (!questions) {
        return {
          total_questions: 0,
          avg_usage_count: 0,
          avg_correct_rate: 0,
          questions_never_used: 0,
          questions_low_performance: 0
        };
      }

      const totalQuestions = questions.length;

      // Since usage_count and correct_rate columns don't exist, return basic stats
      return {
        total_questions: totalQuestions,
        avg_usage_count: 0, // Default since column doesn't exist
        avg_correct_rate: 0, // Default since column doesn't exist
        questions_never_used: totalQuestions, // All questions are "never used" since we can't track usage
        questions_low_performance: 0 // Default since we can't calculate performance
      };

    } catch (error) {
      console.error('Error in getTopicQuestionStats:', error);
      throw error;
    }
  }

  /**
   * Validates if a topic has sufficient questions for the requested quiz length
   * @param topicId - ID of the topic
   * @param requestedLength - Requested quiz length
   * @returns Promise with validation result
   */
  static async validateQuizLengthForTopic(
    topicId: string,
    requestedLength: number
  ): Promise<{
    isValid: boolean;
    availableQuestions: number;
    recommendedLength: number;
    message?: string;
    severity: 'info' | 'warning' | 'error';
  }> {
    try {
      const stats = await this.getTopicQuestionStats(topicId);
      const availableQuestions = stats.total_questions;

      // No questions available
      if (availableQuestions === 0) {
        return {
          isValid: false,
          availableQuestions: 0,
          recommendedLength: 0,
          message: 'No questions available for this topic.',
          severity: 'error'
        };
      }

      // Requested length exceeds available questions
      if (requestedLength > availableQuestions) {
        return {
          isValid: true, // Still valid, but will be adjusted
          availableQuestions,
          recommendedLength: availableQuestions,
          message: `Only ${availableQuestions} questions available. Quiz will be adjusted to ${availableQuestions} questions.`,
          severity: 'warning'
        };
      }

      // Limited question pool (less than ideal for variety)
      if (availableQuestions < 20) {
        return {
          isValid: true,
          availableQuestions,
          recommendedLength: requestedLength,
          message: `This topic has limited questions (${availableQuestions}). Consider adding more questions for better variety.`,
          severity: 'info'
        };
      }

      // Sufficient questions available
      return {
        isValid: true,
        availableQuestions,
        recommendedLength: requestedLength,
        severity: 'info'
      };

    } catch (error) {
      console.error('Error validating quiz length for topic:', error);
      return {
        isValid: false,
        availableQuestions: 0,
        recommendedLength: 10,
        message: 'Error checking question availability.',
        severity: 'error'
      };
    }
  }

  /**
   * Gets available quiz length options for a topic
   * @param topicId - ID of the topic
   * @returns Promise with available quiz length options
   */
  static async getAvailableQuizLengths(topicId: string): Promise<{
    options: Array<{
      value: number;
      label: string;
      available: boolean;
      recommended: boolean;
    }>;
    maxLength: number;
  }> {
    try {
      const stats = await this.getTopicQuestionStats(topicId);
      const availableQuestions = stats.total_questions;

      const standardOptions = [10, 15, 20, 25];
      
      const options = standardOptions.map(length => ({
        value: length,
        label: `${length} Questions`,
        available: length <= availableQuestions,
        recommended: length <= availableQuestions && availableQuestions >= length * 1.5 // 50% more questions than quiz length for good variety
      }));

      return {
        options,
        maxLength: availableQuestions
      };

    } catch (error) {
      console.error('Error getting available quiz lengths:', error);
      return {
        options: [],
        maxLength: 0
      };
    }
  }

  /**
   * Cleans up expired quiz sessions
   * @returns Promise<number> - Number of sessions cleaned up
   */
  static async cleanupExpiredSessions(): Promise<number> {
    try {
      return await QuizSession.cleanupExpired();
    } catch (error) {
      console.error('Error in cleanupExpiredSessions:', error);
      return 0;
    }
  }

  /**
   * Gets active sessions for a user
   * @param userId - ID of the user
   * @returns Promise<QuizSession[]> - Array of active sessions
   */
  static async getActiveSessions(userId: string): Promise<QuizSession[]> {
    try {
      return await QuizSession.findActiveSessions(userId);
    } catch (error) {
      console.error('Error getting active sessions:', error);
      return [];
    }
  }

  /**
   * Gets user session statistics
   * @param userId - ID of the user
   * @returns Promise with user session statistics
   */
  static async getUserSessionStats(userId: string) {
    try {
      return await QuizSession.getUserSessionStats(userId);
    } catch (error) {
      console.error('Error getting user session stats:', error);
      throw error;
    }
  }

  /**
   * Extends session expiration time
   * @param sessionId - ID of the quiz session
   * @param userId - ID of the user
   * @param additionalMinutes - Minutes to add to expiration
   * @returns Promise<boolean> - Success status
   */
  static async extendSessionExpiration(
    sessionId: string,
    userId: string,
    additionalMinutes: number = 60
  ): Promise<boolean> {
    try {
      const session = await QuizSession.findById(sessionId, userId);
      
      if (!session) {
        console.error(`Quiz session ${sessionId} not found for user ${userId}`);
        return false;
      }

      return await session.extendExpiration(additionalMinutes);

    } catch (error) {
      console.error('Error extending session expiration:', error);
      return false;
    }
  }
}

export default QuizRandomizationService;