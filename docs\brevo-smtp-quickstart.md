# [DEPRECATED] Brevo SMTP Quick Start Guide

**NOTE: This guide is deprecated. The application now uses Supabase with Resend for email functionality.**

~~This guide provides simple steps to implement Brevo SMTP for Supabase Auth to fix email verification delivery issues.~~

## Current Email Configuration

The application now uses **Supabase with Resend** for all email functionality. This provides:

1. ✅ Reliable email delivery for verification emails
2. ✅ Password reset functionality
3. ✅ Customizable email templates through the Supabase dashboard

## How to Configure Supabase with Resend

1. Log in to your [Supabase Dashboard](https://app.supabase.io)
2. Navigate to Authentication > Email Templates
3. Configure your email templates as needed
4. Ensure Resend is properly configured as your email provider

## Testing Email Functionality

To test if email delivery is working with Supabase and Resend:

1. Use the Supabase dashboard to send a test email
2. Try the password reset or email verification flow in your application
3. Check the Authentication logs in Supabase to verify emails are being sent

## Troubleshooting

If you encounter issues with email delivery:

1. **Check Supabase Authentication logs** for any error messages
2. **Verify Resend configuration** in your Supabase project settings
3. **Test email deliverability** using Resend's testing tools

## Additional Resources

- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [Resend Documentation](https://resend.com/docs)
2. Consider setting up a custom domain for better deliverability
3. Implement email tracking to see open and click rates

## Security Note

The SMTP password is stored in configuration files. For production:

1. Use environment variables instead of hardcoded values
2. Regularly rotate your SMTP keys
3. Ensure config files with credentials are not committed to version control
