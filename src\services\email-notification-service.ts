/**
 * Email Notification Service
 * Uses Supabase with Resend for email notifications
 */

import { FeedbackData } from './feedback-service';

// Admin email for notifications
const ADMIN_EMAIL = import.meta.env.VITE_ADMIN_EMAIL || '<EMAIL>';



/**
 * Send notification using Supabase with Resend
 * @param feedbackData The feedback data to send
 * @returns Promise with success status
 */
export async function sendFeedbackNotificationSupabase(feedbackData: FeedbackData): Promise<{ success: boolean; error?: string }> {
  try {
    // This would call a Supabase Edge Function that handles email sending
    // You would need to create an edge function in your Supabase project
    const response = await fetch('/api/send-feedback-notification', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...feedbackData,
        adminEmail: ADMIN_EMAIL,
        timestamp: new Date().toISOString()
      }),
    });

    if (response.ok) {
      console.log('✅ Feedback notification sent via Supabase Edge Function');
      return { success: true };
    } else {
      console.error('❌ Supabase Edge Function error:', response.statusText);
      return { success: false, error: 'Failed to send notification via Supabase' };
    }

  } catch (error: any) {
    console.error('❌ Error sending notification via Supabase:', error);
    return { success: false, error: error.message || 'Failed to send notification' };
  }
}

/**
 * Send feedback notification using Supabase
 */
export async function sendFeedbackNotificationWithFallback(feedbackData: FeedbackData): Promise<{ success: boolean; error?: string }> {
  try {
    // Use Supabase Edge Function for sending feedback notifications
    const result = await sendFeedbackNotificationSupabase(feedbackData);
    return result;
  } catch (error: any) {
    console.error('Error sending feedback notification:', error);
    return { success: false, error: error.message || 'Failed to send notification' };
  }
}