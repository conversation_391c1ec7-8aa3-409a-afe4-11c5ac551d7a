import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('Setting up feedback table...');

    // Create the feedback table with proper structure
    const createTableSQL = `
      -- Create the feedback table
      CREATE TABLE IF NOT EXISTS public.feedback (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name TEXT NOT NULL,
        email TEXT NOT NULL,
        subject TEXT NOT NULL,
        message TEXT NOT NULL,
        user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
        status TEXT DEFAULT 'new' CHECK (status IN ('new', 'read', 'responded', 'archived')),
        created_at TIMESTAMPTZ DEFAULT now(),
        updated_at TIMESTAMPTZ DEFAULT now()
      );

      -- Enable Row Level Security
      ALTER TABLE public.feedback ENABLE ROW LEVEL SECURITY;

      -- Drop existing policies if they exist
      DROP POLICY IF EXISTS "Anyone can submit feedback" ON public.feedback;
      DROP POLICY IF EXISTS "Users can view their own feedback" ON public.feedback;
      DROP POLICY IF EXISTS "Admins can view all feedback" ON public.feedback;
      DROP POLICY IF EXISTS "Admins can update feedback" ON public.feedback;

      -- Create policies for feedback
      -- Allow anyone (authenticated or anonymous) to submit feedback
      CREATE POLICY "Anyone can submit feedback"
        ON public.feedback
        FOR INSERT
        TO authenticated, anon
        WITH CHECK (true);

      -- Allow users to view their own feedback
      CREATE POLICY "Users can view their own feedback"
        ON public.feedback
        FOR SELECT
        TO authenticated, anon
        USING (auth.uid() = user_id OR user_id IS NULL);

      -- Allow admins to view all feedback
      CREATE POLICY "Admins can view all feedback"
        ON public.feedback
        FOR SELECT
        TO authenticated
        USING (
          auth.uid() IN (
            SELECT id FROM auth.users WHERE raw_user_meta_data->>'is_admin' = 'true'
          )
        );

      -- Allow admins to update feedback status
      CREATE POLICY "Admins can update feedback"
        ON public.feedback
        FOR UPDATE
        TO authenticated
        USING (
          auth.uid() IN (
            SELECT id FROM auth.users WHERE raw_user_meta_data->>'is_admin' = 'true'
          )
        )
        WITH CHECK (
          auth.uid() IN (
            SELECT id FROM auth.users WHERE raw_user_meta_data->>'is_admin' = 'true'
          )
        );

      -- Create indexes for better performance
      CREATE INDEX IF NOT EXISTS idx_feedback_status ON public.feedback(status);
      CREATE INDEX IF NOT EXISTS idx_feedback_user_id ON public.feedback(user_id);
      CREATE INDEX IF NOT EXISTS idx_feedback_created_at ON public.feedback(created_at DESC);
      CREATE INDEX IF NOT EXISTS idx_feedback_email ON public.feedback(email);
    `;

    // Execute the SQL using the service role key
    const { error: createError } = await supabase.rpc('exec', {
      sql: createTableSQL
    });

    if (createError) {
      console.error('Error creating feedback table:', createError);
      
      // Try alternative approach - direct table creation
      const { error: directError } = await supabase
        .from('feedback')
        .select('id')
        .limit(1);

      if (directError && directError.code === 'PGRST106') {
        // Table doesn't exist, try to create it using a different method
        console.log('Table does not exist, attempting direct creation...');
        
        // Use raw SQL execution
        const { data, error } = await supabase.rpc('execute_sql', {
          query: createTableSQL
        });

        if (error) {
          console.error('Failed to create table via execute_sql:', error);
          return res.status(500).json({ 
            error: 'Failed to create feedback table',
            details: error.message 
          });
        }
      }
    }

    // Test if the table is working by inserting a test record
    const testFeedback = {
      name: 'System Test',
      email: '<EMAIL>',
      subject: 'Table Setup Test',
      message: 'This is a test message to verify the feedback table is working correctly.',
      status: 'new'
    };

    const { data: insertData, error: insertError } = await supabase
      .from('feedback')
      .insert(testFeedback)
      .select();

    if (insertError) {
      console.error('Error inserting test feedback:', insertError);
      return res.status(500).json({ 
        error: 'Feedback table created but insert test failed',
        details: insertError.message 
      });
    }

    console.log('Test feedback inserted successfully:', insertData);

    // Clean up the test record
    if (insertData && insertData.length > 0) {
      await supabase
        .from('feedback')
        .delete()
        .eq('id', insertData[0].id);
    }

    // Verify we can read from the table
    const { data: readData, error: readError } = await supabase
      .from('feedback')
      .select('*')
      .limit(5);

    if (readError) {
      console.error('Error reading from feedback table:', readError);
      return res.status(500).json({ 
        error: 'Feedback table created but read test failed',
        details: readError.message 
      });
    }

    console.log('Feedback table setup completed successfully');

    return res.status(200).json({
      success: true,
      message: 'Feedback table setup completed successfully',
      tableExists: true,
      canInsert: true,
      canRead: true,
      recordCount: readData?.length || 0
    });

  } catch (error) {
    console.error('Error in feedback table setup:', error);
    return res.status(500).json({ 
      error: 'Internal server error during table setup',
      details: error.message 
    });
  }
}
