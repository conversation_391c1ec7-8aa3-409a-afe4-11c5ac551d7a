-- STEP-BY-STEP ADMIN DASHBOARD FIX
-- Run these commands one by one in Supabase SQL Editor

-- STEP 1: Drop all existing functions that might conflict
DROP FUNCTION IF EXISTS public.get_all_user_profiles() CASCADE;
DROP FUNCTION IF EXISTS public.delete_user(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.is_admin() CASCADE;
DROP FUNCTION IF EXISTS public.execute_sql(text) CASCADE;

-- STEP 2: Drop and recreate user_profiles table
DROP TABLE IF EXISTS public.user_profiles CASCADE;

CREATE TABLE public.user_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
  email TEXT,
  full_name TEXT,
  is_subscribed BOOLEAN DEFAULT false,
  is_admin BOOLEAN DEFAULT false,
  subscription_expires_at TIMESTAMPTZ,
  subscription_status TEXT DEFAULT 'free',
  subscription_plan TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- STEP 3: Enable Row Level Security
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- STEP 4: Create RLS policies
CREATE POLICY "Users can view their own profile"
  ON public.user_profiles
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own profile"
  ON public.user_profiles
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all profiles"
  ON public.user_profiles
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles
      WHERE user_id = auth.uid() AND is_admin = true
    )
  );

CREATE POLICY "Admins can update all profiles"
  ON public.user_profiles
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles
      WHERE user_id = auth.uid() AND is_admin = true
    )
  );

CREATE POLICY "Admins can insert profiles"
  ON public.user_profiles
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.user_profiles
      WHERE user_id = auth.uid() AND is_admin = true
    )
  );

-- STEP 5: Create updated_at trigger
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_user_profiles_updated_at
  BEFORE UPDATE ON public.user_profiles
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_updated_at();

-- STEP 6: Backfill all existing users
INSERT INTO public.user_profiles (user_id, email, full_name, is_subscribed, is_admin)
SELECT 
  au.id as user_id,
  au.email,
  au.raw_user_meta_data->>'full_name' as full_name,
  false as is_subscribed,
  false as is_admin
FROM auth.users au
ON CONFLICT (user_id) DO UPDATE SET
  email = EXCLUDED.email,
  full_name = EXCLUDED.full_name,
  updated_at = now();

-- STEP 7: Grant admin privileges to specific emails
UPDATE public.user_profiles
SET is_admin = true, updated_at = now()
WHERE user_id IN (
  SELECT id FROM auth.users
  WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')
);

-- STEP 8: Grant premium access to specific users
UPDATE public.user_profiles
SET 
  is_subscribed = true,
  subscription_expires_at = now() + interval '1 year',
  subscription_status = 'active',
  subscription_plan = 'elite',
  updated_at = now()
WHERE user_id IN (
  SELECT id FROM auth.users
  WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')
);

-- STEP 9: Create is_admin function
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_profiles 
    WHERE user_id = auth.uid() AND is_admin = true
  );
END;
$$ LANGUAGE plpgsql;

-- STEP 10: Create get_all_user_profiles function
CREATE OR REPLACE FUNCTION public.get_all_user_profiles()
RETURNS TABLE (
  id UUID,
  user_id UUID,
  email TEXT,
  full_name TEXT,
  is_subscribed BOOLEAN,
  is_admin BOOLEAN,
  subscription_expires_at TIMESTAMPTZ,
  subscription_status TEXT,
  subscription_plan TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  last_sign_in_at TIMESTAMPTZ
) AS $$
BEGIN
  -- Check if current user is admin
  IF NOT EXISTS (
    SELECT 1 FROM public.user_profiles
    WHERE user_id = auth.uid() AND is_admin = true
  ) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;

  -- Return all user profiles with auth data
  RETURN QUERY
  SELECT
    up.id,
    up.user_id,
    up.email,
    up.full_name,
    up.is_subscribed,
    up.is_admin,
    up.subscription_expires_at,
    up.subscription_status,
    up.subscription_plan,
    up.created_at,
    up.updated_at,
    au.last_sign_in_at
  FROM public.user_profiles up
  LEFT JOIN auth.users au ON up.user_id = au.id
  ORDER BY up.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- STEP 11: Create delete_user function
CREATE OR REPLACE FUNCTION public.delete_user(user_id UUID)
RETURNS JSON
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
BEGIN
  -- Check if current user is admin
  IF NOT EXISTS (
    SELECT 1 FROM public.user_profiles
    WHERE user_id = auth.uid() AND is_admin = true
  ) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;

  -- Delete user and related data
  DELETE FROM public.user_profiles WHERE user_profiles.user_id = delete_user.user_id;
  
  result := json_build_object('success', true, 'message', 'User data deleted successfully');
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- STEP 12: Create execute_sql function for admin tools
CREATE OR REPLACE FUNCTION public.execute_sql(query text)
RETURNS JSON
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
BEGIN
  -- Check if current user is admin
  IF NOT EXISTS (
    SELECT 1 FROM public.user_profiles
    WHERE user_id = auth.uid() AND is_admin = true
  ) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;

  -- Execute the query
  EXECUTE query;
  
  result := json_build_object('success', true, 'message', 'Query executed successfully');
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- STEP 13: Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_all_user_profiles() TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION public.delete_user(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.execute_sql(text) TO authenticated;

-- STEP 14: Create triggers for new users and email sync
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (user_id, email, is_subscribed, is_admin)
  VALUES (NEW.id, NEW.email, false, false)
  ON CONFLICT (user_id) DO UPDATE SET
    email = EXCLUDED.email,
    updated_at = now();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

CREATE OR REPLACE FUNCTION public.sync_user_email()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE public.user_profiles
  SET email = NEW.email, updated_at = now()
  WHERE user_id = NEW.id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

DROP TRIGGER IF EXISTS sync_user_email_trigger ON auth.users;
CREATE TRIGGER sync_user_email_trigger
  AFTER UPDATE OF email ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.sync_user_email();

-- VERIFICATION: Check if everything was created successfully
SELECT 'SUCCESS: user_profiles table created' as status, count(*) as user_count FROM public.user_profiles;
SELECT 'SUCCESS: Admin users found' as status, email FROM public.user_profiles up JOIN auth.users au ON up.user_id = au.id WHERE up.is_admin = true;
SELECT 'SUCCESS: Functions created' as status, proname FROM pg_proc WHERE proname IN ('get_all_user_profiles', 'is_admin', 'delete_user', 'execute_sql');
