import express, { Request, Response } from 'express';
import axios, { AxiosError } from 'axios';
import { updateUserSubscription } from '../services/subscription.js';
import { configManager } from '../lib/config.js';
import { createValidationMiddleware, PAYMENT_VALIDATION_RULES } from '../lib/input-validator.js';
import { secureLogger } from '../lib/secure-logger.js';
import { paymentEventLogger, PaymentEventType } from '../lib/payment-event-logger.js';

const router = express.Router();
const paystackConfig = configManager.getPaystackConfig();

// Standardized API response interface
interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}

// Create standardized response helper
function createResponse<T>(
  success: boolean,
  message: string,
  data?: T,
  error?: { code: string; message: string; details?: any }
): ApiResponse<T> {
  return {
    success,
    message,
    data,
    error,
    timestamp: new Date().toISOString()
  };
}

// Sanitize sensitive data for logging
function sanitizeForLogging(data: any): any {
  if (!data || typeof data !== 'object') return data;
  
  const sanitized = { ...data };
  const sensitiveFields = ['authorization', 'customer', 'metadata'];
  
  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      if (field === 'customer' && sanitized[field].email) {
        // Keep email but mask it partially
        const email = sanitized[field].email;
        const [localPart, domain] = email.split('@');
        sanitized[field] = {
          ...sanitized[field],
          email: `${localPart.substring(0, 2)}***@${domain}`
        };
      } else if (field === 'authorization') {
        sanitized[field] = '[REDACTED]';
      } else if (field === 'metadata') {
        // Keep metadata structure but remove sensitive values
        sanitized[field] = Object.keys(sanitized[field]).reduce((acc, key) => {
          acc[key] = typeof sanitized[field][key] === 'string' ? '[REDACTED]' : sanitized[field][key];
          return acc;
        }, {} as any);
      }
    }
  });
  
  return sanitized;
}

// Endpoint to verify a payment
router.post('/verify', createValidationMiddleware(PAYMENT_VALIDATION_RULES), async (req: Request, res: Response) => {
  const startTime = Date.now();
  const logContext = { reference: '', operation: 'payment_verification' };

  try {
    const { reference } = req.body;
    logContext.reference = reference || 'unknown';

    // Input validation
    if (!reference || typeof reference !== 'string' || reference.trim().length === 0) {
      console.warn(`Payment verification failed: Invalid reference provided`, { 
        reference: reference || 'undefined',
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      
      return res.status(400).json(
        createResponse(false, 'Payment reference is required and must be a valid string', undefined, {
          code: 'INVALID_REFERENCE',
          message: 'Payment reference is required and must be a valid string'
        })
      );
    }

    // Validate Paystack configuration
    if (!paystackConfig.secretKey) {
      console.error('Payment verification failed: Paystack secret key not configured', logContext);
      
      return res.status(500).json(
        createResponse(false, 'Payment service is temporarily unavailable', undefined, {
          code: 'SERVICE_UNAVAILABLE',
          message: 'Payment service configuration error'
        })
      );
    }

    console.info(`Starting payment verification for reference: ${reference}`, logContext);
    
    // Log payment verification start
    await paymentEventLogger.logEvent({
      event_type: PaymentEventType.PAYMENT_VERIFICATION_STARTED,
      severity: 'info',
      reference,
      ip_address: req.ip,
      user_agent: req.get('User-Agent'),
      metadata: { operation: 'payment_verification' }
    });

    // Call Paystack API to verify the transaction with enhanced error handling
    let paystackResponse;
    try {
      paystackResponse = await axios.get(
        `https://api.paystack.co/transaction/verify/${encodeURIComponent(reference)}`,
        {
          headers: {
            Authorization: `Bearer ${paystackConfig.secretKey}`,
            'Content-Type': 'application/json',
            'User-Agent': 'SecQuiz/1.0'
          },
          timeout: 15000, // Reduced timeout to 15 seconds for better UX
          validateStatus: (status) => status < 500 // Don't throw on 4xx errors
        }
      );
    } catch (error) {
      const axiosError = error as AxiosError;
      
      if (axiosError.code === 'ECONNABORTED' || axiosError.code === 'ETIMEDOUT') {
        console.error('Payment verification timeout', { 
          ...logContext, 
          timeout: '15000ms',
          error: axiosError.message 
        });
        
        // Log payment timeout
        await paymentEventLogger.logEvent({
          event_type: PaymentEventType.PAYMENT_TIMEOUT,
          severity: 'error',
          reference,
          error_code: 'VERIFICATION_TIMEOUT',
          error_message: 'Payment service took too long to respond',
          processing_time_ms: Date.now() - startTime,
          metadata: { timeout_ms: 15000 }
        });
        
        return res.status(504).json(
          createResponse(false, 'Payment verification timed out. Please try again.', undefined, {
            code: 'VERIFICATION_TIMEOUT',
            message: 'Payment service took too long to respond'
          })
        );
      }

      if (axiosError.response) {
        const status = axiosError.response.status;
        const responseData = axiosError.response.data;
        
        console.error('Paystack API error', {
          ...logContext,
          status,
          response: sanitizeForLogging(responseData)
        });

        if (status === 404) {
          return res.status(404).json(
            createResponse(false, 'Payment reference not found', undefined, {
              code: 'REFERENCE_NOT_FOUND',
              message: 'The provided payment reference does not exist'
            })
          );
        }

        if (status === 401) {
          return res.status(500).json(
            createResponse(false, 'Payment service authentication failed', undefined, {
              code: 'SERVICE_AUTH_ERROR',
              message: 'Payment service authentication error'
            })
          );
        }

        return res.status(502).json(
          createResponse(false, 'Payment verification service error', undefined, {
            code: 'EXTERNAL_SERVICE_ERROR',
            message: 'Payment service returned an error'
          })
        );
      }

      console.error('Network error during payment verification', {
        ...logContext,
        error: axiosError.message,
        code: axiosError.code
      });

      return res.status(503).json(
        createResponse(false, 'Payment verification service unavailable', undefined, {
          code: 'SERVICE_UNAVAILABLE',
          message: 'Unable to connect to payment service'
        })
      );
    }

    // Check if Paystack returned an error status
    if (paystackResponse.status >= 400) {
      console.warn('Paystack returned error status', {
        ...logContext,
        status: paystackResponse.status,
        response: sanitizeForLogging(paystackResponse.data)
      });

      return res.status(400).json(
        createResponse(false, 'Payment verification failed', undefined, {
          code: 'VERIFICATION_FAILED',
          message: 'Payment could not be verified'
        })
      );
    }

    const { data: transactionData } = paystackResponse.data;

    if (!transactionData) {
      console.error('Invalid response from Paystack - missing transaction data', {
        ...logContext,
        response: sanitizeForLogging(paystackResponse.data)
      });

      return res.status(502).json(
        createResponse(false, 'Invalid response from payment service', undefined, {
          code: 'INVALID_RESPONSE',
          message: 'Payment service returned invalid data'
        })
      );
    }

    // Check if payment was successful
    if (transactionData.status === 'success') {
      console.info('Payment verification successful', {
        ...logContext,
        amount: transactionData.amount,
        currency: transactionData.currency,
        customer: transactionData.customer?.email ? 
          transactionData.customer.email.replace(/(.{2}).*@/, '$1***@') : 'unknown'
      });

      // Extract and validate payment details
      const { amount, customer, metadata } = transactionData;

      if (!customer?.email) {
        console.error('Payment verification failed: Missing customer email', {
          ...logContext,
          transactionData: sanitizeForLogging(transactionData)
        });

        return res.status(400).json(
          createResponse(false, 'Invalid payment data - missing customer information', undefined, {
            code: 'INVALID_PAYMENT_DATA',
            message: 'Payment data is missing required customer information'
          })
        );
      }

      if (!amount || amount <= 0) {
        console.error('Payment verification failed: Invalid amount', {
          ...logContext,
          amount,
          customer: customer.email.replace(/(.{2}).*@/, '$1***@')
        });

        return res.status(400).json(
          createResponse(false, 'Invalid payment data - invalid amount', undefined, {
            code: 'INVALID_AMOUNT',
            message: 'Payment amount is invalid'
          })
        );
      }

      // Find the plan from metadata with multiple fallbacks
      let planId: string | null = null;

      if (metadata) {
        // Check if metadata has custom_fields
        if (metadata.custom_fields && Array.isArray(metadata.custom_fields)) {
          const planField = metadata.custom_fields.find(
            (field: { variable_name: string; value: string }) => field.variable_name === 'plan'
          );
          planId = planField?.value || null;
        }

        // Check if metadata has plan directly
        if (!planId && metadata.plan) {
          planId = metadata.plan;
        }

        // Check if metadata has subscription_type
        if (!planId && metadata.subscription_type) {
          planId = metadata.subscription_type;
        }
      }

      // Default to pro if no plan specified
      if (!planId) {
        planId = 'pro';
        console.warn('No plan specified in metadata, defaulting to pro', {
          ...logContext,
          customer: customer.email.replace(/(.{2}).*@/, '$1***@'),
          metadata: sanitizeForLogging(metadata)
        });
      }

      console.info('Processing subscription update', {
        ...logContext,
        planId,
        amount,
        customer: customer.email.replace(/(.{2}).*@/, '$1***@')
      });

      // Update user subscription in database
      try {
        const subscriptionResult = await updateUserSubscription(customer.email, planId, amount, reference);

        if (subscriptionResult.success) {
          const processingTime = Date.now() - startTime;
          console.info('Payment verification completed successfully', {
            ...logContext,
            processingTimeMs: processingTime,
            subscriptionData: subscriptionResult.data
          });

          // Log successful payment
          await paymentEventLogger.logPaymentSuccess({
            reference,
            userId: subscriptionResult.data?.userId,
            planId,
            amount,
            currency: 'NGN',
            processingTime,
            metadata: {
              subscription_id: subscriptionResult.data?.subscriptionId,
              payment_id: subscriptionResult.data?.paymentId
            }
          });

          return res.status(200).json(
            createResponse(true, 'Payment verified and subscription updated successfully', {
              reference,
              planId,
              amount: amount / 100, // Convert to main currency unit
              subscription: subscriptionResult.data,
              processingTime: `${processingTime}ms`
            })
          );
        } else {
          console.error('Subscription update failed after successful payment verification', {
            ...logContext,
            subscriptionError: subscriptionResult.error,
            customer: customer.email.replace(/(.{2}).*@/, '$1***@')
          });

          // Log subscription update failure
          await paymentEventLogger.logPaymentFailure({
            reference,
            planId,
            amount,
            errorCode: 'SUBSCRIPTION_UPDATE_FAILED',
            errorMessage: subscriptionResult.error || 'Failed to update subscription',
            processingTime: Date.now() - startTime,
            metadata: {
              customer_email: customer.email.replace(/(.{2}).*@/, '$1***@')
            }
          });

          return res.status(500).json(
            createResponse(false, 'Payment verified but subscription update failed', undefined, {
              code: 'SUBSCRIPTION_UPDATE_FAILED',
              message: subscriptionResult.error || 'Failed to update subscription',
              details: { reference, planId }
            })
          );
        }
      } catch (subscriptionError) {
        console.error('Subscription service error', {
          ...logContext,
          error: subscriptionError instanceof Error ? subscriptionError.message : 'Unknown error',
          customer: customer.email.replace(/(.{2}).*@/, '$1***@')
        });

        return res.status(500).json(
          createResponse(false, 'Subscription service error', undefined, {
            code: 'SUBSCRIPTION_SERVICE_ERROR',
            message: 'Failed to process subscription update'
          })
        );
      }
    } else {
      console.warn('Payment verification failed - transaction not successful', {
        ...logContext,
        transactionStatus: transactionData.status,
        gateway_response: transactionData.gateway_response
      });

      return res.status(400).json(
        createResponse(false, 'Payment was not successful', {
          reference,
          status: transactionData.status,
          gateway_response: transactionData.gateway_response
        }, {
          code: 'PAYMENT_NOT_SUCCESSFUL',
          message: transactionData.gateway_response || 'Payment was not completed successfully'
        })
      );
    }
  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error('Unexpected error during payment verification', {
      ...logContext,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      processingTimeMs: processingTime
    });

    return res.status(500).json(
      createResponse(false, 'An unexpected error occurred during payment verification', undefined, {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Please try again later or contact support if the problem persists'
      })
    );
  }
});

export default router;
