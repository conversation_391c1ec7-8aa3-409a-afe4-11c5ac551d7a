import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { 
  parseQuestionCSVEnhanced, 
  resolveTopicsFromCSV,
  type ImportConfig,
  type MultiTopicQuestionCSVRow
} from '@/utils/csv-import';
import { topicService } from '../topic-service';

// Mock the supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(),
  },
}));

// Mock uuid
vi.mock('uuid', () => ({
  v4: vi.fn(() => 'mock-uuid-123'),
}));

// Mock Papa Parse
vi.mock('papaparse', () => ({
  default: {
    parse: vi.fn(),
    unparse: vi.fn(),
  },
}));

describe('Topic-CSV Integration', () => {
  let mockSupabase: any;
  let mockPapa: any;

  beforeEach(async () => {
    // Import the mocked modules
    const { supabase } = await import('@/integrations/supabase/client');
    const Papa = (await import('papaparse')).default;
    
    mockSupabase = supabase;
    mockPapa = Papa;
    
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('resolveTopicsFromCSV', () => {
    it('should extract and resolve topic references from CSV data', async () => {
      const csvData: MultiTopicQuestionCSVRow[] = [
        {
          topic_name: 'Security Fundamentals',
          question_text: 'What is a firewall?',
          option_a: 'Physical barrier',
          option_b: 'Network traffic controller',
          option_c: 'Data encryptor',
          option_d: 'Malware detector',
          correct_answer: 'B',
          explanation: 'Controls network traffic flow',
          difficulty: 'medium',
        },
        {
          topic_name: 'Network Security',
          question_text: 'What is VPN?',
          option_a: 'Virtual Private Network',
          option_b: 'Very Personal Network',
          option_c: 'Verified Public Network',
          option_d: 'Variable Protocol Network',
          correct_answer: 'A',
          explanation: 'Creates secure tunnel',
          difficulty: 'easy',
        },
        {
          topic_name: 'Security Fundamentals', // Duplicate - should be deduplicated
          question_text: 'What is encryption?',
          option_a: 'Data scrambling',
          option_b: 'Data compression',
          option_c: 'Data backup',
          option_d: 'Data deletion',
          correct_answer: 'A',
          explanation: 'Scrambles data for security',
          difficulty: 'hard',
        },
      ];

      // Mock the topic service to return resolved topics
      vi.spyOn(topicService, 'resolveTopicReferences').mockResolvedValue({
        resolved: new Map([
          ['Security Fundamentals', 'topic-123'],
          ['Network Security', 'topic-456'],
        ]),
        missing: [],
        created: [],
        errors: [],
      });

      const result = await resolveTopicsFromCSV(csvData, false);

      expect(result.resolved.size).toBe(2);
      expect(result.resolved.get('Security Fundamentals')).toBe('topic-123');
      expect(result.resolved.get('Network Security')).toBe('topic-456');
      expect(result.missing).toEqual([]);
      expect(result.created).toEqual([]);
      expect(result.errors).toEqual([]);

      // Verify that topic service was called with unique references
      expect(topicService.resolveTopicReferences).toHaveBeenCalledWith(
        ['Security Fundamentals', 'Network Security'],
        false
      );
    });

    it('should handle CSV data with topic_id references', async () => {
      const csvData: MultiTopicQuestionCSVRow[] = [
        {
          topic_id: 'existing-topic-123',
          question_text: 'What is a firewall?',
          option_a: 'Physical barrier',
          option_b: 'Network traffic controller',
          option_c: 'Data encryptor',
          option_d: 'Malware detector',
          correct_answer: 'B',
          explanation: 'Controls network traffic flow',
          difficulty: 'medium',
        },
      ];

      vi.spyOn(topicService, 'resolveTopicReferences').mockResolvedValue({
        resolved: new Map([
          ['existing-topic-123', 'existing-topic-123'],
        ]),
        missing: [],
        created: [],
        errors: [],
      });

      const result = await resolveTopicsFromCSV(csvData, false);

      expect(result.resolved.size).toBe(1);
      expect(result.resolved.get('existing-topic-123')).toBe('existing-topic-123');
      expect(topicService.resolveTopicReferences).toHaveBeenCalledWith(
        ['existing-topic-123'],
        false
      );
    });

    it('should prefer topic_name over topic_id when both are present', async () => {
      const csvData: MultiTopicQuestionCSVRow[] = [
        {
          topic_name: 'Security Fundamentals',
          topic_id: 'different-topic-456',
          question_text: 'What is preferred?',
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Test explanation',
          difficulty: 'easy',
        },
      ];

      vi.spyOn(topicService, 'resolveTopicReferences').mockResolvedValue({
        resolved: new Map([
          ['Security Fundamentals', 'topic-123'],
        ]),
        missing: [],
        created: [],
        errors: [],
      });

      const result = await resolveTopicsFromCSV(csvData, false);

      expect(topicService.resolveTopicReferences).toHaveBeenCalledWith(
        ['Security Fundamentals'], // Only topic_name should be used
        false
      );
    });

    it('should handle empty topic references', async () => {
      const csvData: MultiTopicQuestionCSVRow[] = [
        {
          topic_name: '',
          topic_id: '   ',
          question_text: 'What has no topic?',
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Test explanation',
          difficulty: 'easy',
        },
        {
          question_text: 'What has no topic columns?',
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Test explanation',
          difficulty: 'easy',
        },
      ];

      vi.spyOn(topicService, 'resolveTopicReferences').mockResolvedValue({
        resolved: new Map(),
        missing: [],
        created: [],
        errors: [],
      });

      const result = await resolveTopicsFromCSV(csvData, false);

      expect(result.resolved.size).toBe(0);
      expect(topicService.resolveTopicReferences).toHaveBeenCalledWith(
        [], // No valid references
        false
      );
    });

    it('should enable auto-creation when requested', async () => {
      const csvData: MultiTopicQuestionCSVRow[] = [
        {
          topic_name: 'New Topic',
          question_text: 'What is new?',
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Test explanation',
          difficulty: 'easy',
        },
      ];

      vi.spyOn(topicService, 'resolveTopicReferences').mockResolvedValue({
        resolved: new Map([
          ['New Topic', 'new-topic-123'],
        ]),
        missing: [],
        created: ['New Topic'],
        errors: [],
      });

      const result = await resolveTopicsFromCSV(csvData, true);

      expect(result.created).toEqual(['New Topic']);
      expect(topicService.resolveTopicReferences).toHaveBeenCalledWith(
        ['New Topic'],
        true // Auto-create enabled
      );
    });
  });

  describe('Full Integration with parseQuestionCSVEnhanced', () => {
    it('should successfully process a complete multi-topic CSV workflow', async () => {
      const config: ImportConfig = {
        mode: 'multi-topic',
        autoCreateTopics: true,
      };

      const mockCSVData = [
        {
          topic_name: 'Existing Topic',
          question_text: 'What exists?',
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Test explanation',
          difficulty: 'easy',
        },
        {
          topic_name: 'New Topic',
          question_text: 'What is new?',
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'B',
          explanation: 'Another test explanation',
          difficulty: 'medium',
        },
      ];

      // Mock topic resolution to simulate one existing and one new topic
      vi.spyOn(topicService, 'resolveTopicReferences').mockResolvedValue({
        resolved: new Map([
          ['Existing Topic', 'existing-topic-123'],
          ['New Topic', 'new-topic-456'],
        ]),
        missing: [],
        created: ['New Topic'],
        errors: [],
      });

      mockPapa.parse.mockImplementation((file, options) => {
        options.complete({ data: mockCSVData });
      });

      const file = new File([''], 'test.csv', { type: 'text/csv' });
      const result = await parseQuestionCSVEnhanced(file, config);

      expect(result.success).toBe(true);
      expect(result.totalRows).toBe(2);
      expect(result.topicResults.size).toBe(2);
      expect(result.newTopicsCreated).toEqual(['New Topic']);
      expect(result.globalErrors).toHaveLength(0);

      // Check existing topic result
      const existingTopicResult = result.topicResults.get('existing-topic-123')!;
      expect(existingTopicResult.topicName).toBe('Existing Topic');
      expect(existingTopicResult.isNewTopic).toBe(false);
      expect(existingTopicResult.validQuestions).toHaveLength(1);
      expect(existingTopicResult.validQuestions[0].question_text).toBe('What exists?');

      // Check new topic result
      const newTopicResult = result.topicResults.get('new-topic-456')!;
      expect(newTopicResult.topicName).toBe('New Topic');
      expect(newTopicResult.isNewTopic).toBe(true);
      expect(newTopicResult.validQuestions).toHaveLength(1);
      expect(newTopicResult.validQuestions[0].question_text).toBe('What is new?');
    });

    it('should handle mixed success and failure scenarios', async () => {
      const config: ImportConfig = {
        mode: 'multi-topic',
        autoCreateTopics: false,
      };

      const mockCSVData = [
        {
          topic_name: 'Valid Topic',
          question_text: 'What is valid?',
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Test explanation',
          difficulty: 'easy',
        },
        {
          topic_name: 'Missing Topic',
          question_text: 'What is missing?',
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'B',
          explanation: 'Another test explanation',
          difficulty: 'medium',
        },
        {
          topic_name: 'Valid Topic',
          question_text: '', // Invalid question
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'C',
          explanation: 'Invalid question explanation',
          difficulty: 'hard',
        },
      ];

      // Mock topic resolution with one missing topic
      vi.spyOn(topicService, 'resolveTopicReferences').mockResolvedValue({
        resolved: new Map([
          ['Valid Topic', 'valid-topic-123'],
        ]),
        missing: ['Missing Topic'],
        created: [],
        errors: [],
      });

      mockPapa.parse.mockImplementation((file, options) => {
        options.complete({ data: mockCSVData });
      });

      const file = new File([''], 'test.csv', { type: 'text/csv' });
      const result = await parseQuestionCSVEnhanced(file, config);

      expect(result.success).toBe(true); // Still successful because we have valid questions
      expect(result.totalRows).toBe(3);
      expect(result.topicResults.size).toBe(1); // Only valid topic processed
      expect(result.globalErrors).toHaveLength(1); // Missing topic error
      expect(result.globalErrors[0].message).toContain('Cannot resolve topic reference: "Missing Topic"');

      // Check valid topic result
      const validTopicResult = result.topicResults.get('valid-topic-123')!;
      expect(validTopicResult.validQuestions).toHaveLength(1); // One valid question
      expect(validTopicResult.errors).toHaveLength(1); // One invalid question
      expect(validTopicResult.errors[0].message).toContain('Missing required field "question_text"');
    });

    it('should handle topic service errors gracefully', async () => {
      const config: ImportConfig = {
        mode: 'multi-topic',
        autoCreateTopics: true,
      };

      const mockCSVData = [
        {
          topic_name: 'Problem Topic',
          question_text: 'What has problems?',
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Test explanation',
          difficulty: 'easy',
        },
      ];

      // Mock topic resolution with errors
      vi.spyOn(topicService, 'resolveTopicReferences').mockResolvedValue({
        resolved: new Map(),
        missing: ['Problem Topic'],
        created: [],
        errors: ['Failed to create topic "Problem Topic": Database connection failed'],
      });

      mockPapa.parse.mockImplementation((file, options) => {
        options.complete({ data: mockCSVData });
      });

      const file = new File([''], 'test.csv', { type: 'text/csv' });
      const result = await parseQuestionCSVEnhanced(file, config);

      expect(result.success).toBe(false);
      expect(result.topicResults.size).toBe(0);
      expect(result.globalErrors).toHaveLength(2);
      expect(result.globalErrors[0].message).toContain('Failed to create topic "Problem Topic": Database connection failed');
      expect(result.globalErrors[1].message).toContain('Cannot resolve topic reference: "Problem Topic"');
    });
  });
});