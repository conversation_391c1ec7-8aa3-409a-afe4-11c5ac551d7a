-- Create atomic subscription functions for payment processing
-- This migration creates database functions to handle subscription operations atomically

-- Function 1: Handle payment success atomically
-- This function processes a successful payment and updates all related tables in a single transaction
CREATE OR REPLACE FUNCTION public.handle_payment_success_atomic(
  p_user_id UUID,
  p_plan_id TEXT,
  p_amount DECIMAL,
  p_reference TEXT,
  p_currency TEXT DEFAULT 'NGN'
)
RETURNS JSONB AS $$
DECLARE
  subscription_id UUID;
  payment_id UUID;
  start_date TIMESTAMPTZ;
  end_date TIMESTAMPTZ;
  result JSONB;
BEGIN
  -- Validate input parameters
  IF p_user_id IS NULL OR p_plan_id IS NULL OR p_amount IS NULL OR p_reference IS NULL THEN
    RETURN jsonb_build_object(
      'success', false, 
      'error', 'Missing required parameters'
    );
  END IF;

  -- Check if user exists
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) THEN
    RETURN jsonb_build_object(
      'success', false, 
      'error', 'User not found'
    );
  END IF;

  -- Check if payment reference already exists to prevent duplicate processing
  IF EXISTS (SELECT 1 FROM public.payments WHERE provider_payment_id = p_reference) THEN
    RETURN jsonb_build_object(
      'success', false, 
      'error', 'Payment reference already processed'
    );
  END IF;

  -- Calculate subscription dates based on plan
  start_date := NOW();
  CASE p_plan_id
    WHEN 'basic' THEN end_date := start_date + INTERVAL '1 month';
    WHEN 'pro' THEN end_date := start_date + INTERVAL '3 months';
    WHEN 'elite' THEN end_date := start_date + INTERVAL '1 year';
    WHEN 'premium' THEN end_date := start_date + INTERVAL '6 months';
    ELSE end_date := start_date + INTERVAL '1 month';
  END CASE;

  -- Start transaction (implicit in function)
  BEGIN
    -- 1. Record the payment
    INSERT INTO public.payments (
      user_id, 
      amount, 
      currency,
      status, 
      provider, 
      provider_payment_id, 
      metadata,
      created_at,
      updated_at
    ) VALUES (
      p_user_id, 
      p_amount, 
      p_currency,
      'completed', 
      'paystack',
      p_reference,
      jsonb_build_object(
        'plan_id', p_plan_id, 
        'reference', p_reference,
        'processed_at', NOW()
      ),
      NOW(),
      NOW()
    ) RETURNING id INTO payment_id;

    -- 2. Create or update subscription
    INSERT INTO public.subscriptions (
      user_id, 
      plan_id, 
      amount_paid, 
      start_date, 
      end_date, 
      is_active, 
      last_payment_reference,
      created_at,
      updated_at
    ) VALUES (
      p_user_id, 
      p_plan_id, 
      p_amount, 
      start_date, 
      end_date,
      true, 
      p_reference,
      NOW(),
      NOW()
    ) ON CONFLICT (user_id) DO UPDATE SET
      plan_id = EXCLUDED.plan_id,
      amount_paid = EXCLUDED.amount_paid,
      start_date = EXCLUDED.start_date,
      end_date = EXCLUDED.end_date,
      is_active = true,
      last_payment_reference = EXCLUDED.last_payment_reference,
      updated_at = NOW()
    RETURNING id INTO subscription_id;

    -- 3. Update user profile with subscription status
    UPDATE public.user_profiles SET
      is_subscribed = true,
      subscription_expires_at = end_date,
      subscription_status = 'active',
      subscription_plan = p_plan_id,
      updated_at = NOW()
    WHERE user_id = p_user_id;

    -- Ensure user profile exists (create if missing)
    INSERT INTO public.user_profiles (
      user_id, 
      is_subscribed, 
      subscription_expires_at,
      subscription_status,
      subscription_plan,
      created_at,
      updated_at
    ) VALUES (
      p_user_id,
      true,
      end_date,
      'active',
      p_plan_id,
      NOW(),
      NOW()
    ) ON CONFLICT (user_id) DO NOTHING;

    -- Return success result
    RETURN jsonb_build_object(
      'success', true,
      'user_id', p_user_id,
      'payment_id', payment_id,
      'subscription_id', subscription_id,
      'plan_id', p_plan_id,
      'amount', p_amount,
      'currency', p_currency,
      'start_date', start_date,
      'end_date', end_date,
      'reference', p_reference
    );

  EXCEPTION
    WHEN OTHERS THEN
      -- Return error details
      RETURN jsonb_build_object(
        'success', false,
        'error', 'Transaction failed: ' || SQLERRM,
        'error_code', SQLSTATE
      );
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function 2: Update subscription status atomically
-- This function updates subscription status and syncs with user profile
CREATE OR REPLACE FUNCTION public.update_subscription_status_atomic(
  p_user_id UUID,
  p_status TEXT,
  p_reason TEXT DEFAULT NULL
)
RETURNS JSONB AS $$
DECLARE
  subscription_record RECORD;
  result JSONB;
BEGIN
  -- Validate input parameters
  IF p_user_id IS NULL OR p_status IS NULL THEN
    RETURN jsonb_build_object(
      'success', false, 
      'error', 'Missing required parameters'
    );
  END IF;

  -- Validate status values
  IF p_status NOT IN ('active', 'inactive', 'expired', 'cancelled', 'suspended') THEN
    RETURN jsonb_build_object(
      'success', false, 
      'error', 'Invalid status value'
    );
  END IF;

  -- Check if user exists
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) THEN
    RETURN jsonb_build_object(
      'success', false, 
      'error', 'User not found'
    );
  END IF;

  BEGIN
    -- Get current subscription info
    SELECT * INTO subscription_record 
    FROM public.subscriptions 
    WHERE user_id = p_user_id 
    ORDER BY created_at DESC 
    LIMIT 1;

    -- Update subscription status
    UPDATE public.subscriptions SET
      is_active = CASE WHEN p_status = 'active' THEN true ELSE false END,
      updated_at = NOW()
    WHERE user_id = p_user_id;

    -- Update user profile to match subscription status
    UPDATE public.user_profiles SET
      is_subscribed = CASE WHEN p_status = 'active' THEN true ELSE false END,
      subscription_status = p_status,
      updated_at = NOW()
    WHERE user_id = p_user_id;

    -- Log the status change if reason provided
    IF p_reason IS NOT NULL THEN
      INSERT INTO public.payments (
        user_id,
        amount,
        currency,
        status,
        provider,
        provider_payment_id,
        metadata,
        created_at,
        updated_at
      ) VALUES (
        p_user_id,
        0,
        'NGN',
        'status_change',
        'system',
        'status_' || extract(epoch from now())::text,
        jsonb_build_object(
          'action', 'status_change',
          'old_status', COALESCE(subscription_record.is_active, false),
          'new_status', p_status,
          'reason', p_reason,
          'changed_at', NOW()
        ),
        NOW(),
        NOW()
      );
    END IF;

    RETURN jsonb_build_object(
      'success', true,
      'user_id', p_user_id,
      'status', p_status,
      'reason', p_reason,
      'updated_at', NOW()
    );

  EXCEPTION
    WHEN OTHERS THEN
      RETURN jsonb_build_object(
        'success', false,
        'error', 'Status update failed: ' || SQLERRM,
        'error_code', SQLSTATE
      );
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function 3: Check and update expired subscriptions
-- This function identifies and marks expired subscriptions as inactive
CREATE OR REPLACE FUNCTION public.check_subscription_expiration()
RETURNS JSONB AS $$
DECLARE
  expired_count INTEGER := 0;
  updated_users UUID[];
BEGIN
  BEGIN
    -- Find and update expired subscriptions
    WITH expired_subscriptions AS (
      UPDATE public.subscriptions 
      SET is_active = false, updated_at = NOW()
      WHERE end_date <= NOW() 
        AND is_active = true
      RETURNING user_id
    ),
    updated_profiles AS (
      UPDATE public.user_profiles 
      SET 
        is_subscribed = false,
        subscription_status = 'expired',
        updated_at = NOW()
      WHERE user_id IN (SELECT user_id FROM expired_subscriptions)
      RETURNING user_id
    )
    SELECT array_agg(user_id), count(*) 
    INTO updated_users, expired_count
    FROM updated_profiles;

    -- Log the expiration check
    INSERT INTO public.payments (
      user_id,
      amount,
      currency,
      status,
      provider,
      provider_payment_id,
      metadata,
      created_at,
      updated_at
    ) VALUES (
      '00000000-0000-0000-0000-000000000000'::UUID, -- System user ID
      0,
      'NGN',
      'system_check',
      'system',
      'expiration_check_' || extract(epoch from now())::text,
      jsonb_build_object(
        'action', 'expiration_check',
        'expired_count', expired_count,
        'expired_users', updated_users,
        'checked_at', NOW()
      ),
      NOW(),
      NOW()
    );

    RETURN jsonb_build_object(
      'success', true,
      'expired_count', expired_count,
      'expired_users', updated_users,
      'checked_at', NOW()
    );

  EXCEPTION
    WHEN OTHERS THEN
      RETURN jsonb_build_object(
        'success', false,
        'error', 'Expiration check failed: ' || SQLERRM,
        'error_code', SQLSTATE
      );
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function 4: Get subscription status with validation
-- This function returns comprehensive subscription information
CREATE OR REPLACE FUNCTION public.get_subscription_status(p_user_id UUID)
RETURNS JSONB AS $$
DECLARE
  user_profile RECORD;
  subscription_record RECORD;
  latest_payment RECORD;
  result JSONB;
BEGIN
  -- Validate input
  IF p_user_id IS NULL THEN
    RETURN jsonb_build_object(
      'success', false, 
      'error', 'User ID is required'
    );
  END IF;

  -- Check if user exists
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) THEN
    RETURN jsonb_build_object(
      'success', false, 
      'error', 'User not found'
    );
  END IF;

  -- Get user profile
  SELECT * INTO user_profile 
  FROM public.user_profiles 
  WHERE user_id = p_user_id;

  -- Get latest subscription
  SELECT * INTO subscription_record 
  FROM public.subscriptions 
  WHERE user_id = p_user_id 
  ORDER BY created_at DESC 
  LIMIT 1;

  -- Get latest payment
  SELECT * INTO latest_payment 
  FROM public.payments 
  WHERE user_id = p_user_id 
    AND status = 'completed'
  ORDER BY created_at DESC 
  LIMIT 1;

  -- Build comprehensive result
  result := jsonb_build_object(
    'success', true,
    'user_id', p_user_id,
    'profile', CASE 
      WHEN user_profile IS NOT NULL THEN
        jsonb_build_object(
          'is_subscribed', user_profile.is_subscribed,
          'subscription_status', user_profile.subscription_status,
          'subscription_plan', user_profile.subscription_plan,
          'subscription_expires_at', user_profile.subscription_expires_at,
          'is_admin', user_profile.is_admin
        )
      ELSE NULL
    END,
    'subscription', CASE 
      WHEN subscription_record IS NOT NULL THEN
        jsonb_build_object(
          'id', subscription_record.id,
          'plan_id', subscription_record.plan_id,
          'amount_paid', subscription_record.amount_paid,
          'start_date', subscription_record.start_date,
          'end_date', subscription_record.end_date,
          'is_active', subscription_record.is_active,
          'last_payment_reference', subscription_record.last_payment_reference,
          'is_expired', subscription_record.end_date <= NOW()
        )
      ELSE NULL
    END,
    'latest_payment', CASE 
      WHEN latest_payment IS NOT NULL THEN
        jsonb_build_object(
          'id', latest_payment.id,
          'amount', latest_payment.amount,
          'currency', latest_payment.currency,
          'provider_payment_id', latest_payment.provider_payment_id,
          'created_at', latest_payment.created_at
        )
      ELSE NULL
    END,
    'checked_at', NOW()
  );

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add data validation constraints to subscription tables
-- Add check constraints for subscription data integrity
ALTER TABLE public.subscriptions 
ADD CONSTRAINT check_subscription_dates 
CHECK (end_date > start_date);

ALTER TABLE public.subscriptions 
ADD CONSTRAINT check_positive_amount 
CHECK (amount_paid > 0);

ALTER TABLE public.subscriptions 
ADD CONSTRAINT check_valid_plan_id 
CHECK (plan_id IN ('basic', 'pro', 'elite', 'premium'));

-- Add constraints to payments table
ALTER TABLE public.payments 
ADD CONSTRAINT check_payment_amount 
CHECK (amount >= 0);

ALTER TABLE public.payments 
ADD CONSTRAINT check_payment_status 
CHECK (status IN ('pending', 'completed', 'failed', 'cancelled', 'refunded', 'status_change', 'system_check'));

-- Add constraints to user_profiles table
ALTER TABLE public.user_profiles 
ADD CONSTRAINT check_subscription_status 
CHECK (subscription_status IN ('free', 'active', 'expired', 'cancelled', 'suspended'));

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id_active 
ON public.subscriptions(user_id, is_active);

CREATE INDEX IF NOT EXISTS idx_subscriptions_end_date_active 
ON public.subscriptions(end_date, is_active);

CREATE INDEX IF NOT EXISTS idx_payments_user_id_status 
ON public.payments(user_id, status);

CREATE INDEX IF NOT EXISTS idx_payments_provider_payment_id 
ON public.payments(provider_payment_id);

CREATE INDEX IF NOT EXISTS idx_user_profiles_subscription_status 
ON public.user_profiles(subscription_status, subscription_expires_at);

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.handle_payment_success_atomic TO service_role;
GRANT EXECUTE ON FUNCTION public.update_subscription_status_atomic TO service_role;
GRANT EXECUTE ON FUNCTION public.check_subscription_expiration TO service_role;
GRANT EXECUTE ON FUNCTION public.get_subscription_status TO service_role, authenticated;