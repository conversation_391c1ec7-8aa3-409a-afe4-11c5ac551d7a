/**
 * System Monitoring Service
 * Provides monitoring and alerting capabilities for quiz randomization system issues
 */

import { errorLogger, type ErrorMetrics, type ErrorContext } from './error-logging-service';
import { userFriendlyErrors } from './user-friendly-errors';

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'critical';
  timestamp: Date;
  metrics: {
    errorRate: number;
    criticalErrors: number;
    randomizationFailures: number;
    sessionFailures: number;
    databaseErrors: number;
  };
  alerts: SystemAlert[];
  recommendations: string[];
}

export interface SystemAlert {
  id: string;
  type: 'error_rate' | 'critical_errors' | 'service_degradation' | 'resource_exhaustion';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  context?: ErrorContext;
  acknowledged: boolean;
  resolvedAt?: Date;
}

export interface MonitoringConfig {
  enabled: boolean;
  checkInterval: number; // minutes
  thresholds: {
    errorRateWarning: number; // errors per minute
    errorRateCritical: number;
    criticalErrorThreshold: number; // count in time window
    randomizationFailureThreshold: number;
    sessionFailureThreshold: number;
  };
  alerting: {
    enabled: boolean;
    channels: {
      console: boolean;
      localStorage: boolean;
      // Future: webhook, email, etc.
    };
  };
}

/**
 * System Monitoring Service Class
 * Monitors system health and provides alerting for issues
 */
export class SystemMonitoringService {
  private static instance: SystemMonitoringService;
  private alerts: SystemAlert[] = [];
  private monitoringInterval?: NodeJS.Timeout;
  private config: MonitoringConfig = {
    enabled: true,
    checkInterval: 5, // Check every 5 minutes
    thresholds: {
      errorRateWarning: 5, // 5 errors per minute
      errorRateCritical: 15, // 15 errors per minute
      criticalErrorThreshold: 5, // 5 critical errors in 10 minutes
      randomizationFailureThreshold: 3, // 3 randomization failures in 10 minutes
      sessionFailureThreshold: 3 // 3 session failures in 10 minutes
    },
    alerting: {
      enabled: true,
      channels: {
        console: true,
        localStorage: true
      }
    }
  };

  private constructor() {
    this.loadStoredAlerts();
    this.startMonitoring();
  }

  static getInstance(): SystemMonitoringService {
    if (!SystemMonitoringService.instance) {
      SystemMonitoringService.instance = new SystemMonitoringService();
    }
    return SystemMonitoringService.instance;
  }

  /**
   * Gets current system health status
   */
  getSystemHealth(): SystemHealth {
    const metrics = errorLogger.getErrorMetrics(10); // Last 10 minutes
    const activeAlerts = this.alerts.filter(alert => !alert.acknowledged);

    // Calculate specific error type counts
    const randomizationFailures = metrics.recentErrors.filter(
      error => error.category === 'randomization' && error.level === 'error'
    ).length;

    const sessionFailures = metrics.recentErrors.filter(
      error => error.category === 'session' && error.level === 'error'
    ).length;

    const databaseErrors = metrics.recentErrors.filter(
      error => error.category === 'database' && error.level === 'error'
    ).length;

    const errorRate = metrics.totalErrors / 10; // errors per minute

    // Determine system status
    let status: SystemHealth['status'] = 'healthy';
    if (this.config && this.config.thresholds) {
      if (errorRate >= this.config.thresholds.errorRateCritical || 
          metrics.criticalErrors.length >= this.config.thresholds.criticalErrorThreshold) {
        status = 'critical';
      } else if (errorRate >= this.config.thresholds.errorRateWarning ||
                 randomizationFailures >= this.config.thresholds.randomizationFailureThreshold ||
                 sessionFailures >= this.config.thresholds.sessionFailureThreshold) {
        status = 'degraded';
      }
    }

    // Generate recommendations
    const recommendations = this.generateRecommendations(metrics, {
      randomizationFailures,
      sessionFailures,
      databaseErrors,
      errorRate
    });

    return {
      status,
      timestamp: new Date(),
      metrics: {
        errorRate,
        criticalErrors: metrics.criticalErrors.length,
        randomizationFailures,
        sessionFailures,
        databaseErrors
      },
      alerts: activeAlerts,
      recommendations
    };
  }

  /**
   * Creates a system alert
   */
  createAlert(
    type: SystemAlert['type'],
    severity: SystemAlert['severity'],
    message: string,
    context?: ErrorContext
  ): SystemAlert {
    const alert: SystemAlert = {
      id: this.generateAlertId(),
      type,
      severity,
      message,
      timestamp: new Date(),
      context,
      acknowledged: false
    };

    this.alerts.push(alert);
    this.trimAlerts();
    this.persistAlerts();

    if (this.config.alerting.enabled) {
      this.notifyAlert(alert);
    }

    return alert;
  }

  /**
   * Acknowledges an alert
   */
  acknowledgeAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert && !alert.acknowledged) {
      alert.acknowledged = true;
      this.persistAlerts();
      return true;
    }
    return false;
  }

  /**
   * Resolves an alert
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      alert.resolvedAt = new Date();
      this.persistAlerts();
      return true;
    }
    return false;
  }

  /**
   * Gets active alerts
   */
  getActiveAlerts(): SystemAlert[] {
    return this.alerts.filter(alert => !alert.acknowledged);
  }

  /**
   * Gets all alerts within a time window
   */
  getAlerts(timeWindowMinutes: number = 60): SystemAlert[] {
    const cutoffTime = new Date(Date.now() - timeWindowMinutes * 60 * 1000);
    return this.alerts.filter(alert => alert.timestamp >= cutoffTime);
  }

  /**
   * Updates monitoring configuration
   */
  updateConfig(config: Partial<MonitoringConfig>): void {
    this.config = { ...this.config, ...config };
    
    if (config.enabled !== undefined) {
      if (config.enabled && !this.monitoringInterval) {
        this.startMonitoring();
      } else if (!config.enabled && this.monitoringInterval) {
        this.stopMonitoring();
      }
    }

    if (config.checkInterval && this.monitoringInterval) {
      this.stopMonitoring();
      this.startMonitoring();
    }
  }

  /**
   * Gets current monitoring configuration
   */
  getConfig(): MonitoringConfig {
    return { ...this.config };
  }

  /**
   * Performs a manual health check
   */
  performHealthCheck(): SystemHealth {
    const health = this.getSystemHealth();
    this.checkThresholds();
    return health;
  }

  /**
   * Clears all alerts (for testing or maintenance)
   */
  clearAlerts(): void {
    this.alerts = [];
    this.persistAlerts();
  }

  /**
   * Exports monitoring data for analysis
   */
  exportMonitoringData(): {
    alerts: SystemAlert[];
    errorMetrics: ErrorMetrics;
    systemHealth: SystemHealth;
    config: MonitoringConfig;
  } {
    return {
      alerts: this.alerts,
      errorMetrics: errorLogger.getErrorMetrics(60),
      systemHealth: this.getSystemHealth(),
      config: this.config
    };
  }

  // Private methods

  private startMonitoring(): void {
    if (!this.config.enabled) return;

    this.monitoringInterval = setInterval(() => {
      this.checkThresholds();
    }, this.config.checkInterval * 60 * 1000);
  }

  private stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }
  }

  private checkThresholds(): void {
    if (!this.config || !this.config.thresholds) return;
    
    const metrics = errorLogger.getErrorMetrics(10);
    const errorRate = metrics.totalErrors / 10;

    // Check error rate thresholds
    if (errorRate >= this.config.thresholds.errorRateCritical) {
      this.createAlert(
        'error_rate',
        'critical',
        `Critical error rate: ${errorRate.toFixed(2)} errors/minute (threshold: ${this.config.thresholds.errorRateCritical})`,
        { metadata: { errorRate, threshold: this.config.thresholds.errorRateCritical } }
      );
    } else if (errorRate >= this.config.thresholds.errorRateWarning) {
      this.createAlert(
        'error_rate',
        'medium',
        `High error rate: ${errorRate.toFixed(2)} errors/minute (threshold: ${this.config.thresholds.errorRateWarning})`,
        { metadata: { errorRate, threshold: this.config.thresholds.errorRateWarning } }
      );
    }

    // Check critical errors threshold
    if (metrics.criticalErrors.length >= this.config.thresholds.criticalErrorThreshold) {
      this.createAlert(
        'critical_errors',
        'critical',
        `Multiple critical errors detected: ${metrics.criticalErrors.length} in last 10 minutes`,
        { metadata: { criticalErrorCount: metrics.criticalErrors.length } }
      );
    }

    // Check randomization failures
    const randomizationFailures = metrics.recentErrors.filter(
      error => error.category === 'randomization' && error.level === 'error'
    ).length;

    if (randomizationFailures >= this.config.thresholds.randomizationFailureThreshold) {
      this.createAlert(
        'service_degradation',
        'high',
        `Multiple randomization failures: ${randomizationFailures} in last 10 minutes`,
        { metadata: { randomizationFailures } }
      );
    }

    // Check session failures
    const sessionFailures = metrics.recentErrors.filter(
      error => error.category === 'session' && error.level === 'error'
    ).length;

    if (sessionFailures >= this.config.thresholds.sessionFailureThreshold) {
      this.createAlert(
        'service_degradation',
        'high',
        `Multiple session failures: ${sessionFailures} in last 10 minutes`,
        { metadata: { sessionFailures } }
      );
    }
  }

  private generateRecommendations(
    metrics: ErrorMetrics,
    specificMetrics: {
      randomizationFailures: number;
      sessionFailures: number;
      databaseErrors: number;
      errorRate: number;
    }
  ): string[] {
    const recommendations: string[] = [];

    if (!this.config || !this.config.thresholds) {
      recommendations.push('System monitoring configuration not available');
      return recommendations;
    }

    if (specificMetrics.errorRate >= this.config.thresholds.errorRateWarning) {
      recommendations.push('High error rate detected - consider investigating recent changes or system load');
    }

    if (specificMetrics.randomizationFailures >= 2) {
      recommendations.push('Multiple randomization failures - check question pool availability and data integrity');
    }

    if (specificMetrics.sessionFailures >= 2) {
      recommendations.push('Session management issues detected - verify database connectivity and session storage');
    }

    if (specificMetrics.databaseErrors >= 2) {
      recommendations.push('Database connectivity issues - check network connection and database health');
    }

    if (metrics.errorsByCategory.validation && metrics.errorsByCategory.validation >= 5) {
      recommendations.push('High validation error count - review question data quality and import processes');
    }

    // Performance recommendations
    if (metrics.totalErrors > 20) {
      recommendations.push('Consider implementing circuit breakers and rate limiting to prevent cascade failures');
    }

    if (recommendations.length === 0) {
      recommendations.push('System is operating normally');
    }

    return recommendations;
  }

  private notifyAlert(alert: SystemAlert): void {
    if (this.config.alerting.channels.console) {
      const emoji = this.getAlertEmoji(alert.severity);
      console.warn(`${emoji} SYSTEM ALERT [${alert.type.toUpperCase()}]:`, {
        severity: alert.severity,
        message: alert.message,
        timestamp: alert.timestamp,
        context: alert.context
      });
    }

    // Future: Add webhook, email, or other notification channels
  }

  private getAlertEmoji(severity: SystemAlert['severity']): string {
    switch (severity) {
      case 'critical': return '🚨';
      case 'high': return '⚠️';
      case 'medium': return '⚡';
      case 'low': return 'ℹ️';
      default: return '📊';
    }
  }

  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private trimAlerts(): void {
    // Keep only last 100 alerts
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(-100);
    }
  }

  private persistAlerts(): void {
    try {
      const alertsToStore = this.alerts.slice(-50); // Store only last 50 alerts
      localStorage.setItem('system_monitoring_alerts', JSON.stringify(alertsToStore));
    } catch (error) {
      console.warn('Failed to persist monitoring alerts:', error);
    }
  }

  private loadStoredAlerts(): void {
    try {
      const stored = localStorage.getItem('system_monitoring_alerts');
      if (stored) {
        const alerts = JSON.parse(stored);
        this.alerts = alerts.map((alert: any) => ({
          ...alert,
          timestamp: new Date(alert.timestamp),
          resolvedAt: alert.resolvedAt ? new Date(alert.resolvedAt) : undefined
        }));
      }
    } catch (error) {
      console.warn('Failed to load stored monitoring alerts:', error);
    }
  }
}

// Export singleton instance
export const systemMonitor = SystemMonitoringService.getInstance();

// Convenience functions
export const getSystemHealth = () => systemMonitor.getSystemHealth();
export const createSystemAlert = (
  type: SystemAlert['type'],
  severity: SystemAlert['severity'],
  message: string,
  context?: ErrorContext
) => systemMonitor.createAlert(type, severity, message, context);
export const getActiveAlerts = () => systemMonitor.getActiveAlerts();