import { v4 as uuidv4 } from "uuid";
import type {
  MultiTopicImportResult,
  TopicImportResult,
  BatchImportResult,
  BatchImportError,
  TopicBatchResult,
  ValidationError,
} from "@/utils/csv-import";
import type {
  ImportErrorReport,
  ImportTopicErrors,
  ImportError,
  ImportStatistics,
  ImportSuggestion,
  RecoveryOption,
} from "@/components/admin/ImportErrorReporting";

/**
 * Service for generating comprehensive error reports from import results
 */
export class ImportErrorReportingService {
  /**
   * Generate a comprehensive error report from import and batch results
   * @param importResult Multi-topic import result from CSV parsing
   * @param batchResult Batch import result from database operations
   * @returns Comprehensive error report with actionable suggestions
   */
  generateErrorReport(
    importResult?: MultiTopicImportResult,
    batchResult?: BatchImportResult
  ): ImportErrorReport {
    const errorsByTopic = new Map<string, ImportTopicErrors>();
    const globalErrors: ImportError[] = [];

    // Process import result errors
    if (importResult) {
      this.processImportResultErrors(importResult, errorsByTopic, globalErrors);
    }

    // Process batch result errors
    if (batchResult) {
      this.processBatchResultErrors(batchResult, errorsByTopic, globalErrors);
    }

    // Generate statistics
    const statistics = this.generateStatistics(importResult, batchResult);

    // Generate suggestions based on errors and statistics
    const suggestions = this.generateSuggestions(
      errorsByTopic,
      globalErrors,
      statistics
    );

    // Generate recovery options
    const recoveryOptions = this.generateRecoveryOptions(
      errorsByTopic,
      globalErrors,
      statistics
    );

    return {
      importResult,
      batchResult,
      errorsByTopic,
      globalErrors,
      statistics,
      suggestions,
      recoveryOptions,
    };
  }

  /**
   * Process errors from MultiTopicImportResult
   */
  private processImportResultErrors(
    importResult: MultiTopicImportResult,
    errorsByTopic: Map<string, ImportTopicErrors>,
    globalErrors: ImportError[]
  ): void {
    // Process global errors from import result
    importResult.globalErrors.forEach((error) => {
      globalErrors.push(this.convertToImportError(error, "format"));
    });

    // Process topic-specific errors
    for (const [topicId, topicResult] of importResult.topicResults) {
      const topicErrors = this.getOrCreateTopicErrors(
        errorsByTopic,
        topicId,
        topicResult.topicName,
        topicResult.isNewTopic
      );

      // Update topic statistics
      topicErrors.questionsProcessed =
        topicResult.validQuestions.length + topicResult.errors.length;
      topicErrors.questionsSuccessful = topicResult.validQuestions.length;
      topicErrors.questionsFailed = topicResult.errors.length;

      // Convert topic errors
      topicResult.errors.forEach((error) => {
        const importError = this.convertToImportError(error, "question");
        if (importError.severity === "error") {
          topicErrors.errors.push(importError);
        } else {
          topicErrors.warnings.push(importError);
        }
      });
    }
  }

  /**
   * Process errors from BatchImportResult
   */
  private processBatchResultErrors(
    batchResult: BatchImportResult,
    errorsByTopic: Map<string, ImportTopicErrors>,
    globalErrors: ImportError[]
  ): void {
    // Process batch-level errors
    batchResult.errors.forEach((error) => {
      if (error.topicId && error.topicName) {
        // Topic-specific batch error
        const topicErrors = this.getOrCreateTopicErrors(
          errorsByTopic,
          error.topicId,
          error.topicName,
          false // We don't know if it's new from batch result alone
        );

        const importError = this.convertBatchErrorToImportError(error);
        if (importError.severity === "error") {
          topicErrors.errors.push(importError);
        } else {
          topicErrors.warnings.push(importError);
        }
      } else {
        // Global batch error
        globalErrors.push(this.convertBatchErrorToImportError(error));
      }
    });

    // Process topic-specific batch results
    for (const [topicId, topicResult] of batchResult.topicResults) {
      const topicErrors = this.getOrCreateTopicErrors(
        errorsByTopic,
        topicId,
        topicResult.topicName,
        topicResult.isNewTopic
      );

      // Update or set topic statistics from batch result
      topicErrors.questionsProcessed = Math.max(
        topicErrors.questionsProcessed,
        topicResult.questionsImported + topicResult.questionsFailed
      );
      topicErrors.questionsSuccessful = topicResult.questionsImported;
      topicErrors.questionsFailed = topicResult.questionsFailed;

      // Add topic-specific batch errors
      topicResult.errors.forEach((errorMessage) => {
        topicErrors.errors.push({
          id: uuidv4(),
          message: errorMessage,
          severity: "error",
          category: "system",
          suggestion: this.generateErrorSuggestion(errorMessage, "system"),
          actionable: this.isErrorActionable(errorMessage, "system"),
          canRetry: true,
        });
      });
    }
  }

  /**
   * Get or create topic errors entry
   */
  private getOrCreateTopicErrors(
    errorsByTopic: Map<string, ImportTopicErrors>,
    topicId: string,
    topicName: string,
    isNewTopic: boolean
  ): ImportTopicErrors {
    if (!errorsByTopic.has(topicId)) {
      errorsByTopic.set(topicId, {
        topicId,
        topicName,
        isNewTopic,
        errors: [],
        warnings: [],
        questionsProcessed: 0,
        questionsSuccessful: 0,
        questionsFailed: 0,
      });
    }
    return errorsByTopic.get(topicId)!;
  }

  /**
   * Convert generic error to ImportError
   */
  private convertToImportError(
    error: { row: number; message: string },
    category: ImportError["category"]
  ): ImportError {
    const severity = this.determineSeverity(error.message);

    // Determine category based on message content if not explicitly set
    let finalCategory = category;
    if (category === "format") {
      const lowerMessage = error.message.toLowerCase();
      if (
        lowerMessage.includes("topic") &&
        lowerMessage.includes("not found")
      ) {
        finalCategory = "topic";
      } else if (lowerMessage.includes("duplicate")) {
        finalCategory = "duplicate";
      }
    }

    return {
      id: uuidv4(),
      row: error.row,
      message: error.message,
      severity,
      category: finalCategory,
      suggestion: this.generateErrorSuggestion(error.message, finalCategory),
      actionable: this.isErrorActionable(error.message, finalCategory),
      canRetry: severity !== "error" || finalCategory !== "format",
    };
  }

  /**
   * Convert BatchImportError to ImportError
   */
  private convertBatchErrorToImportError(error: BatchImportError): ImportError {
    const severity = error.type === "validation" ? "warning" : "error";
    const category = this.mapBatchErrorTypeToCategory(error.type);

    return {
      id: uuidv4(),
      message: error.message,
      severity,
      category,
      suggestion: this.generateErrorSuggestion(error.message, category),
      actionable: this.isErrorActionable(error.message, category),
      canRetry: error.type !== "system",
    };
  }

  /**
   * Map batch error type to import error category
   */
  private mapBatchErrorTypeToCategory(
    type: BatchImportError["type"]
  ): ImportError["category"] {
    switch (type) {
      case "topic-creation":
        return "topic";
      case "question-insertion":
        return "question";
      case "validation":
        return "format";
      case "system":
      default:
        return "system";
    }
  }

  /**
   * Determine error severity based on message content
   */
  private determineSeverity(message: string): ImportError["severity"] {
    const lowerMessage = message.toLowerCase();

    // Critical errors that prevent import
    if (
      lowerMessage.includes("failed") ||
      lowerMessage.includes("error") ||
      lowerMessage.includes("invalid") ||
      lowerMessage.includes("missing required")
    ) {
      return "error";
    }

    // Warnings for non-critical issues
    if (
      lowerMessage.includes("warning") ||
      lowerMessage.includes("should") ||
      lowerMessage.includes("recommend") ||
      lowerMessage.includes("consider")
    ) {
      return "warning";
    }

    // Informational messages
    if (
      lowerMessage.includes("created") ||
      lowerMessage.includes("success") ||
      lowerMessage.includes("completed")
    ) {
      return "info";
    }

    return "error"; // Default to error for safety
  }

  /**
   * Generate contextual suggestions for errors
   */
  private generateErrorSuggestion(
    message: string,
    category: ImportError["category"]
  ): string | undefined {
    const lowerMessage = message.toLowerCase();

    switch (category) {
      case "topic":
        if (lowerMessage.includes("not found")) {
          return "Enable auto-create topics or create the topic manually first.";
        }
        if (lowerMessage.includes("invalid characters")) {
          return 'Remove special characters like <, >, :, ", /, \\, |, ?, * from topic names.';
        }
        if (lowerMessage.includes("too long")) {
          return "Shorten topic names to 100 characters or less.";
        }
        if (lowerMessage.includes("reserved")) {
          return "Choose a different topic name that is not reserved by the system.";
        }
        break;

      case "question":
        if (
          lowerMessage.includes("question text") &&
          lowerMessage.includes("short")
        ) {
          return "Add more detail to make the question clearer (minimum 10 characters).";
        }
        if (
          lowerMessage.includes("question text") &&
          lowerMessage.includes("long")
        ) {
          return "Shorten the question or break it into multiple questions.";
        }
        if (lowerMessage.includes("correct_answer")) {
          return "Use only A, B, C, or D for the correct answer field.";
        }
        if (lowerMessage.includes("options")) {
          return "Ensure all four options (A, B, C, D) are provided and unique.";
        }
        if (lowerMessage.includes("difficulty")) {
          return 'Use only "easy", "medium", or "hard" for difficulty level.';
        }
        break;

      case "format":
        if (lowerMessage.includes("csv")) {
          return "Check CSV format: ensure UTF-8 encoding and proper column headers.";
        }
        if (
          lowerMessage.includes("column") ||
          lowerMessage.includes("header")
        ) {
          return "Verify column headers match the template exactly.";
        }
        break;

      case "duplicate":
        return "Consider rephrasing one of the questions or removing the duplicate.";

      case "system":
        if (
          lowerMessage.includes("database") ||
          lowerMessage.includes("connection")
        ) {
          return "Check database connectivity and try again.";
        }
        if (lowerMessage.includes("permission")) {
          return "Verify you have the necessary permissions to create topics and questions.";
        }
        break;
    }

    return undefined;
  }

  /**
   * Determine if an error is actionable by the user
   */
  private isErrorActionable(
    message: string,
    category: ImportError["category"]
  ): boolean {
    const lowerMessage = message.toLowerCase();

    // System errors are generally not actionable by users
    if (
      category === "system" &&
      (lowerMessage.includes("database") ||
        lowerMessage.includes("connection") ||
        lowerMessage.includes("internal"))
    ) {
      return false;
    }

    // Format, topic, question, and duplicate errors are usually actionable
    return ["format", "topic", "question", "duplicate"].includes(category);
  }

  /**
   * Generate import statistics
   */
  private generateStatistics(
    importResult?: MultiTopicImportResult,
    batchResult?: BatchImportResult
  ): ImportStatistics {
    let totalRows = 0;
    let totalTopics = 0;
    let newTopicsCreated = 0;
    let questionsImported = 0;
    let questionsFailed = 0;
    let errorsCount = 0;
    let warningsCount = 0;
    let duration: number | undefined;

    if (importResult) {
      totalRows = importResult.totalRows;
      totalTopics = importResult.topicResults.size;
      newTopicsCreated = importResult.newTopicsCreated.length;

      // Count errors from import result
      errorsCount += importResult.globalErrors.length;
      for (const topicResult of importResult.topicResults.values()) {
        errorsCount += topicResult.errors.length;
      }
    }

    if (batchResult) {
      totalTopics = Math.max(totalTopics, batchResult.totalTopicsProcessed);
      questionsImported = batchResult.totalQuestionsImported;
      newTopicsCreated = Math.max(
        newTopicsCreated,
        batchResult.topicsCreated.length
      );
      duration = batchResult.duration;

      // Count batch errors
      errorsCount += batchResult.errors.filter(
        (e) => e.type !== "validation"
      ).length;
      warningsCount += batchResult.errors.filter(
        (e) => e.type === "validation"
      ).length;

      // Calculate failed questions from batch results
      for (const topicResult of batchResult.topicResults.values()) {
        questionsFailed += topicResult.questionsFailed;
      }
    }

    const totalQuestions = questionsImported + questionsFailed;
    const successRate =
      totalQuestions > 0 ? questionsImported / totalQuestions : 0;

    return {
      totalRows,
      totalTopics,
      newTopicsCreated,
      questionsImported,
      questionsFailed,
      errorsCount,
      warningsCount,
      duration,
      successRate,
    };
  }

  /**
   * Generate actionable suggestions based on errors and statistics
   */
  private generateSuggestions(
    errorsByTopic: Map<string, ImportTopicErrors>,
    globalErrors: ImportError[],
    statistics: ImportStatistics
  ): ImportSuggestion[] {
    const suggestions: ImportSuggestion[] = [];

    // Suggest enabling auto-create if many topic errors
    const topicNotFoundErrors = [
      ...globalErrors,
      ...Array.from(errorsByTopic.values()).flatMap((te) => te.errors),
    ].filter(
      (e) =>
        e.category === "topic" && e.message.toLowerCase().includes("not found")
    ).length;

    if (topicNotFoundErrors > 0) {
      suggestions.push({
        id: uuidv4(),
        title: "Enable Auto-Create Topics",
        description: `${topicNotFoundErrors} topic(s) were not found. Enable auto-create to automatically create missing topics.`,
        priority: "high",
        category: "fix-required",
        actionText: "Enable Auto-Create",
        onAction: () => {
          // This would be handled by the parent component
        },
      });
    }

    // Suggest CSV format fixes if many format errors
    const formatErrors = [
      ...globalErrors,
      ...Array.from(errorsByTopic.values()).flatMap((te) => te.errors),
    ].filter((e) => e.category === "format").length;
    if (formatErrors > 2) {
      suggestions.push({
        id: uuidv4(),
        title: "Check CSV Format",
        description: `${formatErrors} format errors detected. Your CSV may have formatting issues.`,
        priority: "high",
        category: "fix-required",
        actionText: "Download Template",
        onAction: () => {
          // This would be handled by the parent component
        },
      });
    }

    // Suggest reviewing duplicates
    const duplicateErrors = [
      ...globalErrors,
      ...Array.from(errorsByTopic.values()).flatMap((te) => te.errors),
    ].filter((e) => e.category === "duplicate").length;
    if (duplicateErrors > 0) {
      suggestions.push({
        id: uuidv4(),
        title: "Review Duplicate Questions",
        description: `${duplicateErrors} duplicate question(s) found across topics. Consider rephrasing or removing duplicates.`,
        priority: "medium",
        category: "improvement",
        actionText: "Show Duplicates",
      });
    }

    // Suggest improving success rate if low
    if (statistics.successRate < 0.8 && statistics.questionsImported > 0) {
      suggestions.push({
        id: uuidv4(),
        title: "Improve Success Rate",
        description: `Success rate is ${Math.round(
          statistics.successRate * 100
        )}%. Review and fix errors to improve import quality.`,
        priority: "medium",
        category: "improvement",
        actionText: "Review Errors",
      });
    }

    // Suggest topic organization if many small topics
    const smallTopics = Array.from(errorsByTopic.values()).filter(
      (te) => te.questionsProcessed > 0 && te.questionsProcessed < 3
    ).length;

    if (smallTopics > 2) {
      suggestions.push({
        id: uuidv4(),
        title: "Consider Topic Organization",
        description: `${smallTopics} topic(s) have fewer than 3 questions. Consider consolidating small topics for better quiz balance.`,
        priority: "low",
        category: "optimization",
        actionText: "Review Topics",
      });
    }

    // Suggest performance optimization for large imports
    if (
      statistics.totalRows > 100 &&
      statistics.duration &&
      statistics.duration > 30000
    ) {
      suggestions.push({
        id: uuidv4(),
        title: "Optimize Large Imports",
        description: `Import took ${Math.round(
          statistics.duration / 1000
        )}s for ${
          statistics.totalRows
        } rows. Consider breaking large files into smaller batches.`,
        priority: "low",
        category: "optimization",
        actionText: "Learn More",
      });
    }

    return suggestions;
  }

  /**
   * Generate recovery options based on errors
   */
  private generateRecoveryOptions(
    errorsByTopic: Map<string, ImportTopicErrors>,
    globalErrors: ImportError[],
    statistics: ImportStatistics
  ): RecoveryOption[] {
    const options: RecoveryOption[] = [];

    // Option to retry with auto-create enabled
    const hasTopicErrors = [
      ...globalErrors,
      ...Array.from(errorsByTopic.values()).flatMap((te) => te.errors),
    ].some((e) => e.category === "topic");

    if (hasTopicErrors) {
      options.push({
        id: uuidv4(),
        title: "Retry with Auto-Create Topics",
        description:
          "Automatically create missing topics and retry the import.",
        actionText: "Retry with Auto-Create",
        onAction: () => {
          // This would be handled by the parent component
        },
      });
    }

    // Option to download corrected CSV
    const hasActionableErrors = [
      ...globalErrors,
      ...Array.from(errorsByTopic.values()).flatMap((te) => [
        ...te.errors,
        ...te.warnings,
      ]),
    ].some((e) => e.actionable);

    if (hasActionableErrors) {
      options.push({
        id: uuidv4(),
        title: "Download Corrected CSV",
        description:
          "Download a CSV with errors highlighted and suggestions for fixes.",
        actionText: "Download Corrected CSV",
        onAction: () => {
          // This would be handled by the parent component
        },
      });
    }

    // Option to import only successful questions
    if (statistics.questionsImported > 0 && statistics.questionsFailed > 0) {
      options.push({
        id: uuidv4(),
        title: "Keep Successful Imports",
        description: `Keep the ${statistics.questionsImported} successfully imported questions and fix the remaining ${statistics.questionsFailed} separately.`,
        actionText: "Keep Successful",
        onAction: () => {
          // This would be handled by the parent component
        },
      });
    }

    // Option to start over
    options.push({
      id: uuidv4(),
      title: "Start Over",
      description:
        "Clear all data and start the import process from the beginning.",
      actionText: "Start Over",
      destructive: true,
      onAction: () => {
        // This would be handled by the parent component
      },
    });

    return options;
  }

  /**
   * Generate a downloadable error report
   * @param report Import error report
   * @returns CSV content for download
   */
  generateDownloadableErrorReport(report: ImportErrorReport): string {
    const rows: string[][] = [];

    // Header
    rows.push([
      "Type",
      "Topic",
      "Row",
      "Field",
      "Severity",
      "Category",
      "Message",
      "Suggestion",
      "Actionable",
    ]);

    // Global errors
    report.globalErrors.forEach((error) => {
      rows.push([
        "Global",
        "",
        error.row?.toString() || "",
        error.field || "",
        error.severity,
        error.category,
        error.message,
        error.suggestion || "",
        error.actionable ? "Yes" : "No",
      ]);
    });

    // Topic errors
    for (const [topicId, topicErrors] of report.errorsByTopic) {
      [...topicErrors.errors, ...topicErrors.warnings].forEach((error) => {
        rows.push([
          "Topic",
          topicErrors.topicName,
          error.row?.toString() || "",
          error.field || "",
          error.severity,
          error.category,
          error.message,
          error.suggestion || "",
          error.actionable ? "Yes" : "No",
        ]);
      });
    }

    // Convert to CSV
    return rows
      .map((row, index) => {
        if (index === 0) {
          // Don't quote headers
          return row.join(",");
        }
        // Quote data rows
        return row.map((cell) => `"${cell.replace(/"/g, '""')}"`).join(",");
      })
      .join("\n");
  }
}

// Export singleton instance
export const importErrorReportingService = new ImportErrorReportingService();
