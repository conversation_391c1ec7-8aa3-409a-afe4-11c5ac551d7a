export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      admin_users: {
        Row: {
          created_at: string | null
          is_admin: boolean | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          is_admin?: boolean | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          is_admin?: boolean | null
          user_id?: string
        }
        Relationships: []
      }
      domain_learning_paths: {
        Row: {
          created_at: string | null
          description: string | null
          difficulty_level: string | null
          domain_id: string | null
          estimated_hours: number | null
          id: string
          is_active: boolean | null
          name: string
          sort_order: number | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          difficulty_level?: string | null
          domain_id?: string | null
          estimated_hours?: number | null
          id?: string
          is_active?: boolean | null
          name: string
          sort_order?: number | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          difficulty_level?: string | null
          domain_id?: string | null
          estimated_hours?: number | null
          id?: string
          is_active?: boolean | null
          name?: string
          sort_order?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "domain_learning_paths_domain_id_fkey"
            columns: ["domain_id"]
            isOneToOne: false
            referencedRelation: "domains"
            referencedColumns: ["id"]
          },
        ]
      }
      domain_subscription_plans: {
        Row: {
          amount: number
          created_at: string | null
          domain_id: string | null
          features: string[] | null
          id: string
          interval: string | null
          is_active: boolean | null
          name: string
          plan_id: string
          updated_at: string | null
        }
        Insert: {
          amount: number
          created_at?: string | null
          domain_id?: string | null
          features?: string[] | null
          id?: string
          interval?: string | null
          is_active?: boolean | null
          name: string
          plan_id: string
          updated_at?: string | null
        }
        Update: {
          amount?: number
          created_at?: string | null
          domain_id?: string | null
          features?: string[] | null
          id?: string
          interval?: string | null
          is_active?: boolean | null
          name?: string
          plan_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "domain_subscription_plans_domain_id_fkey"
            columns: ["domain_id"]
            isOneToOne: false
            referencedRelation: "domains"
            referencedColumns: ["id"]
          },
        ]
      }
      domains: {
        Row: {
          color_theme: string | null
          created_at: string | null
          description: string | null
          difficulty_level: string | null
          estimated_duration_weeks: number | null
          icon: string | null
          id: string
          is_active: boolean | null
          name: string
          prerequisites: string[] | null
          slug: string
          sort_order: number | null
          updated_at: string | null
        }
        Insert: {
          color_theme?: string | null
          created_at?: string | null
          description?: string | null
          difficulty_level?: string | null
          estimated_duration_weeks?: number | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          prerequisites?: string[] | null
          slug: string
          sort_order?: number | null
          updated_at?: string | null
        }
        Update: {
          color_theme?: string | null
          created_at?: string | null
          description?: string | null
          difficulty_level?: string | null
          estimated_duration_weeks?: number | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          prerequisites?: string[] | null
          slug?: string
          sort_order?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      feedback: {
        Row: {
          created_at: string | null
          email: string
          id: string
          message: string
          name: string
          status: string | null
          subject: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          email: string
          id?: string
          message: string
          name: string
          status?: string | null
          subject: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string
          id?: string
          message?: string
          name?: string
          status?: string | null
          subject?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      learning_materials: {
        Row: {
          content: string
          created_at: string | null
          created_by: string | null
          id: string
          is_premium: boolean | null
          order_index: number | null
          summary: string | null
          title: string
          topic_id: string | null
          updated_at: string | null
        }
        Insert: {
          content: string
          created_at?: string | null
          created_by?: string | null
          id?: string
          is_premium?: boolean | null
          order_index?: number | null
          summary?: string | null
          title: string
          topic_id?: string | null
          updated_at?: string | null
        }
        Update: {
          content?: string
          created_at?: string | null
          created_by?: string | null
          id?: string
          is_premium?: boolean | null
          order_index?: number | null
          summary?: string | null
          title?: string
          topic_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "learning_materials_topic_id_fkey"
            columns: ["topic_id"]
            isOneToOne: false
            referencedRelation: "topics"
            referencedColumns: ["id"]
          },
        ]
      }
      path_topics: {
        Row: {
          created_at: string | null
          id: string
          is_required: boolean | null
          learning_path_id: string | null
          sort_order: number | null
          topic_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_required?: boolean | null
          learning_path_id?: string | null
          sort_order?: number | null
          topic_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_required?: boolean | null
          learning_path_id?: string | null
          sort_order?: number | null
          topic_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "path_topics_learning_path_id_fkey"
            columns: ["learning_path_id"]
            isOneToOne: false
            referencedRelation: "domain_learning_paths"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "path_topics_topic_id_fkey"
            columns: ["topic_id"]
            isOneToOne: false
            referencedRelation: "topics"
            referencedColumns: ["id"]
          },
        ]
      }
      payments: {
        Row: {
          amount: number
          created_at: string | null
          currency: string | null
          id: string
          metadata: Json | null
          provider: string | null
          provider_payment_id: string | null
          status: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          amount: number
          created_at?: string | null
          currency?: string | null
          id?: string
          metadata?: Json | null
          provider?: string | null
          provider_payment_id?: string | null
          status: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          amount?: number
          created_at?: string | null
          currency?: string | null
          id?: string
          metadata?: Json | null
          provider?: string | null
          provider_payment_id?: string | null
          status?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      question_analytics: {
        Row: {
          answered_correctly: boolean
          created_at: string | null
          id: string
          question_id: string
          quiz_session_id: string | null
          selected_option: number | null
          time_to_answer: number | null
          user_id: string
        }
        Insert: {
          answered_correctly: boolean
          created_at?: string | null
          id?: string
          question_id: string
          quiz_session_id?: string | null
          selected_option?: number | null
          time_to_answer?: number | null
          user_id: string
        }
        Update: {
          answered_correctly?: boolean
          created_at?: string | null
          id?: string
          question_id?: string
          quiz_session_id?: string | null
          selected_option?: number | null
          time_to_answer?: number | null
          user_id?: string
        }
        Relationships: []
      }
      questions: {
        Row: {
          correct_answer: string
          created_at: string | null
          created_by: string | null
          difficulty: string | null
          explanation: string | null
          id: string
          is_premium: boolean | null
          options: Json
          question_text: string
          topic_id: string | null
          updated_at: string | null
        }
        Insert: {
          correct_answer: string
          created_at?: string | null
          created_by?: string | null
          difficulty?: string | null
          explanation?: string | null
          id?: string
          is_premium?: boolean | null
          options: Json
          question_text: string
          topic_id?: string | null
          updated_at?: string | null
        }
        Update: {
          correct_answer?: string
          created_at?: string | null
          created_by?: string | null
          difficulty?: string | null
          explanation?: string | null
          id?: string
          is_premium?: boolean | null
          options?: Json
          question_text?: string
          topic_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "questions_topic_id_fkey"
            columns: ["topic_id"]
            isOneToOne: false
            referencedRelation: "topics"
            referencedColumns: ["id"]
          },
        ]
      }
      quiz_attempts: {
        Row: {
          answers: Json | null
          created_at: string | null
          id: string
          score: number
          time_taken: number | null
          topic_id: string | null
          total_questions: number
          updated_at: string | null
          user_id: string
        }
        Insert: {
          answers?: Json | null
          created_at?: string | null
          id?: string
          score: number
          time_taken?: number | null
          topic_id?: string | null
          total_questions: number
          updated_at?: string | null
          user_id: string
        }
        Update: {
          answers?: Json | null
          created_at?: string | null
          id?: string
          score?: number
          time_taken?: number | null
          topic_id?: string | null
          total_questions?: number
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "quiz_attempts_topic_id_fkey"
            columns: ["topic_id"]
            isOneToOne: false
            referencedRelation: "topics"
            referencedColumns: ["id"]
          },
        ]
      }
      quiz_sessions: {
        Row: {
          completed_at: string | null
          created_at: string | null
          expires_at: string | null
          id: string
          questions_data: Json
          quiz_length: number
          score: number | null
          time_taken: number | null
          topic_id: string | null
          total_questions: number | null
          user_id: string
        }
        Insert: {
          completed_at?: string | null
          created_at?: string | null
          expires_at?: string | null
          id?: string
          questions_data: Json
          quiz_length?: number
          score?: number | null
          time_taken?: number | null
          topic_id?: string | null
          total_questions?: number | null
          user_id: string
        }
        Update: {
          completed_at?: string | null
          created_at?: string | null
          expires_at?: string | null
          id?: string
          questions_data?: Json
          quiz_length?: number
          score?: number | null
          time_taken?: number | null
          topic_id?: string | null
          total_questions?: number | null
          user_id?: string
        }
        Relationships: []
      }
      settings: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          is_public: boolean | null
          setting_key: string
          setting_value: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          is_public?: boolean | null
          setting_key: string
          setting_value?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          is_public?: boolean | null
          setting_key?: string
          setting_value?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      subscriptions: {
        Row: {
          amount_paid: number
          created_at: string | null
          end_date: string
          id: string
          is_active: boolean | null
          last_payment_reference: string | null
          plan_id: string
          start_date: string
          subscription_code: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          amount_paid: number
          created_at?: string | null
          end_date: string
          id?: string
          is_active?: boolean | null
          last_payment_reference?: string | null
          plan_id: string
          start_date: string
          subscription_code?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          amount_paid?: number
          created_at?: string | null
          end_date?: string
          id?: string
          is_active?: boolean | null
          last_payment_reference?: string | null
          plan_id?: string
          start_date?: string
          subscription_code?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      topics: {
        Row: {
          created_at: string | null
          created_by: string | null
          description: string | null
          difficulty: string | null
          domain_id: string | null
          icon: string | null
          id: string
          is_active: boolean | null
          is_premium: boolean | null
          title: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          difficulty?: string | null
          domain_id?: string | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          is_premium?: boolean | null
          title: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          difficulty?: string | null
          domain_id?: string | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          is_premium?: boolean | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "topics_domain_id_fkey"
            columns: ["domain_id"]
            isOneToOne: false
            referencedRelation: "domains"
            referencedColumns: ["id"]
          },
        ]
      }
      user_domain_progress: {
        Row: {
          completed_at: string | null
          completed_topics: number | null
          completion_percentage: number | null
          created_at: string | null
          domain_id: string | null
          id: string
          last_activity_at: string | null
          learning_path_id: string | null
          started_at: string | null
          total_topics: number | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          completed_at?: string | null
          completed_topics?: number | null
          completion_percentage?: number | null
          created_at?: string | null
          domain_id?: string | null
          id?: string
          last_activity_at?: string | null
          learning_path_id?: string | null
          started_at?: string | null
          total_topics?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          completed_at?: string | null
          completed_topics?: number | null
          completion_percentage?: number | null
          created_at?: string | null
          domain_id?: string | null
          id?: string
          last_activity_at?: string | null
          learning_path_id?: string | null
          started_at?: string | null
          total_topics?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_domain_progress_domain_id_fkey"
            columns: ["domain_id"]
            isOneToOne: false
            referencedRelation: "domains"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_domain_progress_learning_path_id_fkey"
            columns: ["learning_path_id"]
            isOneToOne: false
            referencedRelation: "domain_learning_paths"
            referencedColumns: ["id"]
          },
        ]
      }
      user_entitlements: {
        Row: {
          created_at: string
          entitlement_type: string
          expires_at: string | null
          id: string
          meta: Json | null
          provider_ref: string | null
          ref_id: string
          source_payment_id: string | null
          started_at: string
          status: string
          user_id: string
        }
        Insert: {
          created_at?: string
          entitlement_type: string
          expires_at?: string | null
          id?: string
          meta?: Json | null
          provider_ref?: string | null
          ref_id: string
          source_payment_id?: string | null
          started_at?: string
          status?: string
          user_id: string
        }
        Update: {
          created_at?: string
          entitlement_type?: string
          expires_at?: string | null
          id?: string
          meta?: Json | null
          provider_ref?: string | null
          ref_id?: string
          source_payment_id?: string | null
          started_at?: string
          status?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_entitlements_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["user_id"]
          },
        ]
      }
      user_profiles: {
        Row: {
          created_at: string | null
          email: string | null
          full_name: string | null
          id: string
          is_admin: boolean | null
          is_subscribed: boolean | null
          subscription_expires_at: string | null
          subscription_plan: string | null
          subscription_status: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          id?: string
          is_admin?: boolean | null
          is_subscribed?: boolean | null
          subscription_expires_at?: string | null
          subscription_plan?: string | null
          subscription_status?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          id?: string
          is_admin?: boolean | null
          is_subscribed?: boolean | null
          subscription_expires_at?: string | null
          subscription_plan?: string | null
          subscription_status?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      user_quiz_results: {
        Row: {
          answers: Json | null
          created_at: string | null
          id: string
          percentage: number | null
          score: number
          time_taken: number | null
          topic_id: string | null
          total_questions: number
          user_id: string
        }
        Insert: {
          answers?: Json | null
          created_at?: string | null
          id?: string
          percentage?: number | null
          score: number
          time_taken?: number | null
          topic_id?: string | null
          total_questions: number
          user_id: string
        }
        Update: {
          answers?: Json | null
          created_at?: string | null
          id?: string
          percentage?: number | null
          score?: number
          time_taken?: number | null
          topic_id?: string | null
          total_questions?: number
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_quiz_results_topic_id_fkey"
            columns: ["topic_id"]
            isOneToOne: false
            referencedRelation: "topics"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      check_expired_subscriptions: {
        Args: Record<PropertyKey, never>
        Returns: {
          expired_at: string
          subscription_id: string
          user_id: string
        }[]
      }
      cleanup_expired_quiz_sessions: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      delete_user: {
        Args: { user_id_param: string }
        Returns: undefined
      }
      execute_sql: {
        Args: { query: string }
        Returns: Json
      }
      get_all_user_profiles: {
        Args: Record<PropertyKey, never>
        Returns: {
          created_at: string
          email: string
          full_name: string
          id: string
          is_admin: boolean
          is_subscribed: boolean
          last_sign_in_at: string
          subscription_expires_at: string
          subscription_plan: string
          subscription_status: string
          updated_at: string
          user_id: string
        }[]
      }
      get_all_users: {
        Args: Record<PropertyKey, never>
        Returns: {
          created_at: string
          email: string
          id: string
          is_admin: boolean
          is_subscribed: boolean
          last_sign_in_at: string
          subscription_expires_at: string
        }[]
      }
      get_all_users_simple: {
        Args: Record<PropertyKey, never>
        Returns: {
          created_at: string
          email: string
          id: string
          is_admin: boolean
          is_subscribed: boolean
          last_sign_in_at: string
          subscription_expires_at: string
        }[]
      }
      get_user_profile_with_email: {
        Args: { user_uuid: string }
        Returns: {
          created_at: string
          email: string
          full_name: string
          id: string
          is_admin: boolean
          is_subscribed: boolean
          subscription_expires_at: string
          subscription_plan: string
          subscription_status: string
          updated_at: string
          user_id: string
        }[]
      }
      handle_payment_success: {
        Args: {
          p_amount: number
          p_plan_id: string
          p_reference: string
          p_user_email: string
        }
        Returns: Json
      }
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_future: {
        Args: { ts: string }
        Returns: boolean
      }
      record_quiz_result: {
        Args: {
          p_answers?: Json
          p_score: number
          p_time_taken?: number
          p_topic_id: string
          p_total_questions: number
        }
        Returns: string
      }
      reset_daily_questions: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      run_sql: {
        Args: Record<PropertyKey, never> | { sql: string }
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
