<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Domains Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🧪 Simple Domains Test</h1>
    <button onclick="testDomains()">Test Domains API</button>
    <div id="results"></div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js';
        
        const supabase = createClient(
            'https://agdyycknlxojiwhlqicq.supabase.co',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFnZHl5Y2tubHhvaml3aGxxaWNxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyMjkzOTgsImV4cCI6MjA1OTgwNTM5OH0.lWZLRByfsyRqkK7XZfi21qSeEuOZHJKkFJGC_2ojQR8'
        );
        
        async function testDomains() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="result">🧪 Testing...</div>';
            
            try {
                console.log('Testing domains API...');
                
                const { data, error } = await supabase
                    .from('domains')
                    .select('*')
                    .eq('is_active', true)
                    .order('sort_order');
                
                if (error) {
                    resultsDiv.innerHTML = `<div class="result error">❌ Error: ${error.message}</div>`;
                    console.error('Error:', error);
                    return;
                }
                
                resultsDiv.innerHTML = `
                    <div class="result success">✅ Success! Found ${data.length} domains</div>
                    <div class="result">
                        <h3>Domains:</h3>
                        ${data.map(d => `<div>• ${d.name} (${d.difficulty_level})</div>`).join('')}
                    </div>
                `;
                
                console.log('Domains data:', data);
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="result error">❌ Exception: ${error.message}</div>`;
                console.error('Exception:', error);
            }
        }
        
        window.testDomains = testDomains;
        
        // Auto-test on load
        setTimeout(testDomains, 1000);
    </script>
</body>
</html>
