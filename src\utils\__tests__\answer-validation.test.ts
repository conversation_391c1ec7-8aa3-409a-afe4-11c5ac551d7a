import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  parseCorrectAnswer,
  validateAnswer,
  parseQuestionOptions,
  logAnswerValidation
} from '../answer-validation';

describe('parseCorrectAnswer', () => {
  let consoleSpy: any;

  beforeEach(() => {
    consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleSpy.mockRestore();
  });

  describe('valid inputs', () => {
    it('should parse valid number correctly', () => {
      const result = parseCorrectAnswer(2, 4);
      expect(result.correctIndex).toBe(2);
      expect(result.isValid).toBe(true);
      expect(result.errorMessage).toBeUndefined();
    });

    it('should parse valid string number correctly', () => {
      const result = parseCorrectAnswer('1', 4);
      expect(result.correctIndex).toBe(1);
      expect(result.isValid).toBe(true);
      expect(result.errorMessage).toBeUndefined();
    });

    it('should parse string with whitespace correctly', () => {
      const result = parseCorrectAnswer('  3  ', 4);
      expect(result.correctIndex).toBe(3);
      expect(result.isValid).toBe(true);
    });

    it('should parse boolean true as 1', () => {
      const result = parseCorrectAnswer(true, 4);
      expect(result.correctIndex).toBe(1);
      expect(result.isValid).toBe(true);
    });

    it('should parse boolean false as 0', () => {
      const result = parseCorrectAnswer(false, 4);
      expect(result.correctIndex).toBe(0);
      expect(result.isValid).toBe(true);
    });
  });

  describe('edge cases and error handling', () => {
    it('should handle null input', () => {
      const result = parseCorrectAnswer(null, 4);
      expect(result.correctIndex).toBe(0);
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toContain('null or undefined');
    });

    it('should handle undefined input', () => {
      const result = parseCorrectAnswer(undefined, 4);
      expect(result.correctIndex).toBe(0);
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toContain('null or undefined');
    });

    it('should handle NaN input', () => {
      const result = parseCorrectAnswer(NaN, 4);
      expect(result.correctIndex).toBe(0);
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toContain('NaN or infinite');
    });

    it('should handle Infinity input', () => {
      const result = parseCorrectAnswer(Infinity, 4);
      expect(result.correctIndex).toBe(0);
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toContain('NaN or infinite');
    });

    it('should handle empty string', () => {
      const result = parseCorrectAnswer('', 4);
      expect(result.correctIndex).toBe(0);
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toContain('empty string');
    });

    it('should handle non-numeric string', () => {
      const result = parseCorrectAnswer('abc', 4);
      expect(result.correctIndex).toBe(0);
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toContain('Cannot parse');
    });

    it('should handle out of range positive number', () => {
      const result = parseCorrectAnswer(5, 4);
      expect(result.correctIndex).toBe(3); // Clamped to max valid index
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toContain('out of range');
    });

    it('should handle negative number', () => {
      const result = parseCorrectAnswer(-1, 4);
      expect(result.correctIndex).toBe(0); // Clamped to min valid index
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toContain('out of range');
    });

    it('should handle decimal number', () => {
      const result = parseCorrectAnswer(2.7, 4);
      expect(result.correctIndex).toBe(2); // Floor of 2.7
      expect(result.isValid).toBe(true);
    });

    it('should handle object input', () => {
      const result = parseCorrectAnswer({ value: 1 }, 4);
      expect(result.correctIndex).toBe(0);
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toContain('Unsupported');
    });

    it('should handle array input', () => {
      const result = parseCorrectAnswer([1, 2], 4);
      expect(result.correctIndex).toBe(0);
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toContain('Unsupported');
    });

    it('should handle boolean true with insufficient options', () => {
      const result = parseCorrectAnswer(true, 1); // Only 1 option, but boolean true = index 1
      expect(result.correctIndex).toBe(0);
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toContain('exceeds options count');
    });
  });
});

describe('validateAnswer', () => {
  let consoleSpy: any;

  beforeEach(() => {
    consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleSpy.mockRestore();
  });

  it('should validate correct answer', () => {
    const result = validateAnswer(2, 2, 4);
    expect(result.isCorrect).toBe(true);
    expect(result.selectedIndex).toBe(2);
    expect(result.correctIndex).toBe(2);
    expect(result.confidence).toBe('high');
    expect(result.warnings).toHaveLength(0);
  });

  it('should validate incorrect answer', () => {
    const result = validateAnswer(1, 2, 4);
    expect(result.isCorrect).toBe(false);
    expect(result.selectedIndex).toBe(1);
    expect(result.correctIndex).toBe(2);
    expect(result.confidence).toBe('high');
    expect(result.warnings).toHaveLength(0);
  });

  it('should handle null selected index', () => {
    const result = validateAnswer(null, 2, 4);
    expect(result.isCorrect).toBe(false);
    expect(result.selectedIndex).toBe(-1);
    expect(result.correctIndex).toBe(2);
    expect(result.confidence).toBe('high');
    expect(result.warnings).toContain('No answer selected');
  });

  it('should handle invalid selected index', () => {
    const result = validateAnswer(NaN, 2, 4);
    expect(result.isCorrect).toBe(false);
    expect(result.selectedIndex).toBe(-1);
    expect(result.confidence).toBe('low');
    expect(result.warnings).toContain('not a valid number');
  });

  it('should handle out of range selected index', () => {
    const result = validateAnswer(5, 2, 4);
    expect(result.isCorrect).toBe(false);
    expect(result.selectedIndex).toBe(5);
    expect(result.correctIndex).toBe(2);
    expect(result.confidence).toBe('low');
    expect(result.warnings).toContain('out of range');
  });

  it('should handle invalid correct answer with medium confidence', () => {
    const result = validateAnswer(0, 'invalid', 4);
    expect(result.isCorrect).toBe(true); // 0 matches default fallback
    expect(result.selectedIndex).toBe(0);
    expect(result.correctIndex).toBe(0);
    expect(result.confidence).toBe('low'); // Low confidence due to parsing error
    expect(result.warnings.length).toBeGreaterThan(0);
  });
});

describe('parseQuestionOptions', () => {
  let consoleSpy: any;

  beforeEach(() => {
    consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleSpy.mockRestore();
  });

  it('should parse array options correctly', () => {
    const options = ['Option A', 'Option B', 'Option C', 'Option D'];
    const result = parseQuestionOptions(options);
    expect(result).toEqual(options);
  });

  it('should parse object options with numeric keys', () => {
    const options = { '0': 'Option A', '1': 'Option B', '2': 'Option C' };
    const result = parseQuestionOptions(options);
    expect(result).toEqual(['Option A', 'Option B', 'Option C']);
  });

  it('should parse object options with unordered keys', () => {
    const options = { '2': 'Option C', '0': 'Option A', '1': 'Option B' };
    const result = parseQuestionOptions(options);
    expect(result).toEqual(['Option A', 'Option B', 'Option C']);
  });

  it('should parse JSON string options', () => {
    const options = '["Option A", "Option B", "Option C"]';
    const result = parseQuestionOptions(options);
    expect(result).toEqual(['Option A', 'Option B', 'Option C']);
  });

  it('should parse JSON object string', () => {
    const options = '{"0": "Option A", "1": "Option B"}';
    const result = parseQuestionOptions(options);
    expect(result).toEqual(['Option A', 'Option B']);
  });

  it('should handle null options', () => {
    const result = parseQuestionOptions(null);
    expect(result).toEqual(['Option not available']);
  });

  it('should handle undefined options', () => {
    const result = parseQuestionOptions(undefined);
    expect(result).toEqual(['Option not available']);
  });

  it('should handle array with null values', () => {
    const options = ['Option A', null, 'Option C'];
    const result = parseQuestionOptions(options);
    expect(result).toEqual(['Option A', 'Option 2', 'Option C']);
  });

  it('should handle array with mixed types', () => {
    const options = ['Option A', 42, true, null];
    const result = parseQuestionOptions(options);
    expect(result).toEqual(['Option A', '42', 'true', 'Option 4']);
  });

  it('should handle invalid JSON string', () => {
    const options = 'invalid json {';
    const result = parseQuestionOptions(options);
    expect(result).toEqual(['invalid json {']);
  });

  it('should handle non-string, non-array, non-object input', () => {
    const result = parseQuestionOptions(42);
    expect(result).toEqual(['Option not available']);
  });

  it('should handle object with non-numeric keys', () => {
    const options = { 'a': 'Option A', 'b': 'Option B' };
    const result = parseQuestionOptions(options);
    expect(result).toEqual(['Option A', 'Option B']);
  });
});

describe('logAnswerValidation', () => {
  let consoleLogSpy: any;
  let consoleWarnSpy: any;

  beforeEach(() => {
    consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleLogSpy.mockRestore();
    consoleWarnSpy.mockRestore();
  });

  it('should log successful validation', () => {
    const validationResult = {
      isCorrect: true,
      selectedIndex: 1,
      correctIndex: 1,
      confidence: 'high' as const,
      warnings: []
    };

    logAnswerValidation('q1', 1, 1, validationResult);
    
    expect(consoleLogSpy).toHaveBeenCalledWith(
      'Answer validation successful:',
      expect.objectContaining({
        questionId: 'q1',
        originalCorrectAnswer: 1,
        selectedIndex: 1,
        validationResult
      })
    );
    expect(consoleWarnSpy).not.toHaveBeenCalled();
  });

  it('should warn for validation with warnings', () => {
    const validationResult = {
      isCorrect: false,
      selectedIndex: 1,
      correctIndex: 0,
      confidence: 'medium' as const,
      warnings: ['Some warning']
    };

    logAnswerValidation('q1', 'invalid', 1, validationResult);
    
    expect(consoleWarnSpy).toHaveBeenCalledWith(
      'Answer validation issues detected:',
      expect.objectContaining({
        questionId: 'q1',
        originalCorrectAnswer: 'invalid',
        selectedIndex: 1,
        validationResult
      })
    );
    expect(consoleLogSpy).not.toHaveBeenCalled();
  });

  it('should warn for low confidence validation', () => {
    const validationResult = {
      isCorrect: true,
      selectedIndex: 0,
      correctIndex: 0,
      confidence: 'low' as const,
      warnings: []
    };

    logAnswerValidation('q1', null, 0, validationResult);
    
    expect(consoleWarnSpy).toHaveBeenCalled();
    expect(consoleLogSpy).not.toHaveBeenCalled();
  });
});