import { describe, it, expect } from 'vitest';
import {
  validateTopicReferences,
  validateQuestionContent,
  detectCrossTopicDuplicates,
  performCrossTopicValidation,
  validateTopicResolution,
  validateTopicResolutionFailures,
  validateCrossTopicConsistency,
  validateSingleTopicReference,
  groupErrorsByTopic,
  generateActionableErrorMessage,
  generateErrorSummary,
  DEFAULT_VALIDATION_CONFIG,
  type ValidationConfig,
  type MultiTopicQuestionCSVRow,
  type TopicResolutionResult,
  type CrossTopicValidationResult,
} from '../multi-topic-validation';

describe('Multi-Topic Validation', () => {
  describe('validateTopicReferences', () => {
    it('should validate valid topic references', () => {
      const topicReferences = ['Security Fundamentals', 'Network Security', 'Incident Response'];
      const result = validateTopicReferences(topicReferences);

      expect(result.size).toBe(3);
      
      for (const [reference, validation] of result) {
        expect(validation.topicReference).toBe(reference);
        expect(validation.isValid).toBe(true);
        expect(validation.errors).toHaveLength(0);
      }
    });

    it('should detect empty topic references', () => {
      const topicReferences = ['Valid Topic', '', '   ', 'Another Valid Topic'];
      const result = validateTopicReferences(topicReferences);

      expect(result.size).toBe(4);
      
      const emptyResult = result.get('');
      expect(emptyResult?.isValid).toBe(false);
      expect(emptyResult?.errors).toHaveLength(1);
      expect(emptyResult?.errors[0].message).toContain('cannot be empty');

      const whitespaceResult = result.get('   ');
      expect(whitespaceResult?.isValid).toBe(false);
      expect(whitespaceResult?.errors).toHaveLength(1);
      expect(whitespaceResult?.errors[0].message).toContain('cannot be empty');
    });

    it('should detect topic names that are too long', () => {
      const longTopicName = 'A'.repeat(101); // Exceeds default max length of 100
      const topicReferences = [longTopicName];
      const result = validateTopicReferences(topicReferences);

      const validation = result.get(longTopicName);
      expect(validation?.isValid).toBe(false);
      expect(validation?.errors).toHaveLength(1);
      expect(validation?.errors[0].message).toContain('exceeds maximum length');
    });

    it('should detect invalid characters in topic names', () => {
      const invalidTopicNames = ['Topic<Test>', 'Topic/Test', 'Topic|Test', 'Topic?Test'];
      
      invalidTopicNames.forEach(topicName => {
        const result = validateTopicReferences([topicName]);
        const validation = result.get(topicName);
        
        expect(validation?.isValid).toBe(false);
        expect(validation?.errors).toHaveLength(1);
        expect(validation?.errors[0].message).toContain('invalid characters');
      });
    });

    it('should warn about potentially temporary topic names', () => {
      const tempTopicNames = ['Test Topic', 'Temp Security', 'testing stuff'];
      
      tempTopicNames.forEach(topicName => {
        const result = validateTopicReferences([topicName]);
        const validation = result.get(topicName);
        
        expect(validation?.isValid).toBe(true); // Still valid, just a warning
        expect(validation?.warnings).toHaveLength(1);
        expect(validation?.warnings[0].message).toContain('temporary or for testing');
      });
    });

    it('should warn about leading/trailing whitespace', () => {
      const topicReferences = [' Leading Space', 'Trailing Space ', '  Both Sides  '];
      const result = validateTopicReferences(topicReferences);

      topicReferences.forEach(reference => {
        const validation = result.get(reference);
        expect(validation?.isValid).toBe(true);
        expect(validation?.warnings).toHaveLength(1);
        expect(validation?.warnings[0].message).toContain('leading or trailing whitespace');
      });
    });

    it('should respect custom validation config', () => {
      const customConfig: ValidationConfig = {
        ...DEFAULT_VALIDATION_CONFIG,
        enableTopicNameValidation: false,
      };

      const invalidTopicNames = ['', 'A'.repeat(200), 'Topic<Invalid>'];
      const result = validateTopicReferences(invalidTopicNames, customConfig);

      // All should be valid when validation is disabled
      for (const validation of result.values()) {
        expect(validation.isValid).toBe(true);
        expect(validation.errors).toHaveLength(0);
      }
    });
  });

  describe('validateQuestionContent', () => {
    const validRow: MultiTopicQuestionCSVRow = {
      topic_name: 'Security',
      question_text: 'What is a firewall used for?',
      option_a: 'Physical security',
      option_b: 'Network traffic control',
      option_c: 'Data encryption',
      option_d: 'Malware detection',
      correct_answer: 'B',
      explanation: 'A firewall controls network traffic based on security rules.',
      difficulty: 'medium',
    };

    it('should validate a correct question', () => {
      const errors = validateQuestionContent(validRow, 0);
      expect(errors).toHaveLength(0);
    });

    it('should detect question text that is too short', () => {
      const shortRow = { ...validRow, question_text: 'Short?' };
      const errors = validateQuestionContent(shortRow, 0);
      
      const shortError = errors.find(e => e.message.includes('too short'));
      expect(shortError).toBeDefined();
      expect(shortError?.severity).toBe('error');
      expect(shortError?.category).toBe('question');
    });

    it('should detect question text that is too long', () => {
      const longRow = { ...validRow, question_text: 'A'.repeat(1001) };
      const errors = validateQuestionContent(longRow, 0);
      
      const longError = errors.find(e => e.message.includes('exceeds maximum length'));
      expect(longError).toBeDefined();
      expect(longError?.severity).toBe('error');
    });

    it('should warn about questions without question marks', () => {
      const noQuestionMarkRow = { ...validRow, question_text: 'What is a firewall used for' };
      const errors = validateQuestionContent(noQuestionMarkRow, 0);
      
      const questionMarkError = errors.find(e => e.message.includes('question mark'));
      expect(questionMarkError).toBeDefined();
      expect(questionMarkError?.severity).toBe('warning');
    });

    it('should detect missing options', () => {
      const missingOptionsRow = { ...validRow, option_c: '', option_d: '' };
      const errors = validateQuestionContent(missingOptionsRow, 0);
      
      const missingError = errors.find(e => e.message.includes('All four options'));
      expect(missingError).toBeDefined();
      expect(missingError?.severity).toBe('error');
    });

    it('should detect duplicate options', () => {
      const duplicateRow = { 
        ...validRow, 
        option_a: 'Same option',
        option_b: 'Same option',
      };
      const errors = validateQuestionContent(duplicateRow, 0);
      
      const duplicateError = errors.find(e => e.message.includes('duplicated'));
      expect(duplicateError).toBeDefined();
      expect(duplicateError?.severity).toBe('warning');
    });

    it('should detect options that are too long', () => {
      const longOptionRow = { ...validRow, option_a: 'A'.repeat(201) };
      const errors = validateQuestionContent(longOptionRow, 0);
      
      const longOptionError = errors.find(e => e.message.includes('Option A is too long'));
      expect(longOptionError).toBeDefined();
      expect(longOptionError?.severity).toBe('error');
    });

    it('should detect invalid difficulty values', () => {
      const invalidDifficultyRow = { ...validRow, difficulty: 'impossible' };
      const errors = validateQuestionContent(invalidDifficultyRow, 0);
      
      const difficultyError = errors.find(e => e.message.includes('Invalid difficulty'));
      expect(difficultyError).toBeDefined();
      expect(difficultyError?.severity).toBe('error');
    });

    it('should validate explanation length', () => {
      const shortExplanationRow = { ...validRow, explanation: 'Too short' };
      const errors = validateQuestionContent(shortExplanationRow, 0);
      
      const shortExplanationError = errors.find(e => e.message.includes('Explanation is too short'));
      expect(shortExplanationError).toBeDefined();
      expect(shortExplanationError?.severity).toBe('warning');

      const longExplanationRow = { ...validRow, explanation: 'A'.repeat(501) };
      const longErrors = validateQuestionContent(longExplanationRow, 0);
      
      const longExplanationError = longErrors.find(e => e.message.includes('Explanation is too long'));
      expect(longExplanationError).toBeDefined();
      expect(longExplanationError?.severity).toBe('error');
    });

    it('should respect custom validation config', () => {
      const customConfig: ValidationConfig = {
        ...DEFAULT_VALIDATION_CONFIG,
        enableQuestionContentValidation: false,
      };

      const invalidRow = { ...validRow, question_text: '', difficulty: 'invalid' };
      const errors = validateQuestionContent(invalidRow, 0, customConfig);
      
      expect(errors).toHaveLength(0);
    });

    it('should include correct row numbers in errors', () => {
      const invalidRow = { ...validRow, question_text: 'Short?' };
      const errors = validateQuestionContent(invalidRow, 5); // Row index 5
      
      expect(errors[0].row).toBe(6); // Should be index + 1
    });
  });

  describe('detectCrossTopicDuplicates', () => {
    it('should detect exact duplicate questions across topics', () => {
      const csvData: MultiTopicQuestionCSVRow[] = [
        {
          topic_name: 'Security Fundamentals',
          question_text: 'What is a firewall?',
          option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
          correct_answer: 'A', explanation: 'Test', difficulty: 'easy',
        },
        {
          topic_name: 'Network Security',
          question_text: 'What is a firewall?', // Exact duplicate
          option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
          correct_answer: 'A', explanation: 'Test', difficulty: 'easy',
        },
      ];

      const duplicates = detectCrossTopicDuplicates(csvData);
      
      expect(duplicates).toHaveLength(1);
      expect(duplicates[0].occurrences).toHaveLength(2);
      expect(duplicates[0].occurrences[0].topicReference).toBe('Security Fundamentals');
      expect(duplicates[0].occurrences[1].topicReference).toBe('Network Security');
    });

    it('should detect normalized duplicate questions', () => {
      const csvData: MultiTopicQuestionCSVRow[] = [
        {
          topic_name: 'Topic A',
          question_text: 'What is a firewall?',
          option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
          correct_answer: 'A', explanation: 'Test', difficulty: 'easy',
        },
        {
          topic_name: 'Topic B',
          question_text: '  What is a firewall???  ', // Different punctuation and spacing
          option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
          correct_answer: 'A', explanation: 'Test', difficulty: 'easy',
        },
      ];

      const duplicates = detectCrossTopicDuplicates(csvData);
      
      expect(duplicates).toHaveLength(1);
      expect(duplicates[0].occurrences).toHaveLength(2);
    });

    it('should not detect duplicates within the same topic', () => {
      const csvData: MultiTopicQuestionCSVRow[] = [
        {
          topic_name: 'Same Topic',
          question_text: 'What is a firewall?',
          option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
          correct_answer: 'A', explanation: 'Test', difficulty: 'easy',
        },
        {
          topic_name: 'Same Topic',
          question_text: 'What is a firewall?',
          option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
          correct_answer: 'A', explanation: 'Test', difficulty: 'easy',
        },
      ];

      const duplicates = detectCrossTopicDuplicates(csvData);
      
      // Should still detect as duplicate since it's the same question text
      expect(duplicates).toHaveLength(1);
    });

    it('should handle empty or missing question text', () => {
      const csvData: MultiTopicQuestionCSVRow[] = [
        {
          topic_name: 'Topic A',
          question_text: '',
          option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
          correct_answer: 'A', explanation: 'Test', difficulty: 'easy',
        },
        {
          topic_name: 'Topic B',
          // question_text is undefined
          option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
          correct_answer: 'A', explanation: 'Test', difficulty: 'easy',
        } as MultiTopicQuestionCSVRow,
      ];

      const duplicates = detectCrossTopicDuplicates(csvData);
      
      expect(duplicates).toHaveLength(0);
    });

    it('should respect validation config', () => {
      const csvData: MultiTopicQuestionCSVRow[] = [
        {
          topic_name: 'Topic A',
          question_text: 'What is a firewall?',
          option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
          correct_answer: 'A', explanation: 'Test', difficulty: 'easy',
        },
        {
          topic_name: 'Topic B',
          question_text: 'What is a firewall?',
          option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
          correct_answer: 'A', explanation: 'Test', difficulty: 'easy',
        },
      ];

      const customConfig: ValidationConfig = {
        ...DEFAULT_VALIDATION_CONFIG,
        enableCrossTopicDuplicateDetection: false,
      };

      const duplicates = detectCrossTopicDuplicates(csvData, customConfig);
      
      expect(duplicates).toHaveLength(0);
    });
  });

  describe('performCrossTopicValidation', () => {
    const validCSVData: MultiTopicQuestionCSVRow[] = [
      {
        topic_name: 'Security Fundamentals',
        question_text: 'What is a firewall used for?',
        option_a: 'Physical security',
        option_b: 'Network traffic control',
        option_c: 'Data encryption',
        option_d: 'Malware detection',
        correct_answer: 'B',
        explanation: 'A firewall controls network traffic based on security rules.',
        difficulty: 'medium',
      },
      {
        topic_name: 'Network Security',
        question_text: 'What does VPN stand for?',
        option_a: 'Virtual Private Network',
        option_b: 'Very Personal Network',
        option_c: 'Verified Public Network',
        option_d: 'Variable Protocol Network',
        correct_answer: 'A',
        explanation: 'VPN stands for Virtual Private Network.',
        difficulty: 'easy',
      },
    ];

    it('should validate correct multi-topic data', () => {
      const topicReferences = ['Security Fundamentals', 'Network Security'];
      const result = performCrossTopicValidation(validCSVData, topicReferences);

      expect(result.isValid).toBe(true);
      expect(result.duplicateQuestions).toHaveLength(0);
      expect(result.topicValidations.size).toBe(2);
      expect(result.globalErrors).toHaveLength(0);
    });

    it('should detect multiple validation issues', () => {
      const invalidCSVData: MultiTopicQuestionCSVRow[] = [
        {
          topic_name: 'Invalid<Topic>', // Invalid characters
          question_text: 'Short?', // Too short
          option_a: 'A', option_b: 'A', option_c: 'C', option_d: 'D', // Duplicate options
          correct_answer: 'A',
          explanation: 'Short', // Too short explanation
          difficulty: 'invalid', // Invalid difficulty
        },
        {
          topic_name: 'Valid Topic',
          question_text: 'Short?', // Duplicate question
          option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
          correct_answer: 'A',
          explanation: 'Valid explanation here.',
          difficulty: 'easy',
        },
      ];

      const topicReferences = ['Invalid<Topic>', 'Valid Topic'];
      const result = performCrossTopicValidation(invalidCSVData, topicReferences);

      expect(result.isValid).toBe(false);
      expect(result.duplicateQuestions).toHaveLength(1);
      expect(result.topicValidations.size).toBe(2);
      expect(result.globalErrors.length).toBeGreaterThan(0);

      // Check that invalid topic was detected
      const invalidTopicValidation = result.topicValidations.get('Invalid<Topic>');
      expect(invalidTopicValidation?.isValid).toBe(false);
    });

    it('should handle empty topic references', () => {
      const result = performCrossTopicValidation(validCSVData, []);

      expect(result.isValid).toBe(true);
      expect(result.topicValidations.size).toBe(0);
    });
  });

  describe('validateTopicResolution', () => {
    const csvData: MultiTopicQuestionCSVRow[] = [
      {
        topic_name: 'Existing Topic',
        question_text: 'Test question?',
        option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
        correct_answer: 'A', explanation: 'Test explanation', difficulty: 'easy',
      },
      {
        topic_name: 'Missing Topic',
        question_text: 'Another test question?',
        option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
        correct_answer: 'A', explanation: 'Test explanation', difficulty: 'easy',
      },
    ];

    it('should handle successful topic resolution', () => {
      const topicResolution: TopicResolutionResult = {
        resolved: new Map([['Existing Topic', 'topic-123']]),
        missing: [],
        created: [],
        errors: [],
      };

      const errors = validateTopicResolution(topicResolution, csvData);
      
      expect(errors).toHaveLength(0);
    });

    it('should handle missing topics with actionable messages', () => {
      const topicResolution: TopicResolutionResult = {
        resolved: new Map([['Existing Topic', 'topic-123']]),
        missing: ['Missing Topic'],
        created: [],
        errors: [],
      };

      const errors = validateTopicResolution(topicResolution, csvData);
      
      expect(errors).toHaveLength(1);
      expect(errors[0].message).toContain('Topic "Missing Topic" not found');
      expect(errors[0].message).toContain('Affects rows: 2');
      expect(errors[0].message).toContain('Suggestion:');
    });

    it('should handle topic creation success', () => {
      const topicResolution: TopicResolutionResult = {
        resolved: new Map([
          ['Existing Topic', 'topic-123'],
          ['New Topic', 'topic-456'],
        ]),
        missing: [],
        created: ['New Topic'],
        errors: [],
      };

      const errors = validateTopicResolution(topicResolution, csvData);
      
      expect(errors).toHaveLength(1);
      expect(errors[0].message).toContain('Successfully created 1 new topics');
      expect(errors[0].severity).toBe('warning'); // Informational
    });

    it('should handle topic resolution errors', () => {
      const topicResolution: TopicResolutionResult = {
        resolved: new Map(),
        missing: [],
        created: [],
        errors: ['Database connection failed', 'Invalid topic name format'],
      };

      const errors = validateTopicResolution(topicResolution, csvData);
      
      expect(errors).toHaveLength(2);
      errors.forEach(error => {
        expect(error.message).toContain('Topic resolution error:');
        expect(error.message).toContain('Suggestion:');
      });
    });
  });

  describe('groupErrorsByTopic', () => {
    const csvData: MultiTopicQuestionCSVRow[] = [
      { topic_name: 'Topic A', question_text: 'Question 1?', option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D', correct_answer: 'A', explanation: 'Test', difficulty: 'easy' },
      { topic_name: 'Topic B', question_text: 'Question 2?', option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D', correct_answer: 'A', explanation: 'Test', difficulty: 'easy' },
      { topic_name: 'Topic A', question_text: 'Question 3?', option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D', correct_answer: 'A', explanation: 'Test', difficulty: 'easy' },
    ];

    it('should group errors by topic correctly', () => {
      const errors = [
        { row: 1, message: 'Error in row 1', severity: 'error' as const, category: 'question' as const },
        { row: 2, message: 'Error in row 2', severity: 'error' as const, category: 'question' as const },
        { row: 3, message: 'Error in row 3', severity: 'error' as const, category: 'question' as const },
        { row: 0, message: 'Global error', severity: 'error' as const, category: 'topic' as const },
      ];

      const grouped = groupErrorsByTopic(errors, csvData);

      expect(grouped.size).toBe(3);
      expect(grouped.get('Topic A')).toHaveLength(2); // Rows 1 and 3
      expect(grouped.get('Topic B')).toHaveLength(1); // Row 2
      expect(grouped.get('_global')).toHaveLength(1); // Global error
    });

    it('should handle errors with invalid row numbers', () => {
      const errors = [
        { row: 999, message: 'Invalid row', severity: 'error' as const, category: 'question' as const },
      ];

      const grouped = groupErrorsByTopic(errors, csvData);

      expect(grouped.size).toBe(0); // Invalid row should not be grouped
    });

    it('should handle rows without topic information', () => {
      const csvDataWithoutTopic: MultiTopicQuestionCSVRow[] = [
        { question_text: 'Question without topic?', option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D', correct_answer: 'A', explanation: 'Test', difficulty: 'easy' },
      ];

      const errors = [
        { row: 1, message: 'Error in row without topic', severity: 'error' as const, category: 'question' as const },
      ];

      const grouped = groupErrorsByTopic(errors, csvDataWithoutTopic);

      expect(grouped.size).toBe(1);
      expect(grouped.get('_unknown')).toHaveLength(1);
    });
  });

  describe('validateTopicResolutionFailures', () => {
    const csvData: MultiTopicQuestionCSVRow[] = [
      {
        topic_name: 'Valid Topic',
        question_text: 'Test question?',
        option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
        correct_answer: 'A', explanation: 'Test explanation', difficulty: 'easy',
      },
      {
        topic_name: 'Failed Topic',
        question_text: 'Another test question?',
        option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
        correct_answer: 'A', explanation: 'Test explanation', difficulty: 'easy',
      },
      {
        topic_name: 'Failed Topic',
        question_text: 'Third test question?',
        option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
        correct_answer: 'A', explanation: 'Test explanation', difficulty: 'easy',
      },
    ];

    it('should provide detailed error messages for failed topic resolution', () => {
      const failedTopics = ['Failed Topic'];
      const errors = validateTopicResolutionFailures(failedTopics, csvData, false);

      expect(errors).toHaveLength(1);
      expect(errors[0].message).toContain('Topic "Failed Topic" could not be resolved');
      expect(errors[0].message).toContain('Affects rows: 2, 3');
      expect(errors[0].message).toContain('Enable auto-create topics option');
    });

    it('should provide different suggestions when auto-create is enabled', () => {
      const failedTopics = ['Failed Topic'];
      const errors = validateTopicResolutionFailures(failedTopics, csvData, true);

      expect(errors).toHaveLength(1);
      expect(errors[0].message).toContain('Check database connectivity');
      expect(errors[0].message).toContain('Verify topic name follows naming conventions');
    });

    it('should detect whitespace issues in topic names', () => {
      const csvDataWithWhitespace: MultiTopicQuestionCSVRow[] = [
        {
          topic_name: '  Whitespace Topic  ',
          question_text: 'Test question?',
          option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
          correct_answer: 'A', explanation: 'Test explanation', difficulty: 'easy',
        },
      ];

      const failedTopics = ['  Whitespace Topic  '];
      const errors = validateTopicResolutionFailures(failedTopics, csvDataWithWhitespace, false);

      expect(errors).toHaveLength(1);
      expect(errors[0].message).toContain('Remove leading/trailing spaces');
    });

    it('should detect invalid characters in topic names', () => {
      const csvDataWithInvalidChars: MultiTopicQuestionCSVRow[] = [
        {
          topic_name: 'Invalid<Topic>',
          question_text: 'Test question?',
          option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
          correct_answer: 'A', explanation: 'Test explanation', difficulty: 'easy',
        },
      ];

      const failedTopics = ['Invalid<Topic>'];
      const errors = validateTopicResolutionFailures(failedTopics, csvDataWithInvalidChars, false);

      expect(errors).toHaveLength(1);
      expect(errors[0].message).toContain('Remove invalid characters');
    });

    it('should handle empty topic references', () => {
      const csvDataWithEmpty: MultiTopicQuestionCSVRow[] = [
        {
          topic_name: '',
          question_text: 'Test question?',
          option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
          correct_answer: 'A', explanation: 'Test explanation', difficulty: 'easy',
        },
      ];

      const failedTopics = [''];
      const errors = validateTopicResolutionFailures(failedTopics, csvDataWithEmpty, false);

      expect(errors).toHaveLength(1);
      expect(errors[0].message).toContain('Provide a valid topic name');
    });
  });

  describe('validateCrossTopicConsistency', () => {
    it('should warn about topics with too few questions', () => {
      const csvData: MultiTopicQuestionCSVRow[] = [
        {
          topic_name: 'Topic A',
          question_text: 'Question 1?',
          option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
          correct_answer: 'A', explanation: 'Test', difficulty: 'easy',
        },
        {
          topic_name: 'Topic A',
          question_text: 'Question 2?',
          option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
          correct_answer: 'A', explanation: 'Test', difficulty: 'easy',
        },
        // Topic A has only 2 questions (less than minimum of 3)
      ];

      const topicReferences = ['Topic A'];
      const errors = validateCrossTopicConsistency(csvData, topicReferences);

      expect(errors).toHaveLength(1);
      expect(errors[0].message).toContain('Topic "Topic A" has only 2 question(s)');
      expect(errors[0].severity).toBe('warning');
    });

    it('should warn about unbalanced difficulty distribution', () => {
      const csvData: MultiTopicQuestionCSVRow[] = [
        // All questions are 'easy' difficulty
        ...Array.from({ length: 5 }, (_, i) => ({
          topic_name: 'Unbalanced Topic',
          question_text: `Question ${i + 1}?`,
          option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
          correct_answer: 'A', explanation: 'Test', difficulty: 'easy',
        })),
      ];

      const topicReferences = ['Unbalanced Topic'];
      const errors = validateCrossTopicConsistency(csvData, topicReferences);

      expect(errors).toHaveLength(1);
      expect(errors[0].message).toContain('has all questions at "easy" difficulty');
      expect(errors[0].severity).toBe('warning');
    });

    it('should not warn about balanced topics', () => {
      const csvData: MultiTopicQuestionCSVRow[] = [
        {
          topic_name: 'Balanced Topic',
          question_text: 'Easy question?',
          option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
          correct_answer: 'A', explanation: 'Test', difficulty: 'easy',
        },
        {
          topic_name: 'Balanced Topic',
          question_text: 'Medium question?',
          option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
          correct_answer: 'A', explanation: 'Test', difficulty: 'medium',
        },
        {
          topic_name: 'Balanced Topic',
          question_text: 'Hard question?',
          option_a: 'A', option_b: 'B', option_c: 'C', option_d: 'D',
          correct_answer: 'A', explanation: 'Test', difficulty: 'hard',
        },
      ];

      const topicReferences = ['Balanced Topic'];
      const errors = validateCrossTopicConsistency(csvData, topicReferences);

      expect(errors).toHaveLength(0);
    });
  });

  describe('validateSingleTopicReference', () => {
    it('should validate correct topic references', () => {
      const errors = validateSingleTopicReference('Valid Topic Name', 0);
      expect(errors).toHaveLength(0);
    });

    it('should detect empty topic references', () => {
      const errors = validateSingleTopicReference('', 0);
      expect(errors).toHaveLength(1);
      expect(errors[0].message).toContain('cannot be empty');
      expect(errors[0].severity).toBe('error');
    });

    it('should detect topic names that are too short', () => {
      const errors = validateSingleTopicReference('A', 0);
      expect(errors).toHaveLength(1);
      expect(errors[0].message).toContain('must be at least 2 characters');
    });

    it('should detect topic names that are too long', () => {
      const longName = 'A'.repeat(101);
      const errors = validateSingleTopicReference(longName, 0);
      expect(errors).toHaveLength(1);
      expect(errors[0].message).toContain('exceeds maximum length');
    });

    it('should detect invalid characters', () => {
      const invalidNames = ['Topic<Test>', 'Topic/Test', 'Topic|Test'];
      
      invalidNames.forEach(name => {
        const errors = validateSingleTopicReference(name, 0);
        expect(errors.length).toBeGreaterThan(0);
        expect(errors[0].message).toContain('invalid characters');
      });
    });

    it('should detect names starting or ending with periods', () => {
      const periodNames = ['.Topic', 'Topic.', '.Topic.'];
      
      periodNames.forEach(name => {
        const errors = validateSingleTopicReference(name, 0);
        expect(errors.length).toBeGreaterThan(0);
        expect(errors[0].message).toContain('cannot start or end with a period');
      });
    });

    it('should detect reserved topic names', () => {
      const reservedNames = ['admin', 'system', 'root', 'test', 'temp', 'default'];
      
      reservedNames.forEach(name => {
        const errors = validateSingleTopicReference(name, 0);
        expect(errors.length).toBeGreaterThan(0);
        expect(errors[0].message).toContain('is reserved');
        expect(errors[0].severity).toBe('error');
      });
    });

    it('should warn about temporary-looking names', () => {
      const tempNames = ['Test Topic', 'Temp Security', 'testing stuff'];
      
      tempNames.forEach(name => {
        const errors = validateSingleTopicReference(name, 0);
        const tempWarning = errors.find(e => e.message.includes('temporary or for testing'));
        expect(tempWarning).toBeDefined();
        expect(tempWarning?.severity).toBe('warning');
      });
    });

    it('should warn about whitespace issues', () => {
      const whitespaceNames = [' Leading', 'Trailing ', '  Both  '];
      
      whitespaceNames.forEach(name => {
        const errors = validateSingleTopicReference(name, 0);
        const whitespaceWarning = errors.find(e => e.message.includes('leading or trailing whitespace'));
        expect(whitespaceWarning).toBeDefined();
        expect(whitespaceWarning?.severity).toBe('warning');
      });
    });

    it('should include correct row numbers', () => {
      const errors = validateSingleTopicReference('', 5);
      expect(errors[0].row).toBe(6); // Should be index + 1
    });
  });

  describe('generateErrorSummary', () => {
    it('should generate comprehensive error summary', () => {
      const validationResult: CrossTopicValidationResult = {
        isValid: false,
        duplicateQuestions: [
          {
            questionText: 'What is a firewall?',
            occurrences: [
              { row: 1, topicReference: 'Topic A', normalizedText: 'what is a firewall' },
              { row: 2, topicReference: 'Topic B', normalizedText: 'what is a firewall' },
            ],
          },
        ],
        topicValidations: new Map([
          ['Invalid Topic', {
            topicReference: 'Invalid Topic',
            isValid: false,
            errors: [{
              row: 1,
              message: 'Topic name contains invalid characters',
              severity: 'error',
              category: 'topic',
            }],
            warnings: [],
          }],
        ]),
        globalErrors: [
          {
            row: 0,
            message: 'Duplicate question found across topics',
            severity: 'error',
            category: 'duplicate',
          },
        ],
      };

      const topicResolution: TopicResolutionResult = {
        resolved: new Map([['Valid Topic', 'topic-123']]),
        missing: ['Missing Topic'],
        created: ['New Topic'],
        errors: [],
      };

      const summary = generateErrorSummary(validationResult, topicResolution);

      expect(summary).toContain('❌ Found');
      expect(summary).toContain('error(s)');
      expect(summary).toContain('✅ Created 1 new topic(s): New Topic');
      expect(summary).toContain('❌ Could not resolve 1 topic(s): Missing Topic');
      expect(summary).toContain('📊 Error breakdown:');
      expect(summary).toContain('Topic errors:');
      expect(summary).toContain('Duplicate errors:');
    });

    it('should handle validation with no errors', () => {
      const validationResult: CrossTopicValidationResult = {
        isValid: true,
        duplicateQuestions: [],
        topicValidations: new Map(),
        globalErrors: [],
      };

      const summary = generateErrorSummary(validationResult);

      expect(summary).toBe('✅ No validation errors found.');
    });

    it('should handle warnings separately from errors', () => {
      const validationResult: CrossTopicValidationResult = {
        isValid: true,
        duplicateQuestions: [],
        topicValidations: new Map(),
        globalErrors: [
          {
            row: 1,
            message: 'This is a warning',
            severity: 'warning',
            category: 'topic',
          },
        ],
      };

      const summary = generateErrorSummary(validationResult);

      expect(summary).toContain('⚠️ Found 1 warning(s):');
      expect(summary).not.toContain('❌ Found');
    });
  });

  describe('generateActionableErrorMessage', () => {
    it('should enhance topic-related error messages', () => {
      const topicError = {
        row: 1,
        message: 'Topic "Test" not found',
        severity: 'error' as const,
        category: 'topic' as const,
      };

      const enhanced = generateActionableErrorMessage(topicError);
      
      expect(enhanced).toContain('Topic "Test" not found');
      expect(enhanced).toContain('Try enabling auto-create topics');
    });

    it('should enhance question-related error messages', () => {
      const questionError = {
        row: 1,
        message: 'Question text is too short',
        severity: 'error' as const,
        category: 'question' as const,
      };

      const enhanced = generateActionableErrorMessage(questionError);
      
      expect(enhanced).toContain('Question text is too short');
      expect(enhanced).toContain('Add more detail');
    });

    it('should enhance duplicate-related error messages', () => {
      const duplicateError = {
        row: 0,
        message: 'Duplicate question found',
        severity: 'error' as const,
        category: 'duplicate' as const,
      };

      const enhanced = generateActionableErrorMessage(duplicateError);
      
      expect(enhanced).toContain('Duplicate question found');
      expect(enhanced).toContain('Consider rephrasing');
    });

    it('should enhance reserved topic name errors', () => {
      const reservedError = {
        row: 1,
        message: 'Topic name "admin" is reserved',
        severity: 'error' as const,
        category: 'topic' as const,
      };

      const enhanced = generateActionableErrorMessage(reservedError);
      
      expect(enhanced).toContain('Choose a different topic name');
    });

    it('should enhance correct answer validation errors', () => {
      const answerError = {
        row: 1,
        message: 'Invalid correct_answer "X"',
        severity: 'error' as const,
        category: 'question' as const,
      };

      const enhanced = generateActionableErrorMessage(answerError);
      
      expect(enhanced).toContain('Use only A, B, C, or D');
    });

    it('should handle format errors', () => {
      const formatError = {
        row: 1,
        message: 'CSV format error',
        severity: 'error' as const,
        category: 'format' as const,
      };

      const enhanced = generateActionableErrorMessage(formatError);
      
      expect(enhanced).toContain('Check the CSV format');
    });
  });
});