-- Migration: Add is_premium field to questions table
-- This migration adds the missing is_premium field to match TypeScript types

-- Add is_premium column to questions table
ALTER TABLE questions 
ADD COLUMN IF NOT EXISTS is_premium BOOLEAN DEFAULT false;

-- Add comment for documentation
COMMENT ON COLUMN questions.is_premium IS 'Indicates if this question requires premium access';

-- Create index for performance (optional but recommended)
CREATE INDEX IF NOT EXISTS idx_questions_is_premium ON questions(is_premium);