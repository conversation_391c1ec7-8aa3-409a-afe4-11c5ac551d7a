# Design Document

## Overview

This design enhances the existing quiz system with question randomization capabilities, fixes answer validation issues, and improves the administrative interface. The solution maintains backward compatibility while introducing new randomization algorithms and a dedicated import page interface.

## Architecture

### Current System Analysis
- Questions are stored in PostgreSQL with JSONB options field
- Current answer validation uses index-based matching (0, 1, 2, 3)
- Import functionality exists as a modal dialog component
- Quiz questions are currently served in database order without randomization

### Enhanced Architecture Components
1. **Question Randomization Service** - Handles question selection and shuffling
2. **Answer Validation Service** - Improved answer mapping and validation
3. **Import Page Interface** - Full-page replacement for modal import
4. **Question Pool Analytics** - Monitoring and recommendations for question variety

## Components and Interfaces

### 1. Question Randomization Service

```typescript
interface QuizRandomizationService {
  selectRandomQuestions(topicId: string, count: number): Promise<Question[]>;
  shuffleAnswerOptions(question: Question): Question;
  generateQuizSession(topicId: string, userId: string, quizLength: number): Promise<QuizSession>;
}

interface QuizSession {
  id: string;
  userId: string;
  topicId: string;
  questions: RandomizedQuestion[];
  createdAt: Date;
  expiresAt: Date;
}

interface RandomizedQuestion extends Question {
  originalCorrectIndex: number;
  shuffledCorrectIndex: number;
  optionMapping: number[]; // Maps shuffled positions to original positions
}
```

### 2. Enhanced Question Model

```typescript
interface Question {
  id: string;
  topicId: string;
  questionText: string;
  options: Record<string, string>; // {"0": "Option A", "1": "Option B", ...}
  correctAnswer: string; // Index as string
  explanation: string;
  difficulty: 'easy' | 'medium' | 'hard';
  usageCount?: number;
  lastUsed?: Date;
}

interface QuestionWithStats extends Question {
  correctAnswerRate: number;
  averageTimeToAnswer: number;
  flaggedForReview: boolean;
}
```

### 3. Import Page Interface

```typescript
interface ImportPageProps {
  onImportComplete: (results: ImportResults) => void;
  onCancel: () => void;
}

interface ImportResults {
  totalProcessed: number;
  successfulImports: number;
  errors: ImportError[];
  topicBreakdown: Record<string, number>;
}

interface ImportError {
  line: number;
  message: string;
  questionText?: string;
}
```

## Data Models

### Database Schema Enhancements

```sql
-- Add columns to questions table for analytics
ALTER TABLE questions ADD COLUMN IF NOT EXISTS usage_count INTEGER DEFAULT 0;
ALTER TABLE questions ADD COLUMN IF NOT EXISTS last_used TIMESTAMP WITH TIME ZONE;
ALTER TABLE questions ADD COLUMN IF NOT EXISTS correct_answer_rate DECIMAL(5,2);

-- Create quiz_sessions table for tracking randomized quizzes
CREATE TABLE IF NOT EXISTS quiz_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id),
  topic_id UUID REFERENCES topics(id),
  questions_data JSONB NOT NULL, -- Stores randomized question order and option mappings
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() + INTERVAL '2 hours',
  completed_at TIMESTAMP WITH TIME ZONE,
  score INTEGER,
  total_questions INTEGER
);

-- Create question_analytics table for performance tracking
CREATE TABLE IF NOT EXISTS question_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  question_id UUID REFERENCES questions(id),
  user_id UUID REFERENCES auth.users(id),
  answered_correctly BOOLEAN,
  time_to_answer INTEGER, -- in seconds
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Question Pool Size Recommendations

Based on research and best practices:
- **Minimum viable**: 15-20 questions per topic
- **Good variety**: 30-40 questions per topic  
- **Excellent variety**: 50+ questions per topic
- **Quiz lengths**: Configurable 10, 15, 20, 25 questions

## Error Handling

### Answer Validation Fixes

1. **Robust Parsing**: Enhanced parsing logic for correct_answer field
2. **Fallback Mechanisms**: Default to first option if parsing fails
3. **Validation Logging**: Comprehensive error logging for debugging
4. **Data Migration**: Script to fix existing misaligned answers

```typescript
function parseCorrectAnswer(correctAnswer: any): number {
  try {
    if (typeof correctAnswer === 'number') {
      return Math.max(0, Math.min(3, correctAnswer));
    }
    
    const parsed = parseInt(correctAnswer);
    if (isNaN(parsed)) {
      console.warn('Invalid correct_answer format, defaulting to 0');
      return 0;
    }
    
    return Math.max(0, Math.min(3, parsed));
  } catch (error) {
    console.error('Error parsing correct_answer:', error);
    return 0;
  }
}
```

### Import Error Handling

1. **Line-by-line validation**: Detailed error reporting per question
2. **Partial success handling**: Continue processing valid questions
3. **Progress preservation**: Save progress during long imports
4. **Rollback capability**: Option to undo failed imports

## Testing Strategy

### Unit Tests
- Question randomization algorithms
- Answer option shuffling logic
- Answer validation parsing
- Import data processing

### Integration Tests
- End-to-end quiz flow with randomization
- Import page functionality
- Database operations for quiz sessions
- Answer validation across different data formats

### Performance Tests
- Question selection performance with large datasets
- Randomization algorithm efficiency
- Import processing speed with large CSV files
- Database query optimization for analytics

### User Acceptance Tests
- Quiz variety verification (multiple attempts show different questions)
- Answer validation accuracy
- Import page usability
- Administrative analytics functionality

## Implementation Phases

### Phase 1: Core Randomization
- Implement question selection randomization
- Add answer option shuffling
- Create quiz session management
- Fix answer validation issues

### Phase 2: Import Page Enhancement  
- Convert modal to full-page interface
- Add progress indicators and detailed error reporting
- Implement batch processing for large imports
- Add import history and rollback features

### Phase 3: Analytics and Optimization
- Implement question usage tracking
- Add performance analytics
- Create administrative dashboards
- Optimize database queries for large question pools

### Phase 4: Advanced Features
- Adaptive question difficulty
- Question performance recommendations
- Automated content quality alerts
- Advanced randomization algorithms (weighted selection)