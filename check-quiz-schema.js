/**
 * <PERSON><PERSON><PERSON> to check if quiz randomization schema exists
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing required environment variables');
  console.error('Required: VITE_SUPABASE_URL, VITE_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkSchema() {
  console.log('Checking quiz randomization schema...');

  try {
    // Check if quiz_sessions table exists by trying to query it
    console.log('Checking quiz_sessions table...');
    const { data: sessionsData, error: sessionsError } = await supabase
      .from('quiz_sessions')
      .select('id')
      .limit(1);

    if (sessionsError) {
      console.log('❌ quiz_sessions table does not exist or is not accessible:', sessionsError.message);
    } else {
      console.log('✅ quiz_sessions table exists and is accessible');
    }

    // Check if question_analytics table exists
    console.log('Checking question_analytics table...');
    const { data: analyticsData, error: analyticsError } = await supabase
      .from('question_analytics')
      .select('id')
      .limit(1);

    if (analyticsError) {
      console.log('❌ question_analytics table does not exist or is not accessible:', analyticsError.message);
    } else {
      console.log('✅ question_analytics table exists and is accessible');
    }

    // Check if questions table has the required columns
    console.log('Checking questions table structure...');
    const { data: questionsData, error: questionsError } = await supabase
      .from('questions')
      .select('id, topic_id, question_text, options, correct_answer, explanation')
      .limit(1);

    if (questionsError) {
      console.log('❌ questions table is not accessible:', questionsError.message);
    } else {
      console.log('✅ questions table is accessible');
      if (questionsData && questionsData.length > 0) {
        console.log('Sample question structure:', Object.keys(questionsData[0]));
      }
    }

    // Check if topics table exists
    console.log('Checking topics table...');
    const { data: topicsData, error: topicsError } = await supabase
      .from('topics')
      .select('id, title')
      .limit(1);

    if (topicsError) {
      console.log('❌ topics table is not accessible:', topicsError.message);
    } else {
      console.log('✅ topics table is accessible');
      if (topicsData && topicsData.length > 0) {
        console.log('Sample topic:', topicsData[0]);
      }
    }

  } catch (error) {
    console.error('❌ Schema check failed:', error);
  }
}

// Run the schema check
checkSchema();