import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { supabase } from '@/integrations/supabase/client';

const SimpleAuthTest: React.FC = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('');
  const [result, setResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const testSignIn = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      console.log('🔐 Testing sign in with:', email);
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      const result = {
        timestamp: new Date().toISOString(),
        success: !error,
        error: error?.message,
        user: data.user ? {
          id: data.user.id,
          email: data.user.email,
          created_at: data.user.created_at
        } : null,
        session: data.session ? {
          access_token: data.session.access_token ? 'SET' : 'NOT SET',
          refresh_token: data.session.refresh_token ? 'SET' : 'NOT SET',
          expires_at: data.session.expires_at
        } : null
      };

      setResult(result);
      console.log('🧪 Sign in result:', result);

      if (data.user) {
        console.log('✅ Sign in successful, testing admin status...');
        // Test admin status
        const { isUserAdmin } = await import('@/utils/auth-helpers');
        const adminStatus = await isUserAdmin(data.user);
        setResult(prev => ({ ...prev, adminStatus }));
      }

    } catch (error) {
      console.error('❌ Sign in test failed:', error);
      setResult({
        timestamp: new Date().toISOString(),
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testSignOut = async () => {
    try {
      await supabase.auth.signOut();
      setResult(null);
      console.log('👋 Signed out successfully');
    } catch (error) {
      console.error('❌ Sign out failed:', error);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>🔐 Simple Auth Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter email"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="password">Password</Label>
          <Input
            id="password"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="Enter password"
          />
        </div>

        <div className="flex gap-2">
          <Button 
            onClick={testSignIn} 
            disabled={isLoading || !email || !password}
            className="flex-1"
          >
            {isLoading ? 'Signing In...' : 'Test Sign In'}
          </Button>
          
          <Button 
            onClick={testSignOut} 
            variant="outline"
            className="flex-1"
          >
            Sign Out
          </Button>
        </div>

        {result && (
          <div className="space-y-3">
            {result.success ? (
              <div className="bg-green-50 border border-green-200 rounded p-3">
                <h3 className="font-semibold text-green-700 mb-2">✅ Sign In Successful</h3>
                <div className="text-sm space-y-1">
                  <p><strong>User ID:</strong> {result.user?.id}</p>
                  <p><strong>Email:</strong> {result.user?.email}</p>
                  <p><strong>Admin Status:</strong> {
                    result.adminStatus === true ? '✅ Admin' : 
                    result.adminStatus === false ? '❌ Not Admin' : 
                    '⏳ Checking...'
                  }</p>
                  <p><strong>Session:</strong> {result.session?.access_token ? '✅ Active' : '❌ No Session'}</p>
                </div>
              </div>
            ) : (
              <div className="bg-red-50 border border-red-200 rounded p-3">
                <h3 className="font-semibold text-red-700 mb-2">❌ Sign In Failed</h3>
                <p className="text-red-600 text-sm">{result.error}</p>
                {result.stack && (
                  <details className="mt-2">
                    <summary className="text-red-600 text-sm cursor-pointer">Stack Trace</summary>
                    <pre className="text-xs text-red-500 mt-1 overflow-auto">{result.stack}</pre>
                  </details>
                )}
              </div>
            )}

            <div className="text-xs text-gray-500">
              Test completed at: {result.timestamp}
            </div>
          </div>
        )}

        <div className="bg-blue-50 border border-blue-200 rounded p-3">
          <p className="text-blue-700 text-sm">
            <strong>Instructions:</strong> Enter the admin email (<EMAIL>) and password to test authentication.
            Check the browser console for detailed logs.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default SimpleAuthTest;
