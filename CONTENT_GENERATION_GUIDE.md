# SecQuiz Content Generation Guide

## Database Schema Overview

### Core Tables Structure

#### 1. Domains Table
```sql
domains (
  id UUID PRIMARY KEY,
  name TEXT NOT NULL,
  slug TEXT UNIQUE,
  description TEXT,
  icon TEXT,
  color_theme TEXT,
  difficulty_level TEXT ('beginner', 'intermediate', 'advanced', 'expert'),
  estimated_duration_weeks INTEGER,
  prerequisites TEXT[],
  is_active BOOLEAN,
  sort_order INTEGER
)
```

#### 2. Topics Table
```sql
topics (
  id UUID PRIMARY KEY,
  domain_id UUID REFERENCES domains(id),
  title TEXT NOT NULL,
  description TEXT,
  icon TEXT,
  difficulty TEXT ('easy', 'medium', 'hard'),
  is_active BOOLEAN,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)
```

#### 3. Questions Table
```sql
questions (
  id UUID PRIMARY KEY,
  topic_id UUID REFERENCES topics(id),
  question_text TEXT NOT NULL,
  options JSONB, -- {"0": "Option A", "1": "Option B", "2": "Option C", "3": "Option D"}
  correct_answer TEXT, -- Index of correct answer ("0", "1", "2", or "3")
  explanation TEXT,
  difficulty TEXT ('easy', 'medium', 'hard'),
  created_at TIMESTAMP
)
```

#### 4. Learning Materials Table
```sql
learning_materials (
  id UUID PRIMARY KEY,
  topic_id UUID REFERENCES topics(id),
  title TEXT NOT NULL,
  content TEXT NOT NULL, -- Markdown formatted content
  summary TEXT,
  is_premium BOOLEAN,
  order_index INTEGER,
  created_at TIMESTAMP
)
```

## Content Structure Requirements

### Topic Structure
```json
{
  "title": "Network Security Fundamentals",
  "description": "Understanding basic network security principles in Nigerian business context",
  "difficulty": "easy|medium|hard",
  "icon": "shield|lock|network",
  "domain_id": "uuid-of-domain",
  "is_active": true
}
```

### Question Structure
```json
{
  "question_text": "What is the primary purpose of a firewall in a Nigerian bank's network infrastructure?",
  "options": {
    "0": "To prevent unauthorized access to the bank's internal network",
    "1": "To increase internet speed for customers",
    "2": "To store customer transaction data",
    "3": "To manage employee email accounts"
  },
  "correct_answer": "0",
  "explanation": "Firewalls act as a barrier between trusted internal networks and untrusted external networks, which is crucial for Nigerian banks to protect customer financial data and comply with CBN cybersecurity guidelines.",
  "difficulty": "medium",
  "topic_id": "uuid-of-topic"
}
```

### Learning Material Structure
```json
{
  "title": "Network Security in Nigerian Banking Sector",
  "summary": "Overview of network security practices specific to Nigerian financial institutions",
  "content": "# Network Security in Nigerian Banking\n\n## Introduction\nNigerian banks face unique cybersecurity challenges...",
  "is_premium": false,
  "order_index": 1,
  "topic_id": "uuid-of-topic"
}
```

## AI Prompt Templates

### 1. Topic Generation Prompt

```
You are a cybersecurity education expert creating content for SecQuiz, a Nigerian-focused cybersecurity learning platform. Generate 5 comprehensive topics for the [DOMAIN_NAME] domain.

Requirements:
- Nigerian/African business context and scenarios
- Reference to local regulations (NDPR, CBN guidelines, NCC regulations)
- Global cybersecurity standards compliance (ISO 27001, NIST)
- Progressive difficulty from beginner to advanced
- Real-world examples from Nigerian companies/sectors

For each topic, provide:
1. Title (concise, professional)
2. Description (2-3 sentences with Nigerian context)
3. Difficulty level (easy/medium/hard)
4. 3-5 key learning objectives
5. Nigerian business scenario example

Domain: [DOMAIN_NAME]
Target Audience: Nigerian cybersecurity professionals, students, and business owners

Format your response as JSON with this structure:
{
  "topics": [
    {
      "title": "",
      "description": "",
      "difficulty": "",
      "learning_objectives": [],
      "nigerian_scenario": ""
    }
  ]
}
```

### 2. Quiz Questions Generation Prompt

```
Generate 10 multiple-choice cybersecurity quiz questions for the topic: "[TOPIC_TITLE]"

Requirements:
- Nigerian business context and scenarios
- Reference Nigerian companies, cities, or regulations where appropriate
- Include scenarios from sectors like banking, telecommunications, oil & gas, fintech
- Mention relevant Nigerian regulations (NDPR, CBN cybersecurity framework, NCC guidelines)
- Realistic African business challenges and solutions
- Mix of difficulty levels: 3 easy, 4 medium, 3 hard
- Each question should have 4 options with only one correct answer
- Provide detailed explanations referencing Nigerian context

Nigerian Context Examples:
- Banks: GTBank, Access Bank, Zenith Bank, First Bank
- Telecoms: MTN Nigeria, Airtel Nigeria, Glo, 9mobile
- Fintech: Paystack, Flutterwave, Interswitch, Kuda Bank
- Cities: Lagos, Abuja, Port Harcourt, Kano
- Regulations: NDPR (Nigeria Data Protection Regulation), CBN cybersecurity guidelines

Format as JSON:
{
  "questions": [
    {
      "question_text": "",
      "options": {
        "0": "",
        "1": "",
        "2": "",
        "3": ""
      },
      "correct_answer": "0",
      "explanation": "",
      "difficulty": "easy|medium|hard",
      "nigerian_context": "brief description of Nigerian relevance"
    }
  ]
}
```

### 3. Learning Materials Generation Prompt

```
Create comprehensive learning material for the cybersecurity topic: "[TOPIC_TITLE]"

Requirements:
- Nigerian business context throughout
- Reference to local regulations and compliance requirements
- Case studies from Nigerian companies or sectors
- Practical examples relevant to African business environment
- Global best practices adapted for Nigerian context
- Include actionable recommendations for Nigerian businesses

Structure:
1. Introduction with Nigerian context
2. Key concepts with local examples
3. Nigerian regulatory landscape
4. Case studies (2-3 Nigerian examples)
5. Best practices for Nigerian businesses
6. Implementation guidelines
7. Compliance checklist
8. Additional resources

Nigerian Sectors to Consider:
- Banking and Financial Services
- Telecommunications
- Oil and Gas
- E-commerce and Fintech
- Government and Public Sector
- Healthcare
- Education

Format as Markdown with the following structure:
```markdown
# [Topic Title]

## Introduction
[Nigerian context introduction]

## Key Concepts
[Core concepts with Nigerian examples]

## Nigerian Regulatory Landscape
[NDPR, CBN, NCC guidelines]

## Case Studies
### Case Study 1: [Nigerian Company/Scenario]
### Case Study 2: [Nigerian Company/Scenario]

## Best Practices for Nigerian Businesses
[Actionable recommendations]

## Implementation Guidelines
[Step-by-step implementation]

## Compliance Checklist
- [ ] NDPR compliance
- [ ] CBN guidelines adherence
- [ ] Industry-specific requirements

## Additional Resources
[Nigerian and international resources]
```

Provide the content in JSON format:
{
  "title": "",
  "summary": "",
  "content": "[Full markdown content]",
  "is_premium": false,
  "nigerian_focus_areas": []
}
```

### 4. Scenario-Based Question Prompt

```
Create 5 scenario-based cybersecurity questions focusing on real Nigerian business situations for the topic: "[TOPIC_TITLE]"

Each scenario should:
- Feature a realistic Nigerian business situation
- Include specific Nigerian context (companies, locations, regulations)
- Present a cybersecurity challenge or incident
- Require application of cybersecurity knowledge to solve
- Reference appropriate Nigerian regulations or guidelines

Scenario Types:
1. Banking sector incident (Lagos financial district)
2. Telecommunications breach (MTN/Airtel scenario)
3. Fintech startup challenge (Lagos/Abuja tech hub)
4. Oil & gas company security (Port Harcourt/Warri)
5. Government agency incident (Abuja federal ministry)

Format each question as:
{
  "scenario": "[Detailed Nigerian business scenario]",
  "question": "[Specific question about the scenario]",
  "options": {
    "0": "[Option A]",
    "1": "[Option B]",
    "2": "[Option C]",
    "3": "[Option D]"
  },
  "correct_answer": "[0-3]",
  "explanation": "[Detailed explanation with Nigerian regulatory references]",
  "difficulty": "[easy|medium|hard]",
  "sector": "[banking|telecom|fintech|oil_gas|government]",
  "regulations_referenced": ["NDPR", "CBN Guidelines", "etc."]
}
```

## Step-by-Step Content Addition Workflow

### Step 1: Domain Setup
1. Insert domain into `domains` table
2. Set appropriate `color_theme`, `difficulty_level`, and `estimated_duration_weeks`
3. Define `prerequisites` array if needed

### Step 2: Topic Creation
1. Use AI prompt to generate topics
2. Insert topics into `topics` table with `domain_id` reference
3. Ensure progressive difficulty distribution

### Step 3: Question Generation
1. For each topic, generate 10-15 questions using AI prompts
2. Ensure mix of difficulties (30% easy, 50% medium, 20% hard)
3. Insert into `questions` table with proper `topic_id` reference

### Step 4: Learning Materials Creation
1. Generate comprehensive learning materials for each topic
2. Create 2-3 materials per topic with different focus areas
3. Insert into `learning_materials` table with proper ordering

### Step 5: Quality Assurance
1. Review all content for Nigerian context accuracy
2. Verify regulatory references are current
3. Test questions for clarity and correctness
4. Ensure learning materials flow logically

## Nigerian Context Guidelines

### Key Regulations to Reference:
- **NDPR (Nigeria Data Protection Regulation)**: Data privacy and protection
- **CBN Cybersecurity Framework**: Banking sector cybersecurity
- **NCC Guidelines**: Telecommunications cybersecurity
- **NITDA Guidelines**: IT development and cybersecurity

### Nigerian Companies to Reference:
- **Banking**: GTBank, Access Bank, Zenith Bank, First Bank, UBA
- **Fintech**: Paystack, Flutterwave, Interswitch, Kuda, PiggyVest
- **Telecoms**: MTN Nigeria, Airtel Nigeria, Glo, 9mobile
- **E-commerce**: Jumia, Konga, Jiji

### Nigerian Cities/Locations:
- **Lagos**: Financial and tech hub
- **Abuja**: Government and corporate headquarters
- **Port Harcourt**: Oil and gas center
- **Kano**: Northern commercial center

### Sectors to Focus On:
1. Banking and Financial Services
2. Telecommunications
3. Oil and Gas
4. Fintech and E-commerce
5. Government and Public Sector
6. Healthcare
7. Education
8. Agriculture and Supply Chain

## Content Quality Standards

### For Questions:
- Clear, unambiguous language
- Realistic Nigerian scenarios
- Single correct answer
- Detailed explanations with regulatory references
- Appropriate difficulty progression

### For Learning Materials:
- Comprehensive coverage of topic
- Nigerian case studies and examples
- Regulatory compliance guidance
- Practical implementation advice
- Current and accurate information

### For Topics:
- Clear learning objectives
- Progressive skill building
- Nigerian business relevance
- Global standard alignment
- Measurable outcomes

## Quick Start Examples

### Example Domain Insert
```sql
INSERT INTO domains (name, slug, description, icon, color_theme, difficulty_level, estimated_duration_weeks, prerequisites)
VALUES (
  'Cloud Security',
  'cloud-security',
  'Comprehensive cloud security practices for Nigerian businesses',
  'cloud',
  '#3B82F6',
  'intermediate',
  6,
  ARRAY['network-security']
);
```

### Example Topic Insert
```sql
INSERT INTO topics (domain_id, title, description, difficulty, icon)
VALUES (
  (SELECT id FROM domains WHERE slug = 'cloud-security'),
  'Cloud Security for Nigerian Banks',
  'Understanding cloud security requirements for Nigerian financial institutions under CBN guidelines',
  'medium',
  'shield'
);
```

### Example Question Insert
```sql
INSERT INTO questions (topic_id, question_text, options, correct_answer, explanation, difficulty)
VALUES (
  (SELECT id FROM topics WHERE title = 'Cloud Security for Nigerian Banks'),
  'According to CBN cybersecurity guidelines, what is the primary requirement for Nigerian banks using cloud services?',
  '{"0": "Data must remain within Nigeria", "1": "Only foreign cloud providers can be used", "2": "Cloud services are prohibited", "3": "No specific requirements exist"}',
  '0',
  'CBN guidelines require that customer data for Nigerian banks must be stored and processed within Nigeria to ensure regulatory compliance and data sovereignty.',
  'medium'
);
```
