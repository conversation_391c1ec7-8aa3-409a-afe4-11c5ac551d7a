#!/bin/bash

# SecQuiz Local Development Setup Script
# This script sets up Supabase CLI and configures local development for SecQuiz

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Check if we're in the SecQuiz project directory
check_project_directory() {
    if [[ ! -f "package.json" ]] || ! grep -q "secquiz\|SecQuiz" package.json 2>/dev/null; then
        print_error "This doesn't appear to be the SecQuiz project directory"
        print_status "Please run this script from the SecQuiz project root"
        print_status "Expected files: package.json, src/, server/"
        exit 1
    fi
    print_success "Found SecQuiz project directory"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        print_status "Please install Node.js from https://nodejs.org/"
        exit 1
    fi
    print_success "Node.js is installed: $(node --version)"
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed"
        exit 1
    fi
    print_success "npm is installed: $(npm --version)"
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_warning "Docker is not installed"
        print_status "Please install Docker from https://www.docker.com/products/docker-desktop"
        print_status "Docker is required for local Supabase development"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_warning "Docker is not running"
        print_status "Please start Docker and run this script again"
        exit 1
    fi
    print_success "Docker is running: $(docker --version)"
}

# Install Supabase CLI if not present
install_supabase_cli() {
    if command -v supabase &> /dev/null; then
        print_success "Supabase CLI is already installed: $(supabase --version)"
        return 0
    fi
    
    print_status "Installing Supabase CLI..."
    
    # Try npm installation (most reliable cross-platform)
    if npm install -g supabase; then
        print_success "Supabase CLI installed via npm"
    else
        print_error "Failed to install Supabase CLI via npm"
        print_status "Please install manually using the installation guide"
        exit 1
    fi
}

# Initialize Supabase in project
initialize_supabase() {
    if [[ -d "supabase" ]] && [[ -f "supabase/config.toml" ]]; then
        print_success "Supabase is already initialized"
        return 0
    fi
    
    print_status "Initializing Supabase in SecQuiz project..."
    
    # Initialize Supabase
    supabase init
    
    print_success "Supabase initialized successfully"
}

# Configure environment for local development
configure_environment() {
    print_status "Configuring environment for local development..."
    
    # Create .env.local for local development
    cat > .env.local << EOF
# Local Supabase Configuration for SecQuiz
# This file is for local development only

# Local Supabase instance (will be populated after 'supabase start')
VITE_SUPABASE_URL=http://localhost:54321
VITE_SUPABASE_ANON_KEY=will-be-updated-after-start

# Application Settings
VITE_APP_NAME=SecQuiz
VITE_APP_DESCRIPTION=A cybersecurity education platform
VITE_APP_URL=http://localhost:5173

# Feature Flags for Development
VITE_ENABLE_ADMIN_FEATURES=true
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_DEBUG_MODE=true

# API Configuration
VITE_API_TIMEOUT=30000
VITE_MAX_UPLOAD_SIZE=5242880
VITE_API_URL=http://localhost:3001

# Paystack Configuration (use test keys for local development)
VITE_PAYSTACK_PUBLIC_KEY=pk_test_your_test_key_here

# Authentication Settings
VITE_AUTH_REDIRECT_URL=http://localhost:5173/auth/verify
EOF

    print_success "Created .env.local for local development"
    
    # Update .gitignore to include .env.local
    if ! grep -q ".env.local" .gitignore 2>/dev/null; then
        echo ".env.local" >> .gitignore
        print_status "Added .env.local to .gitignore"
    fi
}

# Start Supabase and update environment
start_supabase() {
    print_status "Starting local Supabase instance..."
    
    # Start Supabase
    if supabase start; then
        print_success "Supabase started successfully!"
        
        # Get the credentials and update .env.local
        print_status "Updating .env.local with Supabase credentials..."
        
        # Extract anon key from supabase status
        ANON_KEY=$(supabase status | grep "anon key" | awk '{print $3}')
        
        if [[ -n "$ANON_KEY" ]]; then
            # Update .env.local with the actual anon key
            sed -i.bak "s/VITE_SUPABASE_ANON_KEY=.*/VITE_SUPABASE_ANON_KEY=$ANON_KEY/" .env.local
            rm .env.local.bak 2>/dev/null || true
            print_success "Updated .env.local with Supabase credentials"
        else
            print_warning "Could not extract anon key automatically"
            print_status "Please update VITE_SUPABASE_ANON_KEY in .env.local manually"
        fi
        
        # Show status
        print_status "Supabase services are running:"
        supabase status
        
    else
        print_error "Failed to start Supabase"
        print_status "Check the troubleshooting guide for common issues"
        exit 1
    fi
}

# Install project dependencies
install_dependencies() {
    print_status "Installing project dependencies..."
    
    # Install main project dependencies
    if npm install; then
        print_success "Main project dependencies installed"
    else
        print_warning "Failed to install main project dependencies"
    fi
    
    # Install server dependencies if server directory exists
    if [[ -d "server" ]] && [[ -f "server/package.json" ]]; then
        print_status "Installing server dependencies..."
        cd server
        if npm install; then
            print_success "Server dependencies installed"
        else
            print_warning "Failed to install server dependencies"
        fi
        cd ..
    fi
}

# Add helpful npm scripts
add_npm_scripts() {
    print_status "Adding helpful npm scripts..."
    
    # Check if scripts already exist
    if grep -q "supabase:start" package.json 2>/dev/null; then
        print_success "Supabase scripts already exist"
        return 0
    fi
    
    # Create a temporary file with updated package.json
    node -e "
        const fs = require('fs');
        const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        
        pkg.scripts = pkg.scripts || {};
        pkg.scripts['dev:local'] = 'supabase start && npm run dev';
        pkg.scripts['supabase:start'] = 'supabase start';
        pkg.scripts['supabase:stop'] = 'supabase stop';
        pkg.scripts['supabase:status'] = 'supabase status';
        pkg.scripts['supabase:reset'] = 'supabase db reset';
        pkg.scripts['supabase:studio'] = 'open http://localhost:54323';
        
        fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2));
    "
    
    print_success "Added Supabase npm scripts to package.json"
}

# Show next steps
show_next_steps() {
    print_success "🎉 SecQuiz local development setup complete!"
    print_status ""
    print_status "📋 What was set up:"
    print_status "  ✅ Supabase CLI installed"
    print_status "  ✅ Local Supabase instance running"
    print_status "  ✅ Environment configured (.env.local)"
    print_status "  ✅ Project dependencies installed"
    print_status "  ✅ Helpful npm scripts added"
    print_status ""
    print_status "🚀 Next steps:"
    print_status "  1. Open Supabase Studio: http://localhost:54323"
    print_status "  2. Start your development server: npm run dev"
    print_status "  3. Start your backend server: cd server && npm run dev"
    print_status "  4. Open your app: http://localhost:5173"
    print_status ""
    print_status "📝 Useful commands:"
    print_status "  npm run supabase:start    - Start Supabase"
    print_status "  npm run supabase:stop     - Stop Supabase"
    print_status "  npm run supabase:status   - Check status"
    print_status "  npm run supabase:studio   - Open Studio"
    print_status "  npm run dev:local         - Start Supabase + dev server"
    print_status ""
    print_status "🔧 Configuration files:"
    print_status "  .env.local                - Local environment variables"
    print_status "  supabase/config.toml      - Supabase configuration"
    print_status ""
    print_status "📚 Documentation:"
    print_status "  SUPABASE_CLI_SETUP_GUIDE.md       - Complete setup guide"
    print_status "  SUPABASE_CLI_TROUBLESHOOTING.md   - Troubleshooting help"
}

# Main execution
main() {
    print_status "🚀 Setting up SecQuiz for local development with Supabase"
    print_status "=========================================================="
    
    check_project_directory
    check_prerequisites
    install_supabase_cli
    initialize_supabase
    configure_environment
    install_dependencies
    add_npm_scripts
    start_supabase
    show_next_steps
}

# Handle script arguments
case "${1:-}" in
    "--help" | "-h")
        echo "SecQuiz Local Development Setup Script"
        echo ""
        echo "This script sets up local Supabase development for SecQuiz"
        echo ""
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --check        Check current setup status"
        echo ""
        exit 0
        ;;
    "--check")
        print_status "Checking SecQuiz local development setup..."
        check_project_directory
        
        # Check Supabase CLI
        if command -v supabase &> /dev/null; then
            print_success "Supabase CLI: $(supabase --version)"
        else
            print_error "Supabase CLI not installed"
        fi
        
        # Check if Supabase is running
        if supabase status &> /dev/null; then
            print_success "Supabase is running"
            supabase status
        else
            print_warning "Supabase is not running"
        fi
        
        # Check environment file
        if [[ -f ".env.local" ]]; then
            print_success ".env.local exists"
        else
            print_warning ".env.local not found"
        fi
        
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
