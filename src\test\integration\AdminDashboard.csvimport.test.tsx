import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import AdminDashboard from '@/pages/AdminDashboard';
import { useAuth } from '@/hooks/use-auth';
import { useAdminTopics, useAdminQuestions } from '@/hooks/use-admin';
import { useAdminUsers } from '@/hooks/use-admin-users';
import { useToast } from '@/hooks/use-toast';
import { type MultiTopicImportResult } from '@/utils/csv-import';

// Mock all the hooks
vi.mock('@/hooks/use-auth');
vi.mock('@/hooks/use-admin');
vi.mock('@/hooks/use-admin-users');
vi.mock('@/hooks/use-toast');

// Mock the MultiTopicCSVImport component
vi.mock('@/components/admin/MultiTopicCSVImport', () => ({
  MultiTopicCSVImport: ({ onSuccess, onCancel, topics }: any) => (
    <div data-testid="multi-topic-csv-import">
      <div>Topics available: {topics.length}</div>
      <button 
        onClick={() => {
          // Simulate successful single-topic import
          const singleTopicResult: MultiTopicImportResult = {
            success: true,
            totalRows: 5,
            topicResults: new Map([
              ['topic-1', {
                topicId: 'topic-1',
                topicName: 'Security Fundamentals',
                validQuestions: Array(5).fill(null).map((_, i) => ({
                  id: `q-${i}`,
                  topic_id: 'topic-1',
                  question_text: `Question ${i + 1}`,
                  options: { A: 'Option A', B: 'Option B', C: 'Option C', D: 'Option D' },
                  correct_answer: 'A',
                  explanation: 'Test explanation',
                  difficulty: 'medium',
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                })),
                errors: [],
                isNewTopic: false,
              }]
            ]),
            globalErrors: [],
            newTopicsCreated: [],
          };
          onSuccess(singleTopicResult);
        }}
        data-testid="simulate-single-topic-success"
      >
        Simulate Single Topic Success
      </button>
      <button 
        onClick={() => {
          // Simulate successful multi-topic import with new topics
          const multiTopicResult: MultiTopicImportResult = {
            success: true,
            totalRows: 10,
            topicResults: new Map([
              ['topic-1', {
                topicId: 'topic-1',
                topicName: 'Security Fundamentals',
                validQuestions: Array(3).fill(null).map((_, i) => ({
                  id: `q1-${i}`,
                  topic_id: 'topic-1',
                  question_text: `Security Question ${i + 1}`,
                  options: { A: 'Option A', B: 'Option B', C: 'Option C', D: 'Option D' },
                  correct_answer: 'A',
                  explanation: 'Security explanation',
                  difficulty: 'medium',
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                })),
                errors: [],
                isNewTopic: false,
              }],
              ['topic-2', {
                topicId: 'topic-2',
                topicName: 'Network Security',
                validQuestions: Array(4).fill(null).map((_, i) => ({
                  id: `q2-${i}`,
                  topic_id: 'topic-2',
                  question_text: `Network Question ${i + 1}`,
                  options: { A: 'Option A', B: 'Option B', C: 'Option C', D: 'Option D' },
                  correct_answer: 'B',
                  explanation: 'Network explanation',
                  difficulty: 'hard',
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                })),
                errors: [],
                isNewTopic: true,
              }],
              ['topic-3', {
                topicId: 'topic-3',
                topicName: 'Incident Response',
                validQuestions: Array(3).fill(null).map((_, i) => ({
                  id: `q3-${i}`,
                  topic_id: 'topic-3',
                  question_text: `Incident Question ${i + 1}`,
                  options: { A: 'Option A', B: 'Option B', C: 'Option C', D: 'Option D' },
                  correct_answer: 'C',
                  explanation: 'Incident explanation',
                  difficulty: 'easy',
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                })),
                errors: [],
                isNewTopic: true,
              }]
            ]),
            globalErrors: [],
            newTopicsCreated: ['Network Security', 'Incident Response'],
          };
          onSuccess(multiTopicResult);
        }}
        data-testid="simulate-multi-topic-success"
      >
        Simulate Multi Topic Success
      </button>
      <button onClick={onCancel} data-testid="simulate-cancel">
        Cancel
      </button>
    </div>
  ),
}));

// Mock other components that might cause issues
vi.mock('@/components/admin/ScalableAdminTools', () => ({
  ScalableAdminTools: () => <div data-testid="scalable-admin-tools">Scalable Admin Tools</div>,
}));

vi.mock('@/components/admin/PaymentFixDialog', () => ({
  PaymentFixDialog: () => <div data-testid="payment-fix-dialog">Payment Fix Dialog</div>,
}));

vi.mock('@/components/admin/FeedbackManagement', () => ({
  FeedbackManagement: () => <div data-testid="feedback-management">Feedback Management</div>,
}));

vi.mock('@/components/admin/LearningMaterialsManagement', () => ({
  LearningMaterialsManagement: () => <div data-testid="learning-materials-management">Learning Materials Management</div>,
}));

const mockToast = vi.fn();
const mockRefreshQuestions = vi.fn();
const mockRefreshTopics = vi.fn();

const mockTopics = [
  { id: 'topic-1', title: 'Security Fundamentals', description: 'Basic security concepts' },
  { id: 'topic-2', title: 'Network Security', description: 'Network security topics' },
];

const mockQuestions = [
  { id: 'q-1', topic_id: 'topic-1', question_text: 'What is security?', explanation: 'Security explanation' },
  { id: 'q-2', topic_id: 'topic-1', question_text: 'What is encryption?', explanation: 'Encryption explanation' },
];

const mockUsers = [
  { id: 'user-1', email: '<EMAIL>', is_subscribed: false, is_admin: false },
  { id: 'user-2', email: '<EMAIL>', is_subscribed: true, is_admin: false },
];

describe('AdminDashboard CSV Import Integration', () => {
  beforeEach(() => {
    // Mock useAuth
    vi.mocked(useAuth).mockReturnValue({
      user: { id: 'admin-1', email: '<EMAIL>' },
      signOut: vi.fn(),
    } as unknown);

    // Mock useToast
    vi.mocked(useToast).mockReturnValue({
      toast: mockToast,
    });

    // Mock useAdminTopics
    vi.mocked(useAdminTopics).mockReturnValue({
      topics: mockTopics,
      loading: false,
      error: null,
      refreshTopics: mockRefreshTopics,
      deleteTopic: vi.fn(),
    });

    // Mock useAdminQuestions
    vi.mocked(useAdminQuestions).mockReturnValue({
      questions: mockQuestions,
      loading: false,
      error: null,
      refreshQuestions: mockRefreshQuestions,
      deleteQuestion: vi.fn(),
    });

    // Mock useAdminUsers
    vi.mocked(useAdminUsers).mockReturnValue({
      users: mockUsers,
      loading: false,
      error: null,
      fetchUsers: vi.fn(),
      updateUserSubscription: vi.fn(),
      updateUserAdminStatus: vi.fn(),
      deleteUser: vi.fn(),
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const renderAdminDashboard = () => {
    return render(
      <BrowserRouter>
        <AdminDashboard />
      </BrowserRouter>
    );
  };

  it('should display enhanced CSV import option in questions tab', async () => {
    renderAdminDashboard();
    
    // Navigate to questions tab
    const questionsTab = screen.getByRole('tab', { name: /questions/i });
    fireEvent.click(questionsTab);

    // Find and click the import dropdown
    const importButton = screen.getByRole('button', { name: /import/i });
    fireEvent.click(importButton);

    // Check that the CSV import option shows multi-topic support
    expect(screen.getByText('CSV Format (Single/Multi-Topic)')).toBeInTheDocument();
  });

  it('should open CSV import dialog with enhanced description', async () => {
    renderAdminDashboard();
    
    // Navigate to questions tab
    const questionsTab = screen.getByRole('tab', { name: /questions/i });
    fireEvent.click(questionsTab);

    // Open import dropdown and click CSV import
    const importButton = screen.getByRole('button', { name: /import/i });
    fireEvent.click(importButton);
    
    const csvImportOption = screen.getByText('CSV Format (Single/Multi-Topic)');
    fireEvent.click(csvImportOption);

    // Check that the dialog opens with enhanced description
    expect(screen.getByText('Import Questions from CSV')).toBeInTheDocument();
    expect(screen.getByText(/Supports both single-topic and multi-topic import modes/)).toBeInTheDocument();
    expect(screen.getByText(/Choose single-topic to import all questions to one topic/)).toBeInTheDocument();
    expect(screen.getByText(/multi-topic to import questions to multiple topics/)).toBeInTheDocument();
  });

  it('should pass topics to MultiTopicCSVImport component', async () => {
    renderAdminDashboard();
    
    // Navigate to questions tab and open CSV import
    const questionsTab = screen.getByRole('tab', { name: /questions/i });
    fireEvent.click(questionsTab);

    const importButton = screen.getByRole('button', { name: /import/i });
    fireEvent.click(importButton);
    
    const csvImportOption = screen.getByText('CSV Format (Single/Multi-Topic)');
    fireEvent.click(csvImportOption);

    // Check that the component receives the topics
    expect(screen.getByText('Topics available: 2')).toBeInTheDocument();
  });

  it('should handle single-topic import success correctly', async () => {
    renderAdminDashboard();
    
    // Open CSV import dialog
    const questionsTab = screen.getByRole('tab', { name: /questions/i });
    fireEvent.click(questionsTab);

    const importButton = screen.getByRole('button', { name: /import/i });
    fireEvent.click(importButton);
    
    const csvImportOption = screen.getByText('CSV Format (Single/Multi-Topic)');
    fireEvent.click(csvImportOption);

    // Simulate single-topic import success
    const successButton = screen.getByTestId('simulate-single-topic-success');
    fireEvent.click(successButton);

    // Check that success handling works correctly
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Import completed successfully',
        description: 'Successfully imported 5 questions',
      });
    });

    // Check that questions are refreshed but not topics (no new topics created)
    expect(mockRefreshQuestions).toHaveBeenCalled();
    expect(mockRefreshTopics).not.toHaveBeenCalled();
  });

  it('should handle multi-topic import success with new topics correctly', async () => {
    renderAdminDashboard();
    
    // Open CSV import dialog
    const questionsTab = screen.getByRole('tab', { name: /questions/i });
    fireEvent.click(questionsTab);

    const importButton = screen.getByRole('button', { name: /import/i });
    fireEvent.click(importButton);
    
    const csvImportOption = screen.getByText('CSV Format (Single/Multi-Topic)');
    fireEvent.click(csvImportOption);

    // Simulate multi-topic import success
    const successButton = screen.getByTestId('simulate-multi-topic-success');
    fireEvent.click(successButton);

    // Check that success handling works correctly for multi-topic import
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Import completed successfully',
        description: 'Successfully imported 10 questions across 3 topics. Created 2 new topics: Network Security, Incident Response',
      });
    });

    // Check that both questions and topics are refreshed (new topics were created)
    expect(mockRefreshQuestions).toHaveBeenCalled();
    expect(mockRefreshTopics).toHaveBeenCalled();
  });

  it('should close dialog on successful import', async () => {
    renderAdminDashboard();
    
    // Open CSV import dialog
    const questionsTab = screen.getByRole('tab', { name: /questions/i });
    fireEvent.click(questionsTab);

    const importButton = screen.getByRole('button', { name: /import/i });
    fireEvent.click(importButton);
    
    const csvImportOption = screen.getByText('CSV Format (Single/Multi-Topic)');
    fireEvent.click(csvImportOption);

    // Verify dialog is open
    expect(screen.getByText('Import Questions from CSV')).toBeInTheDocument();

    // Simulate successful import
    const successButton = screen.getByTestId('simulate-single-topic-success');
    fireEvent.click(successButton);

    // Check that dialog is closed
    await waitFor(() => {
      expect(screen.queryByText('Import Questions from CSV')).not.toBeInTheDocument();
    });
  });

  it('should close dialog on cancel', async () => {
    renderAdminDashboard();
    
    // Open CSV import dialog
    const questionsTab = screen.getByRole('tab', { name: /questions/i });
    fireEvent.click(questionsTab);

    const importButton = screen.getByRole('button', { name: /import/i });
    fireEvent.click(importButton);
    
    const csvImportOption = screen.getByText('CSV Format (Single/Multi-Topic)');
    fireEvent.click(csvImportOption);

    // Verify dialog is open
    expect(screen.getByText('Import Questions from CSV')).toBeInTheDocument();

    // Click cancel
    const cancelButton = screen.getByTestId('simulate-cancel');
    fireEvent.click(cancelButton);

    // Check that dialog is closed
    await waitFor(() => {
      expect(screen.queryByText('Import Questions from CSV')).not.toBeInTheDocument();
    });
  });

  it('should maintain backward compatibility with existing single-topic workflow', async () => {
    renderAdminDashboard();
    
    // Navigate to questions tab
    const questionsTab = screen.getByRole('tab', { name: /questions/i });
    fireEvent.click(questionsTab);

    // Check that the old text format import is still available
    const importButton = screen.getByRole('button', { name: /import/i });
    fireEvent.click(importButton);

    expect(screen.getByText('Text Format')).toBeInTheDocument();
    expect(screen.getByText('CSV Format (Single/Multi-Topic)')).toBeInTheDocument();
  });

  it('should handle import result logging correctly', async () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    
    renderAdminDashboard();
    
    // Open CSV import and simulate success
    const questionsTab = screen.getByRole('tab', { name: /questions/i });
    fireEvent.click(questionsTab);

    const importButton = screen.getByRole('button', { name: /import/i });
    fireEvent.click(importButton);
    
    const csvImportOption = screen.getByText('CSV Format (Single/Multi-Topic)');
    fireEvent.click(csvImportOption);

    const successButton = screen.getByTestId('simulate-single-topic-success');
    fireEvent.click(successButton);

    // Check that import result is logged
    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Multi-topic import completed:', expect.any(Object));
    });

    consoleSpy.mockRestore();
  });
});