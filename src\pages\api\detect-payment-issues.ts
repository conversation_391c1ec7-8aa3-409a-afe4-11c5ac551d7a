import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('Starting payment issue detection...');

    // Step 1: Get all users with their current subscription status
    const { data: users, error: usersError } = await supabase
      .from('profiles')
      .select(`
        id,
        email,
        subscription_status,
        subscription_plan,
        subscription_expires_at,
        created_at
      `);

    if (usersError) {
      console.error('Error fetching users:', usersError);
      return res.status(500).json({ error: 'Failed to fetch users' });
    }

    // Step 2: Get all successful payment transactions
    const { data: payments, error: paymentsError } = await supabase
      .from('payment_transactions')
      .select(`
        id,
        user_id,
        email,
        amount,
        status,
        plan_id,
        created_at,
        paystack_reference
      `)
      .eq('status', 'success');

    if (paymentsError) {
      console.error('Error fetching payments:', paymentsError);
      return res.status(500).json({ error: 'Failed to fetch payments' });
    }

    console.log(`Found ${users.length} users and ${payments.length} successful payments`);

    // Step 3: Identify users with payment discrepancies
    const usersWithIssues = [];
    const fixedUsers = [];

    for (const payment of payments) {
      // Find the user for this payment
      const user = users.find(u => u.id === payment.user_id || u.email === payment.email);
      
      if (!user) {
        console.log(`No user found for payment ${payment.id} (email: ${payment.email})`);
        continue;
      }

      // Check if user has premium access despite successful payment
      const hasActivePremium = user.subscription_status === 'active' && 
                              user.subscription_plan !== 'free' &&
                              new Date(user.subscription_expires_at) > new Date();

      if (!hasActivePremium) {
        console.log(`User ${user.email} has successful payment but no active premium access`);
        
        usersWithIssues.push({
          userId: user.id,
          email: user.email,
          paymentId: payment.id,
          planId: payment.plan_id,
          paymentAmount: payment.amount,
          paymentDate: payment.created_at,
          currentStatus: user.subscription_status,
          currentPlan: user.subscription_plan
        });

        // Auto-fix: Grant premium access based on the payment
        try {
          const planDurations = {
            'basic': 7, // 7 days
            'pro': 7,   // 7 days  
            'elite': 365 // 1 year for one-time payment
          };

          const durationDays = planDurations[payment.plan_id] || 7;
          const expiresAt = new Date();
          expiresAt.setDate(expiresAt.getDate() + durationDays);

          const { error: updateError } = await supabase
            .from('profiles')
            .update({
              subscription_status: 'active',
              subscription_plan: payment.plan_id,
              subscription_expires_at: expiresAt.toISOString(),
              updated_at: new Date().toISOString()
            })
            .eq('id', user.id);

          if (updateError) {
            console.error(`Failed to fix user ${user.email}:`, updateError);
          } else {
            console.log(`Successfully fixed premium access for ${user.email}`);
            fixedUsers.push({
              email: user.email,
              planId: payment.plan_id,
              expiresAt: expiresAt.toISOString()
            });
          }
        } catch (fixError) {
          console.error(`Error fixing user ${user.email}:`, fixError);
        }
      }
    }

    // Step 4: Also check for users who might have multiple successful payments but expired access
    const now = new Date();
    const expiredPremiumUsers = users.filter(user => 
      user.subscription_status === 'active' && 
      user.subscription_plan !== 'free' &&
      new Date(user.subscription_expires_at) <= now
    );

    for (const user of expiredPremiumUsers) {
      // Check if they have recent successful payments
      const recentPayments = payments.filter(p => 
        (p.user_id === user.id || p.email === user.email) &&
        new Date(p.created_at) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
      );

      if (recentPayments.length > 0) {
        const latestPayment = recentPayments.sort((a, b) => 
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        )[0];

        console.log(`User ${user.email} has expired premium but recent payment`);
        
        usersWithIssues.push({
          userId: user.id,
          email: user.email,
          paymentId: latestPayment.id,
          planId: latestPayment.plan_id,
          paymentAmount: latestPayment.amount,
          paymentDate: latestPayment.created_at,
          currentStatus: user.subscription_status,
          currentPlan: user.subscription_plan,
          issue: 'expired_with_recent_payment'
        });

        // Auto-fix: Extend premium access
        try {
          const planDurations = {
            'basic': 7,
            'pro': 7,
            'elite': 365
          };

          const durationDays = planDurations[latestPayment.plan_id] || 7;
          const expiresAt = new Date();
          expiresAt.setDate(expiresAt.getDate() + durationDays);

          const { error: updateError } = await supabase
            .from('profiles')
            .update({
              subscription_status: 'active',
              subscription_plan: latestPayment.plan_id,
              subscription_expires_at: expiresAt.toISOString(),
              updated_at: new Date().toISOString()
            })
            .eq('id', user.id);

          if (!updateError) {
            fixedUsers.push({
              email: user.email,
              planId: latestPayment.plan_id,
              expiresAt: expiresAt.toISOString()
            });
          }
        } catch (fixError) {
          console.error(`Error fixing expired user ${user.email}:`, fixError);
        }
      }
    }

    console.log(`Detection complete: ${usersWithIssues.length} issues found, ${fixedUsers.length} users fixed`);

    return res.status(200).json({
      success: true,
      usersWithIssues,
      fixed: fixedUsers.length,
      fixedUsers,
      summary: {
        totalUsers: users.length,
        totalPayments: payments.length,
        issuesFound: usersWithIssues.length,
        usersFixed: fixedUsers.length
      }
    });

  } catch (error) {
    console.error('Error in payment issue detection:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      details: error.message 
    });
  }
}
