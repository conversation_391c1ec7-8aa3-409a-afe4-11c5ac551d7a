/**
 * Simple Setup Script
 * No CLI required - just environment variables and database access
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

console.log('🚀 Simple Quiz Randomization Setup (No CLI Required)\n');

// Check environment variables
console.log('1. Checking environment variables...');

const requiredEnvVars = {
  'VITE_SUPABASE_URL': process.env.VITE_SUPABASE_URL,
  'VITE_SUPABASE_ANON_KEY': process.env.VITE_SUPABASE_ANON_KEY
};

const optionalEnvVars = {
  'SUPABASE_SERVICE_ROLE_KEY': process.env.SUPABASE_SERVICE_ROLE_KEY
};

let envOk = true;

for (const [key, value] of Object.entries(requiredEnvVars)) {
  if (!value) {
    console.log(`❌ Missing required: ${key}`);
    envOk = false;
  } else {
    console.log(`✅ Found: ${key}`);
  }
}

for (const [key, value] of Object.entries(optionalEnvVars)) {
  if (!value) {
    console.log(`⚠️  Optional missing: ${key}`);
  } else {
    console.log(`✅ Found: ${key}`);
  }
}

if (!envOk) {
  console.log('\n❌ Missing required environment variables!');
  console.log('\n📝 Please create/update your .env file with:');
  console.log('VITE_SUPABASE_URL=https://agdyycknlxojiwhlqicq.supabase.co');
  console.log('VITE_SUPABASE_ANON_KEY=your_anon_key_here');
  console.log('\n🔍 Get your keys from: Supabase Dashboard → Settings → API');
  process.exit(1);
}

// Test database connection
console.log('\n2. Testing database connection...');

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

try {
  const { data, error } = await supabase
    .from('topics')
    .select('id, title')
    .limit(1);

  if (error) {
    console.log('❌ Database connection failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Check your Supabase project is active');
    console.log('2. Verify your API keys are correct');
    console.log('3. Check your internet connection');
    process.exit(1);
  }

  console.log('✅ Database connection successful');

} catch (connectionError) {
  console.log('❌ Connection error:', connectionError.message);
  process.exit(1);
}

// Check if quiz randomization tables exist
console.log('\n3. Checking quiz randomization tables...');

const tables = ['quiz_sessions', 'question_analytics'];
let tablesOk = true;

for (const table of tables) {
  try {
    const { data, error } = await supabase
      .from(table)
      .select('id')
      .limit(1);

    if (error) {
      console.log(`❌ Table '${table}' issue:`, error.message);
      tablesOk = false;
    } else {
      console.log(`✅ Table '${table}' is accessible`);
    }
  } catch (tableError) {
    console.log(`❌ Error checking '${table}':`, tableError.message);
    tablesOk = false;
  }
}

if (!tablesOk) {
  console.log('\n❌ Some required tables are missing!');
  console.log('\n🔧 To fix this:');
  console.log('1. Go to your Supabase Dashboard');
  console.log('2. Navigate to SQL Editor');
  console.log('3. Run the SQL script from: quiz-randomization-schema-fix.sql');
  console.log('4. Run this setup script again');
  process.exit(1);
}

console.log('\n🎉 Setup completed successfully!');
console.log('\n📊 What you can do now:');
console.log('✅ Take quizzes with randomized questions');
console.log('✅ Questions and answers are shuffled');
console.log('✅ Quiz sessions are tracked');
console.log('✅ Analytics are recorded');

console.log('\n🔧 If you encounter errors:');
console.log('1. Run: node debug-quiz-errors.js');
console.log('2. Check browser console for errors');
console.log('3. Verify quiz data exists in your database');

console.log('\n🚀 Your quiz randomization system is ready to use!');