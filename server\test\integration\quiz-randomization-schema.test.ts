import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mockSupabaseClient } from '../setup.js';

describe('Quiz Randomization Schema Migration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Questions Table Enhancements', () => {
    it('should add usage tracking columns to questions table', async () => {
      // Mock successful column addition
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          columns_added: ['usage_count', 'last_used', 'correct_answer_rate'],
          indexes_created: ['idx_questions_usage_count', 'idx_questions_last_used', 'idx_questions_correct_answer_rate']
        },
        error: null
      });

      const result = await mockSupabaseClient.rpc('test_questions_table_migration');

      expect(result.data.success).toBe(true);
      expect(result.data.columns_added).toContain('usage_count');
      expect(result.data.columns_added).toContain('last_used');
      expect(result.data.columns_added).toContain('correct_answer_rate');
    });

    it('should handle existing columns gracefully', async () => {
      // Mock scenario where columns already exist
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          message: 'Columns already exist, skipping creation',
          existing_columns: ['usage_count', 'last_used', 'correct_answer_rate']
        },
        error: null
      });

      const result = await mockSupabaseClient.rpc('test_questions_table_migration');

      expect(result.data.success).toBe(true);
      expect(result.data.existing_columns).toHaveLength(3);
    });

    it('should set correct default values for new columns', async () => {
      // Mock query to check default values
      const mockQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: {
            usage_count: 0,
            last_used: null,
            correct_answer_rate: 0.00
          },
          error: null
        })
      };

      mockSupabaseClient.from.mockReturnValue(mockQuery);

      const result = await mockSupabaseClient.from('questions')
        .select('usage_count, last_used, correct_answer_rate')
        .eq('id', 'test-question-id')
        .single();

      expect(result.data.usage_count).toBe(0);
      expect(result.data.last_used).toBeNull();
      expect(result.data.correct_answer_rate).toBe(0.00);
    });
  });

  describe('Quiz Sessions Table Creation', () => {
    it('should create quiz_sessions table with correct structure', async () => {
      // Mock successful table creation
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          table_created: 'quiz_sessions',
          columns: [
            'id', 'user_id', 'topic_id', 'questions_data', 'quiz_length',
            'created_at', 'expires_at', 'completed_at', 'score', 'total_questions', 'time_taken'
          ],
          indexes: [
            'idx_quiz_sessions_user_id',
            'idx_quiz_sessions_topic_id',
            'idx_quiz_sessions_created_at',
            'idx_quiz_sessions_expires_at',
            'idx_quiz_sessions_completed_at'
          ]
        },
        error: null
      });

      const result = await mockSupabaseClient.rpc('test_quiz_sessions_table_creation');

      expect(result.data.success).toBe(true);
      expect(result.data.table_created).toBe('quiz_sessions');
      expect(result.data.columns).toContain('questions_data');
      expect(result.data.columns).toContain('expires_at');
      expect(result.data.indexes).toContain('idx_quiz_sessions_user_id');
    });

    it('should create proper RLS policies for quiz_sessions', async () => {
      // Mock RLS policy creation
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          policies_created: [
            'Users can view their own quiz sessions',
            'Users can insert their own quiz sessions',
            'Users can update their own quiz sessions',
            'Service role can manage quiz sessions'
          ]
        },
        error: null
      });

      const result = await mockSupabaseClient.rpc('test_quiz_sessions_rls_policies');

      expect(result.data.success).toBe(true);
      expect(result.data.policies_created).toHaveLength(4);
    });

    it('should set correct default values for quiz_sessions', async () => {
      // Mock insertion with default values
      const mockQuery = {
        insert: vi.fn().mockReturnThis(),
        select: vi.fn().mockResolvedValue({
          data: [{
            id: 'test-session-id',
            user_id: 'test-user-id',
            topic_id: 'test-topic-id',
            questions_data: {},
            quiz_length: 10,
            created_at: new Date().toISOString(),
            expires_at: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
            completed_at: null,
            score: null,
            total_questions: null,
            time_taken: null
          }],
          error: null
        })
      };

      mockSupabaseClient.from.mockReturnValue(mockQuery);

      const result = await mockSupabaseClient.from('quiz_sessions')
        .insert({
          user_id: 'test-user-id',
          topic_id: 'test-topic-id',
          questions_data: {}
        })
        .select();

      expect(result.data[0].quiz_length).toBe(10);
      expect(result.data[0].completed_at).toBeNull();
      expect(result.data[0].expires_at).toBeDefined();
    });

    it('should handle foreign key constraints properly', async () => {
      // Mock foreign key constraint violation
      const mockQuery = {
        insert: vi.fn().mockResolvedValue({
          data: null,
          error: {
            message: 'insert or update on table "quiz_sessions" violates foreign key constraint',
            code: '23503'
          }
        })
      };

      mockSupabaseClient.from.mockReturnValue(mockQuery);

      const result = await mockSupabaseClient.from('quiz_sessions')
        .insert({
          user_id: 'nonexistent-user',
          topic_id: 'test-topic-id',
          questions_data: {}
        });

      expect(result.error).toBeDefined();
      expect(result.error.code).toBe('23503');
    });
  });

  describe('Question Analytics Table Creation', () => {
    it('should create question_analytics table with correct structure', async () => {
      // Mock successful table creation
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          table_created: 'question_analytics',
          columns: [
            'id', 'question_id', 'user_id', 'quiz_session_id',
            'answered_correctly', 'time_to_answer', 'selected_option', 'created_at'
          ],
          indexes: [
            'idx_question_analytics_question_id',
            'idx_question_analytics_user_id',
            'idx_question_analytics_quiz_session_id',
            'idx_question_analytics_created_at',
            'idx_question_analytics_answered_correctly'
          ]
        },
        error: null
      });

      const result = await mockSupabaseClient.rpc('test_question_analytics_table_creation');

      expect(result.data.success).toBe(true);
      expect(result.data.table_created).toBe('question_analytics');
      expect(result.data.columns).toContain('answered_correctly');
      expect(result.data.columns).toContain('time_to_answer');
      expect(result.data.indexes).toContain('idx_question_analytics_question_id');
    });

    it('should create proper RLS policies for question_analytics', async () => {
      // Mock RLS policy creation
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          policies_created: [
            'Users can view their own question analytics',
            'Users can insert their own question analytics',
            'Service role can manage question analytics',
            'Admins can view all question analytics'
          ]
        },
        error: null
      });

      const result = await mockSupabaseClient.rpc('test_question_analytics_rls_policies');

      expect(result.data.success).toBe(true);
      expect(result.data.policies_created).toHaveLength(4);
    });

    it('should handle analytics data insertion correctly', async () => {
      // Mock successful analytics insertion
      const mockQuery = {
        insert: vi.fn().mockReturnThis(),
        select: vi.fn().mockResolvedValue({
          data: [{
            id: 'test-analytics-id',
            question_id: 'test-question-id',
            user_id: 'test-user-id',
            quiz_session_id: 'test-session-id',
            answered_correctly: true,
            time_to_answer: 15,
            selected_option: 2,
            created_at: new Date().toISOString()
          }],
          error: null
        })
      };

      mockSupabaseClient.from.mockReturnValue(mockQuery);

      const result = await mockSupabaseClient.from('question_analytics')
        .insert({
          question_id: 'test-question-id',
          user_id: 'test-user-id',
          quiz_session_id: 'test-session-id',
          answered_correctly: true,
          time_to_answer: 15,
          selected_option: 2
        })
        .select();

      expect(result.data[0].answered_correctly).toBe(true);
      expect(result.data[0].time_to_answer).toBe(15);
      expect(result.data[0].selected_option).toBe(2);
    });
  });

  describe('Database Functions and Triggers', () => {
    it('should create update_question_usage_stats function', async () => {
      // Mock function creation
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          function_created: 'update_question_usage_stats',
          trigger_created: 'trigger_update_question_usage'
        },
        error: null
      });

      const result = await mockSupabaseClient.rpc('test_usage_stats_function_creation');

      expect(result.data.success).toBe(true);
      expect(result.data.function_created).toBe('update_question_usage_stats');
      expect(result.data.trigger_created).toBe('trigger_update_question_usage');
    });

    it('should update question usage when analytics are inserted', async () => {
      // Mock trigger execution
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          question_id: 'test-question-id',
          old_usage_count: 5,
          new_usage_count: 6,
          last_used_updated: true
        },
        error: null
      });

      const result = await mockSupabaseClient.rpc('test_question_usage_trigger', {
        question_id: 'test-question-id'
      });

      expect(result.data.success).toBe(true);
      expect(result.data.new_usage_count).toBe(6);
      expect(result.data.last_used_updated).toBe(true);
    });

    it('should create update_question_correct_answer_rate function', async () => {
      // Mock function creation
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          function_created: 'update_question_correct_answer_rate',
          trigger_created: 'trigger_update_correct_answer_rate'
        },
        error: null
      });

      const result = await mockSupabaseClient.rpc('test_correct_answer_rate_function_creation');

      expect(result.data.success).toBe(true);
      expect(result.data.function_created).toBe('update_question_correct_answer_rate');
    });

    it('should calculate correct answer rate accurately', async () => {
      // Mock correct answer rate calculation
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          question_id: 'test-question-id',
          total_attempts: 10,
          correct_attempts: 7,
          calculated_rate: 70.00
        },
        error: null
      });

      const result = await mockSupabaseClient.rpc('test_correct_answer_rate_calculation', {
        question_id: 'test-question-id'
      });

      expect(result.data.success).toBe(true);
      expect(result.data.calculated_rate).toBe(70.00);
      expect(result.data.total_attempts).toBe(10);
      expect(result.data.correct_attempts).toBe(7);
    });

    it('should create cleanup_expired_quiz_sessions function', async () => {
      // Mock cleanup function creation
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          function_created: 'cleanup_expired_quiz_sessions'
        },
        error: null
      });

      const result = await mockSupabaseClient.rpc('test_cleanup_function_creation');

      expect(result.data.success).toBe(true);
      expect(result.data.function_created).toBe('cleanup_expired_quiz_sessions');
    });

    it('should clean up expired quiz sessions correctly', async () => {
      // Mock cleanup execution
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          deleted_count: 3,
          expired_sessions: ['session1', 'session2', 'session3']
        },
        error: null
      });

      const result = await mockSupabaseClient.rpc('cleanup_expired_quiz_sessions');

      expect(result.data.success).toBe(true);
      expect(result.data.deleted_count).toBe(3);
      expect(result.data.expired_sessions).toHaveLength(3);
    });

    it('should create get_topic_question_stats function', async () => {
      // Mock stats function creation
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          function_created: 'get_topic_question_stats'
        },
        error: null
      });

      const result = await mockSupabaseClient.rpc('test_topic_stats_function_creation');

      expect(result.data.success).toBe(true);
      expect(result.data.function_created).toBe('get_topic_question_stats');
    });

    it('should return accurate topic question statistics', async () => {
      // Mock topic statistics
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          total_questions: 50,
          avg_usage_count: 12.5,
          avg_correct_rate: 75.25,
          questions_never_used: 5,
          questions_low_performance: 3
        },
        error: null
      });

      const result = await mockSupabaseClient.rpc('get_topic_question_stats', {
        topic_uuid: 'test-topic-id'
      });

      expect(result.data.total_questions).toBe(50);
      expect(result.data.avg_usage_count).toBe(12.5);
      expect(result.data.avg_correct_rate).toBe(75.25);
      expect(result.data.questions_never_used).toBe(5);
      expect(result.data.questions_low_performance).toBe(3);
    });
  });

  describe('Migration Rollback and Error Handling', () => {
    it('should handle migration failures gracefully', async () => {
      // Mock migration failure
      mockSupabaseClient.rpc.mockResolvedValue({
        data: null,
        error: {
          message: 'column "usage_count" of relation "questions" already exists',
          code: '42701'
        }
      });

      const result = await mockSupabaseClient.rpc('test_migration_error_handling');

      expect(result.error).toBeDefined();
      expect(result.error.code).toBe('42701');
    });

    it('should support migration rollback', async () => {
      // Mock rollback operation
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          rollback_actions: [
            'Dropped trigger trigger_update_question_usage',
            'Dropped trigger trigger_update_correct_answer_rate',
            'Dropped function update_question_usage_stats',
            'Dropped function update_question_correct_answer_rate',
            'Dropped table question_analytics',
            'Dropped table quiz_sessions',
            'Removed columns from questions table'
          ]
        },
        error: null
      });

      const result = await mockSupabaseClient.rpc('rollback_quiz_randomization_migration');

      expect(result.data.success).toBe(true);
      expect(result.data.rollback_actions).toContain('Dropped table quiz_sessions');
      expect(result.data.rollback_actions).toContain('Dropped table question_analytics');
    });

    it('should validate schema integrity after migration', async () => {
      // Mock schema validation
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          validation_results: {
            questions_table_valid: true,
            quiz_sessions_table_valid: true,
            question_analytics_table_valid: true,
            functions_created: true,
            triggers_created: true,
            indexes_created: true,
            rls_policies_created: true
          }
        },
        error: null
      });

      const result = await mockSupabaseClient.rpc('validate_quiz_randomization_schema');

      expect(result.data.success).toBe(true);
      expect(result.data.validation_results.questions_table_valid).toBe(true);
      expect(result.data.validation_results.quiz_sessions_table_valid).toBe(true);
      expect(result.data.validation_results.question_analytics_table_valid).toBe(true);
    });
  });

  describe('Performance and Indexing', () => {
    it('should create all required indexes', async () => {
      // Mock index creation verification
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          indexes_created: [
            'idx_questions_usage_count',
            'idx_questions_last_used',
            'idx_questions_correct_answer_rate',
            'idx_quiz_sessions_user_id',
            'idx_quiz_sessions_topic_id',
            'idx_quiz_sessions_created_at',
            'idx_quiz_sessions_expires_at',
            'idx_quiz_sessions_completed_at',
            'idx_question_analytics_question_id',
            'idx_question_analytics_user_id',
            'idx_question_analytics_quiz_session_id',
            'idx_question_analytics_created_at',
            'idx_question_analytics_answered_correctly'
          ]
        },
        error: null
      });

      const result = await mockSupabaseClient.rpc('verify_quiz_randomization_indexes');

      expect(result.data.success).toBe(true);
      expect(result.data.indexes_created).toHaveLength(13);
    });

    it('should handle large dataset operations efficiently', async () => {
      // Mock performance test with large dataset
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          performance_metrics: {
            question_selection_time_ms: 45,
            analytics_insertion_time_ms: 12,
            usage_update_time_ms: 8,
            cleanup_time_ms: 150
          }
        },
        error: null
      });

      const result = await mockSupabaseClient.rpc('test_quiz_randomization_performance');

      expect(result.data.success).toBe(true);
      expect(result.data.performance_metrics.question_selection_time_ms).toBeLessThan(100);
      expect(result.data.performance_metrics.analytics_insertion_time_ms).toBeLessThan(50);
    });
  });

  describe('Data Integrity and Constraints', () => {
    it('should enforce foreign key constraints', async () => {
      // Mock foreign key constraint test
      const mockQuery = {
        insert: vi.fn().mockResolvedValue({
          data: null,
          error: {
            message: 'insert or update on table "question_analytics" violates foreign key constraint "question_analytics_question_id_fkey"',
            code: '23503'
          }
        })
      };

      mockSupabaseClient.from.mockReturnValue(mockQuery);

      const result = await mockSupabaseClient.from('question_analytics')
        .insert({
          question_id: 'nonexistent-question',
          user_id: 'test-user',
          answered_correctly: true
        });

      expect(result.error).toBeDefined();
      expect(result.error.code).toBe('23503');
    });

    it('should validate data types and constraints', async () => {
      // Mock data validation test
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          validation_results: {
            usage_count_is_integer: true,
            correct_answer_rate_is_decimal: true,
            answered_correctly_is_boolean: true,
            time_to_answer_is_integer: true,
            expires_at_has_default: true
          }
        },
        error: null
      });

      const result = await mockSupabaseClient.rpc('validate_quiz_randomization_data_types');

      expect(result.data.success).toBe(true);
      expect(result.data.validation_results.usage_count_is_integer).toBe(true);
      expect(result.data.validation_results.correct_answer_rate_is_decimal).toBe(true);
    });

    it('should handle concurrent updates safely', async () => {
      // Mock concurrent update test
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          concurrent_updates_handled: true,
          final_usage_count: 10,
          final_correct_rate: 80.00
        },
        error: null
      });

      const result = await mockSupabaseClient.rpc('test_concurrent_question_updates', {
        question_id: 'test-question-id',
        concurrent_operations: 5
      });

      expect(result.data.success).toBe(true);
      expect(result.data.concurrent_updates_handled).toBe(true);
    });
  });
});