#!/usr/bin/env node

/**
 * Environment Setup Script for SecQuiz
 * This script helps users set up their environment variables
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function setupEnvironment() {
  console.log('🚀 SecQuiz Environment Setup\n');
  console.log('This script will help you create a .env file with the required configuration.\n');

  // Check if .env already exists
  const envPath = path.join(process.cwd(), '.env');
  if (fs.existsSync(envPath)) {
    const overwrite = await question('⚠️  .env file already exists. Do you want to overwrite it? (y/N): ');
    if (overwrite.toLowerCase() !== 'y' && overwrite.toLowerCase() !== 'yes') {
      console.log('Setup cancelled. Your existing .env file was not modified.');
      rl.close();
      return;
    }
  }

  console.log('\n📋 Please provide the following information:\n');

  // Supabase Configuration
  console.log('🔗 Supabase Configuration');
  console.log('You can find these values in your Supabase project dashboard under Settings > API\n');
  
  const supabaseUrl = await question('Supabase URL (e.g., https://your-project.supabase.co): ');
  const supabaseAnonKey = await question('Supabase Anonymous Key: ');

  // Application Settings
  console.log('\n🏠 Application Settings');
  const appName = await question('App Name (default: SecQuiz): ') || 'SecQuiz';
  const appDescription = await question('App Description (default: A cybersecurity education platform): ') || 'A cybersecurity education platform';
  const appUrl = await question('App URL (default: http://localhost:5173): ') || 'http://localhost:5173';

  // API Configuration
  console.log('\n🔌 API Configuration');
  const apiUrl = await question('API URL (default: http://localhost:3001): ') || 'http://localhost:3001';

  // Paystack Configuration
  console.log('\n💳 Paystack Configuration (Optional)');
  console.log('You can find this in your Paystack dashboard under Settings > API Keys\n');
  const paystackPublicKey = await question('Paystack Public Key (optional): ') || 'your_paystack_public_key_here';

  // Feature Flags
  console.log('\n🎛️  Feature Flags');
  const enableAdmin = await question('Enable Admin Features? (y/N): ');
  const enableAnalytics = await question('Enable Analytics? (y/N): ');
  const enableDebug = await question('Enable Debug Mode? (y/N): ');

  // Create .env content
  const envContent = `# Supabase Configuration
VITE_SUPABASE_URL=${supabaseUrl}
VITE_SUPABASE_ANON_KEY=${supabaseAnonKey}

# Application Settings
VITE_APP_NAME=${appName}
VITE_APP_DESCRIPTION=${appDescription}
VITE_APP_URL=${appUrl}

# Feature Flags
VITE_ENABLE_ADMIN_FEATURES=${enableAdmin.toLowerCase() === 'y' || enableAdmin.toLowerCase() === 'yes' ? 'true' : 'false'}
VITE_ENABLE_ANALYTICS=${enableAnalytics.toLowerCase() === 'y' || enableAnalytics.toLowerCase() === 'yes' ? 'true' : 'false'}
VITE_ENABLE_DEBUG_MODE=${enableDebug.toLowerCase() === 'y' || enableDebug.toLowerCase() === 'yes' ? 'true' : 'false'}

# API Configuration
VITE_API_TIMEOUT=30000
VITE_MAX_UPLOAD_SIZE=5242880
VITE_API_URL=${apiUrl}

# Paystack Configuration
VITE_PAYSTACK_PUBLIC_KEY=${paystackPublicKey}

# Authentication Settings
VITE_AUTH_REDIRECT_URL=${appUrl}/auth/verify
`;

  // Write .env file
  try {
    fs.writeFileSync(envPath, envContent);
    console.log('\n✅ Environment file created successfully!');
    console.log(`📁 Created: ${envPath}`);
    
    console.log('\n🎉 Setup Complete!');
    console.log('\nNext steps:');
    console.log('1. Start the development server: npm run dev');
    console.log('2. Start the backend server: cd server && npm run dev');
    console.log('3. Open your browser to http://localhost:5173');
    
    console.log('\n📚 Additional Resources:');
    console.log('- Supabase Dashboard: https://app.supabase.com');
    console.log('- Paystack Dashboard: https://dashboard.paystack.com');
    console.log('- Project Documentation: README.md');
    
  } catch (error) {
    console.error('\n❌ Error creating .env file:', error.message);
    console.log('\nYou can manually create the .env file with the following content:');
    console.log('\n' + envContent);
  }

  rl.close();
}

// Validation functions
function validateSupabaseUrl(url) {
  return url && url.startsWith('https://') && url.includes('.supabase.co');
}

function validateSupabaseKey(key) {
  return key && key.length > 50; // Supabase keys are typically quite long
}

// Run setup if this script is executed directly
if (require.main === module) {
  setupEnvironment().catch(console.error);
}

module.exports = { setupEnvironment };
