/**
 * Integration tests for Quiz Randomization Flow
 * Tests the complete quiz taking experience with randomized questions and answers
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { QuizRandomizationService } from '@/services/quiz-randomization-service';
import { parseCorrectAnswer, validateAnswer, safeValidateAnswer } from '@/utils/answer-validation';
import type { User } from '@supabase/supabase-js';

// Mock the supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(),
    rpc: vi.fn(),
    auth: {
      getUser: vi.fn(),
    },
  },
}));

// Test data
const mockTopic = {
  id: 'test-topic-id',
  title: 'Test Topic',
  description: 'Test topic description',
  difficulty: 'easy',
};

const mockQuestions = [
  {
    id: 'q1',
    topic_id: 'test-topic-id',
    question_text: 'What is 2 + 2?',
    options: { '0': 'Three', '1': 'Four', '2': 'Five', '3': 'Six' },
    correct_answer: '1',
    explanation: 'Two plus two equals four',
    usage_count: 0,
    last_used: null,
    correct_answer_rate: null,
  },
  {
    id: 'q2',
    topic_id: 'test-topic-id',
    question_text: 'What is the capital of France?',
    options: { '0': 'London', '1': 'Berlin', '2': 'Paris', '3': 'Madrid' },
    correct_answer: '2',
    explanation: 'Paris is the capital of France',
    usage_count: 0,
    last_used: null,
    correct_answer_rate: null,
  },
  {
    id: 'q3',
    topic_id: 'test-topic-id',
    question_text: 'Which planet is closest to the Sun?',
    options: { '0': 'Venus', '1': 'Mercury', '2': 'Earth', '3': 'Mars' },
    correct_answer: '1',
    explanation: 'Mercury is the closest planet to the Sun',
    usage_count: 0,
    last_used: null,
    correct_answer_rate: null,
  },
];

const mockQuizSession = {
  sessionId: 'test-session-id',
  topicId: 'test-topic-id',
  userId: 'test-user-id',
  quizLength: 3,
  questions: mockQuestions.map((q, index) => ({
    ...q,
    originalCorrectIndex: parseInt(q.correct_answer),
    shuffledCorrectIndex: (parseInt(q.correct_answer) + index) % 4, // Simulate shuffling
    optionMapping: [1, 2, 3, 0], // Simulate option shuffling
    shuffledOptions: {
      '0': Object.values(q.options)[1],
      '1': Object.values(q.options)[2],
      '2': Object.values(q.options)[3],
      '3': Object.values(q.options)[0],
    },
  })),
  session: {
    id: 'test-session-id',
    userId: 'test-user-id',
    topicId: 'test-topic-id',
    questionsData: {},
    quizLength: 3,
  },
};

describe('Quiz Randomization Flow Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should generate randomized quiz session with shuffled options', async () => {
    // Mock the service methods
    const mockGenerateSession = vi.spyOn(QuizRandomizationService, 'generateQuizSession');
    const mockGetStats = vi.spyOn(QuizRandomizationService, 'getTopicQuestionStats');

    mockGetStats.mockResolvedValue({
      total_questions: 10,
      avg_usage_count: 2,
      avg_correct_rate: 0.75,
      questions_never_used: 3,
      questions_low_performance: 1,
    });

    mockGenerateSession.mockResolvedValue(mockQuizSession);

    // Test session generation
    const session = await QuizRandomizationService.generateQuizSession(
      'test-topic-id',
      'test-user-id',
      3
    );

    expect(session).toBeDefined();
    expect(session.questions).toHaveLength(3);
    expect(session.sessionId).toBe('test-session-id');
    expect(session.topicId).toBe('test-topic-id');
    expect(session.userId).toBe('test-user-id');

    // Verify questions have randomization properties
    session.questions.forEach(question => {
      expect(question).toHaveProperty('originalCorrectIndex');
      expect(question).toHaveProperty('shuffledCorrectIndex');
      expect(question).toHaveProperty('optionMapping');
      expect(question).toHaveProperty('shuffledOptions');
    });
  });

  it('should validate answers correctly with shuffled options', () => {
    // Test scenario: Original correct answer was index 1, but after shuffling it's at index 3
    const originalCorrectAnswer = '1';
    const shuffledCorrectIndex = 3;
    
    // Parse the original correct answer
    const parsedAnswer = parseCorrectAnswer(originalCorrectAnswer, 4);
    expect(parsedAnswer.correctIndex).toBe(1);
    expect(parsedAnswer.isValid).toBe(true);

    // Test correct answer selection in shuffled position
    const correctResult = validateAnswer(shuffledCorrectIndex, shuffledCorrectIndex, 4);
    expect(correctResult.isCorrect).toBe(true);
    expect(correctResult.selectedIndex).toBe(3);
    expect(correctResult.correctIndex).toBe(3);
    expect(correctResult.confidence).toBe('high');

    // Test incorrect answer selection
    const incorrectResult = validateAnswer(0, shuffledCorrectIndex, 4);
    expect(incorrectResult.isCorrect).toBe(false);
    expect(incorrectResult.selectedIndex).toBe(0);
    expect(incorrectResult.correctIndex).toBe(3);
  });

  it('should complete quiz session and record results', async () => {
    const mockCompleteSession = vi.spyOn(QuizRandomizationService, 'completeQuizSession');
    mockCompleteSession.mockResolvedValue(true);

    const result = await QuizRandomizationService.completeQuizSession(
      'test-session-id',
      'test-user-id',
      8, // score
      120 // time taken in seconds
    );

    expect(result).toBe(true);
    expect(mockCompleteSession).toHaveBeenCalledWith(
      'test-session-id',
      'test-user-id',
      8,
      120
    );
  });

  it('should handle insufficient questions gracefully', async () => {
    const mockGetStats = vi.spyOn(QuizRandomizationService, 'getTopicQuestionStats');
    const mockGenerateSession = vi.spyOn(QuizRandomizationService, 'generateQuizSession');

    mockGetStats.mockResolvedValue({
      total_questions: 0,
      avg_usage_count: 0,
      avg_correct_rate: 0,
      questions_never_used: 0,
      questions_low_performance: 0,
    });

    mockGenerateSession.mockRejectedValue(
      new Error('No questions available for topic test-topic-id')
    );

    // First check stats
    const stats = await QuizRandomizationService.getTopicQuestionStats('test-topic-id');
    expect(stats.total_questions).toBe(0);

    // Test that the service properly handles insufficient questions
    await expect(
      QuizRandomizationService.generateQuizSession('test-topic-id', 'test-user-id', 10)
    ).rejects.toThrow('No questions available for topic test-topic-id');

    // Verify stats were checked
    expect(mockGetStats).toHaveBeenCalledWith('test-topic-id');
  });

  it('should record question analytics during quiz', async () => {
    const mockRecordAnalytics = vi.spyOn(QuizRandomizationService, 'recordQuestionAnalytics');
    mockRecordAnalytics.mockResolvedValue(true);

    const result = await QuizRandomizationService.recordQuestionAnalytics(
      'q1',
      'test-user-id',
      'test-session-id',
      true, // answeredCorrectly
      2, // selectedOption
      45 // timeToAnswer
    );

    expect(result).toBe(true);
    expect(mockRecordAnalytics).toHaveBeenCalledWith(
      'q1',
      'test-user-id',
      'test-session-id',
      true,
      2,
      45
    );
  });

  it('should handle session-based answer validation correctly', () => {
    // Test the complete flow of answer validation with randomized options
    const question = mockQuizSession.questions[0];
    
    // Verify the question has randomization properties
    expect(question).toHaveProperty('originalCorrectIndex');
    expect(question).toHaveProperty('shuffledCorrectIndex');
    expect(question).toHaveProperty('optionMapping');
    expect(question).toHaveProperty('shuffledOptions');

    // Test validation using the shuffled correct index
    const userSelection = question.shuffledCorrectIndex;
    const result = validateAnswer(userSelection, question.shuffledCorrectIndex, 4);
    
    expect(result.isCorrect).toBe(true);
    expect(result.selectedIndex).toBe(userSelection);
    expect(result.correctIndex).toBe(question.shuffledCorrectIndex);
    expect(result.confidence).toBe('high');
  });

  it('should validate quiz session flow with proper answer mapping', async () => {
    // Test the complete flow of quiz session generation and answer validation
    const mockGetStats = vi.spyOn(QuizRandomizationService, 'getTopicQuestionStats');
    const mockGenerateSession = vi.spyOn(QuizRandomizationService, 'generateQuizSession');
    const mockCompleteSession = vi.spyOn(QuizRandomizationService, 'completeQuizSession');
    const mockRecordAnalytics = vi.spyOn(QuizRandomizationService, 'recordQuestionAnalytics');

    // Mock the service responses
    mockGetStats.mockResolvedValue({
      total_questions: 3,
      avg_usage_count: 1,
      avg_correct_rate: 0.8,
      questions_never_used: 1,
      questions_low_performance: 0,
    });

    mockGenerateSession.mockResolvedValue(mockQuizSession);
    mockCompleteSession.mockResolvedValue(true);
    mockRecordAnalytics.mockResolvedValue(true);

    // Step 1: Check topic stats
    const stats = await QuizRandomizationService.getTopicQuestionStats('test-topic-id');
    expect(stats.total_questions).toBe(3);

    // Step 2: Generate quiz session
    const session = await QuizRandomizationService.generateQuizSession(
      'test-topic-id',
      'test-user-id',
      3
    );

    expect(session).toBeDefined();
    expect(session.questions).toHaveLength(3);
    expect(session.sessionId).toBe('test-session-id');

    // Step 3: Validate answers using session-based mapping
    let correctAnswers = 0;
    for (let i = 0; i < session.questions.length; i++) {
      const question = session.questions[i];
      
      // Simulate user selecting the correct answer (based on shuffled position)
      const userSelection = question.shuffledCorrectIndex;
      
      // Use direct comparison for session-based validation (simpler approach)
      const isCorrect = userSelection === question.shuffledCorrectIndex;

      expect(isCorrect).toBe(true);
      expect(userSelection).toBe(question.shuffledCorrectIndex);

      if (isCorrect) {
        correctAnswers++;
      }

      // Record analytics
      await QuizRandomizationService.recordQuestionAnalytics(
        question.id,
        'test-user-id',
        session.sessionId,
        isCorrect,
        userSelection,
        30 // time taken
      );
    }

    // Step 4: Complete the session
    const completed = await QuizRandomizationService.completeQuizSession(
      session.sessionId,
      'test-user-id',
      correctAnswers,
      120 // total time
    );

    expect(completed).toBe(true);

    // Verify all service calls were made correctly
    expect(mockGetStats).toHaveBeenCalledWith('test-topic-id');
    expect(mockGenerateSession).toHaveBeenCalledWith('test-topic-id', 'test-user-id', 3);
    expect(mockCompleteSession).toHaveBeenCalledWith('test-session-id', 'test-user-id', 3, 120);
    expect(mockRecordAnalytics).toHaveBeenCalledTimes(3);
  });

  it('should handle quiz length adjustment for limited questions', async () => {
    // Mock scenario with limited questions
    const mockGetStats = vi.spyOn(QuizRandomizationService, 'getTopicQuestionStats');
    const mockGenerateSession = vi.spyOn(QuizRandomizationService, 'generateQuizSession');

    mockGetStats.mockResolvedValue({
      total_questions: 5, // Less than default 10
      avg_usage_count: 1,
      avg_correct_rate: 0.8,
      questions_never_used: 2,
      questions_low_performance: 0,
    });

    const limitedQuizSession = {
      ...mockQuizSession,
      quizLength: 5,
      questions: mockQuizSession.questions.slice(0, 2), // Only 2 questions available
    };

    mockGenerateSession.mockResolvedValue(limitedQuizSession);

    // Test the service behavior with limited questions
    const stats = await QuizRandomizationService.getTopicQuestionStats('test-topic-id');
    expect(stats.total_questions).toBe(5);

    // Generate session with requested length of 10, but only 5 available
    const session = await QuizRandomizationService.generateQuizSession(
      'test-topic-id',
      'test-user-id',
      10 // Requested length
    );

    // Verify the session was created with available questions
    expect(session.quizLength).toBe(5); // Adjusted to available questions
    expect(session.questions).toHaveLength(2); // Mock data has 2 questions

    // Verify service calls
    expect(mockGetStats).toHaveBeenCalledWith('test-topic-id');
    expect(mockGenerateSession).toHaveBeenCalledWith('test-topic-id', 'test-user-id', 10);
  });

  it('should handle answer validation with randomized options correctly', async () => {
    // Test that answer validation works correctly with shuffled options
    const question = mockQuizSession.questions[0];
    
    // Test correct answer validation
    const correctAnswerIndex = question.shuffledCorrectIndex;
    const correctValidation = safeValidateAnswer(
      correctAnswerIndex,
      question.correct_answer,
      question.shuffledCorrectIndex,
      4
    );

    expect(correctValidation.isCorrect).toBe(true);
    expect(correctValidation.selectedIndex).toBe(correctAnswerIndex);
    expect(correctValidation.correctIndex).toBe(question.shuffledCorrectIndex);
    expect(correctValidation.confidence).toBe('high'); // Direct validation should be high confidence

    // Test incorrect answer validation
    const incorrectAnswerIndex = (correctAnswerIndex + 1) % 4;
    const incorrectValidation = safeValidateAnswer(
      incorrectAnswerIndex,
      question.correct_answer,
      question.shuffledCorrectIndex,
      4
    );

    expect(incorrectValidation.isCorrect).toBe(false);
    expect(incorrectValidation.selectedIndex).toBe(incorrectAnswerIndex);
    expect(incorrectValidation.correctIndex).toBe(question.shuffledCorrectIndex);

    // Test null selection
    const nullValidation = safeValidateAnswer(
      null,
      question.correct_answer,
      question.shuffledCorrectIndex,
      4
    );

    expect(nullValidation.isCorrect).toBe(false);
    expect(nullValidation.selectedIndex).toBe(-1);
    expect(nullValidation.correctIndex).toBe(question.shuffledCorrectIndex);

    // Verify the question has proper randomization properties
    expect(question).toHaveProperty('originalCorrectIndex');
    expect(question).toHaveProperty('shuffledCorrectIndex');
    expect(question).toHaveProperty('optionMapping');
    expect(question).toHaveProperty('shuffledOptions');
  });

  it('should handle no questions available scenario', async () => {
    // Mock scenario with no questions
    const mockGetStats = vi.spyOn(QuizRandomizationService, 'getTopicQuestionStats');
    const mockGenerateSession = vi.spyOn(QuizRandomizationService, 'generateQuizSession');

    mockGetStats.mockResolvedValue({
      total_questions: 0,
      avg_usage_count: 0,
      avg_correct_rate: 0,
      questions_never_used: 0,
      questions_low_performance: 0,
    });

    mockGenerateSession.mockRejectedValue(
      new Error('No questions available for topic test-topic-id')
    );

    // Test the service behavior with no questions
    const stats = await QuizRandomizationService.getTopicQuestionStats('test-topic-id');
    expect(stats.total_questions).toBe(0);

    // Attempt to generate session should fail
    await expect(
      QuizRandomizationService.generateQuizSession('test-topic-id', 'test-user-id', 10)
    ).rejects.toThrow('No questions available for topic test-topic-id');

    // Verify service calls
    expect(mockGetStats).toHaveBeenCalledWith('test-topic-id');
    expect(mockGenerateSession).toHaveBeenCalledWith('test-topic-id', 'test-user-id', 10);
  });

  it('should complete full quiz flow with proper scoring', async () => {
    // Test a complete quiz flow with mixed correct/incorrect answers
    const mockGetStats = vi.spyOn(QuizRandomizationService, 'getTopicQuestionStats');
    const mockGenerateSession = vi.spyOn(QuizRandomizationService, 'generateQuizSession');
    const mockCompleteSession = vi.spyOn(QuizRandomizationService, 'completeQuizSession');
    const mockRecordAnalytics = vi.spyOn(QuizRandomizationService, 'recordQuestionAnalytics');

    // Mock the service responses
    mockGetStats.mockResolvedValue({
      total_questions: 3,
      avg_usage_count: 1,
      avg_correct_rate: 0.8,
      questions_never_used: 1,
      questions_low_performance: 0,
    });

    mockGenerateSession.mockResolvedValue(mockQuizSession);
    mockCompleteSession.mockResolvedValue(true);
    mockRecordAnalytics.mockResolvedValue(true);

    // Test the complete flow with scoring logic
    // First check stats
    const stats = await QuizRandomizationService.getTopicQuestionStats('test-topic-id');
    expect(stats.total_questions).toBe(3);

    const session = await QuizRandomizationService.generateQuizSession(
      'test-topic-id',
      'test-user-id',
      3
    );

    let correctAnswers = 0;

    // Simulate answering questions - first two correct, last one incorrect
    for (let i = 0; i < session.questions.length; i++) {
      const question = session.questions[i];
      const shouldAnswerCorrectly = i < 2; // First two correct, last incorrect
      
      const selectedIndex = shouldAnswerCorrectly 
        ? question.shuffledCorrectIndex 
        : (question.shuffledCorrectIndex + 1) % 4;

      // Use direct comparison for session-based validation
      const isCorrect = selectedIndex === question.shuffledCorrectIndex;

      if (isCorrect) {
        correctAnswers++;
      }

      // Record analytics for each question
      await QuizRandomizationService.recordQuestionAnalytics(
        question.id,
        'test-user-id',
        session.sessionId,
        isCorrect,
        selectedIndex,
        30 // time taken
      );
    }

    // Complete the session with final score
    const completed = await QuizRandomizationService.completeQuizSession(
      session.sessionId,
      'test-user-id',
      correctAnswers,
      120 // total time
    );

    expect(completed).toBe(true);
    expect(correctAnswers).toBe(2); // Should have 2 correct answers

    // Verify all service calls were made correctly
    expect(mockGetStats).toHaveBeenCalledWith('test-topic-id');
    expect(mockGenerateSession).toHaveBeenCalledWith('test-topic-id', 'test-user-id', 3);
    expect(mockCompleteSession).toHaveBeenCalledWith('test-session-id', 'test-user-id', 2, 120);
    expect(mockRecordAnalytics).toHaveBeenCalledTimes(3);
  });

  it('should validate quiz length for topic correctly', async () => {
    const mockGetStats = vi.spyOn(QuizRandomizationService, 'getTopicQuestionStats');

    // Test with sufficient questions
    mockGetStats.mockResolvedValue({
      total_questions: 30,
      avg_usage_count: 2,
      avg_correct_rate: 75,
      questions_never_used: 5,
      questions_low_performance: 2
    });

    const validation1 = await QuizRandomizationService.validateQuizLengthForTopic('test-topic-id', 15);
    expect(validation1.isValid).toBe(true);
    expect(validation1.availableQuestions).toBe(30);
    expect(validation1.recommendedLength).toBe(15);
    expect(validation1.severity).toBe('info');

    // Test with insufficient questions
    mockGetStats.mockResolvedValue({
      total_questions: 8,
      avg_usage_count: 1,
      avg_correct_rate: 60,
      questions_never_used: 3,
      questions_low_performance: 1
    });

    const validation2 = await QuizRandomizationService.validateQuizLengthForTopic('test-topic-id', 15);
    expect(validation2.isValid).toBe(true);
    expect(validation2.availableQuestions).toBe(8);
    expect(validation2.recommendedLength).toBe(8);
    expect(validation2.severity).toBe('warning');
    expect(validation2.message).toContain('Only 8 questions available');

    // Test with no questions
    mockGetStats.mockResolvedValue({
      total_questions: 0,
      avg_usage_count: 0,
      avg_correct_rate: 0,
      questions_never_used: 0,
      questions_low_performance: 0
    });

    const validation3 = await QuizRandomizationService.validateQuizLengthForTopic('test-topic-id', 10);
    expect(validation3.isValid).toBe(false);
    expect(validation3.availableQuestions).toBe(0);
    expect(validation3.severity).toBe('error');

    mockGetStats.mockRestore();
  });

  it('should get available quiz lengths for topic', async () => {
    const mockGetStats = vi.spyOn(QuizRandomizationService, 'getTopicQuestionStats');

    // Test with plenty of questions
    mockGetStats.mockResolvedValue({
      total_questions: 50,
      avg_usage_count: 2,
      avg_correct_rate: 75,
      questions_never_used: 10,
      questions_low_performance: 3
    });

    const lengths1 = await QuizRandomizationService.getAvailableQuizLengths('test-topic-id');
    expect(lengths1.maxLength).toBe(50);
    expect(lengths1.options).toHaveLength(4);
    expect(lengths1.options.every(opt => opt.available)).toBe(true);
    expect(lengths1.options.every(opt => opt.recommended)).toBe(true);

    // Test with limited questions
    mockGetStats.mockResolvedValue({
      total_questions: 12,
      avg_usage_count: 1,
      avg_correct_rate: 60,
      questions_never_used: 5,
      questions_low_performance: 2
    });

    const lengths2 = await QuizRandomizationService.getAvailableQuizLengths('test-topic-id');
    expect(lengths2.maxLength).toBe(12);
    expect(lengths2.options.filter(opt => opt.available)).toHaveLength(1); // Only 10 questions option
    expect(lengths2.options.find(opt => opt.value === 10)?.recommended).toBe(false); // Not enough for good variety

    mockGetStats.mockRestore();
  });

  it('should handle different quiz length scenarios in generateQuizSession', async () => {
    const mockGetStats = vi.spyOn(QuizRandomizationService, 'getTopicQuestionStats');
    const mockSelectQuestions = vi.spyOn(QuizRandomizationService, 'selectRandomQuestions');
    const mockGenerateSession = vi.spyOn(QuizRandomizationService, 'generateQuizSession');

    // Test with requested length matching available questions
    mockGetStats.mockResolvedValue({
      total_questions: 15,
      avg_usage_count: 1,
      avg_correct_rate: 70,
      questions_never_used: 5,
      questions_low_performance: 2
    });

    mockSelectQuestions.mockResolvedValue(mockQuestions.slice(0, 3));
    mockGenerateSession.mockResolvedValue(mockQuizSession);

    const session1 = await QuizRandomizationService.generateQuizSession(
      'test-topic-id',
      'test-user-id',
      15
    );

    expect(session1.quizLength).toBe(3); // Based on mock data length
    expect(mockGenerateSession).toHaveBeenCalledWith('test-topic-id', 'test-user-id', 15);

    // Test with requested length exceeding available questions
    mockGetStats.mockResolvedValue({
      total_questions: 8,
      avg_usage_count: 1,
      avg_correct_rate: 70,
      questions_never_used: 3,
      questions_low_performance: 1
    });

    const session2 = await QuizRandomizationService.generateQuizSession(
      'test-topic-id',
      'test-user-id',
      25
    );

    expect(session2.quizLength).toBe(3); // Based on mock data length
    expect(mockGenerateSession).toHaveBeenCalledWith('test-topic-id', 'test-user-id', 25);

    mockGetStats.mockRestore();
    mockSelectQuestions.mockRestore();
    mockGenerateSession.mockRestore();
  });});


export {};