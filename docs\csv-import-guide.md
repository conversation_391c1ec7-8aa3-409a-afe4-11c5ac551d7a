# CSV Import Guide for SecQuiz

This guide explains how to use the CSV import feature to add questions to your SecQuiz application. SecQuiz supports both single-topic and multi-topic CSV import formats.

## Import Modes

### Single-Topic Import
Import all questions from a CSV file to one selected topic. This is the traditional import method.

### Multi-Topic Import
Import questions to multiple topics specified within the CSV file. This allows efficient bulk loading of questions across different topics in a single operation.

## CSV Format Options

### Single-Topic Format (Traditional)

Use this format when importing all questions to a single topic:

| Column | Description | Required |
|--------|-------------|----------|
| question_text | The text of the question | Yes |
| option_a | The text for option A | Yes |
| option_b | The text for option B | Yes |
| option_c | The text for option C | Yes |
| option_d | The text for option D | Yes |
| correct_answer | The correct answer (A, B, C, or D) | Yes |
| explanation | Explanation of the correct answer | Yes |
| difficulty | Difficulty level (easy, medium, hard) | No (defaults to medium) |

### Multi-Topic Formats

#### Name-Based Format (Recommended)
Use topic names to identify topics. New topics will be created automatically if enabled.

| Column | Description | Required |
|--------|-------------|----------|
| topic_name | Name of the topic for this question | Yes |
| question_text | The text of the question | Yes |
| option_a | The text for option A | Yes |
| option_b | The text for option B | Yes |
| option_c | The text for option C | Yes |
| option_d | The text for option D | Yes |
| correct_answer | The correct answer (A, B, C, or D) | Yes |
| explanation | Explanation of the correct answer | Yes |
| difficulty | Difficulty level (easy, medium, hard) | No (defaults to medium) |

#### ID-Based Format
Use existing topic IDs when you know the exact topic identifiers.

| Column | Description | Required |
|--------|-------------|----------|
| topic_id | UUID of the existing topic | Yes |
| question_text | The text of the question | Yes |
| option_a | The text for option A | Yes |
| option_b | The text for option B | Yes |
| option_c | The text for option C | Yes |
| option_d | The text for option D | Yes |
| correct_answer | The correct answer (A, B, C, or D) | Yes |
| explanation | Explanation of the correct answer | Yes |
| difficulty | Difficulty level (easy, medium, hard) | No (defaults to medium) |

#### Both Columns Format
Include both topic_name and topic_id columns. The topic_name takes precedence for topic resolution.

| Column | Description | Required |
|--------|-------------|----------|
| topic_name | Name of the topic for this question | Yes |
| topic_id | UUID of the existing topic | No |
| question_text | The text of the question | Yes |
| option_a | The text for option A | Yes |
| option_b | The text for option B | Yes |
| option_c | The text for option C | Yes |
| option_d | The text for option D | Yes |
| correct_answer | The correct answer (A, B, C, or D) | Yes |
| explanation | Explanation of the correct answer | Yes |
| difficulty | Difficulty level (easy, medium, hard) | No (defaults to medium) |

## Example CSV Files

### Single-Topic Format Example

```csv
question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty
What is the primary purpose of a firewall?,To prevent physical access to a network,To control network traffic based on security rules,To encrypt data during transmission,To detect malware on endpoints,B,A firewall controls network traffic flow based on predetermined security rules allowing or blocking traffic based on these rules.,medium
What is a vulnerability assessment?,A process to identify security vulnerabilities in systems or applications,An evaluation of employee performance,A check for physical security weaknesses,A financial audit,A,A vulnerability assessment is a systematic process of identifying classifying and prioritizing security vulnerabilities in systems networks or applications.,easy
What is a data breach?,A hardware failure,An unauthorized access and extraction of sensitive data,A routine backup process,A software update,B,A data breach is a security incident where sensitive protected or confidential data is accessed viewed stolen or used by an unauthorized party.,hard
```

### Multi-Topic Name-Based Format Example

```csv
topic_name,question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty
Security Fundamentals,What is the primary purpose of a firewall?,To prevent physical access to a network,To control network traffic based on security rules,To encrypt data during transmission,To detect malware on endpoints,B,A firewall controls network traffic flow based on predetermined security rules,medium
Network Security,What does VPN stand for?,Virtual Private Network,Very Personal Network,Verified Public Network,Variable Protocol Network,A,VPN stands for Virtual Private Network which creates a secure tunnel over the internet,easy
Incident Response,What is the first step in incident response?,Containment,Identification,Recovery,Lessons learned,B,The first step in incident response is identification - recognizing that a security incident has occurred,hard
Security Fundamentals,What is encryption?,A method to hide files,A process to convert data into unreadable format,A way to delete data,A backup technique,B,Encryption converts readable data into an unreadable format using cryptographic algorithms,medium
```

### Multi-Topic ID-Based Format Example

```csv
topic_id,question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty
security-fundamentals-uuid,What is the primary purpose of a firewall?,To prevent physical access to a network,To control network traffic based on security rules,To encrypt data during transmission,To detect malware on endpoints,B,A firewall controls network traffic flow based on predetermined security rules,medium
network-security-uuid,What does VPN stand for?,Virtual Private Network,Very Personal Network,Verified Public Network,Variable Protocol Network,A,VPN stands for Virtual Private Network which creates a secure tunnel over the internet,easy
incident-response-uuid,What is the first step in incident response?,Containment,Identification,Recovery,Lessons learned,B,The first step in incident response is identification - recognizing that a security incident has occurred,hard
```

## How to Import Questions

### Single-Topic Import Process

1. **Prepare your CSV file**:
   - Create a CSV file with the single-topic format columns
   - Make sure each question has all required fields
   - Save the file with a `.csv` extension

2. **Import the questions**:
   - Log in to your SecQuiz admin dashboard
   - Go to the "Questions" tab
   - Click the "Import" button and select "CSV Format"
   - Choose "Single Topic" mode
   - Select the topic you want to add the questions to
   - Upload your CSV file
   - Click "Preview Import" to review the questions
   - Click "Confirm Import" to complete the process

3. **Review the results**:
   - The system will validate your CSV file
   - If there are any errors, they will be displayed
   - Successfully imported questions will be added to the selected topic

### Multi-Topic Import Process

1. **Prepare your CSV file**:
   - Create a CSV file with one of the multi-topic formats
   - Include topic identification columns (topic_name or topic_id)
   - Make sure each question has all required fields
   - Save the file with a `.csv` extension

2. **Configure import settings**:
   - Log in to your SecQuiz admin dashboard
   - Go to the "Questions" tab
   - Click the "Import" button and select "CSV Format"
   - Choose "Multi Topic" mode
   - Enable "Auto-create Topics" if you want new topics created automatically
   - Download a template if needed to ensure correct format

3. **Import and preview**:
   - Upload your CSV file
   - Click "Preview Import" to review the questions grouped by topic
   - Check for any validation errors or warnings
   - Review which topics will be created (if auto-create is enabled)
   - Verify question counts per topic

4. **Confirm and complete**:
   - Click "Confirm Import" to execute the import
   - Monitor the progress as questions are imported
   - Review the final results showing success/error counts per topic

## Tips for Successful Imports

### General Tips
- Make sure your CSV file uses commas (,) as separators
- If your text contains commas, enclose the text in double quotes (")
- Keep question texts concise and clear (minimum 10 characters)
- Provide detailed explanations for correct answers (minimum 10 characters)
- Use consistent formatting for options
- Verify the correct answer is one of the options (A, B, C, or D)
- Use UTF-8 encoding for your CSV files to support special characters

### Single-Topic Import Tips
- Ensure all questions are relevant to the selected topic
- For large imports, consider splitting into multiple smaller files (recommended: 50-100 questions per file)
- Use the template download feature to get the correct format

### Multi-Topic Import Tips
- Use consistent topic naming conventions (no special characters except spaces, hyphens, underscores)
- Topic names should be 1-100 characters long
- Start with a small test file to verify your format works correctly
- Enable auto-create topics if you're setting up new quiz categories
- Group related questions together in the CSV for better organization
- Use the preview feature to verify questions are assigned to correct topics
- Consider using the name-based format for better readability and maintenance

## Troubleshooting

### Common Issues and Solutions

#### CSV Format Issues

**"Invalid CSV format" or "CSV parsing error"**
- Ensure your file is saved as CSV format (not Excel .xlsx)
- Check that you're using commas as separators
- Verify UTF-8 encoding is used
- Remove any empty rows at the end of the file
- Ensure column headers match the expected format exactly

**"Missing required columns"**
- Verify all required columns are present in your CSV
- Check column header spelling (case-sensitive)
- Ensure no extra spaces in column headers
- Download and compare with the provided templates

#### Single-Topic Import Issues

**"No topic selected"**
- Choose a topic from the dropdown before importing
- Ensure you have permission to add questions to the selected topic

**"Validation failed for questions"**
- Check that question_text is at least 10 characters long
- Verify all four options (A, B, C, D) are provided and non-empty
- Ensure correct_answer is exactly A, B, C, or D (case-sensitive)
- Check that explanation is at least 10 characters long
- If using difficulty, ensure it's exactly "easy", "medium", or "hard"

#### Multi-Topic Import Issues

**"Topic not found" errors**
- Enable "Auto-create Topics" to create missing topics automatically
- Create topics manually in the admin interface before importing
- Check topic name spelling and ensure no extra spaces
- Verify topic IDs are correct UUIDs if using ID-based format

**"Invalid topic name" errors**
- Topic names must be 1-100 characters long
- Only use letters, numbers, spaces, hyphens, and underscores
- Avoid special characters like @, #, $, %, etc.
- Ensure topic names are not empty

**"Duplicate question" warnings**
- Review flagged questions to determine if they are actual duplicates
- Modify question text slightly if duplicates are unintentional
- Consider if duplicates across topics are acceptable for your use case

#### Import Process Issues

**"Preview failed" or "Import failed"**
- Check your internet connection
- Ensure the file size is reasonable (under 10MB recommended)
- Try importing a smaller subset of questions first
- Check browser console for detailed error messages

**"Partial import success"**
- Review the detailed results to see which questions failed
- Fix the problematic questions in your CSV
- Re-import only the failed questions using the error information provided

**"Permission denied"**
- Ensure you have admin privileges
- Check that you're logged in with the correct account
- Contact your system administrator if permissions are unclear

### Performance Tips

**For large imports (500+ questions):**
- Split into smaller files (50-100 questions each)
- Import during off-peak hours
- Use the preview feature with smaller batches first
- Monitor system performance during import

**For slow imports:**
- Check your internet connection speed
- Reduce batch size in the import configuration
- Avoid importing during high system usage periods
- Consider importing topics sequentially rather than all at once

### Getting Additional Help

If you continue to experience issues:

1. **Check the error messages**: The system provides specific error details with row numbers and descriptions
2. **Use the preview feature**: Always preview before importing to catch issues early
3. **Download templates**: Use the provided templates to ensure correct formatting
4. **Test with small files**: Start with 5-10 questions to verify your format works
5. **Check the browser console**: Look for detailed technical error messages
6. **Contact support**: Provide specific error messages, CSV samples, and steps to reproduce the issue

For technical support, include:
- The exact error message
- A sample of your CSV file (with sensitive data removed)
- The import mode and settings you used
- Your browser and operating system information
