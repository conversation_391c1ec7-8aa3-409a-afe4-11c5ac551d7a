/**
 * Quiz Length Selector Component Tests
 */

import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { QuizLengthSelector, QUIZ_LENGTH_OPTIONS } from '../QuizLengthSelector';
import { beforeEach } from 'node:test';

describe('QuizLengthSelector', () => {
  const defaultProps = {
    availableQuestions: 30,
    selectedLength: 15,
    onLengthChange: vi.fn(),
    onStartQuiz: vi.fn(),
    loading: false,
    disabled: false
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render quiz length options', () => {
    render(<QuizLengthSelector {...defaultProps} />);

    expect(screen.getByText('Choose Quiz Length')).toBeInTheDocument();
    expect(screen.getByText(/Available questions:\s*30/)).toBeInTheDocument();

    // Check that all quiz length options are rendered
    QUIZ_LENGTH_OPTIONS.forEach(option => {
      expect(screen.getByText(option.label)).toBeInTheDocument();
    });
  });

  it('should highlight selected option', () => {
    render(<QuizLengthSelector {...defaultProps} selectedLength={20} />);

    const selectedCard = screen.getByText('20 Questions').closest('.cursor-pointer');
    expect(selectedCard).toHaveClass('border-cyber-primary');
  });

  it('should call onLengthChange when option is selected', () => {
    const onLengthChange = vi.fn();
    render(<QuizLengthSelector {...defaultProps} onLengthChange={onLengthChange} />);

    const option20 = screen.getByText('20 Questions').closest('.cursor-pointer');
    fireEvent.click(option20!);

    expect(onLengthChange).toHaveBeenCalledWith(20);
  });

  it('should disable unavailable options', () => {
    render(<QuizLengthSelector {...defaultProps} availableQuestions={12} />);

    // Options that exceed available questions should be disabled/limited
    const disabledOptions = document.querySelectorAll('.cursor-not-allowed');
    expect(disabledOptions.length).toBeGreaterThan(0);
    
    // Check that disabled options have the correct classes
    disabledOptions.forEach(option => {
      expect(option).toHaveClass('opacity-50');
      expect(option).toHaveClass('cursor-not-allowed');
    });
  });

  it('should adjust quiz length display for limited questions', () => {
    render(<QuizLengthSelector {...defaultProps} availableQuestions={8} />);

    // All options should show adjusted length (there will be multiple instances)
    const adjustedOptions = screen.getAllByText('8 Questions');
    expect(adjustedOptions.length).toBeGreaterThan(0);
  });

  it('should show validation message for no questions', () => {
    render(<QuizLengthSelector {...defaultProps} availableQuestions={0} />);

    expect(screen.getByText('No questions available for this topic.')).toBeInTheDocument();
  });

  it('should show warning for insufficient questions', () => {
    render(<QuizLengthSelector {...defaultProps} availableQuestions={5} selectedLength={10} />);

    expect(screen.getByText(/Only 5 questions available/)).toBeInTheDocument();
  });

  it('should show info message for limited question pool', () => {
    render(<QuizLengthSelector {...defaultProps} availableQuestions={15} />);

    expect(screen.getByText(/limited questions \(15\)/)).toBeInTheDocument();
  });

  it('should call onStartQuiz when start button is clicked', () => {
    const onStartQuiz = vi.fn();
    render(<QuizLengthSelector {...defaultProps} onStartQuiz={onStartQuiz} />);

    const startButton = screen.getByText('Start Quiz');
    fireEvent.click(startButton);

    expect(onStartQuiz).toHaveBeenCalled();
  });

  it('should disable start button when no questions available', () => {
    render(<QuizLengthSelector {...defaultProps} availableQuestions={0} />);

    const startButton = screen.getByText('Start Quiz');
    expect(startButton).toBeDisabled();
  });

  it('should disable start button when loading', () => {
    render(<QuizLengthSelector {...defaultProps} loading={true} />);

    const startButton = screen.getByText('Starting...');
    expect(startButton).toBeDisabled();
  });

  it('should disable start button when disabled prop is true', () => {
    render(<QuizLengthSelector {...defaultProps} disabled={true} />);

    const startButton = screen.getByText('Start Quiz');
    expect(startButton).toBeDisabled();
  });

  it('should show correct estimated time', () => {
    render(<QuizLengthSelector {...defaultProps} />);

    // Check that estimated times are shown
    QUIZ_LENGTH_OPTIONS.forEach(option => {
      expect(screen.getByText(option.estimatedTime)).toBeInTheDocument();
    });
  });

  it('should show adjusted time for limited questions', () => {
    render(<QuizLengthSelector {...defaultProps} availableQuestions={8} />);

    // Should show adjusted time (~10 min for 8 questions)
    expect(screen.getAllByText('~10 min')[0]).toBeInTheDocument();
  });

  it('should display quiz summary correctly', () => {
    render(<QuizLengthSelector {...defaultProps} selectedLength={20} />);

    expect(screen.getByText('Quiz Summary')).toBeInTheDocument();
    expect(screen.getByText('20 questions • ~24 minutes')).toBeInTheDocument();
  });

  it('should handle edge case with very limited questions', () => {
    render(<QuizLengthSelector {...defaultProps} availableQuestions={1} />);

    expect(screen.getAllByText('1 Questions')[0]).toBeInTheDocument();
    expect(screen.getAllByText('~2 min')[0]).toBeInTheDocument();
  });

  it('should not allow selection of unavailable options', () => {
    const onLengthChange = vi.fn();
    render(<QuizLengthSelector {...defaultProps} availableQuestions={5} onLengthChange={onLengthChange} />);

    // Try to click on a disabled option (should have cursor-not-allowed class)
    const disabledOptions = document.querySelectorAll('.cursor-not-allowed');
    expect(disabledOptions.length).toBeGreaterThan(0);

    // Click on a disabled option
    fireEvent.click(disabledOptions[0]);

    // Should not call onLengthChange for disabled options
    expect(onLengthChange).not.toHaveBeenCalledWith(25);
  });
});