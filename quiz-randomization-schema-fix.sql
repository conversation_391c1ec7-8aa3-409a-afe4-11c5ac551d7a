-- Quiz Randomization Schema Fix
-- Run this SQL directly in Supabase SQL editor to restore quiz randomization functionality

-- Create quiz_sessions table for tracking randomized quiz instances
CREATE TABLE IF NOT EXISTS quiz_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL,
  topic_id UUID,
  questions_data JSONB NOT NULL,
  quiz_length INTEGER NOT NULL DEFAULT 10,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() + INTERVAL '2 hours',
  completed_at TIMESTAMP WITH TIME ZONE,
  score INTEGER,
  total_questions INTEGER,
  time_taken INTEGER
);

-- Create question_analytics table for performance tracking
CREATE TABLE IF NOT EXISTS question_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  question_id UUID NOT NULL,
  user_id UUID NOT NULL,
  quiz_session_id UUID,
  answered_correctly BOOLEAN NOT NULL,
  time_to_answer INTEGER,
  selected_option INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE quiz_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE question_analytics ENABLE ROW LEVEL SECURITY;

-- Create permissive policies for quiz_sessions
DROP POLICY IF EXISTS "Users can manage quiz sessions" ON quiz_sessions;
CREATE POLICY "Users can manage quiz sessions"
  ON quiz_sessions FOR ALL
  USING (true);

-- Create permissive policies for question_analytics  
DROP POLICY IF EXISTS "Users can manage question analytics" ON question_analytics;
CREATE POLICY "Users can manage question analytics"
  ON question_analytics FOR ALL
  USING (true);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_user_id ON quiz_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_topic_id ON quiz_sessions(topic_id);
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_created_at ON quiz_sessions(created_at);
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_expires_at ON quiz_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_completed_at ON quiz_sessions(completed_at);

CREATE INDEX IF NOT EXISTS idx_question_analytics_question_id ON question_analytics(question_id);
CREATE INDEX IF NOT EXISTS idx_question_analytics_user_id ON question_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_question_analytics_quiz_session_id ON question_analytics(quiz_session_id);
CREATE INDEX IF NOT EXISTS idx_question_analytics_created_at ON question_analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_question_analytics_answered_correctly ON question_analytics(answered_correctly);

-- Create cleanup function for expired sessions
CREATE OR REPLACE FUNCTION cleanup_expired_quiz_sessions()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM quiz_sessions 
  WHERE expires_at < NOW() 
    AND completed_at IS NULL;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT ALL ON quiz_sessions TO anon, authenticated;
GRANT ALL ON question_analytics TO anon, authenticated;
GRANT EXECUTE ON FUNCTION cleanup_expired_quiz_sessions() TO anon, authenticated;

-- Add helpful comments
COMMENT ON TABLE quiz_sessions IS 'Tracks randomized quiz instances with question order and option mappings';
COMMENT ON TABLE question_analytics IS 'Stores individual question performance data for analytics';
COMMENT ON FUNCTION cleanup_expired_quiz_sessions() IS 'Removes expired quiz sessions to keep database clean';

-- Verify tables were created successfully
SELECT 
  'quiz_sessions' as table_name,
  COUNT(*) as row_count
FROM quiz_sessions
UNION ALL
SELECT 
  'question_analytics' as table_name,
  COUNT(*) as row_count  
FROM question_analytics;