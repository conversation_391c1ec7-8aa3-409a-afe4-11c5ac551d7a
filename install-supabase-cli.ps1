# Supabase CLI Installation Script for Windows PowerShell
# Run this script in PowerShell as Administrator for best results

param(
    [switch]$Force,
    [switch]$Check,
    [switch]$Help
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Red
}

function Test-CommandExists {
    param([string]$Command)
    $null = Get-Command $Command -ErrorAction SilentlyContinue
    return $?
}

function Install-WithScoop {
    Write-Status "Installing Supabase CLI using Scoop..."
    
    if (-not (Test-CommandExists "scoop")) {
        Write-Status "Installing Scoop first..."
        try {
            Set-ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
            Invoke-RestMethod get.scoop.sh | Invoke-Expression
        }
        catch {
            Write-Error "Failed to install Scoop: $_"
            return $false
        }
    }
    
    try {
        scoop bucket add supabase https://github.com/supabase/scoop-bucket.git
        scoop install supabase
        return $true
    }
    catch {
        Write-Error "Failed to install Supabase CLI with Scoop: $_"
        return $false
    }
}

function Install-WithChocolatey {
    Write-Status "Installing Supabase CLI using Chocolatey..."
    
    if (-not (Test-CommandExists "choco")) {
        Write-Status "Installing Chocolatey first..."
        try {
            Set-ExecutionPolicy Bypass -Scope Process -Force
            [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
            Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        }
        catch {
            Write-Error "Failed to install Chocolatey: $_"
            return $false
        }
    }
    
    try {
        choco install supabase -y
        return $true
    }
    catch {
        Write-Error "Failed to install Supabase CLI with Chocolatey: $_"
        return $false
    }
}

function Install-WithNpm {
    Write-Status "Installing Supabase CLI using npm..."
    
    if (-not (Test-CommandExists "npm")) {
        Write-Error "npm not found. Please install Node.js first from https://nodejs.org/"
        return $false
    }
    
    try {
        npm install -g supabase
        return $true
    }
    catch {
        Write-Error "Failed to install Supabase CLI with npm: $_"
        return $false
    }
}

function Install-ManualDownload {
    Write-Status "Installing Supabase CLI via manual download..."
    
    $downloadUrl = "https://github.com/supabase/cli/releases/latest/download/supabase_windows_amd64.tar.gz"
    $tempDir = "$env:TEMP\supabase-install"
    $installDir = "$env:LOCALAPPDATA\supabase"
    
    try {
        # Create directories
        New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
        New-Item -ItemType Directory -Path $installDir -Force | Out-Null
        
        # Download
        Write-Status "Downloading Supabase CLI..."
        $archivePath = "$tempDir\supabase.tar.gz"
        Invoke-WebRequest -Uri $downloadUrl -OutFile $archivePath
        
        # Extract (requires tar command available in Windows 10+)
        Write-Status "Extracting archive..."
        tar -xzf $archivePath -C $tempDir
        
        # Move to install directory
        Move-Item "$tempDir\supabase.exe" "$installDir\supabase.exe" -Force
        
        # Add to PATH
        $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
        if ($currentPath -notlike "*$installDir*") {
            Write-Status "Adding to PATH..."
            [Environment]::SetEnvironmentVariable("PATH", "$currentPath;$installDir", "User")
            $env:PATH += ";$installDir"
        }
        
        # Cleanup
        Remove-Item $tempDir -Recurse -Force
        
        return $true
    }
    catch {
        Write-Error "Failed to install manually: $_"
        return $false
    }
}

function Test-DockerInstallation {
    Write-Status "Checking Docker installation..."
    
    if (Test-CommandExists "docker") {
        Write-Success "Docker is installed"
        docker --version
        
        # Check if Docker is running
        try {
            docker info | Out-Null
            Write-Success "Docker is running"
        }
        catch {
            Write-Warning "Docker is installed but not running"
            Write-Status "Please start Docker Desktop and try again"
        }
    }
    else {
        Write-Warning "Docker is not installed"
        Write-Status "Supabase CLI requires Docker to run local instances"
        Write-Status "Install Docker Desktop from: https://www.docker.com/products/docker-desktop"
    }
}

function Test-Installation {
    Write-Status "Verifying Supabase CLI installation..."
    
    if (Test-CommandExists "supabase") {
        Write-Success "Supabase CLI installed successfully!"
        supabase --version
        
        Write-Status "Available commands:"
        supabase --help | Select-Object -First 20
        
        return $true
    }
    else {
        Write-Error "Supabase CLI installation failed"
        Write-Status "Please check the installation manually or try a different method"
        return $false
    }
}

function Show-Help {
    Write-Host @"
Supabase CLI Installation Script for Windows

Usage: .\install-supabase-cli.ps1 [OPTIONS]

Options:
  -Help          Show this help message
  -Force         Force reinstallation
  -Check         Check current installation

Examples:
  .\install-supabase-cli.ps1
  .\install-supabase-cli.ps1 -Force
  .\install-supabase-cli.ps1 -Check

Installation Methods (tried in order):
1. Scoop (recommended)
2. Chocolatey
3. npm (if Node.js is installed)
4. Manual download

Requirements:
- PowerShell 5.0 or later
- Internet connection
- Docker Desktop (for running local Supabase instances)

"@
}

function Main {
    Write-Status "🚀 Supabase CLI Installation Script for Windows"
    Write-Status "================================================"
    
    # Check if already installed
    if (Test-CommandExists "supabase") {
        Write-Warning "Supabase CLI is already installed"
        supabase --version
        if (-not $Force) {
            Write-Status "Use -Force to reinstall"
            return
        }
    }
    
    # Try different installation methods
    $methods = @(
        { Install-WithScoop },
        { Install-WithChocolatey },
        { Install-WithNpm },
        { Install-ManualDownload }
    )
    
    $installed = $false
    foreach ($method in $methods) {
        if (& $method) {
            $installed = $true
            break
        }
        Write-Status "Trying next installation method..."
    }
    
    if ($installed) {
        # Refresh PATH for current session
        $env:PATH = [System.Environment]::GetEnvironmentVariable("PATH", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("PATH", "User")
        
        if (Test-Installation) {
            Write-Success "✅ Installation completed successfully!"
            
            # Check Docker
            Test-DockerInstallation
            
            Write-Status "🎉 Next steps:"
            Write-Status "1. Restart your terminal to refresh PATH"
            Write-Status "2. Navigate to your project: cd C:\path\to\your\project"
            Write-Status "3. Initialize Supabase: supabase init"
            Write-Status "4. Start local instance: supabase start"
            Write-Status "5. Open Supabase Studio: http://localhost:54323"
        }
        else {
            Write-Error "❌ Installation verification failed"
        }
    }
    else {
        Write-Error "❌ All installation methods failed"
        Write-Status "Please try manual installation or check the documentation"
        Write-Status "Manual installation steps:"
        Write-Status "1. Download from: https://github.com/supabase/cli/releases"
        Write-Status "2. Extract to a folder (e.g., C:\supabase)"
        Write-Status "3. Add the folder to your PATH environment variable"
    }
}

# Handle script parameters
if ($Help) {
    Show-Help
    exit 0
}

if ($Check) {
    Write-Status "Checking current installation..."
    if (Test-CommandExists "supabase") {
        Write-Success "Supabase CLI is installed"
        supabase --version
    }
    else {
        Write-Error "Supabase CLI is not installed"
    }
    Test-DockerInstallation
    exit 0
}

# Run main installation
Main
