import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { 
  parseQuestionCSV, 
  parseQuestionCSVEnhanced, 
  generateCSVTemplate, 
  generateMultiTopicCSVTemplate,
  type ImportConfig,
  type MultiTopicImportResult,
  type ImportResult,
  type ValidationConfig,
  type MultiTopicTemplateFormat
} from '../csv-import';
import { DEFAULT_VALIDATION_CONFIG } from '../multi-topic-validation';

// Mock the topic service
vi.mock('@/services/topic-service', () => ({
  topicService: {
    getTopicById: vi.fn(),
    resolveTopicReferences: vi.fn(),
  },
}));

// Mock uuid
vi.mock('uuid', () => ({
  v4: vi.fn(() => 'mock-uuid-123'),
}));

// Mock Papa Parse
vi.mock('papaparse', () => ({
  default: {
    parse: vi.fn(),
    unparse: vi.fn(),
  },
}));

describe('CSV Import Enhanced', () => {
  let mockTopicService: unknown;
  let mockPapa: unknown;

  beforeEach(async () => {
    // Import the mocked modules
    const { topicService } = await import('@/services/topic-service');
    const Papa = (await import('papaparse')).default;
    
    mockTopicService = topicService;
    mockPapa = Papa;
    
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('parseQuestionCSVEnhanced', () => {
    const createMockFile = (name: string = 'test.csv'): File => {
      return new File([''], name, { type: 'text/csv' });
    };

    const mockCSVData = [
      {
        question_text: 'What is a firewall?',
        option_a: 'Physical barrier',
        option_b: 'Network traffic controller',
        option_c: 'Data encryptor',
        option_d: 'Malware detector',
        correct_answer: 'B',
        explanation: 'Controls network traffic flow',
        difficulty: 'medium',
      },
      {
        question_text: 'What is VPN?',
        option_a: 'Virtual Private Network',
        option_b: 'Very Personal Network',
        option_c: 'Verified Public Network',
        option_d: 'Variable Protocol Network',
        correct_answer: 'A',
        explanation: 'Creates secure tunnel',
        difficulty: 'easy',
      },
    ];

    const mockMultiTopicCSVData = [
      {
        topic_name: 'Security Fundamentals',
        question_text: 'What is a firewall?',
        option_a: 'Physical barrier',
        option_b: 'Network traffic controller',
        option_c: 'Data encryptor',
        option_d: 'Malware detector',
        correct_answer: 'B',
        explanation: 'Controls network traffic flow',
        difficulty: 'medium',
      },
      {
        topic_name: 'Network Security',
        question_text: 'What is VPN?',
        option_a: 'Virtual Private Network',
        option_b: 'Very Personal Network',
        option_c: 'Verified Public Network',
        option_d: 'Variable Protocol Network',
        correct_answer: 'A',
        explanation: 'Creates secure tunnel',
        difficulty: 'easy',
      },
    ];

    it('should detect single-topic mode when no topic columns are present', async () => {
      const config: ImportConfig = {
        mode: 'single-topic',
        autoCreateTopics: false,
        selectedTopicId: 'topic-123',
      };

      mockTopicService.getTopicById.mockResolvedValue({
        id: 'topic-123',
        title: 'Test Topic',
      });

      mockPapa.parse.mockImplementation((file, options) => {
        options.complete({ data: mockCSVData });
      });

      const file = createMockFile();
      const result = await parseQuestionCSVEnhanced(file, config);

      expect(result.success).toBe(true);
      expect(result.totalRows).toBe(2);
      expect(result.topicResults.size).toBe(1);
      expect(result.topicResults.has('topic-123')).toBe(true);
      
      const topicResult = result.topicResults.get('topic-123')!;
      expect(topicResult.validQuestions).toHaveLength(2);
      expect(topicResult.errors).toHaveLength(0);
      expect(topicResult.isNewTopic).toBe(false);
    });

    it('should detect multi-topic mode when topic columns are present', async () => {
      const config: ImportConfig = {
        mode: 'multi-topic',
        autoCreateTopics: false,
      };

      mockTopicService.resolveTopicReferences.mockResolvedValue({
        resolved: new Map([
          ['Security Fundamentals', 'topic-123'],
          ['Network Security', 'topic-456'],
        ]),
        missing: [],
        created: [],
        errors: [],
      });

      mockPapa.parse.mockImplementation((file, options) => {
        options.complete({ data: mockMultiTopicCSVData });
      });

      const file = createMockFile();
      const result = await parseQuestionCSVEnhanced(file, config);

      expect(result.success).toBe(true);
      expect(result.totalRows).toBe(2);
      expect(result.topicResults.size).toBe(2);
      expect(result.topicResults.has('topic-123')).toBe(true);
      expect(result.topicResults.has('topic-456')).toBe(true);
      
      const securityResult = result.topicResults.get('topic-123')!;
      expect(securityResult.validQuestions).toHaveLength(1);
      expect(securityResult.topicName).toBe('Security Fundamentals');
      
      const networkResult = result.topicResults.get('topic-456')!;
      expect(networkResult.validQuestions).toHaveLength(1);
      expect(networkResult.topicName).toBe('Network Security');
    });

    it('should auto-create topics when enabled', async () => {
      const config: ImportConfig = {
        mode: 'multi-topic',
        autoCreateTopics: true,
      };

      mockTopicService.resolveTopicReferences.mockResolvedValue({
        resolved: new Map([
          ['New Topic', 'topic-new-123'],
        ]),
        missing: [],
        created: ['New Topic'],
        errors: [],
      });

      const csvDataWithNewTopic = [
        {
          topic_name: 'New Topic',
          question_text: 'What is new?',
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Test explanation',
          difficulty: 'easy',
        },
      ];

      mockPapa.parse.mockImplementation((file, options) => {
        options.complete({ data: csvDataWithNewTopic });
      });

      const file = createMockFile();
      const result = await parseQuestionCSVEnhanced(file, config);

      expect(result.success).toBe(true);
      expect(result.newTopicsCreated).toEqual(['New Topic']);
      expect(result.topicResults.has('topic-new-123')).toBe(true);
      
      const topicResult = result.topicResults.get('topic-new-123')!;
      expect(topicResult.isNewTopic).toBe(true);
    });

    it('should handle missing topics when auto-create is disabled', async () => {
      const config: ImportConfig = {
        mode: 'multi-topic',
        autoCreateTopics: false,
      };

      mockTopicService.resolveTopicReferences.mockResolvedValue({
        resolved: new Map(),
        missing: ['Missing Topic'],
        created: [],
        errors: [],
      });

      const csvDataWithMissingTopic = [
        {
          topic_name: 'Missing Topic',
          question_text: 'What is missing?',
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Test explanation',
          difficulty: 'easy',
        },
      ];

      mockPapa.parse.mockImplementation((file, options) => {
        options.complete({ data: csvDataWithMissingTopic });
      });

      const file = createMockFile();
      const result = await parseQuestionCSVEnhanced(file, config);

      expect(result.success).toBe(false);
      expect(result.topicResults.size).toBe(0);
      expect(result.globalErrors.length).toBeGreaterThan(0);
      
      // Should contain the main resolution error
      const resolutionError = result.globalErrors.find(error => 
        error.message.includes('Cannot resolve topic reference: "Missing Topic"')
      );
      expect(resolutionError).toBeDefined();
    });

    it('should handle validation errors for individual questions', async () => {
      const config: ImportConfig = {
        mode: 'single-topic',
        autoCreateTopics: false,
        selectedTopicId: 'topic-123',
      };

      mockTopicService.getTopicById.mockResolvedValue({
        id: 'topic-123',
        title: 'Test Topic',
      });

      const invalidCSVData = [
        {
          question_text: 'Valid question?',
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Valid explanation',
          difficulty: 'easy',
        },
        {
          question_text: '', // Missing required field
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Invalid explanation',
          difficulty: 'easy',
        },
        {
          question_text: 'Another question?',
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'X', // Invalid correct answer
          explanation: 'Invalid answer explanation',
          difficulty: 'easy',
        },
      ];

      mockPapa.parse.mockImplementation((file, options) => {
        options.complete({ data: invalidCSVData });
      });

      const file = createMockFile();
      const result = await parseQuestionCSVEnhanced(file, config);

      expect(result.success).toBe(true); // Still successful because we have one valid question
      expect(result.totalRows).toBe(3);
      
      const topicResult = result.topicResults.get('topic-123')!;
      expect(topicResult.validQuestions).toHaveLength(1);
      expect(topicResult.errors).toHaveLength(2);
      expect(topicResult.errors[0].message).toContain('Missing required field "question_text"');
      expect(topicResult.errors[1].message).toContain('Invalid correct_answer "X"');
    });

    it('should handle empty CSV files', async () => {
      const config: ImportConfig = {
        mode: 'single-topic',
        autoCreateTopics: false,
        selectedTopicId: 'topic-123',
      };

      mockPapa.parse.mockImplementation((file, options) => {
        options.complete({ data: [] });
      });

      const file = createMockFile();
      const result = await parseQuestionCSVEnhanced(file, config);

      expect(result.success).toBe(false);
      expect(result.totalRows).toBe(0);
      expect(result.globalErrors).toHaveLength(1);
      expect(result.globalErrors[0].message).toContain('CSV file is empty');
    });

    it('should handle CSV parsing errors', async () => {
      const config: ImportConfig = {
        mode: 'single-topic',
        autoCreateTopics: false,
        selectedTopicId: 'topic-123',
      };

      mockPapa.parse.mockImplementation((file, options) => {
        options.error({ message: 'Invalid CSV format' });
      });

      const file = createMockFile();
      const result = await parseQuestionCSVEnhanced(file, config);

      expect(result.success).toBe(false);
      expect(result.globalErrors).toHaveLength(1);
      expect(result.globalErrors[0].message).toContain('CSV parsing error: Invalid CSV format');
    });

    it('should prefer topic_name over topic_id when both are present', async () => {
      const config: ImportConfig = {
        mode: 'multi-topic',
        autoCreateTopics: false,
      };

      mockTopicService.resolveTopicReferences.mockResolvedValue({
        resolved: new Map([
          ['Security Fundamentals', 'topic-123'],
        ]),
        missing: [],
        created: [],
        errors: [],
      });

      const csvDataWithBothColumns = [
        {
          topic_name: 'Security Fundamentals',
          topic_id: 'different-topic-456',
          question_text: 'What is preferred?',
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Test explanation',
          difficulty: 'easy',
        },
      ];

      mockPapa.parse.mockImplementation((file, options) => {
        options.complete({ data: csvDataWithBothColumns });
      });

      const file = createMockFile();
      const result = await parseQuestionCSVEnhanced(file, config);

      expect(result.success).toBe(true);
      expect(mockTopicService.resolveTopicReferences).toHaveBeenCalledWith(
        ['Security Fundamentals'],
        false
      );
    });

    it('should handle topic resolution errors', async () => {
      const config: ImportConfig = {
        mode: 'multi-topic',
        autoCreateTopics: true,
      };

      mockTopicService.resolveTopicReferences.mockResolvedValue({
        resolved: new Map(),
        missing: ['Failed Topic'],
        created: [],
        errors: ['Failed to create topic "Failed Topic": Database error'],
      });

      const csvDataWithFailedTopic = [
        {
          topic_name: 'Failed Topic',
          question_text: 'What failed?',
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Test explanation',
          difficulty: 'easy',
        },
      ];

      mockPapa.parse.mockImplementation((file, options) => {
        options.complete({ data: csvDataWithFailedTopic });
      });

      const file = createMockFile();
      const result = await parseQuestionCSVEnhanced(file, config);

      expect(result.success).toBe(false);
      expect(result.globalErrors.length).toBeGreaterThan(0);
      
      // Should contain the database error
      const dbError = result.globalErrors.find(error => 
        error.message.includes('Failed to create topic "Failed Topic": Database error')
      );
      expect(dbError).toBeDefined();
      
      // Should contain the resolution error
      const resolutionError = result.globalErrors.find(error => 
        error.message.includes('Cannot resolve topic reference: "Failed Topic"')
      );
      expect(resolutionError).toBeDefined();
    });

    it('should perform cross-topic validation and detect duplicates', async () => {
      const config: ImportConfig = {
        mode: 'multi-topic',
        autoCreateTopics: false,
      };

      mockTopicService.resolveTopicReferences.mockResolvedValue({
        resolved: new Map([
          ['Topic A', 'topic-123'],
          ['Topic B', 'topic-456'],
        ]),
        missing: [],
        created: [],
        errors: [],
      });

      const csvDataWithDuplicates = [
        {
          topic_name: 'Topic A',
          question_text: 'What is a firewall?',
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Test explanation for firewall',
          difficulty: 'easy',
        },
        {
          topic_name: 'Topic B',
          question_text: 'What is a firewall?', // Duplicate question
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Another explanation for firewall',
          difficulty: 'medium',
        },
      ];

      mockPapa.parse.mockImplementation((file, options) => {
        options.complete({ data: csvDataWithDuplicates });
      });

      const file = createMockFile();
      const result = await parseQuestionCSVEnhanced(file, config);

      expect(result.success).toBe(true); // May succeed if duplicates are allowed in current config
      expect(result.globalErrors.length).toBeGreaterThan(0);
      
      // Should contain duplicate detection error
      const duplicateError = result.globalErrors.find(error => 
        error.message.includes('Duplicate question found')
      );
      expect(duplicateError).toBeDefined();
    });

    it('should validate topic names and provide actionable errors', async () => {
      const config: ImportConfig = {
        mode: 'multi-topic',
        autoCreateTopics: true,
      };

      mockTopicService.resolveTopicReferences.mockResolvedValue({
        resolved: new Map(),
        missing: ['Invalid<Topic>'],
        created: [],
        errors: [],
      });

      const csvDataWithInvalidTopic = [
        {
          topic_name: 'Invalid<Topic>', // Contains invalid characters
          question_text: 'What is invalid about this topic name?',
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Test explanation for invalid topic',
          difficulty: 'easy',
        },
      ];

      mockPapa.parse.mockImplementation((file, options) => {
        options.complete({ data: csvDataWithInvalidTopic });
      });

      const file = createMockFile();
      const result = await parseQuestionCSVEnhanced(file, config);

      expect(result.success).toBe(false);
      expect(result.globalErrors.length).toBeGreaterThan(0);
      
      // Should contain topic validation error
      const topicError = result.globalErrors.find(error => 
        error.message.includes('invalid characters')
      );
      expect(topicError).toBeDefined();
    });

    it('should validate question content and provide detailed errors', async () => {
      const config: ImportConfig = {
        mode: 'single-topic',
        autoCreateTopics: false,
        selectedTopicId: 'topic-123',
      };

      mockTopicService.getTopicById.mockResolvedValue({
        id: 'topic-123',
        title: 'Test Topic',
      });

      const csvDataWithInvalidQuestions = [
        {
          question_text: 'Short?', // Too short
          option_a: 'Option A',
          option_b: 'Option A', // Duplicate option
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Short', // Too short explanation
          difficulty: 'impossible', // Invalid difficulty
        },
      ];

      mockPapa.parse.mockImplementation((file, options) => {
        options.complete({ data: csvDataWithInvalidQuestions });
      });

      const file = createMockFile();
      const result = await parseQuestionCSVEnhanced(file, config);

      expect(result.success).toBe(false);
      
      // Should have validation errors in topic results since this is single-topic mode
      const topicResult = result.topicResults.get('topic-123');
      expect(topicResult).toBeDefined();
      expect(topicResult!.errors.length).toBeGreaterThan(0);
      
      // Should contain various validation errors in topic results or global errors
      const allErrors = [
        ...result.globalErrors.map(e => e.message),
        ...(topicResult?.errors.map(e => e.message) || [])
      ];
      
      const hasQuestionLengthError = allErrors.some(message => 
        message.includes('too short')
      );
      const hasDuplicateOptionError = allErrors.some(message => 
        message.includes('duplicated')
      );
      const hasInvalidDifficultyError = allErrors.some(message => 
        message.includes('Invalid difficulty')
      );
      
      expect(hasQuestionLengthError || hasDuplicateOptionError || hasInvalidDifficultyError).toBe(true);
    });

    it('should respect custom validation configuration', async () => {
      const config: ImportConfig = {
        mode: 'multi-topic',
        autoCreateTopics: false,
      };

      const customValidationConfig: ValidationConfig = {
        ...DEFAULT_VALIDATION_CONFIG,
        enableCrossTopicDuplicateDetection: false,
        enableQuestionContentValidation: false,
      };

      mockTopicService.resolveTopicReferences.mockResolvedValue({
        resolved: new Map([
          ['Topic A', 'topic-123'],
          ['Topic B', 'topic-456'],
        ]),
        missing: [],
        created: [],
        errors: [],
      });

      const csvDataWithIssues = [
        {
          topic_name: 'Topic A',
          question_text: 'What is a firewall?',
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Test explanation',
          difficulty: 'easy',
        },
        {
          topic_name: 'Topic B',
          question_text: 'What is a firewall?', // Would be duplicate if detection enabled
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Test explanation',
          difficulty: 'easy',
        },
      ];

      mockPapa.parse.mockImplementation((file, options) => {
        options.complete({ data: csvDataWithIssues });
      });

      const file = createMockFile();
      const result = await parseQuestionCSVEnhanced(file, config, customValidationConfig);

      expect(result.success).toBe(true); // Should succeed with validation disabled
      expect(result.topicResults.size).toBe(2);
      
      // Should not contain duplicate detection errors
      const duplicateError = result.globalErrors.find(error => 
        error.message.includes('Duplicate question found')
      );
      expect(duplicateError).toBeUndefined();
    });
  });

  describe('parseQuestionCSV (legacy compatibility)', () => {
    it('should maintain backward compatibility with single-topic imports', async () => {
      const mockCSVData = [
        {
          question_text: 'What is a firewall?',
          option_a: 'Physical barrier',
          option_b: 'Network traffic controller',
          option_c: 'Data encryptor',
          option_d: 'Malware detector',
          correct_answer: 'B',
          explanation: 'Controls network traffic flow',
          difficulty: 'medium',
        },
      ];

      mockTopicService.getTopicById.mockResolvedValue({
        id: 'topic-123',
        title: 'Test Topic',
      });

      mockPapa.parse.mockImplementation((file, options) => {
        options.complete({ data: mockCSVData });
      });

      const file = new File([''], 'test.csv', { type: 'text/csv' });
      const result = await parseQuestionCSV(file, 'topic-123');

      expect(result.success).toBe(true);
      expect(result.totalRows).toBe(1);
      expect(result.validQuestions).toHaveLength(1);
      expect(result.validQuestions[0].topic_id).toBe('topic-123');
      expect(result.errors).toHaveLength(0);
    });

    it('should aggregate errors from enhanced parser', async () => {
      const invalidCSVData = [
        {
          question_text: '', // Invalid
          option_a: 'Option A',
          option_b: 'Option B',
          option_c: 'Option C',
          option_d: 'Option D',
          correct_answer: 'A',
          explanation: 'Test explanation',
          difficulty: 'easy',
        },
      ];

      mockTopicService.getTopicById.mockResolvedValue({
        id: 'topic-123',
        title: 'Test Topic',
      });

      mockPapa.parse.mockImplementation((file, options) => {
        options.complete({ data: invalidCSVData });
      });

      const file = new File([''], 'test.csv', { type: 'text/csv' });
      const result = await parseQuestionCSV(file, 'topic-123');

      expect(result.success).toBe(false);
      expect(result.validQuestions).toHaveLength(0);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].message).toContain('Missing required field "question_text"');
    });
  });

  describe('Template Generation', () => {
    it('should generate single-topic CSV template', () => {
      mockPapa.unparse.mockReturnValue('mocked,csv,content');
      
      const template = generateCSVTemplate();
      
      expect(template).toBe('mocked,csv,content');
      expect(mockPapa.unparse).toHaveBeenCalledWith({
        fields: [
          'question_text',
          'option_a',
          'option_b',
          'option_c',
          'option_d',
          'correct_answer',
          'explanation',
          'difficulty',
        ],
        data: expect.arrayContaining([expect.any(Array)]),
      });
    });

    it('should generate multi-topic CSV template with name-based format by default', () => {
      mockPapa.unparse.mockReturnValue('mocked,multi,topic,csv,content');
      
      const template = generateMultiTopicCSVTemplate();
      
      expect(template).toBe('mocked,multi,topic,csv,content');
      expect(mockPapa.unparse).toHaveBeenCalledWith({
        fields: [
          'topic_name',
          'question_text',
          'option_a',
          'option_b',
          'option_c',
          'option_d',
          'correct_answer',
          'explanation',
          'difficulty',
        ],
        data: expect.arrayContaining([
          expect.arrayContaining(['Security Fundamentals']),
          expect.arrayContaining(['Network Security']),
          expect.arrayContaining(['Incident Response']),
        ]),
      });
    });

    it('should generate multi-topic CSV template with name-based format explicitly', () => {
      mockPapa.unparse.mockReturnValue('mocked,name,based,csv,content');
      
      const template = generateMultiTopicCSVTemplate('name-based');
      
      expect(template).toBe('mocked,name,based,csv,content');
      expect(mockPapa.unparse).toHaveBeenCalledWith({
        fields: [
          'topic_name',
          'question_text',
          'option_a',
          'option_b',
          'option_c',
          'option_d',
          'correct_answer',
          'explanation',
          'difficulty',
        ],
        data: expect.arrayContaining([
          expect.arrayContaining(['Security Fundamentals']),
          expect.arrayContaining(['Network Security']),
          expect.arrayContaining(['Incident Response']),
        ]),
      });
    });

    it('should generate multi-topic CSV template with id-based format', () => {
      mockPapa.unparse.mockReturnValue('mocked,id,based,csv,content');
      
      const template = generateMultiTopicCSVTemplate('id-based');
      
      expect(template).toBe('mocked,id,based,csv,content');
      expect(mockPapa.unparse).toHaveBeenCalledWith({
        fields: [
          'topic_id',
          'question_text',
          'option_a',
          'option_b',
          'option_c',
          'option_d',
          'correct_answer',
          'explanation',
          'difficulty',
        ],
        data: expect.arrayContaining([
          expect.arrayContaining(['security-fundamentals-uuid']),
          expect.arrayContaining(['network-security-uuid']),
          expect.arrayContaining(['incident-response-uuid']),
        ]),
      });
    });

    it('should generate multi-topic CSV template with both columns format', () => {
      mockPapa.unparse.mockReturnValue('mocked,both,columns,csv,content');
      
      const template = generateMultiTopicCSVTemplate('both');
      
      expect(template).toBe('mocked,both,columns,csv,content');
      expect(mockPapa.unparse).toHaveBeenCalledWith({
        fields: [
          'topic_name',
          'topic_id',
          'question_text',
          'option_a',
          'option_b',
          'option_c',
          'option_d',
          'correct_answer',
          'explanation',
          'difficulty',
        ],
        data: expect.arrayContaining([
          expect.arrayContaining(['Security Fundamentals', 'security-fundamentals-uuid']),
          expect.arrayContaining(['Network Security', 'network-security-uuid']),
          expect.arrayContaining(['Incident Response', 'incident-response-uuid']),
        ]),
      });
    });

    it('should include sample data with proper question structure in all formats', () => {
      mockPapa.unparse.mockReturnValue('sample,csv,content');
      
      // Test name-based format
      generateMultiTopicCSVTemplate('name-based');
      let lastCall = mockPapa.unparse.mock.calls[mockPapa.unparse.mock.calls.length - 1][0];
      expect(lastCall.data).toHaveLength(3); // Three sample questions
      expect(lastCall.data[0]).toContain('What is the primary purpose of a firewall?');
      expect(lastCall.data[1]).toContain('What does VPN stand for?');
      expect(lastCall.data[2]).toContain('What is the first step in incident response?');

      // Test id-based format
      generateMultiTopicCSVTemplate('id-based');
      lastCall = mockPapa.unparse.mock.calls[mockPapa.unparse.mock.calls.length - 1][0];
      expect(lastCall.data).toHaveLength(3); // Three sample questions
      expect(lastCall.data[0]).toContain('security-fundamentals-uuid');
      expect(lastCall.data[1]).toContain('network-security-uuid');
      expect(lastCall.data[2]).toContain('incident-response-uuid');

      // Test both columns format
      generateMultiTopicCSVTemplate('both');
      lastCall = mockPapa.unparse.mock.calls[mockPapa.unparse.mock.calls.length - 1][0];
      expect(lastCall.data).toHaveLength(3); // Three sample questions
      expect(lastCall.data[0]).toContain('Security Fundamentals');
      expect(lastCall.data[0]).toContain('security-fundamentals-uuid');
    });

    it('should include proper difficulty levels and answer formats in sample data', () => {
      mockPapa.unparse.mockReturnValue('sample,csv,content');
      
      generateMultiTopicCSVTemplate('name-based');
      const lastCall = mockPapa.unparse.mock.calls[mockPapa.unparse.mock.calls.length - 1][0];
      
      // Check that sample data includes different difficulty levels
      const difficulties = lastCall.data.map((row: string[]) => row[row.length - 1]); // Last column is difficulty
      expect(difficulties).toContain('easy');
      expect(difficulties).toContain('medium');
      expect(difficulties).toContain('hard');
      
      // Check that correct answers are in proper format (A, B, C, D)
      const correctAnswers = lastCall.data.map((row: string[]) => row[row.length - 3]); // Third from last is correct_answer
      correctAnswers.forEach(answer => {
        expect(['A', 'B', 'C', 'D']).toContain(answer);
      });
    });

    it('should include comprehensive explanations in sample data', () => {
      mockPapa.unparse.mockReturnValue('sample,csv,content');
      
      generateMultiTopicCSVTemplate('name-based');
      const lastCall = mockPapa.unparse.mock.calls[mockPapa.unparse.mock.calls.length - 1][0];
      
      // Check that explanations are meaningful and not empty
      const explanations = lastCall.data.map((row: string[]) => row[row.length - 2]); // Second from last is explanation
      explanations.forEach(explanation => {
        expect(explanation).toBeTruthy();
        expect(explanation.length).toBeGreaterThan(10); // Should be meaningful explanations
      });
    });

    it('should generate templates with consistent column ordering', () => {
      mockPapa.unparse.mockReturnValue('sample,csv,content');
      
      // Test that all formats have consistent column ordering
      const formats: Array<'name-based' | 'id-based' | 'both'> = ['name-based', 'id-based', 'both'];
      
      formats.forEach(format => {
        generateMultiTopicCSVTemplate(format);
        const lastCall = mockPapa.unparse.mock.calls[mockPapa.unparse.mock.calls.length - 1][0];
        const fields = lastCall.fields;
        
        // All formats should have question_text after topic columns
        const questionTextIndex = fields.indexOf('question_text');
        expect(questionTextIndex).toBeGreaterThan(0);
        
        // All formats should have options in order A, B, C, D
        const optionAIndex = fields.indexOf('option_a');
        const optionBIndex = fields.indexOf('option_b');
        const optionCIndex = fields.indexOf('option_c');
        const optionDIndex = fields.indexOf('option_d');
        
        expect(optionAIndex).toBeLessThan(optionBIndex);
        expect(optionBIndex).toBeLessThan(optionCIndex);
        expect(optionCIndex).toBeLessThan(optionDIndex);
        
        // Correct answer should come after options
        const correctAnswerIndex = fields.indexOf('correct_answer');
        expect(correctAnswerIndex).toBeGreaterThan(optionDIndex);
        
        // Explanation should come after correct answer
        const explanationIndex = fields.indexOf('explanation');
        expect(explanationIndex).toBeGreaterThan(correctAnswerIndex);
        
        // Difficulty should be last
        const difficultyIndex = fields.indexOf('difficulty');
        expect(difficultyIndex).toBe(fields.length - 1);
      });
    });
  });
});