import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import BottomNavigation from "@/components/BottomNavigation";
import DesktopSideNav from "@/components/DesktopSideNav";
import SubscriptionStatus from "@/components/SubscriptionStatus";
import MobileHeader from "@/components/MobileHeader";
import { UserCircle, LogOut, Settings } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { getQuizzesTaken, getAverageScore } from "@/utils/user-stats";

// Add a type for the profile returned from Supabase
type SupabaseProfile = {
  last_login_at?: string | null;
};

const ProfilePage = () => {
  const { toast } = useToast();
  const { user, signOut } = useAuth();
  const navigate = useNavigate();
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
  });
  const [quizzesTaken, setQuizzesTaken] = useState(0);
  const [averageScore, setAverageScore] = useState(0);
  const [lastLoginAt, setLastLoginAt] = useState<string | null>(null);

  // Check if user is authenticated and redirect if not
  useEffect(() => {
    if (!user && !isLoading) {
      console.log("No user found, redirecting to login");
      navigate("/auth");
      return;
    }
  }, [user, isLoading, navigate]);

  // Initialize form data with user information when available
  useEffect(() => {
    setIsLoading(true);

    if (user) {
      // Get the user's full name from metadata if available
      const fullName = user.user_metadata?.full_name || user.user_metadata?.name || "";

      setFormData({
        name: fullName,
        email: user.email || "",
      });

      // Main async function to fetch all user data
      const fetchUserData = async () => {
        try {
          // Fetch user statistics
          console.log("Fetching user quiz statistics...");
          const quizCount = await getQuizzesTaken(user);
          console.log("Quizzes taken:", quizCount);

          const avgScore = await getAverageScore(user);
          console.log("Average score:", avgScore);

          setQuizzesTaken(quizCount);
          setAverageScore(avgScore);

          // Fetch last login time from profiles table
          try {
            const { data: profile } = await supabase
              .from('user_profiles')
              .select('last_login_at')
              .eq('user_id', user.id)
              .maybeSingle();

            if ((profile as SupabaseProfile)?.last_login_at) {
              setLastLoginAt((profile as SupabaseProfile).last_login_at!);
            }
          } catch (err) {
            console.error("Error fetching last login time:", err);
          }
        } catch (error) {
          console.error("Error fetching user data:", error);
          // Set default values in case of error
          setQuizzesTaken(0);
          setAverageScore(0);
        } finally {
          setIsLoading(false);
        }
      };

      // Execute the main fetch function
      fetchUserData();
    } else {
      setIsLoading(false);
    }
  }, [user]);

  // User statistics are now fetched from the database

  const handleSaveProfile = async () => {
    try {
      if (!user) return;

      // Update user metadata in Supabase
      const { error } = await supabase.auth.updateUser({
        data: { full_name: formData.name }
      });

      if (error) {
        throw error;
      }

      setIsEditing(false);
      toast({
        title: "Profile updated",
        description: "Your profile has been successfully updated.",
      });
    } catch (err) {
      console.error("Error updating profile:", err);
      toast({
        title: "Update failed",
        description: "There was an error updating your profile. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Fix: Remove unused error variable in handleLogout
  const handleLogout = async () => {
    try {
      await signOut();
      toast({
        title: "Logged out",
        description: "You have been successfully logged out.",
      });
      // Navigate to homepage after logout
      navigate("/");
    } catch {
      toast({
        title: "Logout failed",
        description: "An error occurred while logging out.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex min-h-screen cyber-grid-bg">
      {/* Desktop Side Navigation */}
      <DesktopSideNav />

      <div className="flex flex-col flex-1 pb-16 w-full md:max-w-[calc(100%-16rem)]">
        {/* Mobile Header */}
        <MobileHeader title="Profile" />

        <header className="hidden md:block p-4 border-b bg-background/95 backdrop-blur-sm sticky top-0 z-10">
          <div className="flex items-center justify-between">
            <h1 className="text-lg font-medium">Profile</h1>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLogout}
              className="text-muted-foreground"
            >
              <LogOut className="h-4 w-4 mr-1" />
              Logout
            </Button>
          </div>
        </header>

        <div className="p-4">
          {/* Profile Card */}
          <Card className="cyber-card p-6 mb-6">
            <div className="flex flex-col items-center mb-4">
              <div className="profile-avatar-container mb-3">
                {/* Avatar container with proper spacing for badge */}
                <div className="h-20 w-20 rounded-full bg-cyber-primary/10 flex items-center justify-center border-2 border-cyber-primary/40">
                  <UserCircle className="h-12 w-12 text-cyber-primary" />
                </div>
                {/* Subscription status badge positioned to avoid overflow */}
                <div className="subscription-badge-overlay">
                  <SubscriptionStatus compact realTimeUpdates={false} />
                </div>
              </div>
              {isLoading ? (
                <div className="space-y-2 w-full">
                  <div className="h-6 w-32 mx-auto bg-muted animate-pulse rounded"></div>
                  <div className="h-4 w-48 mx-auto bg-muted animate-pulse rounded"></div>
                </div>
              ) : (
                <>
                  <h2 className="text-xl font-bold">{formData.name || "No name provided"}</h2>
                  <p className="text-sm text-muted-foreground">{formData.email || "No email available"}</p>
                  {lastLoginAt && (
                    <p className="text-xs text-muted-foreground mt-1">
                      Last login: {new Date(lastLoginAt).toLocaleString()}
                    </p>
                  )}
                </>
              )}
            </div>

            {isLoading ? (
              <div className="h-10 w-full bg-muted animate-pulse rounded mt-4"></div>
            ) : isEditing ? (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Display Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    disabled={isLoading}
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    disabled={true} // Email cannot be changed directly
                    title="Email cannot be changed directly. Please contact support."
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    className="flex-1"
                    onClick={() => setIsEditing(false)}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                  <Button
                    className="flex-1 bg-cyber-primary hover:bg-cyber-primary/90"
                    onClick={handleSaveProfile}
                    disabled={isLoading}
                  >
                    Save Changes
                  </Button>
                </div>
              </div>
            ) : (
              <Button
                variant="outline"
                className="w-full"
                onClick={() => setIsEditing(true)}
                disabled={isLoading}
              >
                <Settings className="h-4 w-4 mr-2" />
                Edit Profile
              </Button>
            )}

          {/* Mobile Logout Button (hidden on md and up) */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleLogout}
            className="flex items-center justify-center w-full mt-2 text-muted-foreground md:hidden"
          >
            <LogOut className="h-4 w-4 mr-1" />
            Logout
          </Button>
        </Card>

          {/* Enhanced Subscription Status */}
          <SubscriptionStatus
            className="cyber-card mb-6"
            showActions={true}
            realTimeUpdates={true}
          />

          {/* Stats Card */}
          <Card className="cyber-card mb-6">
            <div className="grid grid-cols-2 divide-x">
              <div className="p-4 text-center">
                {isLoading ? (
                  <div className="flex flex-col items-center">
                    <div className="h-8 w-16 bg-muted animate-pulse rounded mb-2 mx-auto"></div>
                    <div className="h-4 w-24 bg-muted animate-pulse rounded mx-auto"></div>
                  </div>
                ) : (
                  <>
                    <p className="text-2xl font-bold text-cyber-primary">{quizzesTaken}</p>
                    <p className="text-xs text-muted-foreground">Quizzes Taken</p>
                  </>
                )}
              </div>
              <div className="p-4 text-center">
                {isLoading ? (
                  <div className="flex flex-col items-center">
                    <div className="h-8 w-16 bg-muted animate-pulse rounded mb-2 mx-auto"></div>
                    <div className="h-4 w-24 bg-muted animate-pulse rounded mx-auto"></div>
                  </div>
                ) : (
                  <>
                    <p className="text-2xl font-bold text-cyber-accent">{averageScore}%</p>
                    <p className="text-xs text-muted-foreground">Average Score</p>
                  </>
                )}
              </div>
            </div>
          </Card>




        </div>

        <BottomNavigation />
      </div>
    </div>
  );
};

export default ProfilePage;
