/**
 * Topic Analytics Card Component
 * Displays analytics and insights for a specific topic
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  AlertTriangle, 
  CheckCircle, 
  TrendingUp, 
  TrendingDown, 
  BarChart3,
  Clock,
  Target,
  Users
} from 'lucide-react';
import { useQuestionAnalytics } from '@/hooks/use-question-analytics';
import type { TopicInsights, TopicAlert } from '@/services/question-pool-analytics-service';

interface TopicAnalyticsCardProps {
  topicId: string;
  topicTitle?: string;
  className?: string;
  showDetails?: boolean;
}

const getVarietyColor = (variety: string) => {
  switch (variety) {
    case 'excellent': return 'bg-green-100 text-green-800';
    case 'good': return 'bg-blue-100 text-blue-800';
    case 'minimal': return 'bg-yellow-100 text-yellow-800';
    case 'insufficient': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getAlertIcon = (type: TopicAlert['type']) => {
  switch (type) {
    case 'insufficient_questions': return <AlertTriangle className="h-4 w-4" />;
    case 'low_performance': return <TrendingDown className="h-4 w-4" />;
    case 'unused_questions': return <Clock className="h-4 w-4" />;
    case 'high_difficulty': return <Target className="h-4 w-4" />;
    default: return <AlertTriangle className="h-4 w-4" />;
  }
};

const getSeverityVariant = (severity: 'low' | 'medium' | 'high') => {
  switch (severity) {
    case 'high': return 'destructive';
    case 'medium': return 'default';
    case 'low': return 'secondary';
  }
};

export const TopicAnalyticsCard: React.FC<TopicAnalyticsCardProps> = ({
  topicId,
  topicTitle,
  className = '',
  showDetails = true
}) => {
  const { topicInsights, loading, error, loadTopicInsights } = useQuestionAnalytics();
  const [expanded, setExpanded] = useState(false);

  useEffect(() => {
    if (topicId) {
      loadTopicInsights(topicId);
    }
  }, [topicId, loadTopicInsights]);

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center space-x-2">
            <div className="animate-pulse bg-gray-200 h-4 w-4 rounded"></div>
            <div className="animate-pulse bg-gray-200 h-4 w-32 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>Failed to load topic analytics: {error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!topicInsights) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <BarChart3 className="h-8 w-8 mx-auto mb-2" />
            <p>No analytics data available for this topic</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { stats, questionVariety, alerts, recommendations } = topicInsights;

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">
              {topicTitle || topicInsights.topicTitle}
            </CardTitle>
            <CardDescription>Question Pool Analytics</CardDescription>
          </div>
          <Badge className={getVarietyColor(questionVariety)}>
            {questionVariety.toUpperCase()}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Key Metrics */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <p className="text-sm font-medium">Total Questions</p>
            <p className="text-2xl font-bold">{stats.total_questions}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium">Avg Correct Rate</p>
            <p className="text-2xl font-bold">{stats.avg_correct_rate.toFixed(1)}%</p>
          </div>
        </div>

        {/* Usage Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Questions Used</span>
            <span>{stats.total_questions - stats.questions_never_used}/{stats.total_questions}</span>
          </div>
          <Progress 
            value={stats.total_questions > 0 ? ((stats.total_questions - stats.questions_never_used) / stats.total_questions) * 100 : 0}
            className="h-2"
          />
        </div>

        {/* Alerts */}
        {alerts.length > 0 && (
          <div className="space-y-2">
            <p className="text-sm font-medium">Alerts</p>
            {alerts.slice(0, expanded ? alerts.length : 2).map((alert, index) => (
              <Alert key={index} variant={getSeverityVariant(alert.severity)}>
                <div className="flex items-start space-x-2">
                  {getAlertIcon(alert.type)}
                  <AlertDescription className="text-xs">
                    {alert.message}
                  </AlertDescription>
                </div>
              </Alert>
            ))}
            {alerts.length > 2 && !expanded && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setExpanded(true)}
                className="text-xs"
              >
                Show {alerts.length - 2} more alerts
              </Button>
            )}
          </div>
        )}

        {/* Detailed Stats (when expanded or showDetails is true) */}
        {(expanded || showDetails) && (
          <div className="space-y-3 pt-2 border-t">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground">Avg Usage Count</p>
                <p className="font-medium">{stats.avg_usage_count.toFixed(1)}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Never Used</p>
                <p className="font-medium">{stats.questions_never_used}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Low Performance</p>
                <p className="font-medium">{stats.questions_low_performance}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Variety Level</p>
                <p className="font-medium capitalize">{questionVariety}</p>
              </div>
            </div>

            {/* Recommendations */}
            {recommendations.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm font-medium">Recommendations</p>
                <ul className="space-y-1">
                  {recommendations.slice(0, 3).map((rec, index) => (
                    <li key={index} className="text-xs text-muted-foreground flex items-start space-x-1">
                      <CheckCircle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                      <span>{rec}</span>
                    </li>
                  ))}
                </ul>
                {recommendations.length > 3 && (
                  <p className="text-xs text-muted-foreground">
                    +{recommendations.length - 3} more recommendations
                  </p>
                )}
              </div>
            )}
          </div>
        )}

        {/* Toggle Details Button */}
        {!showDetails && (
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setExpanded(!expanded)}
            className="w-full text-xs"
          >
            {expanded ? 'Show Less' : 'Show Details'}
          </Button>
        )}
      </CardContent>
    </Card>
  );
};

export default TopicAnalyticsCard;