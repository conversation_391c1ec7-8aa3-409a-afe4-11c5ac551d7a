/**
 * Error Recovery Service Tests
 * Tests for error recovery and fallback strategies
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock Supabase first
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          limit: vi.fn(() => ({ data: [], error: null }))
        })),
        neq: vi.fn(() => ({
          limit: vi.fn(() => ({ data: [], error: null }))
        })),
        in: vi.fn(() => ({ data: [], error: null }))
      })),
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(() => ({ data: null, error: null }))
        }))
      }))
    }))
  }
}));

import { errorRecovery, ErrorRecoveryService } from '../error-recovery-service';
import { errorLogger } from '../error-logging-service';
import { systemMonitor } from '../system-monitoring-service';
import { supabase } from '@/integrations/supabase/client';

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};
Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

describe('ErrorRecoveryService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    errorLogger.clearLogs();
    systemMonitor.clearAlerts();
    mockLocalStorage.getItem.mockReturnValue(null);
    
    // Configure shorter delays for testing
    errorRecovery.updateEmergencyConfig({
      maxRetries: 3,
      retryDelay: 10 // 10ms instead of 1000ms
    });
  });

  describe('Question Selection Recovery', () => {
    it('should recover with reduced question count', async () => {
      const mockQuestions = [
        { id: 'q1', topic_id: 'topic1', question_text: 'Q1', options: { '0': 'A', '1': 'B' }, correct_answer: '0' },
        { id: 'q2', topic_id: 'topic1', question_text: 'Q2', options: { '0': 'A', '1': 'B' }, correct_answer: '1' },
        { id: 'q3', topic_id: 'topic1', question_text: 'Q3', options: { '0': 'A', '1': 'B' }, correct_answer: '0' }
      ];

      (supabase.from as any).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            limit: vi.fn(() => ({ data: mockQuestions, error: null }))
          }))
        }))
      });

      const result = await errorRecovery.recoverFromQuestionSelectionFailure(
        'topic1',
        10, // Request 10 but only 3 available
        new Error('Insufficient questions')
      );

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(3);
      expect(result.strategy.type).toBe('graceful_degradation');
      expect(result.userMessage).toContain('3 questions');
    });

    it('should try fallback topic when primary topic fails', async () => {
      const fallbackTopics = [
        { id: 'fallback_topic', title: 'Fallback Topic' }
      ];

      const fallbackQuestions = [
        { id: 'fb1', topic_id: 'fallback_topic', question_text: 'FB1', options: { '0': 'A', '1': 'B' }, correct_answer: '0' },
        { id: 'fb2', topic_id: 'fallback_topic', question_text: 'FB2', options: { '0': 'A', '1': 'B' }, correct_answer: '1' }
      ];

      let callCount = 0;
      (supabase.from as any).mockImplementation((table) => {
        if (table === 'topics') {
          return {
            select: vi.fn(() => ({
              neq: vi.fn(() => ({
                limit: vi.fn(() => ({ data: fallbackTopics, error: null }))
              }))
            }))
          };
        } else if (table === 'questions') {
          callCount++;
          if (callCount === 1) {
            // First call fails (original topic)
            return {
              select: vi.fn(() => ({
                eq: vi.fn(() => ({
                  limit: vi.fn(() => ({ data: null, error: { message: 'No questions' } }))
                }))
              }))
            };
          } else {
            // Second call succeeds (fallback topic)
            return {
              select: vi.fn(() => ({
                eq: vi.fn(() => ({
                  limit: vi.fn(() => ({ data: fallbackQuestions, error: null }))
                }))
              }))
            };
          }
        }
      });

      const result = await errorRecovery.recoverFromQuestionSelectionFailure(
        'original_topic',
        5,
        new Error('Original topic failed')
      );

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
      expect(result.strategy.type).toBe('fallback');
      expect(result.userMessage).toContain('Fallback Topic');
    });

    it('should use emergency mode when all else fails', async () => {
      // Configure emergency mode with fallback questions
      errorRecovery.updateEmergencyConfig({
        enabled: true,
        fallbackQuestions: [
          {
            id: 'emergency_1',
            topic_id: 'emergency',
            question_text: 'Emergency Question',
            options: { '0': 'A', '1': 'B', '2': 'C', '3': 'D' },
            correct_answer: '0',
            explanation: 'Emergency explanation',
            difficulty: 'easy',
            usage_count: 0,
            last_used: null,
            correct_answer_rate: null,
            created_at: '2024-01-01',
            updated_at: '2024-01-01',
            created_by: null,
            is_premium: false
          }
        ]
      });

      // Mock all database calls to fail
      (supabase.from as any).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            limit: vi.fn(() => ({ data: null, error: { message: 'Database error' } }))
          })),
          neq: vi.fn(() => ({
            limit: vi.fn(() => ({ data: null, error: { message: 'Database error' } }))
          }))
        }))
      });

      const result = await errorRecovery.recoverFromQuestionSelectionFailure(
        'topic1',
        5,
        new Error('Complete failure')
      );

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.strategy.type).toBe('emergency_mode');
      expect(result.userMessage).toContain('practice questions');
    });

    it('should handle complete failure gracefully', async () => {
      // Disable emergency mode
      errorRecovery.updateEmergencyConfig({
        enabled: false,
        fallbackQuestions: []
      });

      // Mock all calls to fail
      (supabase.from as any).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            limit: vi.fn(() => ({ data: null, error: { message: 'Complete failure' } }))
          })),
          neq: vi.fn(() => ({
            limit: vi.fn(() => ({ data: null, error: { message: 'Complete failure' } }))
          }))
        }))
      });

      const result = await errorRecovery.recoverFromQuestionSelectionFailure(
        'topic1',
        5,
        new Error('Original error')
      );

      expect(result.success).toBe(false);
      expect(result.strategy.canRecover).toBe(false);
      expect(result.userMessage).toBeTruthy();
    });
  });

  describe('Shuffling Recovery', () => {
    it('should recover with original order when shuffling fails', () => {
      const question = {
        id: 'q1',
        topic_id: 'topic1',
        question_text: 'Test Question',
        options: { '0': 'Option A', '1': 'Option B', '2': 'Option C', '3': 'Option D' },
        correct_answer: '2',
        explanation: 'Test explanation',
        difficulty: 'medium',
        usage_count: 0,
        last_used: null,
        correct_answer_rate: null,
        created_at: '2024-01-01',
        updated_at: '2024-01-01',
        created_by: null,
        is_premium: false
      } as any;

      const result = errorRecovery.recoverFromShufflingFailure(
        question,
        new Error('Shuffling failed')
      );

      expect(result.success).toBe(true);
      expect(result.data.originalCorrectIndex).toBe(2);
      expect(result.data.shuffledCorrectIndex).toBe(2);
      expect(result.data.optionMapping).toEqual([0, 1, 2, 3]);
      expect(result.strategy.type).toBe('graceful_degradation');
    });

    it('should create safe default options for corrupted questions', () => {
      const corruptedQuestion = {
        id: 'q1',
        topic_id: 'topic1',
        question_text: 'Test Question',
        options: null, // Corrupted options
        correct_answer: 'invalid',
        explanation: 'Test explanation',
        difficulty: 'medium',
        usage_count: 0,
        last_used: null,
        correct_answer_rate: null,
        created_at: '2024-01-01',
        updated_at: '2024-01-01',
        created_by: null,
        is_premium: false
      } as any;

      const result = errorRecovery.recoverFromShufflingFailure(
        corruptedQuestion,
        new Error('Corrupted question')
      );

      expect(result.success).toBe(true);
      expect(result.data.shuffledOptions).toEqual({
        '0': 'Option A',
        '1': 'Option B',
        '2': 'Option C',
        '3': 'Option D'
      });
      expect(result.strategy.type).toBe('emergency_mode');
    });

    it('should handle questions with circular references', () => {
      const circularOptions: any = { '0': 'Option A' };
      circularOptions['1'] = circularOptions; // Circular reference

      const questionWithCircularRef = {
        id: 'q1',
        topic_id: 'topic1',
        question_text: 'Test Question',
        options: circularOptions,
        correct_answer: '0',
        explanation: 'Test explanation',
        difficulty: 'medium',
        usage_count: 0,
        last_used: null,
        correct_answer_rate: null,
        created_at: '2024-01-01',
        updated_at: '2024-01-01',
        created_by: null,
        is_premium: false
      } as any;

      const result = errorRecovery.recoverFromShufflingFailure(
        questionWithCircularRef,
        new Error('Circular reference')
      );

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });

    it('should handle complete shuffling failure', () => {
      const invalidQuestion = {
        id: null,
        options: 'not an object',
        correct_answer: undefined
      } as any;

      const result = errorRecovery.recoverFromShufflingFailure(
        invalidQuestion,
        new Error('Complete failure')
      );

      expect(result.success).toBe(true); // Should still provide fallback
      expect(result.data.shuffledOptions).toBeDefined();
    });
  });

  describe('Session Recovery', () => {
    it('should retry session creation with exponential backoff', async () => {
      let attemptCount = 0;
      (supabase.from as any).mockReturnValue({
        insert: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn(() => {
              attemptCount++;
              if (attemptCount < 3) {
                return { data: null, error: { message: 'Temporary failure' } };
              }
              return { data: { id: 'session_123', user_id: 'user1' }, error: null };
            })
          }))
        }))
      });

      const result = await errorRecovery.recoverFromSessionFailure(
        'user1',
        'topic1',
        { questions: [{ id: 'q1' }] },
        new Error('Session creation failed')
      );

      expect(result.success).toBe(true);
      expect(result.data.id).toBe('session_123');
      expect(result.strategy.type).toBe('retry');
      expect(attemptCount).toBe(3);
    });

    it('should create simplified session when normal creation fails', async () => {
      (supabase.from as any).mockReturnValue({
        insert: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn()
              .mockReturnValueOnce({ data: null, error: { message: 'Complex data failed' } })
              .mockReturnValueOnce({ data: null, error: { message: 'Complex data failed' } })
              .mockReturnValueOnce({ data: null, error: { message: 'Complex data failed' } })
              .mockReturnValueOnce({ data: { id: 'simple_session', user_id: 'user1' }, error: null })
          }))
        }))
      });

      const complexQuestionsData = {
        questions: [{ id: 'q1', complexData: 'x'.repeat(10000) }],
        metadata: { large: true }
      };

      const result = await errorRecovery.recoverFromSessionFailure(
        'user1',
        'topic1',
        complexQuestionsData,
        new Error('Complex data failed')
      );

      expect(result.success).toBe(true);
      expect(result.data.id).toBe('simple_session');
      expect(result.strategy.type).toBe('graceful_degradation');
    });

    it('should create temporary in-memory session as last resort', async () => {
      // Mock all database operations to fail
      (supabase.from as any).mockReturnValue({
        insert: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn(() => ({ data: null, error: { message: 'Database unavailable' } }))
          }))
        }))
      });

      const result = await errorRecovery.recoverFromSessionFailure(
        'user1',
        'topic1',
        { questions: [{ id: 'q1' }] },
        new Error('Database unavailable')
      );

      expect(result.success).toBe(true);
      expect(result.data.temporary).toBe(true);
      expect(result.data.id).toContain('temp_');
      expect(result.strategy.type).toBe('emergency_mode');
      expect(result.userMessage).toContain('temporary mode');
    });

    it('should handle invalid session parameters', async () => {
      const result = await errorRecovery.recoverFromSessionFailure(
        '', // Invalid user ID
        null as any, // Invalid topic ID
        { questions: [] },
        new Error('Invalid parameters')
      );

      expect(result.success).toBe(true); // Should still create temporary session
      expect(result.data.temporary).toBe(true);
    });
  });

  describe('Database Recovery', () => {
    it('should retry database operations with exponential backoff', async () => {
      let attemptCount = 0;
      const retryFunction = async () => {
        attemptCount++;
        if (attemptCount < 3) {
          throw new Error('Temporary database error');
        }
        return { success: true, data: 'recovered' };
      };

      const result = await errorRecovery.recoverFromDatabaseFailure(
        'test_operation',
        retryFunction,
        new Error('Initial database error')
      );

      expect(result.success).toBe(true);
      expect(result.data).toEqual({ success: true, data: 'recovered' });
      expect(result.strategy.type).toBe('retry');
      expect(attemptCount).toBe(3);
    });

    it('should use cached data when available', async () => {
      // Mock cached data
      const cachedData = { cached: true, data: 'from cache' };
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify({
        value: cachedData,
        timestamp: Date.now() - 30 * 60 * 1000 // 30 minutes ago
      }));

      const failingFunction = async () => {
        throw new Error('Database completely unavailable');
      };

      const result = await errorRecovery.recoverFromDatabaseFailure(
        'cached_operation',
        failingFunction,
        new Error('Database error'),
        { topicId: 'topic1' }
      );

      expect(result.success).toBe(true);
      expect(result.data).toEqual(cachedData);
      expect(result.strategy.type).toBe('fallback');
      expect(result.userMessage).toContain('cached data');
    });

    it('should ignore stale cached data', async () => {
      // Mock stale cached data (older than 1 hour)
      const staleData = { cached: true, data: 'stale' };
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify({
        value: staleData,
        timestamp: Date.now() - 2 * 60 * 60 * 1000 // 2 hours ago
      }));

      const failingFunction = async () => {
        throw new Error('Database unavailable');
      };

      const result = await errorRecovery.recoverFromDatabaseFailure(
        'stale_cache_operation',
        failingFunction,
        new Error('Database error')
      );

      expect(result.success).toBe(false);
      expect(result.strategy.canRecover).toBe(false);
    });

    it('should handle cache access errors gracefully', async () => {
      mockLocalStorage.getItem.mockImplementation(() => {
        throw new Error('Cache access error');
      });

      const failingFunction = async () => {
        throw new Error('Database error');
      };

      const result = await errorRecovery.recoverFromDatabaseFailure(
        'cache_error_operation',
        failingFunction,
        new Error('Database error')
      );

      expect(result.success).toBe(false);
      expect(result.strategy.canRecover).toBe(false);
    });
  });

  describe('Emergency Configuration', () => {
    it('should update emergency configuration correctly', () => {
      const newConfig = {
        enabled: false,
        maxRetries: 5,
        retryDelay: 2000,
        fallbackQuestions: [
          {
            id: 'emergency_q1',
            topic_id: 'emergency',
            question_text: 'Emergency Question',
            options: { '0': 'A', '1': 'B' },
            correct_answer: '0'
          }
        ] as any
      };

      errorRecovery.updateEmergencyConfig(newConfig);
      const config = errorRecovery.getEmergencyConfig();

      expect(config.enabled).toBe(false);
      expect(config.maxRetries).toBe(5);
      expect(config.retryDelay).toBe(2000);
      expect(config.fallbackQuestions).toHaveLength(1);
    });

    it('should handle partial configuration updates', () => {
      const originalConfig = errorRecovery.getEmergencyConfig();
      
      errorRecovery.updateEmergencyConfig({
        maxRetries: 10
      });

      const updatedConfig = errorRecovery.getEmergencyConfig();
      expect(updatedConfig.maxRetries).toBe(10);
      expect(updatedConfig.enabled).toBe(originalConfig.enabled); // Should remain unchanged
    });

    it('should handle invalid configuration gracefully', () => {
      expect(() => {
        errorRecovery.updateEmergencyConfig({
          maxRetries: -1,
          retryDelay: 'invalid' as any,
          fallbackQuestions: 'not an array' as any
        });
      }).not.toThrow();
    });
  });

  describe('Integration with Monitoring', () => {
    it('should create system alerts during recovery', async () => {
      // Configure emergency mode
      errorRecovery.updateEmergencyConfig({
        enabled: true,
        fallbackQuestions: [
          {
            id: 'emergency_1',
            topic_id: 'emergency',
            question_text: 'Emergency Question',
            options: { '0': 'A', '1': 'B' },
            correct_answer: '0'
          }
        ] as any
      });

      // Mock database failure
      (supabase.from as any).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            limit: vi.fn(() => ({ data: null, error: { message: 'Database error' } }))
          })),
          neq: vi.fn(() => ({
            limit: vi.fn(() => ({ data: null, error: { message: 'Database error' } }))
          }))
        }))
      });

      await errorRecovery.recoverFromQuestionSelectionFailure(
        'topic1',
        5,
        new Error('Database failure')
      );

      const alerts = systemMonitor.getActiveAlerts();
      const degradationAlerts = alerts.filter(a => a.type === 'service_degradation');
      expect(degradationAlerts.length).toBeGreaterThan(0);
    });

    it('should log recovery attempts', async () => {
      const mockQuestions = [
        { id: 'q1', options: { '0': 'A', '1': 'B' }, correct_answer: '0' }
      ];

      (supabase.from as any).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            limit: vi.fn(() => ({ data: mockQuestions, error: null }))
          }))
        }))
      });

      await errorRecovery.recoverFromQuestionSelectionFailure(
        'topic1',
        10,
        new Error('Test error')
      );

      const metrics = errorLogger.getErrorMetrics();
      expect(metrics.totalErrors).toBeGreaterThan(0);
    });
  });

  describe('Performance', () => {
    it('should handle recovery operations efficiently', async () => {
      const startTime = Date.now();

      // Perform multiple recovery operations
      const operations = Array.from({ length: 10 }, (_, i) =>
        errorRecovery.recoverFromShufflingFailure(
          {
            id: `q${i}`,
            options: { '0': 'A', '1': 'B' },
            correct_answer: '0'
          } as any,
          new Error(`Error ${i}`)
        )
      );

      const results = await Promise.all(operations);
      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(1000); // Should complete within 1 second
      expect(results.every(r => r.success)).toBe(true);
    });

    it('should handle concurrent recovery operations', async () => {
      const concurrentOperations = Array.from({ length: 20 }, (_, i) =>
        Promise.resolve().then(async () => {
          if (i % 2 === 0) {
            return errorRecovery.recoverFromShufflingFailure(
              { id: `q${i}`, options: null } as unknown,
              new Error(`Shuffle error ${i}`)
            );
          } else {
            return errorRecovery.recoverFromQuestionSelectionFailure(
              `topic${i}`,
              5,
              new Error(`Selection error ${i}`)
            );
          }
        })
      );

      const startTime = Date.now();
      const results = await Promise.all(concurrentOperations);
      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(2000);
      expect(results.length).toBe(20);
    });
  });
});