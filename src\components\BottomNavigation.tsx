import { Home, BookOpen, UserCircle, LayoutDashboard, GraduationCap } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { useIsMobile } from "@/hooks/use-mobile";
import { useAuth } from "@/hooks/use-auth";
import { useAdminStatus } from "@/hooks/use-admin-status";

const BottomNavigation = () => {
  const location = useLocation();
  const path = location.pathname;
  const isMobile = useIsMobile();
  const { user } = useAuth();
  const { isAdmin } = useAdminStatus(user);

  // Hide bottom navigation on desktop or when mobile menu is open
  if (!isMobile) return null;

  // Define base navigation items
  const baseNavItems = [
    {
      name: "Domains",
      path: "/domains",
      icon: GraduationCap,
      active: path === "/domains" || path.startsWith("/domains/"),
      requiresAuth: false
    },
    {
      name: "<PERSON>rn",
      path: "/learn",
      icon: BookOpen,
      active: path === "/learn" || path.startsWith("/learn/"),
      requiresAuth: false
    },
    {
      name: "Quizzes",
      path: "/quizzes",
      icon: BookOpen,
      active: path === "/quizzes",
      requiresAuth: false
    }
  ];

  // Auth-dependent items
  const authItems = user ? [
    {
      name: "Profile",
      path: "/profile",
      icon: UserCircle,
      active: path === "/profile",
      requiresAuth: true
    }
  ] : [
    {
      name: "Sign In",
      path: "/auth",
      icon: UserCircle,
      active: path === "/auth",
      requiresAuth: false
    }
  ];

  // Admin items
  const adminItems = isAdmin ? [
    {
      name: "Admin",
      path: "/admin",
      icon: LayoutDashboard,
      active: path === "/admin",
      requiresAuth: true,
      requiresAdmin: true
    }
  ] : [];

  // Combine all items
  const displayItems = [...baseNavItems, ...authItems, ...adminItems];

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-indigo-900 border-t border-indigo-800 flex justify-around py-2 px-2 z-10 backdrop-blur-sm">
      {displayItems.map((item) => (
        <Link
          key={item.name}
          to={item.path}
          className={`flex flex-col items-center justify-center text-xs font-medium transition-colors px-1 sm:px-2 py-1 min-w-0 flex-1 ${
            item.active
              ? "text-cyan-400"
              : "text-white hover:text-cyan-400"
          }`}
        >
          <item.icon className="w-4 h-4 sm:w-5 sm:h-5 mb-1 flex-shrink-0" />
          <span className="truncate text-xs leading-tight">{item.name}</span>
        </Link>
      ))}
    </div>
  );
};

export default BottomNavigation;
