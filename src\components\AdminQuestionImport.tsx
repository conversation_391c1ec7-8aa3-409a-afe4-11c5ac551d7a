
import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { AlertCircle, FileText, Upload } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Topic } from "@/hooks/use-admin";

type AdminQuestionImportProps = {
  topics: Topic[];
  onSuccess: () => void;
  onCancel: () => void;
};

const AdminQuestionImport = ({ topics, onSuccess, onCancel }: AdminQuestionImportProps) => {
  const { toast } = useToast();
  const [importText, setImportText] = useState("");
  const [topicId, setTopicId] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    
    if (!topicId) {
      setError("Please select a topic");
      return;
    }
    
    if (!importText.trim()) {
      setError("Please enter questions to import");
      return;
    }
    
    setIsSubmitting(true);
    try {
      // Q: Question text
      // A: Option 1
      // B: Option 2
      // C: Option 3
      // D: Option 4
      // Answer: A
      // Explanation: Explanation text
      // ---
      const questions = [];
      const questionBlocks = importText.split("---").filter(block => block.trim());
      interface ImportedQuestion {
        topic_id: string;
        options: Record<string, string>;
        correct_answer: string;
        question_text?: string;
        explanation?: string;
      }
      for (const block of questionBlocks) {
        const lines = block.trim().split("\n");
        const questionObj: ImportedQuestion = {
          topic_id: topicId,
          options: {},
          correct_answer: "",
        };
        for (const line of lines) {
          if (line.startsWith("Q:")) {
            questionObj.question_text = line.substring(2).trim();
          } else if (line.startsWith("A:") || line.startsWith("B:") || line.startsWith("C:") || line.startsWith("D:")) {
            const optionKey = line.substring(0, 1);
            questionObj.options[optionKey] = line.substring(2).trim();
          } else if (line.startsWith("Answer:")) {
            questionObj.correct_answer = line.substring(7).trim();
          } else if (line.startsWith("Explanation:")) {
            questionObj.explanation = line.substring(12).trim();
          }
        }
        // Validate question has required fields
        if (
          questionObj.question_text &&
          questionObj.correct_answer
        ) {
          questions.push(questionObj);
        }
      }
      if (questions.length === 0) {
        throw new Error("No valid questions found in the import text");
      }
      // Insert questions into database
      const { error } = await supabase.from("questions").insert(
        questions.map(q => ({
          topic_id: q.topic_id,
          question_text: q.question_text,
          options: q.options,
          correct_answer: q.correct_answer,
          explanation: q.explanation || "",
        }))
      );
      if (error) throw error;
      toast({ 
        title: "Questions imported successfully", 
        description: `Imported ${questions.length} questions`
      });
      onSuccess();
    } catch (error: unknown) {
      console.error("Error importing questions:", error);
      let message = "An error occurred";
      if (
        typeof error === "object" &&
        error !== null &&
        "message" in error &&
        typeof (error as { message?: unknown }).message === "string"
      ) {
        message = (error as { message: string }).message;
      }
      setError(message);
      toast({ 
        title: "Error importing questions", 
        description: message,
        variant: "destructive" 
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <Card className="cyber-card p-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <FileText className="h-5 w-5 mr-2 text-cyber-primary" />
            <h3 className="text-lg font-medium">Bulk Import Questions</h3>
          </div>
        </div>
        
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
  <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="topic">Select Topic</Label>
            <Select value={topicId} onValueChange={setTopicId}>
              <SelectTrigger>
                <SelectValue placeholder="Select a topic" />
              </SelectTrigger>
              <SelectContent>
                {topics.map((topic) => (
                  <SelectItem key={topic.id} value={topic.id}>
                    {topic.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="importText">
              Paste Questions (Format: Q: Question, A: Option, B: Option, etc., Answer: X, Explanation: Text, ---)
            </Label>
            <Textarea
              id="importText"
              value={importText}
              onChange={(e) => setImportText(e.target.value)}
              placeholder={`Q: What is the primary goal of cybersecurity?\nA: Prevent all attacks\nB: Protect confidentiality, integrity, and availability\nC: Encrypt all data\nD: Monitor network traffic\nAnswer: B\nExplanation: Cybersecurity aims to protect the CIA triad.\n---`}
              rows={10}
              className="font-mono text-sm"
            />
          </div>
          
          <div className="flex items-center text-sm text-muted-foreground mb-4">
            <Upload className="h-4 w-4 mr-2" />
            <span>Separate multiple questions with "---"</span>
          </div>
          
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-cyber-primary hover:bg-cyber-primary/90"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Importing..." : "Import Questions"}
            </Button>
          </div>
        </form>
      </div>
    </Card>
  );
};

export default AdminQuestionImport;
