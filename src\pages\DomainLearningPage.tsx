import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import BottomNavigation from "@/components/BottomNavigation";
import Navbar from "@/components/Navbar";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/components/ui/use-toast";
import {
  ArrowLeft,
  BookOpen,
  Clock,
  CheckCircle,
  PlayCircle,
  Lock,
  FileText,
  Video,
  Download,
  ExternalLink
} from "lucide-react";
import { DomainWithDetails } from "@/types/domain";
import { LearningMaterial } from "@/utils/fetch-learning-materials";
import { fetchDomainBySlug, getDomainEnrollmentStatus } from "@/utils/domain-utils";
import { fetchLearningMaterialsByTopic } from "@/utils/fetch-learning-materials";
import { motion } from "framer-motion";

interface TopicWithMaterials {
  id: string;
  title: string;
  description: string;
  difficulty: string;
  is_premium: boolean;
  question_count: number;
  materials: LearningMaterial[];
}

const DomainLearningPage = () => {
  const { domainSlug } = useParams<{ domainSlug: string }>();
  const { user } = useAuth();
  const { toast } = useToast();

  const [domain, setDomain] = useState<DomainWithDetails | null>(null);
  const [topicsWithMaterials, setTopicsWithMaterials] = useState<TopicWithMaterials[]>([]);
  const [hasAccess, setHasAccess] = useState(false);
  const [loading, setLoading] = useState(true);
  const [selectedTopic, setSelectedTopic] = useState<string | null>(null);

  useEffect(() => {
    if (domainSlug) {
      loadDomainLearningData();
    }
  }, [domainSlug, user]);

  const loadDomainLearningData = async () => {
    if (!domainSlug) return;

    try {
      setLoading(true);

      // Load domain details
      const domainData = await fetchDomainBySlug(domainSlug);
      if (!domainData) {
        toast({
          title: "Domain not found",
          description: "The requested domain could not be found.",
          variant: "destructive",
        });
        return;
      }

      setDomain(domainData);

      // Check access if user is authenticated
      if (user) {
        const enrollment = await getDomainEnrollmentStatus(user.id, domainData.id);
        setHasAccess(enrollment.hasActiveSubscription || domainData.topics.some(t => !t.is_premium));
      }

      // Load learning materials for each topic
      const topicsWithMats = await Promise.all(
        domainData.topics.map(async (topic) => {
          const materials = await fetchLearningMaterialsByTopic(topic.id);
          return {
            ...topic,
            materials
          };
        })
      );

      setTopicsWithMaterials(topicsWithMats);

      // Auto-select first topic
      if (topicsWithMats.length > 0) {
        setSelectedTopic(topicsWithMats[0].id);
      }
    } catch (error) {
      console.error('Error loading domain learning data:', error);
      toast({
        title: "Error",
        description: "Failed to load learning materials.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getSelectedTopicData = () => {
    return topicsWithMaterials.find(t => t.id === selectedTopic);
  };

  const canAccessTopic = (topic: TopicWithMaterials) => {
    if (!user) return !topic.is_premium;
    return hasAccess || !topic.is_premium;
  };

  const getMaterialIcon = (content: string) => {
    if (content.includes('<video') || content.includes('youtube') || content.includes('vimeo')) {
      return Video;
    }
    if (content.includes('download') || content.includes('.pdf') || content.includes('.doc')) {
      return Download;
    }
    return FileText;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-white text-lg">Loading learning materials...</div>
          </div>
        </div>
        <BottomNavigation />
      </div>
    );
  }

  if (!domain) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold text-white mb-4">Domain Not Found</h1>
            <Link to="/domains">
              <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
                Back to Domains
              </Button>
            </Link>
          </div>
        </div>
        <BottomNavigation />
      </div>
    );
  }

  const selectedTopicData = getSelectedTopicData();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <Navbar />

      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-6">
          <Link to={`/domains/${domain.slug}`}>
            <Button variant="ghost" className="text-white hover:bg-white/10 mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to {domain.name}
            </Button>
          </Link>

          <div className="flex items-center gap-4 mb-4">
            <div
              className="p-3 rounded-lg"
              style={{ backgroundColor: `${domain.colorTheme}20` }}
            >
              <BookOpen
                className="h-6 w-6"
                style={{ color: domain.colorTheme }}
              />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white">
                {domain.name} - Learning Materials
              </h1>
              <p className="text-gray-300">
                Comprehensive learning resources and materials
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Topics Sidebar */}
          <div className="lg:col-span-1">
            <Card className="bg-white/10 backdrop-blur-sm border-white/20 p-4">
              <h3 className="text-lg font-semibold text-white mb-4">Topics</h3>
              <div className="space-y-2">
                {topicsWithMaterials.map((topic, index) => {
                  const isAccessible = canAccessTopic(topic);
                  const isSelected = selectedTopic === topic.id;

                  return (
                    <motion.button
                      key={topic.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      onClick={() => isAccessible && setSelectedTopic(topic.id)}
                      disabled={!isAccessible}
                      className={`w-full text-left p-3 rounded-lg transition-all duration-200 ${isSelected
                        ? 'bg-white/25 border-l-4 shadow-sm'
                        : isAccessible
                          ? 'hover:bg-white/15 hover:shadow-sm'
                          : 'opacity-60 cursor-not-allowed bg-white/5'
                        }`}
                      style={isSelected ? { borderLeftColor: domain.colorTheme } : {}}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="font-medium text-white text-xs sm:text-sm mb-1 drop-shadow-sm break-words leading-tight">
                            {topic.title}
                          </div>
                          <div className="text-gray-300 text-xs font-medium">
                            {topic.materials.length} materials
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          {!isAccessible && <Lock className="h-3 w-3 text-gray-400" />}
                          {topic.is_premium && (
                            <Badge className="text-yellow-600 bg-yellow-100 text-xs">
                              Premium
                            </Badge>
                          )}
                        </div>
                      </div>
                    </motion.button>
                  );
                })}
              </div>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {selectedTopicData ? (
              <div className="space-y-6">
                {/* Topic Header */}
                <Card className="bg-white/10 backdrop-blur-sm border-white/20 p-4 sm:p-6 overflow-hidden">
                  <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 mb-4">
                    <div className="flex-1 min-w-0">
                      <h2 className="text-lg sm:text-xl lg:text-2xl font-bold text-white mb-2 break-words leading-tight">
                        {selectedTopicData.title}
                      </h2>
                      <p className="text-gray-300 mb-4 text-sm sm:text-base">
                        {selectedTopicData.description}
                      </p>
                      <div className="flex flex-wrap items-center gap-2">
                        <Badge className="text-blue-600 bg-blue-100 text-xs">
                          {selectedTopicData.difficulty}
                        </Badge>
                        <Badge className="text-green-600 bg-green-100 text-xs">
                          <BookOpen className="h-3 w-3 mr-1" />
                          {selectedTopicData.materials.length} Materials
                        </Badge>
                        {selectedTopicData.question_count > 0 && (
                          <Badge className="text-purple-600 bg-purple-100 text-xs">
                            {selectedTopicData.question_count} Questions
                          </Badge>
                        )}
                      </div>
                    </div>
                    {selectedTopicData.question_count > 0 && (
                      <Link to={`/quiz/${selectedTopicData.id}`} className="shrink-0">
                        <Button
                          style={{ backgroundColor: domain.colorTheme }}
                          disabled={!canAccessTopic(selectedTopicData)}
                          className="w-full sm:w-auto min-w-[120px]"
                        >
                          <PlayCircle className="h-4 w-4 mr-2" />
                          Take Quiz
                        </Button>
                      </Link>
                    )}
                  </div>
                </Card>

                {/* Learning Materials */}
                <div className="space-y-4">
                  {selectedTopicData.materials.length > 0 ? (
                    selectedTopicData.materials.map((material, index) => {
                      const MaterialIcon = getMaterialIcon(material.content);
                      const isAccessible = canAccessTopic(selectedTopicData);

                      return (
                        <motion.div
                          key={material.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                        >
                          <Card className={`bg-white/10 backdrop-blur-sm border-white/20 p-4 sm:p-6 overflow-hidden ${!isAccessible ? 'opacity-50' : 'hover:bg-white/15'
                            } transition-all duration-200`}>
                            <div className="flex items-start gap-3 sm:gap-4">
                              <div
                                className="p-2 sm:p-3 rounded-lg flex-shrink-0"
                                style={{ backgroundColor: `${domain.colorTheme}20` }}
                              >
                                <MaterialIcon
                                  className="h-4 w-4 sm:h-5 sm:w-5"
                                  style={{ color: domain.colorTheme }}
                                />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-start justify-between mb-2">
                                  <h4 className="text-sm sm:text-base lg:text-lg font-semibold text-white break-words leading-tight flex-1 pr-2">
                                    {material.title}
                                  </h4>
                                  {material.is_premium && (
                                    <Badge className="text-yellow-600 bg-yellow-100">
                                      Premium
                                    </Badge>
                                  )}
                                </div>
                                {material.summary && (
                                  <p className="text-gray-300 mb-4">
                                    {material.summary}
                                  </p>
                                )}
                                <div className="flex items-center gap-4">
                                  {isAccessible ? (
                                    <Link to={`/learn/${material.id}`}>
                                      <Button
                                        size="sm"
                                        style={{ backgroundColor: domain.colorTheme }}
                                      >
                                        <ExternalLink className="h-3 w-3 mr-2" />
                                        Read Material
                                      </Button>
                                    </Link>
                                  ) : (
                                    <Button size="sm" disabled>
                                      <Lock className="h-3 w-3 mr-2" />
                                      Locked
                                    </Button>
                                  )}
                                  <div className="text-gray-400 text-sm flex items-center gap-1">
                                    <Clock className="h-3 w-3" />
                                    <span>~10 min read</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </Card>
                        </motion.div>
                      );
                    })
                  ) : (
                    <Card className="bg-white/10 backdrop-blur-sm border-white/20 p-8 text-center">
                      <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-white mb-2">
                        No Learning Materials Yet
                      </h3>
                      <p className="text-gray-400">
                        Learning materials for this topic are coming soon.
                      </p>
                    </Card>
                  )}
                </div>
              </div>
            ) : (
              <Card className="bg-white/10 backdrop-blur-sm border-white/20 p-8 text-center">
                <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">
                  Select a Topic
                </h3>
                <p className="text-gray-400">
                  Choose a topic from the sidebar to view learning materials.
                </p>
              </Card>
            )}
          </div>
        </div>
      </div>

      <BottomNavigation />
    </div>
  );
};

export default DomainLearningPage;
