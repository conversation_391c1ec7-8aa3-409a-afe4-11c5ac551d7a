import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://agdyycknlxojiwhlqicq.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFnZHl5Y2tubHhvaml3aGxxaWNxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyMjkzOTgsImV4cCI6MjA1OTgwNTM5OH0.lWZLRByfsyRqkK7XZfi21qSeEuOZHJKkFJGC_2ojQR8';

// Use anon key first to test
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function setupFeedbackTable() {
  console.log('Setting up feedback table with service role...');
  
  try {
    // First, let's try to create the table using a simple approach
    console.log('Step 1: Creating feedback table...');
    
    // Try to insert a test record to see if table exists
    const { data: testData, error: testError } = await supabase
      .from('feedback')
      .select('id')
      .limit(1);
    
    if (testError && testError.code === 'PGRST106') {
      console.log('Table does not exist, need to create it...');
      
      // Since we can't create tables directly via the client, let's try a different approach
      // Let's check if we can use the SQL editor approach
      console.log('Attempting to create table via SQL...');
      
      // For now, let's just test if we can insert into an existing table
      console.log('Testing with manual table creation...');
      
      // If the table doesn't exist, we need to create it manually in Supabase dashboard
      console.log('❌ Feedback table does not exist.');
      console.log('📝 Please create the feedback table manually in Supabase dashboard with this SQL:');
      console.log(`
CREATE TABLE IF NOT EXISTS public.feedback (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  subject TEXT NOT NULL,
  message TEXT NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  status TEXT DEFAULT 'new' CHECK (status IN ('new', 'read', 'responded', 'archived')),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.feedback ENABLE ROW LEVEL SECURITY;

-- Create policies for feedback
CREATE POLICY "Anyone can submit feedback"
  ON public.feedback
  FOR INSERT
  TO authenticated, anon
  WITH CHECK (true);

CREATE POLICY "Users can view their own feedback"
  ON public.feedback
  FOR SELECT
  TO authenticated, anon
  USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Admins can view all feedback"
  ON public.feedback
  FOR SELECT
  TO authenticated
  USING (
    auth.uid() IN (
      SELECT id FROM auth.users WHERE raw_user_meta_data->>'is_admin' = 'true'
    )
  );

CREATE POLICY "Admins can update feedback"
  ON public.feedback
  FOR UPDATE
  TO authenticated
  USING (
    auth.uid() IN (
      SELECT id FROM auth.users WHERE raw_user_meta_data->>'is_admin' = 'true'
    )
  );

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_feedback_status ON public.feedback(status);
CREATE INDEX IF NOT EXISTS idx_feedback_user_id ON public.feedback(user_id);
CREATE INDEX IF NOT EXISTS idx_feedback_created_at ON public.feedback(created_at DESC);
      `);
      
      return false;
    } else if (testError) {
      console.error('Other error accessing feedback table:', testError);
      return false;
    } else {
      console.log('✅ Feedback table already exists!');
      console.log('Table data sample:', testData);
      
      // Test inserting a record
      console.log('Step 2: Testing feedback insertion...');
      const testFeedback = {
        name: 'Test User',
        email: '<EMAIL>',
        subject: 'Test Subject',
        message: 'This is a test message to verify feedback submission works.',
        status: 'new'
      };
      
      const { data: insertData, error: insertError } = await supabase
        .from('feedback')
        .insert(testFeedback)
        .select();
      
      if (insertError) {
        console.error('❌ Error inserting test feedback:', insertError);
        return false;
      }
      
      console.log('✅ Successfully inserted test feedback:', insertData);
      
      // Test retrieving feedback
      console.log('Step 3: Testing feedback retrieval...');
      const { data: allFeedback, error: retrieveError } = await supabase
        .from('feedback')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(5);
      
      if (retrieveError) {
        console.error('❌ Error retrieving feedback:', retrieveError);
        return false;
      }
      
      console.log('✅ Successfully retrieved feedback:', allFeedback);
      console.log(`📊 Total feedback items retrieved: ${allFeedback.length}`);
      
      // Clean up test record
      if (insertData && insertData.length > 0) {
        await supabase
          .from('feedback')
          .delete()
          .eq('id', insertData[0].id);
        console.log('🧹 Cleaned up test record');
      }
      
      return true;
    }
  } catch (e) {
    console.error('❌ Exception during setup:', e);
    return false;
  }
}

setupFeedbackTable();
