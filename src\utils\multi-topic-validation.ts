import { TopicResolutionResult } from './csv-import';

import { MultiTopicQuestionCSVRow } from './csv-import';

import { MultiTopicQuestionCSVRow } from './csv-import';

import { MultiTopicQuestionCSVRow } from './csv-import';

import { MultiTopicQuestionCSVRow } from './csv-import';

import { TopicResolutionResult } from './csv-import';

import { MultiTopicQuestionCSVRow } from './csv-import';

import { MultiTopicQuestionCSVRow } from './csv-import';

import { MultiTopicQuestionCSVRow } from './csv-import';

// Import types from csv-import - these will be re-exported
export type { 
  MultiTopicQuestionCSVRow, 
  ValidatedQuestion, 
  TopicResolutionResult 
} from './csv-import';

// Validation error types for better categorization
export interface ValidationError {
  row: number;
  field?: string;
  message: string;
  severity: 'error' | 'warning';
  category: 'topic' | 'question' | 'format' | 'duplicate';
}

// Topic validation result
export interface TopicValidationResult {
  topicReference: string;
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
}

// Cross-topic validation result
export interface CrossTopicValidationResult {
  isValid: boolean;
  duplicateQuestions: DuplicateQuestionGroup[];
  topicValidations: Map<string, TopicValidationResult>;
  globalErrors: ValidationError[];
}

// Duplicate question detection
export interface DuplicateQuestionGroup {
  questionText: string;
  occurrences: Array<{
    row: number;
    topicReference: string;
    normalizedText: string;
  }>;
}

// Validation configuration
export interface ValidationConfig {
  enableCrossTopicDuplicateDetection: boolean;
  enableTopicNameValidation: boolean;
  enableQuestionContentValidation: boolean;
  maxTopicNameLength: number;
  minQuestionTextLength: number;
  maxQuestionTextLength: number;
  allowedDifficulties: string[];
}

// Default validation configuration
export const DEFAULT_VALIDATION_CONFIG: ValidationConfig = {
  enableCrossTopicDuplicateDetection: true,
  enableTopicNameValidation: true,
  enableQuestionContentValidation: true,
  maxTopicNameLength: 100,
  minQuestionTextLength: 10,
  maxQuestionTextLength: 1000,
  allowedDifficulties: ['easy', 'medium', 'hard'],
};

/**
 * Validate topic names and references according to business rules
 * @param topicReferences Array of unique topic references from CSV
 * @param config Validation configuration
 * @returns Map of topic reference to validation result
 */
export function validateTopicReferences(
  topicReferences: string[],
  config: ValidationConfig = DEFAULT_VALIDATION_CONFIG
): Map<string, TopicValidationResult> {
  const results = new Map<string, TopicValidationResult>();

  topicReferences.forEach((reference, index) => {
    const result: TopicValidationResult = {
      topicReference: reference,
      isValid: true,
      errors: [],
      warnings: [],
    };

    if (!config.enableTopicNameValidation) {
      results.set(reference, result);
      return;
    }

    // Validate topic reference is not empty
    if (!reference || reference.trim().length === 0) {
      result.errors.push({
        row: index,
        field: 'topic_name',
        message: 'Topic reference cannot be empty',
        severity: 'error',
        category: 'topic',
      });
      result.isValid = false;
    } else {
      const trimmedReference = reference.trim();

      // Validate topic name length
      if (trimmedReference.length > config.maxTopicNameLength) {
        result.errors.push({
          row: index,
          field: 'topic_name',
          message: `Topic name exceeds maximum length of ${config.maxTopicNameLength} characters`,
          severity: 'error',
          category: 'topic',
        });
        result.isValid = false;
      }

      // Validate topic name format (no special characters that could cause issues)
      const invalidCharsRegex = /[<>:"/\\|?*\x00-\x1f]/;
      if (invalidCharsRegex.test(trimmedReference)) {
        result.errors.push({
          row: index,
          field: 'topic_name',
          message: 'Topic name contains invalid characters',
          severity: 'error',
          category: 'topic',
        });
        result.isValid = false;
      }

      // Warn about potentially problematic topic names
      if (trimmedReference.toLowerCase().includes('test') || 
          trimmedReference.toLowerCase().includes('temp')) {
        result.warnings.push({
          row: index,
          field: 'topic_name',
          message: 'Topic name appears to be temporary or for testing',
          severity: 'warning',
          category: 'topic',
        });
      }

      // Validate topic name doesn't start or end with whitespace (after initial trim check)
      if (reference !== trimmedReference) {
        result.warnings.push({
          row: index,
          field: 'topic_name',
          message: 'Topic name has leading or trailing whitespace',
          severity: 'warning',
          category: 'topic',
        });
      }
    }

    results.set(reference, result);
  });

  return results;
}

/**
 * Validate individual question content according to business rules
 * @param row CSV row data
 * @param rowIndex Row index for error reporting
 * @param config Validation configuration
 * @returns Array of validation errors
 */
export function validateQuestionContent(
  row: MultiTopicQuestionCSVRow,
  rowIndex: number,
  config: ValidationConfig = DEFAULT_VALIDATION_CONFIG
): ValidationError[] {
  const errors: ValidationError[] = [];

  if (!config.enableQuestionContentValidation) {
    return errors;
  }

  // Validate question text length
  if (row.question_text) {
    const questionText = row.question_text.trim();
    
    if (questionText.length < config.minQuestionTextLength) {
      errors.push({
        row: rowIndex + 1,
        field: 'question_text',
        message: `Question text is too short (minimum ${config.minQuestionTextLength} characters)`,
        severity: 'error',
        category: 'question',
      });
    }

    if (questionText.length > config.maxQuestionTextLength) {
      errors.push({
        row: rowIndex + 1,
        field: 'question_text',
        message: `Question text exceeds maximum length of ${config.maxQuestionTextLength} characters`,
        severity: 'error',
        category: 'question',
      });
    }

    // Check for question mark at the end (warning only)
    if (!questionText.endsWith('?')) {
      errors.push({
        row: rowIndex + 1,
        field: 'question_text',
        message: 'Question text should end with a question mark',
        severity: 'warning',
        category: 'question',
      });
    }
  }

  // Validate options are not empty and not duplicated
  const options = [row.option_a, row.option_b, row.option_c, row.option_d];
  const optionLabels = ['option_a', 'option_b', 'option_c', 'option_d'];
  const nonEmptyOptions = options.filter(opt => opt && opt.trim().length > 0);
  
  if (nonEmptyOptions.length < 4) {
    errors.push({
      row: rowIndex + 1,
      message: 'All four options (A, B, C, D) must be provided',
      severity: 'error',
      category: 'question',
    });
  }

  // Check for duplicate options
  const optionTexts = options.map(opt => opt?.trim().toLowerCase()).filter(Boolean);
  const uniqueOptions = new Set(optionTexts);
  if (optionTexts.length > 0 && uniqueOptions.size < optionTexts.length) {
    errors.push({
      row: rowIndex + 1,
      message: 'Question options should not be duplicated',
      severity: 'warning',
      category: 'question',
    });
  }

  // Validate option lengths
  options.forEach((option, index) => {
    if (option && option.trim().length > 200) {
      errors.push({
        row: rowIndex + 1,
        field: optionLabels[index],
        message: `Option ${String.fromCharCode(65 + index)} is too long (maximum 200 characters)`,
        severity: 'error',
        category: 'question',
      });
    }
  });

  // Validate difficulty
  if (row.difficulty) {
    const difficulty = row.difficulty.trim().toLowerCase();
    if (!config.allowedDifficulties.includes(difficulty)) {
      errors.push({
        row: rowIndex + 1,
        field: 'difficulty',
        message: `Invalid difficulty "${row.difficulty}". Must be one of: ${config.allowedDifficulties.join(', ')}`,
        severity: 'error',
        category: 'question',
      });
    }
  }

  // Validate explanation length
  if (row.explanation) {
    const explanation = row.explanation.trim();
    if (explanation.length < 10) {
      errors.push({
        row: rowIndex + 1,
        field: 'explanation',
        message: 'Explanation is too short (minimum 10 characters)',
        severity: 'warning',
        category: 'question',
      });
    }
    if (explanation.length > 500) {
      errors.push({
        row: rowIndex + 1,
        field: 'explanation',
        message: 'Explanation is too long (maximum 500 characters)',
        severity: 'error',
        category: 'question',
      });
    }
  }

  return errors;
}

/**
 * Detect duplicate questions across topics
 * @param csvData Array of CSV rows
 * @param config Validation configuration
 * @returns Array of duplicate question groups
 */
export function detectCrossTopicDuplicates(
  csvData: MultiTopicQuestionCSVRow[],
  config: ValidationConfig = DEFAULT_VALIDATION_CONFIG
): DuplicateQuestionGroup[] {
  if (!config.enableCrossTopicDuplicateDetection) {
    return [];
  }

  const questionMap = new Map<string, Array<{
    row: number;
    topicReference: string;
    normalizedText: string;
  }>>();

  // Normalize and group questions
  csvData.forEach((row, index) => {
    if (!row.question_text) return;

    const normalizedText = normalizeQuestionText(row.question_text);
    const topicReference = row.topic_name || row.topic_id || 'unknown';

    if (!questionMap.has(normalizedText)) {
      questionMap.set(normalizedText, []);
    }

    questionMap.get(normalizedText)!.push({
      row: index + 1,
      topicReference,
      normalizedText,
    });
  });

  // Find duplicates (questions that appear more than once)
  const duplicates: DuplicateQuestionGroup[] = [];
  
  for (const [questionText, occurrences] of questionMap) {
    if (occurrences.length > 1) {
      duplicates.push({
        questionText,
        occurrences,
      });
    }
  }

  return duplicates;
}

/**
 * Normalize question text for duplicate detection
 * @param questionText Original question text
 * @returns Normalized text for comparison
 */
function normalizeQuestionText(questionText: string): string {
  return questionText
    .trim()
    .toLowerCase()
    .replace(/[^\w\s]/g, '') // Remove punctuation
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();
}

/**
 * Perform comprehensive cross-topic validation
 * @param csvData Array of CSV rows
 * @param topicReferences Array of unique topic references
 * @param config Validation configuration
 * @returns Cross-topic validation result
 */
export function performCrossTopicValidation(
  csvData: MultiTopicQuestionCSVRow[],
  topicReferences: string[],
  config: ValidationConfig = DEFAULT_VALIDATION_CONFIG
): CrossTopicValidationResult {
  const result: CrossTopicValidationResult = {
    isValid: true,
    duplicateQuestions: [],
    topicValidations: new Map(),
    globalErrors: [],
  };

  // Validate topic references
  const topicValidations = validateTopicReferences(topicReferences, config);
  result.topicValidations = topicValidations;

  // Check if any topic validations failed
  const hasTopicErrors = Array.from(topicValidations.values())
    .some(validation => !validation.isValid);

  if (hasTopicErrors) {
    result.isValid = false;
  }

  // Detect cross-topic duplicates
  result.duplicateQuestions = detectCrossTopicDuplicates(csvData, config);

  if (result.duplicateQuestions.length > 0) {
    result.isValid = false;
    
    // Add global errors for duplicates
    result.duplicateQuestions.forEach(duplicate => {
      const topicList = duplicate.occurrences
        .map(occ => `${occ.topicReference} (row ${occ.row})`)
        .join(', ');
      
      result.globalErrors.push({
        row: 0,
        message: `Duplicate question found across topics: "${duplicate.questionText}" appears in ${topicList}`,
        severity: 'error',
        category: 'duplicate',
      });
    });
  }

  // Validate individual question content
  csvData.forEach((row, index) => {
    const questionErrors = validateQuestionContent(row, index, config);
    
    if (questionErrors.some(error => error.severity === 'error')) {
      result.isValid = false;
    }

    // Add question errors to global errors for now
    // In a real implementation, these might be grouped by topic
    result.globalErrors.push(...questionErrors);
  });

  return result;
}

/**
 * Validate topic resolution results and provide actionable error messages
 * @param topicResolution Result from topic service resolution
 * @param csvData Original CSV data for context
 * @returns Array of validation errors with suggestions
 */
export function validateTopicResolution(
  topicResolution: TopicResolutionResult,
  csvData: MultiTopicQuestionCSVRow[]
): ValidationError[] {
  const errors: ValidationError[] = [];

  // Handle missing topics
  topicResolution.missing.forEach(missingTopic => {
    const affectedRows = csvData
      .map((row, index) => ({ row, index }))
      .filter(({ row }) => 
        (row.topic_name && row.topic_name.trim() === missingTopic) ||
        (row.topic_id && row.topic_id.trim() === missingTopic)
      )
      .map(({ index }) => index + 1);

    errors.push({
      row: 0,
      field: 'topic_name',
      message: `Topic "${missingTopic}" not found. Affects rows: ${affectedRows.join(', ')}. ` +
               `Suggestion: Enable auto-create topics or create the topic manually first.`,
      severity: 'error',
      category: 'topic',
    });
  });

  // Handle topic resolution errors
  topicResolution.errors.forEach(error => {
    errors.push({
      row: 0,
      message: `Topic resolution error: ${error}. ` +
               `Suggestion: Check topic names for typos or database connectivity.`,
      severity: 'error',
      category: 'topic',
    });
  });

  // Provide success feedback for created topics
  if (topicResolution.created.length > 0) {
    errors.push({
      row: 0,
      message: `Successfully created ${topicResolution.created.length} new topics: ${topicResolution.created.join(', ')}`,
      severity: 'warning', // Using warning to indicate informational message
      category: 'topic',
    });
  }

  return errors;
}

/**
 * Group validation errors by topic for better reporting
 * @param errors Array of validation errors
 * @param csvData Original CSV data for topic context
 * @returns Map of topic reference to errors
 */
export function groupErrorsByTopic(
  errors: ValidationError[],
  csvData: MultiTopicQuestionCSVRow[]
): Map<string, ValidationError[]> {
  const errorsByTopic = new Map<string, ValidationError[]>();

  errors.forEach(error => {
    if (error.row === 0) {
      // Global errors go to a special category
      if (!errorsByTopic.has('_global')) {
        errorsByTopic.set('_global', []);
      }
      errorsByTopic.get('_global')!.push(error);
    } else {
      // Find the topic for this row
      const rowIndex = error.row - 1;
      if (rowIndex >= 0 && rowIndex < csvData.length) {
        const row = csvData[rowIndex];
        const topicReference = row.topic_name || row.topic_id || '_unknown';
        
        if (!errorsByTopic.has(topicReference)) {
          errorsByTopic.set(topicReference, []);
        }
        errorsByTopic.get(topicReference)!.push(error);
      }
    }
  });

  return errorsByTopic;
}

/**
 * Validate topic resolution failures and provide comprehensive error handling
 * @param topicReferences Array of topic references that failed resolution
 * @param csvData Original CSV data for context
 * @param autoCreateEnabled Whether auto-create was enabled
 * @returns Array of detailed validation errors with recovery suggestions
 */
export function validateTopicResolutionFailures(
  topicReferences: string[],
  csvData: MultiTopicQuestionCSVRow[],
  autoCreateEnabled: boolean
): ValidationError[] {
  const errors: ValidationError[] = [];

  topicReferences.forEach(failedReference => {
    // Find all rows affected by this failed topic
    const affectedRows = csvData
      .map((row, index) => ({ row, index }))
      .filter(({ row }) => 
        (row.topic_name && row.topic_name === failedReference) ||
        (row.topic_id && row.topic_id === failedReference)
      );

    // For empty references, we still want to report the error even if no rows match
    const rowNumbers = affectedRows.map(({ index }) => index + 1);
    
    // Analyze the failure reason and provide specific guidance
    let errorMessage: string;
    if (affectedRows.length === 0) {
      errorMessage = `Topic "${failedReference}" could not be resolved (no matching rows found).`;
    } else {
      errorMessage = `Topic "${failedReference}" could not be resolved. Affects rows: ${rowNumbers.join(', ')}.`;
    }
    
    let suggestions: string[] = [];

    // Check for common issues
    if (failedReference.trim() !== failedReference) {
      suggestions.push('Remove leading/trailing spaces from topic name');
    }

    if (failedReference.length === 0) {
      suggestions.push('Provide a valid topic name');
    } else if (failedReference.length > 100) {
      suggestions.push('Shorten topic name to 100 characters or less');
    }

    const invalidCharsRegex = /[<>:"/\\|?*\x00-\x1f]/;
    if (invalidCharsRegex.test(failedReference)) {
      suggestions.push('Remove invalid characters (<, >, :, ", /, \\, |, ?, *)');
    }

    // Add resolution suggestions based on context
    if (!autoCreateEnabled) {
      suggestions.push('Enable auto-create topics option');
      suggestions.push('Create the topic manually in the admin interface first');
    } else {
      suggestions.push('Check database connectivity and permissions');
      suggestions.push('Verify topic name follows naming conventions');
    }

    if (suggestions.length > 0) {
      errorMessage += ` Suggestions: ${suggestions.join('; ')}.`;
    }

    errors.push({
      row: 0,
      field: 'topic_name',
      message: errorMessage,
      severity: 'error',
      category: 'topic',
    });
  });

  return errors;
}

/**
 * Validate cross-topic consistency and business rules
 * @param csvData Array of CSV rows
 * @param topicReferences Array of unique topic references
 * @param config Validation configuration
 * @returns Array of cross-topic validation errors
 */
export function validateCrossTopicConsistency(
  csvData: MultiTopicQuestionCSVRow[],
  topicReferences: string[],
  config: ValidationConfig = DEFAULT_VALIDATION_CONFIG
): ValidationError[] {
  const errors: ValidationError[] = [];

  // Check for topic distribution balance
  const topicQuestionCounts = new Map<string, number>();
  
  csvData.forEach(row => {
    const topicRef = row.topic_name || row.topic_id || 'unknown';
    topicQuestionCounts.set(topicRef, (topicQuestionCounts.get(topicRef) || 0) + 1);
  });

  // Warn about topics with very few questions
  const minQuestionsPerTopic = 3;
  for (const [topicRef, count] of topicQuestionCounts) {
    if (count < minQuestionsPerTopic && topicRef !== 'unknown') {
      errors.push({
        row: 0,
        message: `Topic "${topicRef}" has only ${count} question(s). Consider adding more questions for better quiz balance.`,
        severity: 'warning',
        category: 'topic',
      });
    }
  }

  // Check for difficulty distribution within topics
  const topicDifficultyMap = new Map<string, Map<string, number>>();
  
  csvData.forEach(row => {
    const topicRef = row.topic_name || row.topic_id || 'unknown';
    const difficulty = row.difficulty?.toLowerCase() || 'medium';
    
    if (!topicDifficultyMap.has(topicRef)) {
      topicDifficultyMap.set(topicRef, new Map());
    }
    
    const difficultyMap = topicDifficultyMap.get(topicRef)!;
    difficultyMap.set(difficulty, (difficultyMap.get(difficulty) || 0) + 1);
  });

  // Warn about topics with unbalanced difficulty distribution
  for (const [topicRef, difficultyMap] of topicDifficultyMap) {
    if (topicRef === 'unknown') continue;
    
    const totalQuestions = Array.from(difficultyMap.values()).reduce((sum, count) => sum + count, 0);
    const difficulties = Array.from(difficultyMap.keys());
    
    if (totalQuestions >= 5 && difficulties.length === 1) {
      errors.push({
        row: 0,
        message: `Topic "${topicRef}" has all questions at "${difficulties[0]}" difficulty. Consider adding variety in difficulty levels.`,
        severity: 'warning',
        category: 'topic',
      });
    }
  }

  return errors;
}

/**
 * Validate individual topic reference format and business rules
 * @param topicReference Topic reference to validate
 * @param rowIndex Row index for error reporting (0-based)
 * @param config Validation configuration
 * @returns Array of validation errors for this topic reference
 */
export function validateSingleTopicReference(
  topicReference: string,
  rowIndex: number,
  config: ValidationConfig = DEFAULT_VALIDATION_CONFIG
): ValidationError[] {
  const errors: ValidationError[] = [];

  if (!config.enableTopicNameValidation) {
    return errors;
  }

  // Basic validation
  if (!topicReference || topicReference.trim().length === 0) {
    errors.push({
      row: rowIndex + 1,
      field: 'topic_name',
      message: 'Topic reference cannot be empty',
      severity: 'error',
      category: 'topic',
    });
    return errors;
  }

  const trimmedReference = topicReference.trim();

  // Length validation
  if (trimmedReference.length > config.maxTopicNameLength) {
    errors.push({
      row: rowIndex + 1,
      field: 'topic_name',
      message: `Topic name exceeds maximum length of ${config.maxTopicNameLength} characters`,
      severity: 'error',
      category: 'topic',
    });
  }

  if (trimmedReference.length < 2) {
    errors.push({
      row: rowIndex + 1,
      field: 'topic_name',
      message: 'Topic name must be at least 2 characters long',
      severity: 'error',
      category: 'topic',
    });
  }

  // Character validation
  const invalidCharsRegex = /[<>:"/\\|?*\x00-\x1f]/;
  if (invalidCharsRegex.test(trimmedReference)) {
    const invalidChars = trimmedReference.match(invalidCharsRegex)?.join(', ') || 'unknown';
    errors.push({
      row: rowIndex + 1,
      field: 'topic_name',
      message: `Topic name contains invalid characters: ${invalidChars}`,
      severity: 'error',
      category: 'topic',
    });
  }

  // Format validation
  if (trimmedReference.startsWith('.') || trimmedReference.endsWith('.')) {
    errors.push({
      row: rowIndex + 1,
      field: 'topic_name',
      message: 'Topic name cannot start or end with a period',
      severity: 'error',
      category: 'topic',
    });
  }

  // Business rule validation
  const reservedNames = ['admin', 'system', 'root', 'test', 'temp', 'default'];
  if (reservedNames.includes(trimmedReference.toLowerCase())) {
    errors.push({
      row: rowIndex + 1,
      field: 'topic_name',
      message: `Topic name "${trimmedReference}" is reserved and cannot be used`,
      severity: 'error',
      category: 'topic',
    });
  }

  // Warnings for potentially problematic names
  if (trimmedReference.toLowerCase().includes('test') || 
      trimmedReference.toLowerCase().includes('temp')) {
    errors.push({
      row: rowIndex + 1,
      field: 'topic_name',
      message: 'Topic name appears to be temporary or for testing purposes',
      severity: 'warning',
      category: 'topic',
    });
  }

  if (topicReference !== trimmedReference) {
    errors.push({
      row: rowIndex + 1,
      field: 'topic_name',
      message: 'Topic name has leading or trailing whitespace',
      severity: 'warning',
      category: 'topic',
    });
  }

  return errors;
}

/**
 * Generate comprehensive error summary for multi-topic import failures
 * @param validationResult Cross-topic validation result
 * @param topicResolution Topic resolution result
 * @returns Formatted error summary with actionable guidance
 */
export function generateErrorSummary(
  validationResult: CrossTopicValidationResult,
  topicResolution?: TopicResolutionResult
): string {
  const errorSummary: string[] = [];
  const warningSummary: string[] = [];

  // Count errors by category
  const errorCounts = {
    topic: 0,
    question: 0,
    duplicate: 0,
    format: 0,
  };

  validationResult.globalErrors.forEach(error => {
    errorCounts[error.category]++;
    
    if (error.severity === 'error') {
      errorSummary.push(`• ${error.message}`);
    } else {
      warningSummary.push(`• ${error.message}`);
    }
  });

  // Add topic validation errors
  for (const topicValidation of validationResult.topicValidations.values()) {
    topicValidation.errors.forEach(error => {
      errorCounts[error.category]++;
      errorSummary.push(`• ${error.message}`);
    });
    
    topicValidation.warnings.forEach(warning => {
      warningSummary.push(`• ${warning.message}`);
    });
  }

  let summary = '';

  if (errorSummary.length > 0) {
    summary += `❌ Found ${errorSummary.length} error(s):\n${errorSummary.join('\n')}\n\n`;
  }

  if (warningSummary.length > 0) {
    summary += `⚠️ Found ${warningSummary.length} warning(s):\n${warningSummary.join('\n')}\n\n`;
  }

  // Add resolution summary if provided
  if (topicResolution) {
    if (topicResolution.created.length > 0) {
      summary += `✅ Created ${topicResolution.created.length} new topic(s): ${topicResolution.created.join(', ')}\n`;
    }
    
    if (topicResolution.missing.length > 0) {
      summary += `❌ Could not resolve ${topicResolution.missing.length} topic(s): ${topicResolution.missing.join(', ')}\n`;
    }
  }

  // Add category breakdown
  if (Object.values(errorCounts).some(count => count > 0)) {
    summary += '\n📊 Error breakdown:\n';
    if (errorCounts.topic > 0) summary += `• Topic errors: ${errorCounts.topic}\n`;
    if (errorCounts.question > 0) summary += `• Question errors: ${errorCounts.question}\n`;
    if (errorCounts.duplicate > 0) summary += `• Duplicate errors: ${errorCounts.duplicate}\n`;
    if (errorCounts.format > 0) summary += `• Format errors: ${errorCounts.format}\n`;
  }

  return summary || '✅ No validation errors found.';
}

/**
 * Generate actionable error messages with suggestions for common issues
 * @param error Validation error
 * @returns Enhanced error message with suggestions
 */
export function generateActionableErrorMessage(error: ValidationError): string {
  let message = error.message;

  // Add suggestions based on error category and content
  switch (error.category) {
    case 'topic':
      if (error.message.includes('not found')) {
        message += ' Try enabling auto-create topics or verify the topic name spelling.';
      } else if (error.message.includes('invalid characters')) {
        message += ' Remove special characters like <, >, :, ", /, \\, |, ?, *.';
      } else if (error.message.includes('too long')) {
        message += ' Shorten the topic name to 100 characters or less.';
      } else if (error.message.includes('reserved')) {
        message += ' Choose a different topic name that is not reserved.';
      } else if (error.message.includes('too short')) {
        message += ' Use a more descriptive topic name with at least 2 characters.';
      }
      break;

    case 'question':
      if (error.message.includes('too short')) {
        message += ' Add more detail to make the question clearer.';
      } else if (error.message.includes('too long')) {
        message += ' Consider breaking into multiple questions or shortening the text.';
      } else if (error.message.includes('question mark')) {
        message += ' Add a "?" at the end of the question.';
      } else if (error.message.includes('options')) {
        message += ' Ensure all four options (A, B, C, D) are unique and meaningful.';
      } else if (error.message.includes('correct_answer')) {
        message += ' Use only A, B, C, or D for the correct answer.';
      } else if (error.message.includes('difficulty')) {
        message += ' Use only "easy", "medium", or "hard" for difficulty.';
      }
      break;

    case 'duplicate':
      message += ' Consider rephrasing one of the questions or removing the duplicate.';
      break;

    case 'format':
      message += ' Check the CSV format and ensure all required columns are present.';
      break;
  }

  return message;
}