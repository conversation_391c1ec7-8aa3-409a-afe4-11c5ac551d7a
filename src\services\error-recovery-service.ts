/**
 * Error Recovery Service
 * Provides intelligent error recovery and fallback strategies for quiz randomization failures
 */

import { supabase } from '@/integrations/supabase/client';
import { parseCorrectAnswer, parseQuestionOptions } from '@/utils/answer-validation';
import { 
  errorLogger, 
  logRandomizationError, 
  logSessionError, 
  logDatabaseError,
  type ErrorContext 
} from './error-logging-service';
import { 
  userFriendlyErrors,
  getRandomizationErrorMessage,
  getSessionErrorMessage,
  getDatabaseErrorMessage,
  type UserFriendlyError,
  type ErrorFallback
} from './user-friendly-errors';
import { systemMonitor } from './system-monitoring-service';
import type { Question, RandomizedQuestion } from './quiz-randomization-service';

export interface RecoveryStrategy {
  type: 'retry' | 'fallback' | 'graceful_degradation' | 'emergency_mode';
  description: string;
  canRecover: boolean;
  estimatedSuccessRate: number; // 0-1
  fallbackData?: any;
}

export interface RecoveryResult<T = any> {
  success: boolean;
  data?: T;
  strategy: RecoveryStrategy;
  error?: Error;
  userMessage?: string;
  shouldNotifyUser: boolean;
}

export interface EmergencyModeConfig {
  enabled: boolean;
  fallbackQuestions: Question[];
  fallbackTopics: string[];
  maxRetries: number;
  retryDelay: number; // milliseconds
}

/**
 * Error Recovery Service Class
 * Handles error recovery and fallback strategies
 */
export class ErrorRecoveryService {
  private static instance: ErrorRecoveryService;
  private emergencyConfig: EmergencyModeConfig = {
    enabled: true,
    fallbackQuestions: [],
    fallbackTopics: [],
    maxRetries: 3,
    retryDelay: 1000
  };

  private constructor() {
    this.initializeFallbackData();
  }

  static getInstance(): ErrorRecoveryService {
    if (!ErrorRecoveryService.instance) {
      ErrorRecoveryService.instance = new ErrorRecoveryService();
    }
    return ErrorRecoveryService.instance;
  }

  /**
   * Recovers from question selection failures
   */
  async recoverFromQuestionSelectionFailure(
    topicId: string,
    requestedCount: number,
    originalError: Error,
    context: ErrorContext = {}
  ): Promise<RecoveryResult<Question[]>> {
    const recoveryContext = { ...context, operation: 'question_selection_recovery' };

    try {
      // Strategy 1: Retry with reduced count
      if (requestedCount > 5) {
        const reducedCount = Math.max(5, Math.floor(requestedCount * 0.6));
        const retryStrategy: RecoveryStrategy = {
          type: 'graceful_degradation',
          description: `Retry with reduced question count (${reducedCount})`,
          canRecover: true,
          estimatedSuccessRate: 0.8
        };

        try {
          const { data: questions, error } = await supabase
            .from('questions')
            .select('*')
            .eq('topic_id', topicId)
            .limit(reducedCount);

          if (!error && questions && questions.length > 0) {
            errorLogger.logError(
              `Recovered from question selection failure using reduced count strategy`,
              undefined,
              { ...recoveryContext, metadata: { originalCount: requestedCount, recoveredCount: questions.length } },
              'info',
              'randomization'
            );

            return {
              success: true,
              data: this.shuffleArray(questions).slice(0, Math.min(reducedCount, questions.length)),
              strategy: retryStrategy,
              userMessage: `Quiz adjusted to ${questions.length} questions due to availability`,
              shouldNotifyUser: true
            };
          }
        } catch (retryError) {
          logRandomizationError('question_selection_recovery', retryError as Error, recoveryContext);
        }
      }

      // Strategy 2: Try different topic with similar content
      const fallbackStrategy = await this.tryFallbackTopic(topicId, requestedCount, recoveryContext);
      if (fallbackStrategy.success) {
        return fallbackStrategy;
      }

      // Strategy 3: Emergency mode with cached questions
      if (this.emergencyConfig.enabled && this.emergencyConfig.fallbackQuestions.length > 0) {
        const emergencyStrategy: RecoveryStrategy = {
          type: 'emergency_mode',
          description: 'Using cached fallback questions',
          canRecover: true,
          estimatedSuccessRate: 0.9,
          fallbackData: { source: 'emergency_cache' }
        };

        const fallbackQuestions = this.emergencyConfig.fallbackQuestions
          .slice(0, Math.min(requestedCount, this.emergencyConfig.fallbackQuestions.length));

        errorLogger.logError(
          `Using emergency mode fallback questions`,
          undefined,
          { ...recoveryContext, metadata: { fallbackCount: fallbackQuestions.length } },
          'warning',
          'randomization'
        );

        systemMonitor.createAlert(
          'service_degradation',
          'medium',
          'Quiz system using emergency fallback questions',
          recoveryContext
        );

        return {
          success: true,
          data: fallbackQuestions,
          strategy: emergencyStrategy,
          userMessage: 'Using practice questions while we resolve a temporary issue',
          shouldNotifyUser: true
        };
      }

      // Strategy 4: Complete failure - return error with user guidance
      const failureStrategy: RecoveryStrategy = {
        type: 'fallback',
        description: 'Unable to recover - redirect to topic selection',
        canRecover: false,
        estimatedSuccessRate: 0
      };

      const userFriendlyError = getRandomizationErrorMessage(originalError, context);

      return {
        success: false,
        strategy: failureStrategy,
        error: originalError,
        userMessage: userFriendlyError.message,
        shouldNotifyUser: true
      };

    } catch (recoveryError) {
      logRandomizationError('question_selection_recovery', recoveryError as Error, recoveryContext);
      
      const failureStrategy: RecoveryStrategy = {
        type: 'fallback',
        description: 'Recovery process failed',
        canRecover: false,
        estimatedSuccessRate: 0
      };

      return {
        success: false,
        strategy: failureStrategy,
        error: recoveryError as Error,
        userMessage: 'Unable to load quiz questions. Please try again later.',
        shouldNotifyUser: true
      };
    }
  }

  /**
   * Recovers from answer shuffling failures
   */
  recoverFromShufflingFailure(
    question: Question,
    originalError: Error,
    context: ErrorContext = {}
  ): RecoveryResult<RandomizedQuestion> {
    const recoveryContext = { ...context, operation: 'shuffling_recovery', questionId: question.id };

    try {
      // Strategy 1: Use original order without shuffling
      const originalOrderStrategy: RecoveryStrategy = {
        type: 'graceful_degradation',
        description: 'Use original answer order without shuffling',
        canRecover: true,
        estimatedSuccessRate: 0.95
      };

      try {
        const originalOptions = parseQuestionOptions(question.options || {});
        const parsedAnswer = parseCorrectAnswer(question.correct_answer, originalOptions.length);

        if (originalOptions.length >= 2 && parsedAnswer.isValid) {
          errorLogger.logError(
            `Recovered from shuffling failure using original order`,
            undefined,
            { ...recoveryContext, metadata: { recoveryStrategy: 'original_order' } },
            'info',
            'randomization'
          );

          const recoveredQuestion: RandomizedQuestion = {
            ...question,
            originalCorrectIndex: parsedAnswer.correctIndex,
            shuffledCorrectIndex: parsedAnswer.correctIndex,
            optionMapping: Array.from({ length: originalOptions.length }, (_, i) => i),
            shuffledOptions: question.options as Record<string, string>
          };

          return {
            success: true,
            data: recoveredQuestion,
            strategy: originalOrderStrategy,
            shouldNotifyUser: false // Don't notify for minor shuffling issues
          };
        }
      } catch (parseError) {
        logRandomizationError('shuffling_recovery', parseError as Error, recoveryContext);
      }

      // Strategy 2: Create safe default options
      const defaultOptionsStrategy: RecoveryStrategy = {
        type: 'emergency_mode',
        description: 'Create safe default answer options',
        canRecover: true,
        estimatedSuccessRate: 0.7,
        fallbackData: { source: 'default_options' }
      };

      const safeOptions = {
        '0': 'Option A',
        '1': 'Option B', 
        '2': 'Option C',
        '3': 'Option D'
      };

      errorLogger.logError(
        `Using emergency default options for question ${question.id}`,
        undefined,
        { ...recoveryContext, metadata: { recoveryStrategy: 'default_options' } },
        'warning',
        'randomization'
      );

      const emergencyQuestion: RandomizedQuestion = {
        ...question,
        originalCorrectIndex: 0,
        shuffledCorrectIndex: 0,
        optionMapping: [0, 1, 2, 3],
        shuffledOptions: safeOptions,
        options: safeOptions,
        correct_answer: '0'
      };

      return {
        success: true,
        data: emergencyQuestion,
        strategy: defaultOptionsStrategy,
        userMessage: 'This question has been temporarily simplified',
        shouldNotifyUser: false
      };

    } catch (recoveryError) {
      logRandomizationError('shuffling_recovery', recoveryError as Error, recoveryContext);

      const failureStrategy: RecoveryStrategy = {
        type: 'fallback',
        description: 'Unable to recover question - skip it',
        canRecover: false,
        estimatedSuccessRate: 0
      };

      return {
        success: false,
        strategy: failureStrategy,
        error: recoveryError as Error,
        userMessage: 'Skipping problematic question',
        shouldNotifyUser: false
      };
    }
  }

  /**
   * Recovers from session creation failures
   */
  async recoverFromSessionFailure(
    userId: string,
    topicId: string,
    questionsData: any,
    originalError: Error,
    context: ErrorContext = {}
  ): Promise<RecoveryResult<any>> {
    const recoveryContext = { ...context, operation: 'session_recovery' };

    try {
      // Strategy 1: Retry with exponential backoff
      for (let attempt = 1; attempt <= this.emergencyConfig.maxRetries; attempt++) {
        const retryStrategy: RecoveryStrategy = {
          type: 'retry',
          description: `Retry session creation (attempt ${attempt})`,
          canRecover: true,
          estimatedSuccessRate: Math.max(0.3, 1 - (attempt * 0.2))
        };

        try {
          await new Promise(resolve => setTimeout(resolve, this.emergencyConfig.retryDelay * attempt));

          const { data: sessionData, error } = await supabase
            .from('quiz_sessions')
            .insert({
              user_id: userId,
              topic_id: topicId,
              questions_data: questionsData,
              quiz_length: questionsData.questions?.length || 0
            })
            .select()
            .single();

          if (!error && sessionData) {
            errorLogger.logError(
              `Recovered from session creation failure on attempt ${attempt}`,
              undefined,
              { ...recoveryContext, metadata: { attempt, sessionId: sessionData.id } },
              'info',
              'session'
            );

            return {
              success: true,
              data: sessionData,
              strategy: retryStrategy,
              shouldNotifyUser: false
            };
          }
        } catch (retryError) {
          if (attempt === this.emergencyConfig.maxRetries) {
            logSessionError('session_recovery', retryError as Error, { ...recoveryContext, metadata: { finalAttempt: true } });
          }
        }
      }

      // Strategy 2: Simplified session data
      const simplifiedStrategy: RecoveryStrategy = {
        type: 'graceful_degradation',
        description: 'Create session with simplified data structure',
        canRecover: true,
        estimatedSuccessRate: 0.8
      };

      try {
        const simplifiedData = {
          questions: questionsData.questions?.map((q: any) => ({ id: q.id })) || [],
          metadata: {
            simplified: true,
            originalError: originalError.message,
            createdAt: new Date().toISOString()
          }
        };

        const { data: sessionData, error } = await supabase
          .from('quiz_sessions')
          .insert({
            user_id: userId,
            topic_id: topicId,
            questions_data: simplifiedData,
            quiz_length: simplifiedData.questions.length
          })
          .select()
          .single();

        if (!error && sessionData) {
          errorLogger.logError(
            `Recovered using simplified session data`,
            undefined,
            { ...recoveryContext, metadata: { sessionId: sessionData.id, simplified: true } },
            'warning',
            'session'
          );

          return {
            success: true,
            data: sessionData,
            strategy: simplifiedStrategy,
            userMessage: 'Quiz started with basic tracking',
            shouldNotifyUser: false
          };
        }
      } catch (simplifiedError) {
        logSessionError('session_recovery', simplifiedError as Error, recoveryContext);
      }

      // Strategy 3: In-memory session (no persistence)
      const memoryStrategy: RecoveryStrategy = {
        type: 'emergency_mode',
        description: 'Create temporary in-memory session',
        canRecover: true,
        estimatedSuccessRate: 1.0,
        fallbackData: { persistent: false }
      };

      const memorySession = {
        id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        user_id: userId,
        topic_id: topicId,
        questions_data: questionsData,
        quiz_length: questionsData.questions?.length || 0,
        created_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours
        temporary: true
      };

      errorLogger.logError(
        `Using temporary in-memory session as fallback`,
        undefined,
        { ...recoveryContext, metadata: { sessionId: memorySession.id, temporary: true } },
        'warning',
        'session'
      );

      systemMonitor.createAlert(
        'service_degradation',
        'medium',
        'Quiz sessions using temporary storage due to database issues',
        recoveryContext
      );

      return {
        success: true,
        data: memorySession,
        strategy: memoryStrategy,
        userMessage: 'Quiz started in temporary mode - progress may not be saved',
        shouldNotifyUser: true
      };

    } catch (recoveryError) {
      logSessionError('session_recovery', recoveryError as Error, recoveryContext);

      const failureStrategy: RecoveryStrategy = {
        type: 'fallback',
        description: 'Complete session creation failure',
        canRecover: false,
        estimatedSuccessRate: 0
      };

      const userFriendlyError = getSessionErrorMessage(originalError, context);

      return {
        success: false,
        strategy: failureStrategy,
        error: recoveryError as Error,
        userMessage: userFriendlyError.message,
        shouldNotifyUser: true
      };
    }
  }

  /**
   * Recovers from database connection failures
   */
  async recoverFromDatabaseFailure<T>(
    operation: string,
    retryFunction: () => Promise<T>,
    originalError: Error,
    context: ErrorContext = {}
  ): Promise<RecoveryResult<T>> {
    const recoveryContext = { ...context, operation: 'database_recovery' };

    try {
      // Strategy 1: Exponential backoff retry
      for (let attempt = 1; attempt <= this.emergencyConfig.maxRetries; attempt++) {
        const retryStrategy: RecoveryStrategy = {
          type: 'retry',
          description: `Retry database operation (attempt ${attempt})`,
          canRecover: true,
          estimatedSuccessRate: Math.max(0.2, 1 - (attempt * 0.25))
        };

        try {
          const delay = Math.min(this.emergencyConfig.retryDelay * Math.pow(2, attempt - 1), 10000);
          await new Promise(resolve => setTimeout(resolve, delay));

          const result = await retryFunction();

          errorLogger.logError(
            `Recovered from database failure on attempt ${attempt}`,
            undefined,
            { ...recoveryContext, metadata: { attempt, operation } },
            'info',
            'database'
          );

          return {
            success: true,
            data: result,
            strategy: retryStrategy,
            shouldNotifyUser: false
          };

        } catch (retryError) {
          if (attempt === this.emergencyConfig.maxRetries) {
            logDatabaseError('database_recovery', retryError as Error, { ...recoveryContext, metadata: { finalAttempt: true } });
          }
        }
      }

      // Strategy 2: Fallback to cached data if available
      const cachedData = this.getCachedData(operation, context);
      if (cachedData) {
        const cacheStrategy: RecoveryStrategy = {
          type: 'fallback',
          description: 'Using cached data as fallback',
          canRecover: true,
          estimatedSuccessRate: 0.7,
          fallbackData: { source: 'cache' }
        };

        errorLogger.logError(
          `Using cached data as fallback for database operation`,
          undefined,
          { ...recoveryContext, metadata: { operation, cacheHit: true } },
          'warning',
          'database'
        );

        return {
          success: true,
          data: cachedData,
          strategy: cacheStrategy,
          userMessage: 'Using cached data while resolving connection issues',
          shouldNotifyUser: true
        };
      }

      // Complete failure
      const failureStrategy: RecoveryStrategy = {
        type: 'fallback',
        description: 'Database operation failed completely',
        canRecover: false,
        estimatedSuccessRate: 0
      };

      const userFriendlyError = getDatabaseErrorMessage(originalError, context);

      systemMonitor.createAlert(
        'critical_errors',
        'critical',
        `Database operation "${operation}" failed after all recovery attempts`,
        recoveryContext
      );

      return {
        success: false,
        strategy: failureStrategy,
        error: originalError,
        userMessage: userFriendlyError.message,
        shouldNotifyUser: true
      };

    } catch (recoveryError) {
      logDatabaseError('database_recovery', recoveryError as Error, recoveryContext);

      const failureStrategy: RecoveryStrategy = {
        type: 'fallback',
        description: 'Recovery process failed',
        canRecover: false,
        estimatedSuccessRate: 0
      };

      return {
        success: false,
        strategy: failureStrategy,
        error: recoveryError as Error,
        userMessage: 'System temporarily unavailable. Please try again later.',
        shouldNotifyUser: true
      };
    }
  }

  /**
   * Updates emergency mode configuration
   */
  updateEmergencyConfig(config: Partial<EmergencyModeConfig>): void {
    this.emergencyConfig = { ...this.emergencyConfig, ...config };
  }

  /**
   * Gets current emergency configuration
   */
  getEmergencyConfig(): EmergencyModeConfig {
    return { ...this.emergencyConfig };
  }

  // Private methods

  private async tryFallbackTopic(
    originalTopicId: string,
    requestedCount: number,
    context: ErrorContext
  ): Promise<RecoveryResult<Question[]>> {
    try {
      // Try to find a similar topic with available questions
      const { data: topics, error } = await supabase
        .from('topics')
        .select('id, title')
        .neq('id', originalTopicId)
        .limit(5);

      if (!error && topics && topics.length > 0) {
        for (const topic of topics) {
          try {
            const { data: questions, error: questionsError } = await supabase
              .from('questions')
              .select('*')
              .eq('topic_id', topic.id)
              .limit(requestedCount);

            if (!questionsError && questions && questions.length >= Math.min(5, requestedCount)) {
              const fallbackStrategy: RecoveryStrategy = {
                type: 'fallback',
                description: `Using questions from similar topic: ${topic.title}`,
                canRecover: true,
                estimatedSuccessRate: 0.6,
                fallbackData: { fallbackTopicId: topic.id, fallbackTopicTitle: topic.title }
              };

              errorLogger.logError(
                `Using fallback topic "${topic.title}" for question selection`,
                undefined,
                { ...context, metadata: { originalTopicId, fallbackTopicId: topic.id } },
                'warning',
                'randomization'
              );

              return {
                success: true,
                data: this.shuffleArray(questions),
                strategy: fallbackStrategy,
                userMessage: `Using questions from "${topic.title}" due to availability`,
                shouldNotifyUser: true
              };
            }
          } catch (topicError) {
            // Continue to next topic
            continue;
          }
        }
      }
    } catch (error) {
      // Fallback topic search failed, continue with other strategies
    }

    return {
      success: false,
      strategy: {
        type: 'fallback',
        description: 'No suitable fallback topic found',
        canRecover: false,
        estimatedSuccessRate: 0
      },
      shouldNotifyUser: false
    };
  }

  private getCachedData(operation: string, context: ErrorContext): any {
    try {
      const cacheKey = `recovery_cache_${operation}_${context.topicId || 'global'}`;
      const cached = localStorage.getItem(cacheKey);
      if (cached) {
        const data = JSON.parse(cached);
        const age = Date.now() - data.timestamp;
        // Use cache if less than 1 hour old
        if (age < 60 * 60 * 1000) {
          return data.value;
        }
      }
    } catch (error) {
      // Cache access failed
    }
    return null;
  }

  private async initializeFallbackData(): Promise<void> {
    try {
      // Load some basic fallback questions for emergency mode
      // This would typically be done during app initialization
      this.emergencyConfig.fallbackQuestions = [
        {
          id: 'fallback_1',
          topic_id: 'general',
          question_text: 'What is the primary purpose of cybersecurity?',
          options: {
            '0': 'To protect digital assets and information',
            '1': 'To increase system performance',
            '2': 'To reduce hardware costs',
            '3': 'To improve user interface design'
          },
          correct_answer: '0',
          explanation: 'Cybersecurity primarily focuses on protecting digital assets and information from threats.',
          difficulty: 'easy',
          usage_count: 0,
          last_used: null,
          correct_answer_rate: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          created_by: null,
          is_premium: false
        }
      ];
    } catch (error) {
      console.warn('Failed to initialize fallback data:', error);
    }
  }

  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }
}

// Export singleton instance
export const errorRecovery = ErrorRecoveryService.getInstance();

// Convenience functions
export const recoverFromQuestionSelectionFailure = (
  topicId: string,
  requestedCount: number,
  error: Error,
  context?: ErrorContext
) => errorRecovery.recoverFromQuestionSelectionFailure(topicId, requestedCount, error, context);

export const recoverFromShufflingFailure = (
  question: Question,
  error: Error,
  context?: ErrorContext
) => errorRecovery.recoverFromShufflingFailure(question, error, context);

export const recoverFromSessionFailure = (
  userId: string,
  topicId: string,
  questionsData: any,
  error: Error,
  context?: ErrorContext
) => errorRecovery.recoverFromSessionFailure(userId, topicId, questionsData, error, context);