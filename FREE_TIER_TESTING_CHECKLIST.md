# Free Tier Testing Checklist

## Pre-Testing Setup

- [ ] Ensure server is running (`npm run dev` in server directory)
- [ ] Ensure client is running (`npm run dev` in root directory)
- [ ] Verify environment variables are set correctly
- [ ] Check database connection is working

## 1. UI Testing

### Pricing Section
- [ ] Navigate to the pricing section on the homepage
- [ ] Verify Basic plan shows "Free" instead of "₦998"
- [ ] Verify Basic plan subtitle shows "REQUIRES LOGIN"
- [ ] Verify Basic plan features show:
  - [ ] "Access to 4 quiz domains"
  - [ ] "400 questions weekly"
  - [ ] "Requires authentication"

### Button Behavior
- [ ] **When NOT logged in**: Basic plan button shows "Sign Up for Free"
- [ ] **When logged in**: Basic plan button shows "Already Active" (disabled)
- [ ] Clicking "Sign Up for Free" redirects to auth page

## 2. Authentication Flow Testing

### New User Registration
- [ ] Go to `/auth?tab=signup`
- [ ] Register with a new email address
- [ ] Verify email verification process works
- [ ] After email verification, check if free tier is automatically activated
- [ ] Verify user can access authenticated topics

### Existing User Login
- [ ] Go to `/auth?tab=login`
- [ ] Login with existing credentials
- [ ] Verify free tier is activated if user has no active subscription
- [ ] Verify existing paid subscribers maintain their current access

## 3. Topic Access Testing

### Public Topics (No Authentication Required)
- [ ] Access "CISSP Fundamentals" without logging in
- [ ] Verify quiz loads and works correctly

### Basic Tier Topics (Authentication Required)
Test access to these 4 domains when logged in:
- [ ] "CIA Triad: Confidentiality, Integrity, and Availability"
- [ ] "ISC2 Certification"
- [ ] "Cybersecurity Awareness Skill"
- [ ] "Network Security Fundamentals"

### Premium Topics (Paid Subscription Required)
- [ ] Try accessing premium topics with free tier account
- [ ] Verify access is denied or upgrade prompt is shown
- [ ] Test that paid subscribers can still access premium content

## 4. API Endpoint Testing

### Free Tier Activation Endpoint
Run the test script: `node test-free-tier.js`

Or test manually:
- [ ] **Valid Email Test**:
  ```bash
  curl -X POST http://localhost:3001/api/subscriptions/activate-free-tier \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>"}'
  ```
  Expected: 200 status, success: true

- [ ] **Invalid Email Test**:
  ```bash
  curl -X POST http://localhost:3001/api/subscriptions/activate-free-tier \
    -H "Content-Type: application/json" \
    -d '{"email":"invalid-email"}'
  ```
  Expected: 400 status, success: false

- [ ] **Missing Email Test**:
  ```bash
  curl -X POST http://localhost:3001/api/subscriptions/activate-free-tier \
    -H "Content-Type: application/json" \
    -d '{}'
  ```
  Expected: 400 status, success: false

## 5. Database Verification

### Check Subscription Records
- [ ] Verify new users get subscription record with:
  - [ ] plan_id: 'basic'
  - [ ] amount_paid: 0
  - [ ] is_active: true
  - [ ] Appropriate start_date and end_date

### Check User Profiles
- [ ] Verify user profiles are updated with subscription status
- [ ] Check subscription_expires_at is set correctly

## 6. Edge Cases Testing

### Multiple Activations
- [ ] Try activating free tier multiple times for same user
- [ ] Verify it doesn't create duplicate records
- [ ] Verify existing subscription is updated, not duplicated

### Paid User Scenarios
- [ ] Test user with existing paid subscription
- [ ] Verify free tier activation doesn't downgrade paid users
- [ ] Test expired paid subscription gets free tier

### Error Scenarios
- [ ] Test with invalid user email (not in auth system)
- [ ] Test with malformed requests
- [ ] Test server error handling

## 7. Performance Testing

### Load Testing
- [ ] Test multiple simultaneous free tier activations
- [ ] Verify server handles concurrent requests properly
- [ ] Check for any memory leaks or performance issues

## 8. Security Testing

### Input Validation
- [ ] Test SQL injection attempts in email field
- [ ] Test XSS attempts in email field
- [ ] Verify email sanitization works correctly

### Authentication
- [ ] Verify free tier requires actual authentication
- [ ] Test that unauthenticated users can't access basic tier content
- [ ] Verify session management works correctly

## 9. Integration Testing

### Payment Flow Integration
- [ ] Verify paid subscriptions still work correctly
- [ ] Test upgrade from free to paid tier
- [ ] Test downgrade from paid to free tier (if applicable)

### Email Integration
- [ ] Verify welcome emails are sent for free tier users
- [ ] Test email templates include free tier information

## 10. Browser Compatibility

### Cross-Browser Testing
- [ ] Test in Chrome
- [ ] Test in Firefox
- [ ] Test in Safari
- [ ] Test in Edge

### Mobile Testing
- [ ] Test responsive design on mobile devices
- [ ] Verify touch interactions work correctly
- [ ] Test mobile authentication flow

## Post-Testing Verification

### Monitoring
- [ ] Check server logs for any errors
- [ ] Verify no security warnings in browser console
- [ ] Monitor database for any anomalies

### Documentation
- [ ] Update user documentation if needed
- [ ] Verify API documentation is accurate
- [ ] Update deployment notes

## Rollback Plan

If issues are found:
- [ ] Document all issues discovered
- [ ] Prepare rollback commits if necessary
- [ ] Have database backup ready
- [ ] Plan communication to users if needed

## Success Criteria

✅ **Implementation is successful if:**
- All UI elements display correctly
- New users automatically get free tier access
- Authentication is required for basic tier content
- Paid subscriptions continue to work normally
- No security vulnerabilities introduced
- Performance remains acceptable
- All tests pass

## Notes

- Test with both new and existing user accounts
- Use different email providers for testing
- Test during different times of day for load scenarios
- Document any unexpected behavior for future reference
