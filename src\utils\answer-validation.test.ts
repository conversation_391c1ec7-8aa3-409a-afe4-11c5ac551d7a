import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { 
  parseCorrectAnswer, 
  validateAnswer, 
  parseQuestionOptions, 
  logAnswerValidation,
  safeValidateAnswer
} from './answer-validation';

describe('parseCorrectAnswer', () => {
  let consoleSpy: any;

  beforeEach(() => {
    consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleSpy.mockRestore();
  });

  describe('valid inputs', () => {
    it('should parse valid number correctly', () => {
      const result = parseCorrectAnswer(2, 4);
      expect(result.correctIndex).toBe(2);
      expect(result.isValid).toBe(true);
      expect(result.originalValue).toBe(2);
      expect(result.errorMessage).toBeUndefined();
    });

    it('should parse valid string number correctly', () => {
      const result = parseCorrectAnswer('1', 4);
      expect(result.correctIndex).toBe(1);
      expect(result.isValid).toBe(true);
      expect(result.originalValue).toBe('1');
      expect(result.errorMessage).toBeUndefined();
    });

    it('should parse string number with whitespace', () => {
      const result = parseCorrectAnswer('  3  ', 4);
      expect(result.correctIndex).toBe(3);
      expect(result.isValid).toBe(true);
    });

    it('should handle boolean values', () => {
      const result = parseCorrectAnswer(true, 4);
      expect(result.correctIndex).toBe(1);
      expect(result.isValid).toBe(true);

      const result2 = parseCorrectAnswer(false, 4);
      expect(result2.correctIndex).toBe(0);
      expect(result2.isValid).toBe(true);
    });

    it('should handle zero correctly', () => {
      const result = parseCorrectAnswer(0, 4);
      expect(result.correctIndex).toBe(0);
      expect(result.isValid).toBe(true);
    });
  });

  describe('invalid inputs with fallbacks', () => {
    it('should handle null values', () => {
      const result = parseCorrectAnswer(null, 4);
      expect(result.correctIndex).toBe(0);
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toBe('Correct answer is null or undefined');
      expect(consoleSpy).toHaveBeenCalledWith('Answer validation: Correct answer is null/undefined, defaulting to 0');
    });

    it('should handle undefined values', () => {
      const result = parseCorrectAnswer(undefined, 4);
      expect(result.correctIndex).toBe(0);
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toBe('Correct answer is null or undefined');
    });

    it('should handle NaN values', () => {
      const result = parseCorrectAnswer(NaN, 4);
      expect(result.correctIndex).toBe(0);
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toBe('Correct answer is NaN or infinite');
    });

    it('should handle Infinity values', () => {
      const result = parseCorrectAnswer(Infinity, 4);
      expect(result.correctIndex).toBe(0);
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toBe('Correct answer is NaN or infinite');
    });

    it('should handle empty string', () => {
      const result = parseCorrectAnswer('', 4);
      expect(result.correctIndex).toBe(0);
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toBe('Correct answer is empty string');
    });

    it('should handle non-numeric string', () => {
      const result = parseCorrectAnswer('abc', 4);
      expect(result.correctIndex).toBe(0);
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toBe('Cannot parse "abc" as integer');
    });

    it('should handle out of range numbers by clamping', () => {
      const result = parseCorrectAnswer(5, 4);
      expect(result.correctIndex).toBe(3); // Clamped to max valid index
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toBe('Correct answer index 5 is out of range (0-3)');
    });

    it('should handle negative numbers by clamping', () => {
      const result = parseCorrectAnswer(-1, 4);
      expect(result.correctIndex).toBe(0); // Clamped to min valid index
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toBe('Correct answer index -1 is out of range (0-3)');
    });

    it('should handle unsupported types', () => {
      const result = parseCorrectAnswer({}, 4);
      expect(result.correctIndex).toBe(0);
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toBe('Unsupported correct answer type: object');
    });

    it('should handle boolean out of range', () => {
      const result = parseCorrectAnswer(true, 1); // Only 1 option, so index 1 is out of range
      expect(result.correctIndex).toBe(0);
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toBe('Boolean value true converted to index 1 exceeds options count');
    });
  });

  describe('edge cases', () => {
    it('should handle decimal numbers by flooring', () => {
      const result = parseCorrectAnswer(2.7, 4);
      expect(result.correctIndex).toBe(2);
      expect(result.isValid).toBe(true);
    });

    it('should handle string with decimal by parsing as integer', () => {
      const result = parseCorrectAnswer('2.7', 4);
      expect(result.correctIndex).toBe(2);
      expect(result.isValid).toBe(true);
    });
  });
});

describe('validateAnswer', () => {
  let consoleSpy: any;

  beforeEach(() => {
    consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleSpy.mockRestore();
  });

  it('should validate correct answer', () => {
    const result = validateAnswer(2, 2, 4);
    expect(result.isCorrect).toBe(true);
    expect(result.selectedIndex).toBe(2);
    expect(result.correctIndex).toBe(2);
    expect(result.confidence).toBe('high');
    expect(result.warnings).toHaveLength(0);
  });

  it('should validate incorrect answer', () => {
    const result = validateAnswer(1, 2, 4);
    expect(result.isCorrect).toBe(false);
    expect(result.selectedIndex).toBe(1);
    expect(result.correctIndex).toBe(2);
    expect(result.confidence).toBe('high');
    expect(result.warnings).toHaveLength(0);
  });

  it('should handle null selected index', () => {
    const result = validateAnswer(null, 2, 4);
    expect(result.isCorrect).toBe(false);
    expect(result.selectedIndex).toBe(-1);
    expect(result.correctIndex).toBe(2);
    expect(result.confidence).toBe('high');
    expect(result.warnings).toContain('No answer selected');
  });

  it('should handle invalid selected index', () => {
    const result = validateAnswer(NaN, 2, 4);
    expect(result.isCorrect).toBe(false);
    expect(result.selectedIndex).toBe(-1);
    expect(result.correctIndex).toBe(2);
    expect(result.confidence).toBe('low');
    expect(result.warnings).toContain('Selected index is not a valid number');
  });

  it('should handle out of range selected index', () => {
    const result = validateAnswer(5, 2, 4);
    expect(result.isCorrect).toBe(false);
    expect(result.selectedIndex).toBe(5);
    expect(result.correctIndex).toBe(2);
    expect(result.confidence).toBe('low');
    expect(result.warnings).toContain('Selected index 5 is out of range (0-3)');
  });

  it('should handle invalid correct answer with low confidence', () => {
    const result = validateAnswer(0, 'invalid', 4);
    expect(result.isCorrect).toBe(true); // 0 matches default fallback
    expect(result.selectedIndex).toBe(0);
    expect(result.correctIndex).toBe(0);
    expect(result.confidence).toBe('low');
    expect(result.warnings.length).toBeGreaterThan(0);
  });

  it('should handle string correct answer with medium confidence', () => {
    const result = validateAnswer(2, '2', 4);
    expect(result.isCorrect).toBe(true);
    expect(result.selectedIndex).toBe(2);
    expect(result.correctIndex).toBe(2);
    expect(result.confidence).toBe('high');
  });
});

describe('parseQuestionOptions', () => {
  it('should parse array options', () => {
    const options = ['Option A', 'Option B', 'Option C', 'Option D'];
    const result = parseQuestionOptions(options);
    expect(result).toEqual(options);
  });

  it('should parse object options with numeric keys', () => {
    const options = { '0': 'Option A', '1': 'Option B', '2': 'Option C', '3': 'Option D' };
    const result = parseQuestionOptions(options);
    expect(result).toEqual(['Option A', 'Option B', 'Option C', 'Option D']);
  });

  it('should parse object options with unordered keys', () => {
    const options = { '2': 'Option C', '0': 'Option A', '3': 'Option D', '1': 'Option B' };
    const result = parseQuestionOptions(options);
    expect(result).toEqual(['Option A', 'Option B', 'Option C', 'Option D']);
  });

  it('should parse JSON string options', () => {
    const options = '["Option A", "Option B", "Option C", "Option D"]';
    const result = parseQuestionOptions(options);
    expect(result).toEqual(['Option A', 'Option B', 'Option C', 'Option D']);
  });

  it('should parse JSON object string', () => {
    const options = '{"0": "Option A", "1": "Option B", "2": "Option C", "3": "Option D"}';
    const result = parseQuestionOptions(options);
    expect(result).toEqual(['Option A', 'Option B', 'Option C', 'Option D']);
  });

  it('should handle null options', () => {
    const result = parseQuestionOptions(null);
    expect(result).toEqual(['Option not available']);
  });

  it('should handle undefined options', () => {
    const result = parseQuestionOptions(undefined);
    expect(result).toEqual(['Option not available']);
  });

  it('should handle array with null values', () => {
    const options = ['Option A', null, 'Option C', undefined];
    const result = parseQuestionOptions(options);
    expect(result).toEqual(['Option A', 'Option 2', 'Option C', 'Option 4']);
  });

  it('should handle non-string array values', () => {
    const options = ['Option A', 123, true, { text: 'Option D' }];
    const result = parseQuestionOptions(options);
    expect(result).toEqual(['Option A', '123', 'true', '[object Object]']);
  });

  it('should handle invalid JSON string', () => {
    const options = 'invalid json string';
    const result = parseQuestionOptions(options);
    expect(result).toEqual(['invalid json string']);
  });

  it('should handle object with null values', () => {
    const options = { '0': 'Option A', '1': null, '2': 'Option C', '3': undefined };
    const result = parseQuestionOptions(options);
    expect(result).toEqual(['Option A', 'Option 1', 'Option C', 'Option 3']);
  });

  it('should handle unsupported types', () => {
    const result = parseQuestionOptions(123);
    expect(result).toEqual(['Option not available']);
  });
});

describe('safeValidateAnswer', () => {
  let consoleWarnSpy: any;
  let consoleErrorSpy: any;

  beforeEach(() => {
    consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
    consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleWarnSpy.mockRestore();
    consoleErrorSpy.mockRestore();
  });

  it('should use normal validation for valid inputs', () => {
    const result = safeValidateAnswer(2, 2, 2, 4);
    expect(result.isCorrect).toBe(true);
    expect(result.confidence).toBe('high');
    expect(result.warnings).toHaveLength(0);
  });

  it('should fall back to index comparison for invalid correct answer', () => {
    const result = safeValidateAnswer(1, 'invalid', 1, 4);
    expect(result.isCorrect).toBe(true);
    expect(result.confidence).toBe('medium');
    expect(result.warnings).toContain('Using fallback validation due to parsing issues');
  });

  it('should handle null selected index with fallback', () => {
    const result = safeValidateAnswer(null, 'invalid', 2, 4);
    expect(result.isCorrect).toBe(false);
    expect(result.selectedIndex).toBe(-1);
    expect(result.correctIndex).toBe(0); // Falls back to 0 due to invalid correct answer
    expect(result.confidence).toBe('high'); // validateAnswer handles null correctly with high confidence
    expect(result.warnings).toContain('No answer selected');
  });

  it('should handle edge case with matching indices', () => {
    // Test a case where the fallback logic works correctly
    const result = safeValidateAnswer(1, null, 1, 4);
    expect(result.isCorrect).toBe(true);
    expect(result.selectedIndex).toBe(1);
    expect(result.correctIndex).toBe(1);
    expect(result.confidence).toBe('medium');
    expect(result.warnings).toContain('Using fallback validation due to parsing issues');
  });
});

describe('logAnswerValidation', () => {
  let consoleLogSpy: any;
  let consoleWarnSpy: any;

  beforeEach(() => {
    consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleLogSpy.mockRestore();
    consoleWarnSpy.mockRestore();
  });

  it('should log successful validation', () => {
    const validationResult = {
      isCorrect: true,
      selectedIndex: 2,
      correctIndex: 2,
      confidence: 'high' as const,
      warnings: []
    };

    logAnswerValidation('q1', 2, 2, validationResult);

    expect(consoleLogSpy).toHaveBeenCalledWith(
      'Answer validation successful:',
      expect.objectContaining({
        questionId: 'q1',
        originalCorrectAnswer: 2,
        selectedIndex: 2,
        validationResult,
        timestamp: expect.any(String)
      })
    );
    expect(consoleWarnSpy).not.toHaveBeenCalled();
  });

  it('should warn for validation with warnings', () => {
    const validationResult = {
      isCorrect: false,
      selectedIndex: 1,
      correctIndex: 2,
      confidence: 'medium' as const,
      warnings: ['Some warning']
    };

    logAnswerValidation('q1', 'invalid', 1, validationResult);

    expect(consoleWarnSpy).toHaveBeenCalledWith(
      'Answer validation issues detected:',
      expect.objectContaining({
        questionId: 'q1',
        originalCorrectAnswer: 'invalid',
        selectedIndex: 1,
        validationResult,
        timestamp: expect.any(String)
      })
    );
    expect(consoleLogSpy).not.toHaveBeenCalled();
  });

  it('should warn for low confidence validation', () => {
    const validationResult = {
      isCorrect: true,
      selectedIndex: 0,
      correctIndex: 0,
      confidence: 'low' as const,
      warnings: []
    };

    logAnswerValidation('q1', null, 0, validationResult);

    expect(consoleWarnSpy).toHaveBeenCalled();
    expect(consoleLogSpy).not.toHaveBeenCalled();
  });
});