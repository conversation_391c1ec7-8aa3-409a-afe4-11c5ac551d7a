/**
 * Integration tests for Question Pool Analytics
 * Tests the complete analytics workflow
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import QuestionPoolAnalyticsDashboard from '../QuestionPoolAnalyticsDashboard';
import QuestionPoolAnalyticsService from '@/services/question-pool-analytics-service';

// Mock the analytics service
vi.mock('@/services/question-pool-analytics-service');

// Mock the supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        order: vi.fn(() => Promise.resolve({
          data: [
            { id: 'topic-1', title: 'Topic 1' },
            { id: 'topic-2', title: 'Topic 2' }
          ],
          error: null
        }))
      }))
    }))
  }
}));

// Mock the toast hook
const mockToast = vi.fn();
vi.mock('@/components/ui/use-toast', () => ({
  useToast: () => ({ toast: mockToast })
}));

const mockAnalyticsService = vi.mocked(QuestionPoolAnalyticsService);

describe('Question Pool Analytics Integration', () => {
  const renderComponent = () => {
    return render(
      <BrowserRouter>
        <QuestionPoolAnalyticsDashboard />
      </BrowserRouter>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock successful analytics data
    mockAnalyticsService.getAnalyticsSummary.mockResolvedValue({
      totalQuestions: 100,
      questionsUsed: 75,
      questionsNeverUsed: 25,
      questionsNeedingReview: 8,
      topicsWithInsufficientQuestions: 3,
      recentActivity: {
        weeklyAttempts: 150,
        weeklyCorrectRate: 72.5
      },
      averageUsageCount: 5,
      averageCorrectRate: 68.2
    });

    mockAnalyticsService.getContentRecommendations.mockResolvedValue([]);
    mockAnalyticsService.getQuestionsNeedingReview.mockResolvedValue([]);
  });

  it('should render analytics dashboard successfully', async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.getByText('Question Pool Analytics')).toBeInTheDocument();
    });

    // Verify summary cards are displayed
    expect(screen.getByText('Total Questions')).toBeInTheDocument();
    expect(screen.getByText('100')).toBeInTheDocument();
    expect(screen.getByText('Questions Used')).toBeInTheDocument();
    expect(screen.getByText('75')).toBeInTheDocument();
  });

  it('should call analytics service methods on load', async () => {
    renderComponent();

    await waitFor(() => {
      expect(mockAnalyticsService.getAnalyticsSummary).toHaveBeenCalled();
    });

    expect(mockAnalyticsService.getContentRecommendations).toHaveBeenCalled();
    expect(mockAnalyticsService.getQuestionsNeedingReview).toHaveBeenCalled();
  });

  it('should display success toast when data loads', async () => {
    renderComponent();

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: "Analytics loaded",
        description: "Question pool analytics data has been refreshed.",
      });
    });
  });

  it('should handle service errors gracefully', async () => {
    mockAnalyticsService.getAnalyticsSummary.mockRejectedValue(
      new Error('Service unavailable')
    );

    renderComponent();

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: "Error loading analytics",
        description: "Failed to load analytics data. Please try again.",
        variant: "destructive",
      });
    });
  });
});