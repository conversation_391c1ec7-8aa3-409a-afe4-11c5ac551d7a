/**
 * User-Friendly Error Messages Service
 * Converts technical errors into user-friendly messages with actionable guidance
 */

export interface UserFriendlyError {
  title: string;
  message: string;
  action?: string;
  severity: 'error' | 'warning' | 'info';
  canRetry: boolean;
  retryDelay?: number; // seconds
  helpUrl?: string;
}

export interface ErrorFallback {
  type: 'redirect' | 'retry' | 'fallback_content' | 'graceful_degradation';
  data?: any;
}

/**
 * User-Friendly Error Service Class
 * Provides user-friendly error messages and fallback strategies
 */
export class UserFriendlyErrorService {
  private static instance: UserFriendlyErrorService;

  private constructor() {}

  static getInstance(): UserFriendlyErrorService {
    if (!UserFriendlyErrorService.instance) {
      UserFriendlyErrorService.instance = new UserFriendlyErrorService();
    }
    return UserFriendlyErrorService.instance;
  }

  /**
   * Converts randomization errors to user-friendly messages
   */
  getRandomizationErrorMessage(error: Error, context: any = {}): UserFriendlyError {
    const errorMessage = error.message.toLowerCase();

    // No questions available
    if (errorMessage.includes('no questions found') || errorMessage.includes('no questions available')) {
      return {
        title: 'No Questions Available',
        message: 'This quiz topic doesn\'t have any questions yet. Please try a different topic or check back later.',
        action: 'Browse other topics',
        severity: 'error',
        canRetry: false,
        helpUrl: '/quizzes'
      };
    }

    // Insufficient questions for requested length
    if (errorMessage.includes('only') && errorMessage.includes('questions')) {
      const availableCount = this.extractNumberFromError(errorMessage);
      return {
        title: 'Limited Questions Available',
        message: `This topic has only ${availableCount || 'a few'} questions available. We'll create a shorter quiz for you.`,
        action: 'Continue with available questions',
        severity: 'warning',
        canRetry: true,
        retryDelay: 0
      };
    }

    // Database connection issues
    if (errorMessage.includes('failed to fetch') || errorMessage.includes('network')) {
      return {
        title: 'Connection Issue',
        message: 'We\'re having trouble connecting to our servers. Please check your internet connection and try again.',
        action: 'Retry',
        severity: 'error',
        canRetry: true,
        retryDelay: 3
      };
    }

    // Question selection timeout
    if (errorMessage.includes('timeout') || errorMessage.includes('took too long')) {
      return {
        title: 'Loading Taking Too Long',
        message: 'Question selection is taking longer than expected. This might be due to high server load.',
        action: 'Try again',
        severity: 'warning',
        canRetry: true,
        retryDelay: 5
      };
    }

    // Invalid topic ID
    if (errorMessage.includes('topic') && (errorMessage.includes('not found') || errorMessage.includes('invalid'))) {
      return {
        title: 'Quiz Not Found',
        message: 'The requested quiz topic could not be found. It may have been moved or removed.',
        action: 'Browse available quizzes',
        severity: 'error',
        canRetry: false,
        helpUrl: '/quizzes'
      };
    }

    // Generic randomization error
    return {
      title: 'Quiz Setup Issue',
      message: 'We encountered an issue while preparing your quiz. Our team has been notified.',
      action: 'Try again',
      severity: 'error',
      canRetry: true,
      retryDelay: 2
    };
  }

  /**
   * Converts session management errors to user-friendly messages
   */
  getSessionErrorMessage(error: Error, context: any = {}): UserFriendlyError {
    const errorMessage = error.message.toLowerCase();

    // Session expired
    if (errorMessage.includes('expired') || errorMessage.includes('session not found')) {
      return {
        title: 'Quiz Session Expired',
        message: 'Your quiz session has expired for security reasons. You can start a new quiz.',
        action: 'Start new quiz',
        severity: 'warning',
        canRetry: true,
        retryDelay: 0
      };
    }

    // Session creation failed
    if (errorMessage.includes('failed to create') || errorMessage.includes('session creation')) {
      return {
        title: 'Unable to Start Quiz',
        message: 'We couldn\'t start your quiz session. This might be a temporary issue.',
        action: 'Try again',
        severity: 'error',
        canRetry: true,
        retryDelay: 3
      };
    }

    // Session validation failed
    if (errorMessage.includes('invalid session') || errorMessage.includes('validation failed')) {
      return {
        title: 'Session Issue',
        message: 'There was an issue with your quiz session. Starting a fresh session will resolve this.',
        action: 'Restart quiz',
        severity: 'warning',
        canRetry: true,
        retryDelay: 0
      };
    }

    // Generic session error
    return {
      title: 'Session Error',
      message: 'We encountered an issue with your quiz session. Please try starting a new quiz.',
      action: 'Start new quiz',
      severity: 'error',
      canRetry: true,
      retryDelay: 1
    };
  }

  /**
   * Converts validation errors to user-friendly messages
   */
  getValidationErrorMessage(message: string, context: any = {}): UserFriendlyError {
    const lowerMessage = message.toLowerCase();

    // Answer parsing issues
    if (lowerMessage.includes('correct answer') && lowerMessage.includes('parse')) {
      return {
        title: 'Question Format Issue',
        message: 'This question has a formatting issue, but we\'ve applied a fix so you can continue.',
        action: 'Continue quiz',
        severity: 'info',
        canRetry: false
      };
    }

    // Option mapping issues
    if (lowerMessage.includes('option') && (lowerMessage.includes('mapping') || lowerMessage.includes('shuffle'))) {
      return {
        title: 'Display Issue Fixed',
        message: 'We detected and corrected a display issue with this question\'s options.',
        action: 'Continue',
        severity: 'info',
        canRetry: false
      };
    }

    // Invalid question data
    if (lowerMessage.includes('invalid') && lowerMessage.includes('question')) {
      return {
        title: 'Question Data Issue',
        message: 'This question has invalid data. We\'ve skipped it and will review it for future improvement.',
        action: 'Continue to next question',
        severity: 'warning',
        canRetry: false
      };
    }

    // Generic validation error
    return {
      title: 'Validation Issue',
      message: 'We detected and corrected a minor issue with this question.',
      action: 'Continue',
      severity: 'info',
      canRetry: false
    };
  }

  /**
   * Converts database errors to user-friendly messages
   */
  getDatabaseErrorMessage(error: Error, context: any = {}): UserFriendlyError {
    const errorMessage = error.message.toLowerCase();

    // Connection timeout
    if (errorMessage.includes('timeout') || errorMessage.includes('connection')) {
      return {
        title: 'Server Busy',
        message: 'Our servers are experiencing high traffic. Please wait a moment and try again.',
        action: 'Retry',
        severity: 'warning',
        canRetry: true,
        retryDelay: 5
      };
    }

    // Permission denied
    if (errorMessage.includes('permission') || errorMessage.includes('unauthorized')) {
      return {
        title: 'Access Issue',
        message: 'You don\'t have permission to access this content. Please sign in or check your subscription.',
        action: 'Sign in',
        severity: 'error',
        canRetry: false,
        helpUrl: '/auth'
      };
    }

    // Data not found
    if (errorMessage.includes('not found') || errorMessage.includes('no data')) {
      return {
        title: 'Content Not Available',
        message: 'The requested content is not available. It may have been moved or removed.',
        action: 'Go back',
        severity: 'error',
        canRetry: false
      };
    }

    // Generic database error
    return {
      title: 'Data Access Issue',
      message: 'We\'re having trouble accessing the quiz data. Please try again in a moment.',
      action: 'Retry',
      severity: 'error',
      canRetry: true,
      retryDelay: 3
    };
  }

  /**
   * Gets fallback strategy for insufficient question pools
   */
  getInsufficientQuestionsStrategy(
    availableQuestions: number,
    requestedLength: number,
    topicTitle: string
  ): ErrorFallback {
    // No questions at all
    if (availableQuestions === 0) {
      return {
        type: 'redirect',
        data: {
          url: '/quizzes',
          message: 'No questions available for this topic'
        }
      };
    }

    // Very few questions (less than 5)
    if (availableQuestions < 5) {
      return {
        type: 'fallback_content',
        data: {
          message: `This topic has only ${availableQuestions} question(s). Consider studying the learning materials first.`,
          alternativeAction: 'View learning materials',
          alternativeUrl: `/learning-materials?topic=${encodeURIComponent(topicTitle)}`
        }
      };
    }

    // Some questions but less than requested
    if (availableQuestions < requestedLength) {
      return {
        type: 'graceful_degradation',
        data: {
          adjustedLength: availableQuestions,
          message: `Adjusted quiz length to ${availableQuestions} questions (all available questions for this topic).`
        }
      };
    }

    // Sufficient questions
    return {
      type: 'retry',
      data: {
        message: 'Sufficient questions available, retrying...'
      }
    };
  }

  /**
   * Gets recovery suggestions based on error type and context
   */
  getRecoverySuggestions(errorType: string, context: any = {}): string[] {
    const suggestions: string[] = [];

    switch (errorType) {
      case 'randomization':
        suggestions.push('Try refreshing the page');
        suggestions.push('Select a different quiz topic');
        suggestions.push('Try a shorter quiz length');
        if (context.topicId) {
          suggestions.push('Check if this topic has enough questions');
        }
        break;

      case 'session':
        suggestions.push('Start a new quiz session');
        suggestions.push('Clear your browser cache');
        suggestions.push('Try signing out and back in');
        break;

      case 'validation':
        suggestions.push('Continue with the quiz (issue has been auto-corrected)');
        suggestions.push('Report this question for review');
        break;

      case 'database':
        suggestions.push('Check your internet connection');
        suggestions.push('Try again in a few minutes');
        suggestions.push('Contact support if the issue persists');
        break;

      case 'insufficient_questions':
        suggestions.push('Try a shorter quiz length');
        suggestions.push('Study learning materials for this topic');
        suggestions.push('Choose a different topic with more questions');
        break;

      default:
        suggestions.push('Refresh the page and try again');
        suggestions.push('Contact support if the issue continues');
    }

    return suggestions;
  }

  /**
   * Determines if an error should trigger a user notification
   */
  shouldNotifyUser(errorLevel: string, errorCategory: string): boolean {
    // Always notify for errors
    if (errorLevel === 'error') return true;

    // Notify for warnings in critical categories
    if (errorLevel === 'warning' && ['randomization', 'session'].includes(errorCategory)) {
      return true;
    }

    // Don't notify for info-level validation issues (they're auto-corrected)
    return false;
  }

  /**
   * Gets appropriate toast configuration for an error
   */
  getToastConfig(userFriendlyError: UserFriendlyError): {
    title: string;
    description: string;
    variant: 'default' | 'destructive';
    duration?: number;
  } {
    return {
      title: userFriendlyError.title,
      description: userFriendlyError.message,
      variant: userFriendlyError.severity === 'error' ? 'destructive' : 'default',
      duration: userFriendlyError.severity === 'info' ? 3000 : 5000
    };
  }

  // Private helper methods

  private extractNumberFromError(errorMessage: string): number | null {
    const match = errorMessage.match(/(\d+)/);
    return match ? parseInt(match[1], 10) : null;
  }
}

// Export singleton instance
export const userFriendlyErrors = UserFriendlyErrorService.getInstance();

// Convenience functions
export const getRandomizationErrorMessage = (error: Error, context?: any) => {
  return userFriendlyErrors.getRandomizationErrorMessage(error, context);
};

export const getSessionErrorMessage = (error: Error, context?: any) => {
  return userFriendlyErrors.getSessionErrorMessage(error, context);
};

export const getValidationErrorMessage = (message: string, context?: any) => {
  return userFriendlyErrors.getValidationErrorMessage(message, context);
};

export const getDatabaseErrorMessage = (error: Error, context?: any) => {
  return userFriendlyErrors.getDatabaseErrorMessage(error, context);
};

export const getInsufficientQuestionsStrategy = (available: number, requested: number, topic: string) => {
  return userFriendlyErrors.getInsufficientQuestionsStrategy(available, requested, topic);
};