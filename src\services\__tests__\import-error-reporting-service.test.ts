import { describe, it, expect, beforeEach } from 'vitest';
import { ImportErrorReportingService } from '../import-error-reporting-service';
import type {
  MultiTopicImportResult,
  TopicImportResult,
  BatchImportResult,
  BatchImportError,
  TopicBatchResult,
  ValidatedQuestion
} from '@/utils/csv-import';

describe('ImportErrorReportingService', () => {
  let service: ImportErrorReportingService;

  beforeEach(() => {
    service = new ImportErrorReportingService();
  });

  const createMockValidatedQuestion = (overrides: Partial<ValidatedQuestion> = {}): ValidatedQuestion => ({
    id: 'question-1',
    topic_id: 'topic-1',
    question_text: 'Test question?',
    options: {
      A: 'Option A',
      B: 'Option B',
      C: 'Option C',
      D: 'Option D',
    },
    correct_answer: 'A',
    explanation: 'Test explanation',
    difficulty: 'medium',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    ...overrides,
  });

  const createMockTopicResult = (overrides: Partial<TopicImportResult> = {}): TopicImportResult => ({
    topicId: 'topic-1',
    topicName: 'Test Topic',
    validQuestions: [createMockValidatedQuestion()],
    errors: [],
    isNewTopic: false,
    ...overrides,
  });

  const createMockImportResult = (overrides: Partial<MultiTopicImportResult> = {}): MultiTopicImportResult => {
    const topicResults = new Map<string, TopicImportResult>();
    topicResults.set('topic-1', createMockTopicResult());

    return {
      success: true,
      totalRows: 5,
      topicResults,
      globalErrors: [],
      newTopicsCreated: [],
      ...overrides,
    };
  };

  const createMockBatchError = (overrides: Partial<BatchImportError> = {}): BatchImportError => ({
    type: 'question-insertion',
    topicId: 'topic-1',
    topicName: 'Test Topic',
    message: 'Test batch error',
    ...overrides,
  });

  const createMockTopicBatchResult = (overrides: Partial<TopicBatchResult> = {}): TopicBatchResult => ({
    topicId: 'topic-1',
    topicName: 'Test Topic',
    questionsImported: 3,
    questionsFailed: 1,
    isNewTopic: false,
    errors: [],
    duration: 1000,
    ...overrides,
  });

  const createMockBatchResult = (overrides: Partial<BatchImportResult> = {}): BatchImportResult => {
    const topicResults = new Map<string, TopicBatchResult>();
    topicResults.set('topic-1', createMockTopicBatchResult());

    return {
      success: true,
      totalTopicsProcessed: 1,
      totalQuestionsImported: 3,
      topicsCreated: [],
      topicResults,
      errors: [],
      duration: 5000,
      ...overrides,
    };
  };

  describe('generateErrorReport', () => {
    it('generates report from import result only', () => {
      const importResult = createMockImportResult({
        globalErrors: [{ row: 1, message: 'Global error' }],
        topicResults: new Map([
          ['topic-1', createMockTopicResult({
            errors: [{ row: 2, message: 'Topic error' }],
          })],
        ]),
      });

      const report = service.generateErrorReport(importResult);

      expect(report.importResult).toBe(importResult);
      expect(report.globalErrors).toHaveLength(1);
      expect(report.globalErrors[0].message).toBe('Global error');
      expect(report.errorsByTopic.size).toBe(1);
      expect(report.errorsByTopic.get('topic-1')?.errors).toHaveLength(1);
    });

    it('generates report from batch result only', () => {
      const batchResult = createMockBatchResult({
        errors: [createMockBatchError({ message: 'Batch error' })],
      });

      const report = service.generateErrorReport(undefined, batchResult);

      expect(report.batchResult).toBe(batchResult);
      expect(report.errorsByTopic.size).toBe(1);
      expect(report.errorsByTopic.get('topic-1')?.errors).toHaveLength(1);
    });

    it('combines import and batch results', () => {
      const importResult = createMockImportResult({
        globalErrors: [{ row: 1, message: 'Import error' }],
      });

      const batchResult = createMockBatchResult({
        errors: [createMockBatchError({ message: 'Batch error' })],
      });

      const report = service.generateErrorReport(importResult, batchResult);

      expect(report.importResult).toBe(importResult);
      expect(report.batchResult).toBe(batchResult);
      expect(report.globalErrors).toHaveLength(1);
      expect(report.errorsByTopic.size).toBe(1);
    });

    it('generates statistics correctly', () => {
      const importResult = createMockImportResult({
        totalRows: 10,
        newTopicsCreated: ['New Topic'],
      });

      const batchResult = createMockBatchResult({
        totalQuestionsImported: 8,
        totalTopicsProcessed: 2,
        duration: 5000,
      });

      const report = service.generateErrorReport(importResult, batchResult);

      expect(report.statistics.totalRows).toBe(10);
      expect(report.statistics.totalTopics).toBe(2);
      expect(report.statistics.newTopicsCreated).toBe(1);
      expect(report.statistics.questionsImported).toBe(8);
      expect(report.statistics.duration).toBe(5000);
      expect(report.statistics.successRate).toBeCloseTo(0.89, 2); // 8/9 (8 imported + 1 failed)
    });

    it('generates suggestions for topic errors', () => {
      const importResult = createMockImportResult({
        globalErrors: [
          { row: 1, message: 'Topic "Missing Topic" not found' },
          { row: 2, message: 'Topic "Another Missing" not found' },
        ],
      });

      const report = service.generateErrorReport(importResult);

      const autoCreateSuggestion = report.suggestions.find(s => 
        s.title === 'Enable Auto-Create Topics'
      );
      expect(autoCreateSuggestion).toBeDefined();
      expect(autoCreateSuggestion?.priority).toBe('high');
      expect(autoCreateSuggestion?.category).toBe('fix-required');
    });

    it('generates suggestions for format errors', () => {
      const importResult = createMockImportResult({
        globalErrors: [
          { row: 1, message: 'Invalid CSV format' },
          { row: 2, message: 'Missing column header' },
          { row: 3, message: 'Malformed row' },
        ],
      });

      const report = service.generateErrorReport(importResult);

      const formatSuggestion = report.suggestions.find(s => 
        s.title === 'Check CSV Format'
      );
      expect(formatSuggestion).toBeDefined();
      expect(formatSuggestion?.priority).toBe('high');
    });

    it('generates suggestions for duplicate errors', () => {
      const importResult = createMockImportResult({
        globalErrors: [
          { row: 1, message: 'Duplicate question found across topics' },
        ],
      });

      const report = service.generateErrorReport(importResult);

      const duplicateSuggestion = report.suggestions.find(s => 
        s.title === 'Review Duplicate Questions'
      );
      expect(duplicateSuggestion).toBeDefined();
      expect(duplicateSuggestion?.priority).toBe('medium');
    });

    it('generates suggestions for low success rate', () => {
      const batchResult = createMockBatchResult({
        totalQuestionsImported: 2,
        topicResults: new Map([
          ['topic-1', createMockTopicBatchResult({
            questionsImported: 2,
            questionsFailed: 8, // Low success rate
          })],
        ]),
      });

      const report = service.generateErrorReport(undefined, batchResult);

      const successRateSuggestion = report.suggestions.find(s => 
        s.title === 'Improve Success Rate'
      );
      expect(successRateSuggestion).toBeDefined();
      expect(successRateSuggestion?.priority).toBe('medium');
    });

    it('generates recovery options', () => {
      const importResult = createMockImportResult({
        globalErrors: [{ row: 1, message: 'Topic not found' }],
      });

      const batchResult = createMockBatchResult({
        totalQuestionsImported: 5,
        topicResults: new Map([
          ['topic-1', createMockTopicBatchResult({
            questionsImported: 5,
            questionsFailed: 2,
          })],
        ]),
      });

      const report = service.generateErrorReport(importResult, batchResult);

      expect(report.recoveryOptions.length).toBeGreaterThan(0);
      
      const retryOption = report.recoveryOptions.find(o => 
        o.title === 'Retry with Auto-Create Topics'
      );
      expect(retryOption).toBeDefined();

      const keepSuccessfulOption = report.recoveryOptions.find(o => 
        o.title === 'Keep Successful Imports'
      );
      expect(keepSuccessfulOption).toBeDefined();

      const startOverOption = report.recoveryOptions.find(o => 
        o.title === 'Start Over'
      );
      expect(startOverOption).toBeDefined();
      expect(startOverOption?.destructive).toBe(true);
    });
  });

  describe('error severity determination', () => {
    it('classifies critical errors correctly', () => {
      const importResult = createMockImportResult({
        globalErrors: [
          { row: 1, message: 'Failed to parse CSV' },
          { row: 2, message: 'Invalid format detected' },
          { row: 3, message: 'Missing required field' },
        ],
      });

      const report = service.generateErrorReport(importResult);

      report.globalErrors.forEach(error => {
        expect(error.severity).toBe('error');
      });
    });

    it('classifies warnings correctly', () => {
      const importResult = createMockImportResult({
        globalErrors: [
          { row: 1, message: 'Warning: question should end with question mark' },
          { row: 2, message: 'Consider adding more detail' },
          { row: 3, message: 'Recommend using standard format' },
        ],
      });

      const report = service.generateErrorReport(importResult);

      report.globalErrors.forEach(error => {
        expect(error.severity).toBe('warning');
      });
    });

    it('classifies info messages correctly', () => {
      const importResult = createMockImportResult({
        globalErrors: [
          { row: 1, message: 'Successfully created new topic' },
          { row: 2, message: 'Import completed successfully' },
        ],
      });

      const report = service.generateErrorReport(importResult);

      report.globalErrors.forEach(error => {
        expect(error.severity).toBe('info');
      });
    });
  });

  describe('error suggestions', () => {
    it('generates topic-specific suggestions', () => {
      const importResult = createMockImportResult({
        globalErrors: [
          { row: 1, message: 'Topic "Test" not found' },
          { row: 2, message: 'Topic name contains invalid characters' },
          { row: 3, message: 'Topic name is too long' },
          { row: 4, message: 'Topic name is reserved' },
        ],
      });

      const report = service.generateErrorReport(importResult);

      const suggestions = report.globalErrors.map(e => e.suggestion).filter(Boolean);
      expect(suggestions).toContain('Enable auto-create topics or create the topic manually first.');
      expect(suggestions).toContain('Remove special characters like <, >, :, ", /, \\, |, ?, * from topic names.');
      expect(suggestions).toContain('Shorten topic names to 100 characters or less.');
      expect(suggestions).toContain('Choose a different topic name that is not reserved by the system.');
    });

    it('generates question-specific suggestions', () => {
      const importResult = createMockImportResult({
        topicResults: new Map([
          ['topic-1', createMockTopicResult({
            errors: [
              { row: 1, message: 'Question text is too short' },
              { row: 2, message: 'Question text is too long' },
              { row: 3, message: 'Invalid correct_answer format' },
              { row: 4, message: 'Missing required options' },
              { row: 5, message: 'Invalid difficulty level' },
            ],
          })],
        ]),
      });

      const report = service.generateErrorReport(importResult);

      const topicErrors = report.errorsByTopic.get('topic-1');
      const suggestions = topicErrors?.errors.map(e => e.suggestion).filter(Boolean) || [];
      
      expect(suggestions).toContain('Add more detail to make the question clearer (minimum 10 characters).');
      expect(suggestions).toContain('Shorten the question or break it into multiple questions.');
      expect(suggestions).toContain('Use only A, B, C, or D for the correct answer field.');
      expect(suggestions).toContain('Ensure all four options (A, B, C, D) are provided and unique.');
      expect(suggestions).toContain('Use only "easy", "medium", or "hard" for difficulty level.');
    });
  });

  describe('actionable error detection', () => {
    it('identifies actionable errors correctly', () => {
      const importResult = createMockImportResult({
        globalErrors: [
          { row: 1, message: 'Topic not found' }, // Actionable
          { row: 2, message: 'Invalid CSV format' }, // Actionable
          { row: 3, message: 'Database connection failed' }, // Not actionable (system error)
        ],
      });

      const report = service.generateErrorReport(importResult);

      expect(report.globalErrors[0].actionable).toBe(true);
      expect(report.globalErrors[1].actionable).toBe(true);
      // Database errors would be classified as system errors and not actionable
    });
  });

  describe('generateDownloadableErrorReport', () => {
    it('generates CSV format error report', () => {
      const importResult = createMockImportResult({
        globalErrors: [{ row: 1, message: 'Global error' }],
        topicResults: new Map([
          ['topic-1', createMockTopicResult({
            errors: [{ row: 2, message: 'Topic error' }],
          })],
        ]),
      });

      const report = service.generateErrorReport(importResult);
      const csvContent = service.generateDownloadableErrorReport(report);

      expect(csvContent).toContain('Type,Topic,Row,Field,Severity,Category,Message,Suggestion,Actionable');
      expect(csvContent).toContain('Global error');
      expect(csvContent).toContain('Topic error');
      expect(csvContent).toContain('Test Topic');
    });

    it('handles special characters in CSV export', () => {
      const importResult = createMockImportResult({
        globalErrors: [{ row: 1, message: 'Error with "quotes" and, commas' }],
      });

      const report = service.generateErrorReport(importResult);
      const csvContent = service.generateDownloadableErrorReport(report);

      expect(csvContent).toContain('""quotes""'); // Escaped quotes
      expect(csvContent).toContain('"Error with ""quotes"" and, commas"'); // Quoted field with special chars
    });
  });

  describe('error categorization', () => {
    it('maps batch error types to categories correctly', () => {
      const batchResult = createMockBatchResult({
        errors: [
          createMockBatchError({ type: 'topic-creation', message: 'Topic creation failed' }),
          createMockBatchError({ type: 'question-insertion', message: 'Question insertion failed' }),
          createMockBatchError({ type: 'validation', message: 'Validation failed' }),
          createMockBatchError({ type: 'system', message: 'System error' }),
        ],
      });

      const report = service.generateErrorReport(undefined, batchResult);

      const topicErrors = report.errorsByTopic.get('topic-1')?.errors || [];
      expect(topicErrors.some(e => e.category === 'topic')).toBe(true);
      expect(topicErrors.some(e => e.category === 'question')).toBe(true);
      expect(topicErrors.some(e => e.category === 'format')).toBe(true);
      expect(topicErrors.some(e => e.category === 'system')).toBe(true);
    });
  });

  describe('statistics calculation', () => {
    it('calculates success rate correctly', () => {
      const batchResult = createMockBatchResult({
        totalQuestionsImported: 8,
        topicResults: new Map([
          ['topic-1', createMockTopicBatchResult({
            questionsImported: 8,
            questionsFailed: 2,
          })],
        ]),
      });

      const report = service.generateErrorReport(undefined, batchResult);

      expect(report.statistics.successRate).toBeCloseTo(0.8, 2); // 8/(8+2) = 0.8
    });

    it('handles zero questions gracefully', () => {
      const batchResult = createMockBatchResult({
        totalQuestionsImported: 0,
        topicResults: new Map([
          ['topic-1', createMockTopicBatchResult({
            questionsImported: 0,
            questionsFailed: 0,
          })],
        ]),
      });

      const report = service.generateErrorReport(undefined, batchResult);

      expect(report.statistics.successRate).toBe(0);
    });
  });
});