import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  CreditCard, 
  Calendar, 
  CheckCircle, 
  Lock, 
  AlertTriangle, 
  RefreshCw,
  Clock,
  Crown,
  Zap,
  Star
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { useAuth } from '@/hooks/use-auth';
import { getUserSubscriptionStatus, getUserSubscriptionDetails } from '@/utils/auth-helpers';
import { toast } from 'sonner';

// Subscription status types
type SubscriptionStatus = 'Free' | 'Premium' | 'Expired' | 'Expiring Soon';

interface SubscriptionDetails {
  plan_id?: string;
  amount_paid?: number;
  start_date?: string;
  end_date?: string;
  is_active?: boolean;
  payment_reference?: string;
}

interface SubscriptionStatusProps {
  className?: string;
  showActions?: boolean;
  compact?: boolean;
  realTimeUpdates?: boolean;
}

const SubscriptionStatus = ({ 
  className = '', 
  showActions = true, 
  compact = false,
  realTimeUpdates = true 
}: SubscriptionStatusProps) => {
  const { user } = useAuth();
  const [status, setStatus] = useState<SubscriptionStatus>('Free');
  const [details, setDetails] = useState<SubscriptionDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [daysUntilExpiry, setDaysUntilExpiry] = useState<number | null>(null);

  // Fetch subscription data
  const fetchSubscriptionData = async (showToast = false) => {
    if (!user) {
      setStatus('Free');
      setDetails(null);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      
      // Get subscription status
      const subscriptionStatus = await getUserSubscriptionStatus(user);
      
      // Get detailed subscription information
      let subscriptionDetails: SubscriptionDetails | null = null;
      if (subscriptionStatus === 'Premium') {
        subscriptionDetails = await getUserSubscriptionDetails(user);
      }

      // Calculate days until expiry and determine if expiring soon
      let finalStatus: SubscriptionStatus = subscriptionStatus as SubscriptionStatus;
      let daysLeft: number | null = null;

      if (subscriptionDetails?.end_date) {
        const endDate = new Date(subscriptionDetails.end_date);
        const now = new Date();
        const timeDiff = endDate.getTime() - now.getTime();
        daysLeft = Math.ceil(timeDiff / (1000 * 3600 * 24));
        
        if (daysLeft <= 0) {
          finalStatus = 'Expired';
        } else if (daysLeft <= 7 && subscriptionStatus === 'Premium') {
          finalStatus = 'Expiring Soon';
        }
      }

      setStatus(finalStatus);
      setDetails(subscriptionDetails);
      setDaysUntilExpiry(daysLeft);
      setLastUpdated(new Date());

      if (showToast) {
        toast.success('Subscription status updated');
      }
    } catch (error) {
      console.error('Error fetching subscription data:', error);
      toast.error('Failed to update subscription status');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchSubscriptionData();
  }, [user]);

  // Real-time updates
  useEffect(() => {
    if (!realTimeUpdates) return;

    const interval = setInterval(() => {
      fetchSubscriptionData();
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [user, realTimeUpdates]);

  // Manual refresh
  const handleRefresh = () => {
    fetchSubscriptionData(true);
  };

  // Get status configuration
  const getStatusConfig = () => {
    switch (status) {
      case 'Premium':
        return {
          color: 'bg-green-500/20 text-green-600 border-green-500/30',
          icon: <Crown className="h-4 w-4" />,
          label: 'Premium Active',
          description: 'You have full access to all features'
        };
      case 'Expiring Soon':
        return {
          color: 'bg-amber-500/20 text-amber-600 border-amber-500/30',
          icon: <Clock className="h-4 w-4" />,
          label: 'Expiring Soon',
          description: `Your subscription expires in ${daysUntilExpiry} day${daysUntilExpiry !== 1 ? 's' : ''}`
        };
      case 'Expired':
        return {
          color: 'bg-red-500/20 text-red-600 border-red-500/30',
          icon: <AlertTriangle className="h-4 w-4" />,
          label: 'Expired',
          description: 'Your subscription has expired. Renew to continue accessing premium features.'
        };
      default:
        return {
          color: 'bg-gray-500/20 text-gray-600 border-gray-500/30',
          icon: <Lock className="h-4 w-4" />,
          label: 'Free Plan',
          description: 'Upgrade to unlock premium features and unlimited access'
        };
    }
  };

  // Get plan benefits
  const getPlanBenefits = () => {
    if (!details?.plan_id) return [];

    switch (details.plan_id) {
      case 'basic':
        return [
          'Access to 4 quiz domains',
          '1000+ questions weekly',
          'Basic progress tracking'
        ];
      case 'pro':
        return [
          'Access to all quiz domains',
          'Unlimited questions',
          'Free international certification',
          'Advanced analytics'
        ];
      case 'elite':
        return [
          'Everything in Pro',
          'Community access',
          '24/7 priority support',
          'Daily job alerts',
          'Mentorship program'
        ];
      default:
        return [];
    }
  };

  const statusConfig = getStatusConfig();
  const benefits = getPlanBenefits();

  if (compact) {
    return (
      <div className={`subscription-badge-compact flex items-center space-x-1 ${className}`}>
        <Badge className={`${statusConfig.color} text-xs px-2 py-1 whitespace-nowrap shadow-sm border`}>
          {statusConfig.icon}
          <span className="ml-1 hidden sm:inline">{statusConfig.label}</span>
          <span className="ml-1 sm:hidden">
            {status === 'Premium' ? 'Pro' :
             status === 'Expiring Soon' ? 'Exp' :
             status === 'Expired' ? 'Exp' : 'Free'}
          </span>
        </Badge>
        {realTimeUpdates && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
            className="h-5 w-5 p-0 hidden sm:flex"
            title="Refresh subscription status"
          >
            <RefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        )}
      </div>
    );
  }

  return (
    <Card className={`p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold flex items-center space-x-2">
          <CreditCard className="h-5 w-5" />
          <span>Subscription Status</span>
        </h3>
        
        <div className="flex items-center space-x-2">
          <Badge className={statusConfig.color}>
            {statusConfig.icon}
            <span className="ml-1">{statusConfig.label}</span>
          </Badge>
          
          {realTimeUpdates && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
              title="Refresh subscription status"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          )}
        </div>
      </div>

      {isLoading ? (
        <div className="space-y-3">
          <div className="h-4 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
          <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2" />
        </div>
      ) : (
        <div className="space-y-4">
          {/* Status Description */}
          <p className="text-sm text-muted-foreground">
            {statusConfig.description}
          </p>

          {/* Subscription Details */}
          {details && (status === 'Premium' || status === 'Expiring Soon' || status === 'Expired') && (
            <div className="space-y-3">
              {/* Plan Information */}
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <Star className="h-4 w-4 text-yellow-500" />
                  <span className="text-sm font-medium">
                    {details.plan_id ? 
                      details.plan_id.charAt(0).toUpperCase() + details.plan_id.slice(1) + ' Plan' : 
                      'Premium Plan'
                    }
                  </span>
                </div>
                {details.amount_paid && (
                  <span className="text-sm text-muted-foreground">
                    ₦{details.amount_paid}
                  </span>
                )}
              </div>

              {/* Expiry Information */}
              {details.end_date && (
                <div className="flex items-center space-x-3">
                  <Calendar className="h-4 w-4 text-blue-500" />
                  <div>
                    <span className="text-sm font-medium">
                      {status === 'Expired' ? 'Expired on: ' : 'Valid until: '}
                    </span>
                    <span className={`text-sm ${
                      status === 'Expired' ? 'text-red-600' : 
                      status === 'Expiring Soon' ? 'text-amber-600' : 
                      'text-muted-foreground'
                    }`}>
                      {new Date(details.end_date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </span>
                  </div>
                </div>
              )}

              {/* Benefits */}
              {benefits.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Zap className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium">Your Benefits:</span>
                  </div>
                  <ul className="text-sm text-muted-foreground space-y-1 ml-6">
                    {benefits.map((benefit, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <CheckCircle className="h-3 w-3 text-green-500 flex-shrink-0" />
                        <span>{benefit}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          {/* Expiry Warning */}
          {status === 'Expiring Soon' && daysUntilExpiry && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="p-3 bg-amber-50 border border-amber-200 rounded-lg"
            >
              <div className="flex items-start space-x-2">
                <AlertTriangle className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-amber-800">
                    Subscription Expiring Soon
                  </p>
                  <p className="text-sm text-amber-700">
                    Your subscription will expire in {daysUntilExpiry} day{daysUntilExpiry !== 1 ? 's' : ''}. 
                    Renew now to avoid interruption of service.
                  </p>
                </div>
              </div>
            </motion.div>
          )}

          {/* Actions */}
          {showActions && (
            <div className="flex flex-col space-y-2 pt-2">
              {status === 'Free' && (
                <Link to="/#pricing">
                  <Button className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
                    <Crown className="h-4 w-4 mr-2" />
                    Upgrade to Premium
                  </Button>
                </Link>
              )}
              
              {(status === 'Expired' || status === 'Expiring Soon') && (
                <Link to="/#pricing">
                  <Button className="w-full bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Renew Subscription
                  </Button>
                </Link>
              )}

              {status === 'Premium' && (
                <Link to="/payment/troubleshoot">
                  <Button variant="outline" className="w-full">
                    <CreditCard className="h-4 w-4 mr-2" />
                    Manage Subscription
                  </Button>
                </Link>
              )}
            </div>
          )}

          {/* Last Updated */}
          {realTimeUpdates && (
            <p className="text-xs text-muted-foreground text-center pt-2">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </p>
          )}
        </div>
      )}
    </Card>
  );
};

export default SubscriptionStatus;