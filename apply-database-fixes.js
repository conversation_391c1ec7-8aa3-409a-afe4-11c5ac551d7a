import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyDatabaseFixes() {
  console.log('🔧 Applying database fixes...');

  try {
    // 1. Ensure all users have profiles
    console.log('📝 Creating missing user profiles...');
    
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
    if (authError) {
      throw new Error(`Failed to fetch users: ${authError.message}`);
    }

    console.log(`Found ${authUsers.users.length} users in auth.users`);

    // Get existing profiles
    const { data: existingProfiles, error: profilesError } = await supabase
      .from('user_profiles')
      .select('user_id');

    if (profilesError) {
      console.warn('Error fetching existing profiles:', profilesError);
    }

    const existingProfileIds = new Set(existingProfiles?.map(p => p.user_id) || []);
    const usersNeedingProfiles = authUsers.users.filter(user => !existingProfileIds.has(user.id));

    console.log(`${usersNeedingProfiles.length} users need profiles created`);

    if (usersNeedingProfiles.length > 0) {
      const profilesToCreate = usersNeedingProfiles.map(user => ({
        user_id: user.id,
        is_subscribed: false,
        is_admin: false
      }));

      const { error: insertError } = await supabase
        .from('user_profiles')
        .insert(profilesToCreate);

      if (insertError) {
        console.error('Error creating profiles:', insertError);
      } else {
        console.log(`✅ Created ${profilesToCreate.length} user profiles`);
      }
    }

    // 2. Check for users with payments but no subscriptions
    console.log('💳 Checking for payment/subscription mismatches...');
    
    const { data: payments, error: paymentsError } = await supabase
      .from('payments')
      .select('*')
      .eq('status', 'completed');

    if (paymentsError) {
      console.warn('Error fetching payments:', paymentsError);
    } else {
      console.log(`Found ${payments?.length || 0} completed payments`);
    }

    // 3. Update subscription statuses based on expiration dates
    console.log('⏰ Updating expired subscriptions...');
    
    const { data: expiredSubs, error: expiredError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('is_active', true)
      .lt('end_date', new Date().toISOString());

    if (expiredError) {
      console.warn('Error fetching expired subscriptions:', expiredError);
    } else if (expiredSubs && expiredSubs.length > 0) {
      console.log(`Found ${expiredSubs.length} expired subscriptions`);
      
      // Update expired subscriptions
      const { error: updateError } = await supabase
        .from('subscriptions')
        .update({ is_active: false })
        .in('id', expiredSubs.map(sub => sub.id));

      if (updateError) {
        console.error('Error updating expired subscriptions:', updateError);
      } else {
        console.log(`✅ Marked ${expiredSubs.length} subscriptions as inactive`);
      }

      // Update user profiles for expired subscriptions
      const { error: profileUpdateError } = await supabase
        .from('user_profiles')
        .update({ is_subscribed: false })
        .in('user_id', expiredSubs.map(sub => sub.user_id));

      if (profileUpdateError) {
        console.error('Error updating user profiles for expired subscriptions:', profileUpdateError);
      } else {
        console.log(`✅ Updated ${expiredSubs.length} user profiles for expired subscriptions`);
      }
    }

    // 4. Sync user_profiles with active subscriptions
    console.log('🔄 Syncing user profiles with active subscriptions...');
    
    const { data: activeSubs, error: activeSubsError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('is_active', true)
      .gt('end_date', new Date().toISOString());

    if (activeSubsError) {
      console.warn('Error fetching active subscriptions:', activeSubsError);
    } else if (activeSubs && activeSubs.length > 0) {
      console.log(`Found ${activeSubs.length} active subscriptions`);
      
      for (const sub of activeSubs) {
        const { error: syncError } = await supabase
          .from('user_profiles')
          .update({
            is_subscribed: true,
            subscription_expires_at: sub.end_date
          })
          .eq('user_id', sub.user_id);

        if (syncError) {
          console.error(`Error syncing profile for user ${sub.user_id}:`, syncError);
        }
      }
      
      console.log(`✅ Synced ${activeSubs.length} user profiles with active subscriptions`);
    }

    // 5. Final statistics
    console.log('\n📊 Final Statistics:');
    
    const { data: finalProfiles, error: finalError } = await supabase
      .from('user_profiles')
      .select('*');

    if (!finalError && finalProfiles) {
      const totalUsers = finalProfiles.length;
      const subscribedUsers = finalProfiles.filter(p => p.is_subscribed).length;
      const adminUsers = finalProfiles.filter(p => p.is_admin).length;
      
      console.log(`Total users with profiles: ${totalUsers}`);
      console.log(`Subscribed users: ${subscribedUsers}`);
      console.log(`Admin users: ${adminUsers}`);
    }

    const { data: totalSubs, error: subsError } = await supabase
      .from('subscriptions')
      .select('*');

    if (!subsError && totalSubs) {
      const activeSubs = totalSubs.filter(s => s.is_active).length;
      console.log(`Total subscriptions: ${totalSubs.length}`);
      console.log(`Active subscriptions: ${activeSubs}`);
    }

    const { data: totalPayments, error: payError } = await supabase
      .from('payments')
      .select('*');

    if (!payError && totalPayments) {
      const completedPayments = totalPayments.filter(p => p.status === 'completed').length;
      console.log(`Total payments: ${totalPayments.length}`);
      console.log(`Completed payments: ${completedPayments}`);
    }

    console.log('\n✅ Database fixes completed successfully!');
    
  } catch (error) {
    console.error('❌ Error applying database fixes:', error);
    process.exit(1);
  }
}

applyDatabaseFixes();
