import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Quiz<PERSON>ilterTabs, QuizFilterTabsList, QuizFilterTabsTrigger } from "@/components/ui/quiz-filter-tabs";
import BottomNavigation from "@/components/BottomNavigation";
import Navbar from "@/components/Navbar";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "@/hooks/use-auth";
import { isUserSubscribed } from "@/utils/auth-helpers";
import { useToast } from "@/components/ui/use-toast";
import {
  Search,
  Shield,
  BookOpen,
  Lock,
  Star,
  Clock,
  BarChart,
  Network,
  Database,
  Code,
  Server,
  Wifi,
  FileText,
  Fingerprint,
  Key,
  ShieldAlert,
  ShieldCheck,
  Cloud,
  Smartphone,
  Settings
} from "lucide-react";

import { fetchAllTopics } from "@/utils/fetch-topics";
import { canAccessTopicSync, canAccessTopic } from "@/utils/topic-access";

// Topic type definition
interface Topic {
  id: string;
  title: string;
  description: string;
  icon: string;
  difficulty: string;
  is_active: boolean;
  is_premium: boolean;
  question_count: number;
  domain_id?: string | null;
}

// Get icon component by name
const getIconByName = (iconName: string) => {
  const icons = {
    shield: Shield,
    book: BookOpen,
    star: Star,
    clock: Clock,
    chart: BarChart,
    network: Network,
    database: Database,
    code: Code,
    server: Server,
    wifi: Wifi,
    file: FileText,
    fingerprint: Fingerprint,
    key: Key,
    shieldAlert: ShieldAlert,
    shieldCheck: ShieldCheck,
    cloud: Cloud,
    smartphone: Smartphone
  };

  // Default to Shield if icon not found
  return icons[iconName] || Shield;
};

// Mock topics data - will be replaced with real data from Supabase
const mockTopics: Topic[] = [
  {
    id: "1",
    title: "CISSP Fundamentals",
    description: "Core concepts and principles of the CISSP certification",
    icon: "shield",
    difficulty: "easy",
    is_active: true,
    is_premium: false,
    question_count: 25
  },
  {
    id: "2",
    title: "Cybersecurity Foundation - Hard",
    description: "Check your knowledge level. Attempt the hard level questions in Cybersecurity Foundations.",
    icon: "shieldAlert",
    difficulty: "hard",
    is_active: true,
    is_premium: true,
    question_count: 20
  },
  {
    id: "3",
    title: "Cybersecurity Foundation - Medium",
    description: "Check your knowledge level. Try the medium level questions in Cybersecurity Foundations.",
    icon: "shield",
    difficulty: "medium",
    is_active: true,
    is_premium: true,
    question_count: 18
  },
  {
    id: "4",
    title: "Cybersecurity Foundation - Easy",
    description: "Get ready to test your knowledge and skills in cybersecurity with our comprehensive quiz app!",
    icon: "shield",
    difficulty: "easy",
    is_active: true,
    is_premium: false,
    question_count: 15
  }
];

const QuizzesPage = () => {
  const { user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [topics, setTopics] = useState<Topic[]>(mockTopics);
  const [filteredTopics, setFilteredTopics] = useState<Topic[]>([]);
  const [loading, setLoading] = useState(true);

  // Use useState to track subscription status
  const [isSubscribed, setIsSubscribed] = useState(false);

  // Coupon state for each topic
  const [couponInputs, setCouponInputs] = useState<{ [topicId: string]: string }>({});
  const [couponErrors, setCouponErrors] = useState<{ [topicId: string]: string }>({});

  // Check if we're coming from a learning material page
  const fromLearningMaterial = location.state?.fromLearningMaterial;
  const topicTitleFromLearningMaterial = location.state?.topicTitle;

  // Check subscription status when user changes
  useEffect(() => {
    const checkSubscription = async () => {
      if (!user) {
        setIsSubscribed(false);
        return;
      }

      try {
        const subscribed = await isUserSubscribed(user);
        setIsSubscribed(subscribed);
      } catch (error) {
        console.error("Error checking subscription status:", error);
        setIsSubscribed(false);
      }
    };

    checkSubscription();
  }, [user]);

  // Filter topics based on search term and active tab
  useEffect(() => {
    const filtered = topics.filter(topic => {
      const matchesSearch =
        topic.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        topic.description.toLowerCase().includes(searchTerm.toLowerCase());

      if (activeTab === "all") return matchesSearch;
      if (activeTab === "free") return matchesSearch && !topic.is_premium;
      if (activeTab === "premium") return matchesSearch && topic.is_premium;

      return matchesSearch;
    });

    setFilteredTopics(filtered);
  }, [topics, searchTerm, activeTab]);

  // Fetch topics from Supabase
  useEffect(() => {
    const loadTopics = async () => {
      try {
        setLoading(true);
        const topics = await fetchAllTopics();

        // Sort topics by those with questions first, then by title
        const sortedTopics = topics.sort((a, b) => {
          // First sort by whether they have questions (topics with questions come first)
          if (a.question_count > 0 && b.question_count === 0) return -1;
          if (a.question_count === 0 && b.question_count > 0) return 1;

          // Then sort alphabetically by title
          return a.title.localeCompare(b.title);
        });

        setTopics(sortedTopics);
        // Also set filtered topics initially
        setFilteredTopics(sortedTopics);

        // If we're coming from a learning material page, find the matching topic and navigate to it
        if (fromLearningMaterial && topicTitleFromLearningMaterial) {
          const matchingTopic = sortedTopics.find(topic =>
            topic.title === topicTitleFromLearningMaterial
          );

          if (matchingTopic) {
            // First do a quick synchronous check
            let canAccess = canAccessTopicSync(matchingTopic.title, matchingTopic.id, user);

            // If the sync check says user can't access, do a more thorough async check
            if (!canAccess) {
              try {
                // Show loading toast
                const loadingToast = toast({
                  title: "Checking Access",
                  description: "Verifying your subscription status...",
                });

                // Do the async check
                canAccess = await canAccessTopic(matchingTopic.title, matchingTopic.id, user);

                // Dismiss the loading toast
                loadingToast.dismiss();
              } catch (error) {
                console.error("Error checking async access:", error);
                // Keep the result from the sync check
              }
            }

            if (canAccess) {
              // Navigate to the quiz page
              navigate(`/quiz/${matchingTopic.id}`, { replace: true });
            } else {
              // Show toast message if user can't access the topic
              toast({
                title: "Premium Content",
                description: "This quiz requires a premium subscription",
                variant: "destructive",
              });
            }
          }
        }
      } catch (error) {
        console.error("Error loading topics:", error);
        setTopics([]);
        setFilteredTopics([]);
      } finally {
        setLoading(false);
      }
    };

    loadTopics();
  }, [fromLearningMaterial, topicTitleFromLearningMaterial, user, navigate, toast]);

  // Handler for coupon input change
  const handleCouponInputChange = (topicId: string, value: string) => {
    setCouponInputs((prev) => ({ ...prev, [topicId]: value }));
    setCouponErrors((prev) => ({ ...prev, [topicId]: "" }));
  };

  // Handler for coupon redeem
  const handleRedeemCoupon = (topic: Topic) => {
    const code = (couponInputs[topic.id] || "").trim();
    // Accept any valid coupon for any premium topic
    if (code === "FOUNDATION2025" || code === "GRC0125") {
      localStorage.setItem(`coupon_${user?.id || 'guest'}_${topic.title}`, "true");
      setCouponErrors((prev) => ({ ...prev, [topic.id]: "" }));
      toast({ title: "Coupon applied!", description: `You now have access to ${topic.title}.` });
      // Force re-render to update access
      setFilteredTopics((prev) => [...prev]);
    } else {
      setCouponErrors((prev) => ({ ...prev, [topic.id]: "Invalid coupon code" }));
    }
  };

  return (
    <div className="flex flex-col min-h-screen cyber-grid-bg pb-16">
      <Navbar />

      <header className="p-4 border-b bg-background/95 backdrop-blur-sm sticky top-0 z-10">
        <div className="container">
          <h1 className="text-xl font-bold">Quizzes & Topics</h1>
        </div>
      </header>

      <div className="container p-4">
        {/* Search and Filter */}
        <div className="mb-6">
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search topics..."
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <QuizFilterTabs defaultValue="all" className="w-full" onValueChange={setActiveTab}>
            <QuizFilterTabsList className="grid grid-cols-3 gap-1 w-full">
              <QuizFilterTabsTrigger value="all" variant="all">
                <BookOpen className="h-4 w-4 mr-2" /> All Topics
              </QuizFilterTabsTrigger>
              <QuizFilterTabsTrigger value="free" variant="free">
                <Shield className="h-4 w-4 mr-2" /> Free
              </QuizFilterTabsTrigger>
              <QuizFilterTabsTrigger value="premium" variant="premium">
                <Star className="h-4 w-4 mr-2" /> Premium
              </QuizFilterTabsTrigger>
            </QuizFilterTabsList>
          </QuizFilterTabs>
        </div>

        {/* Topics Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <Card key={i} className="cyber-card p-6 h-48 animate-pulse">
                <div className="h-6 w-3/4 bg-muted rounded mb-2"></div>
                <div className="h-4 w-full bg-muted rounded mb-4"></div>
                <div className="h-4 w-2/3 bg-muted rounded"></div>
                <div className="mt-auto pt-4">
                  <div className="h-8 w-full bg-muted rounded"></div>
                </div>
              </Card>
            ))}
          </div>
        ) : filteredTopics.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTopics.map((topic) => {
              const IconComponent = getIconByName(topic.icon);
              // Determine if the user can access this topic (using sync version)
              const canAccess = canAccessTopicSync(topic.title, topic.id, user);
              const isPremiumLocked = !canAccess;
              const ownedBadge = !isPremiumLocked ? (
                <Badge className="bg-green-600 text-white border-0">Owned</Badge>
              ) : null;

              // If sync check says user can't access, do an async check that will update the UI
              if (!canAccess) {
                // Store the topic ID to track which ones we're checking
                const topicKey = `checking_access_topic_${topic.id}`;

                // Only do the check if we haven't already started it
                if (!localStorage.getItem(topicKey)) {
                  localStorage.setItem(topicKey, "true");

                  // Do the async check
                  canAccessTopic(topic.title, topic.id, user)
                    .then(asyncAccess => {
                      if (asyncAccess) {
                        // If async check says user can access, update the UI
                        // We'll do this by forcing a re-render of the component
                        setFilteredTopics(prev => [...prev]);
                      }
                      // Clean up
                      localStorage.removeItem(topicKey);
                    })
                    .catch(error => {
                      console.error("Error checking async access:", error);
                      localStorage.removeItem(topicKey);
                    });
                }
              }

              // Determine if this is a premium topic
              const isPremiumTopic = topic.is_premium;

              return (
                <Card key={topic.id} className="cyber-card overflow-hidden">
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center">
                        <div className={`h-10 w-10 rounded-full flex items-center justify-center mr-3 ${
                          isPremiumTopic
                            ? "bg-cyber-accent/10 text-cyber-accent"
                            : "bg-cyber-primary/10 text-cyber-primary"
                        }`}>
                          <IconComponent className="h-5 w-5" />
                        </div>
                        <div>
                          <h3 className="font-medium">{topic.title}</h3>
                          <p className="text-xs text-muted-foreground">{topic.description}</p>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        {isPremiumTopic && (
                          <Badge className="bg-cyber-accent text-white border-0">PRO</Badge>
                        )}
                        {ownedBadge}
                      </div>
                    </div>

                    <div className="flex items-center text-xs text-muted-foreground mb-4">
                      <div className="flex items-center mr-3">
                        <FileText className="h-3 w-3 mr-1" />
                        <span>{topic.question_count} questions</span>
                      </div>
                      <div className={`px-2 py-0.5 rounded-full text-xs font-medium
                        ${topic.difficulty === "easy"
                          ? "bg-green-100 text-green-700"
                          : topic.difficulty === "medium"
                            ? "bg-blue-100 text-blue-700"
                            : "bg-purple-100 text-purple-700"}`}>
                        {topic.difficulty}
                      </div>
                    </div>

                    {isPremiumLocked ? (
                      <div className="mt-auto flex flex-col gap-2">
                        <Button
                          asChild
                          className="w-full bg-pink-200 text-cyber-dark hover:bg-pink-600 hover:text-white"
                        >
                          <Link to={user ? "/#pricing" : "/auth?tab=login"}>
                            <Lock className="h-4 w-4 mr-2" /> {user ? "Upgrade to Access" : "Sign In to Access"}
                          </Link>
                        </Button>
                        {/* Coupon input for locked premium topics */}
                        <div className="flex flex-col gap-1 mt-2">
                          <Input
                            placeholder="Enter coupon code"
                            value={couponInputs[topic.id] || ""}
                            onChange={e => handleCouponInputChange(topic.id, e.target.value)}
                            className="max-w-xs"
                          />
                          <Button
                            variant="outline"
                            className="w-full"
                            onClick={() => handleRedeemCoupon(topic)}
                          >
                            Redeem Coupon
                          </Button>
                          {couponErrors[topic.id] && (
                            <div className="text-red-500 text-xs mt-1">{couponErrors[topic.id]}</div>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="mt-auto">
                        <Button
                          asChild
                          size="sm"
                          className={`w-full ${
                            isPremiumTopic
                              ? "bg-green-600 hover:bg-green-800"
                              : "bg-blue-500 hover:bg-blue-600"
                          }`}
                        >
                          <Link to={`/quiz/${topic.id}`}>
                            <BookOpen className="h-4 w-4 mr-2" /> Start Quiz
                          </Link>
                        </Button>
                      </div>
                    )}
                  </div>
                </Card>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="h-16 w-16 bg-muted/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium mb-1">No topics found</h3>
            <p className="text-muted-foreground">
              {searchTerm
                ? `No topics matching "${searchTerm}"`
                : activeTab === "premium"
                  ? "No premium topics available"
                  : activeTab === "free"
                    ? "No free topics available"
                    : "No topics available"}
            </p>
          </div>
        )}

        {/* Premium Upgrade Banner (only for non-subscribed users) */}
        {!isSubscribed && (
          <Card className="cyber-card mt-8 overflow-hidden">
            <div className="bg-gradient-to-r from-cyber-primary to-cyber-accent p-0.5">
              <div className="bg-background p-6">
                <div className="flex flex-col md:flex-row items-center justify-between gap-4">
                  <div className="flex items-center">
                    <div className="h-12 w-12 rounded-full bg-cyber-accent/10 flex items-center justify-center mr-4">
                      <Star className="h-6 w-6 text-cyber-accent" />
                    </div>
                    <div>
                      <h3 className="font-bold text-lg">Upgrade to Premium</h3>
                      <p className="text-sm text-muted-foreground">
                        Get unlimited access to all premium topics and questions
                      </p>
                    </div>
                  </div>
                  <Button
                    asChild
                    className="bg-gradient-to-r from-cyber-primary to-cyber-accent text-white hover:opacity-90 transition-opacity"
                  >
                    <Link to="/#pricing">
                      Upgrade Now
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        )}
      </div>

      <BottomNavigation />
    </div>
  );
};

export default QuizzesPage;
