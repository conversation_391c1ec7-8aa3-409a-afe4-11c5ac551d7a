/**
 * UUID Helper Utilities
 * Ensures consistent UUID generation and validation across the application
 */

/**
 * Generates a proper UUID v4
 * @returns string - A valid UUID v4
 */
export function generateUUID(): string {
  return crypto.randomUUID();
}

/**
 * Validates if a string is a proper UUID format
 * @param uuid - String to validate
 * @returns boolean - True if valid UUID format
 */
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

/**
 * Ensures a value is a valid UUID, generates one if not
 * @param value - Value to check/convert
 * @returns string - A valid UUID
 */
export function ensureValidUUID(value: string | null | undefined): string {
  if (value && isValidUUID(value)) {
    return value;
  }
  
  console.warn(`Invalid UUID provided: ${value}, generating new UUID`);
  return generateUUID();
}

/**
 * Generates a guest user UUID with consistent format
 * @returns string - A valid UUID for guest users
 */
export function generateGuestUserUUID(): string {
  return generateUUID();
}

/**
 * Generates a quiz session UUID
 * @returns string - A valid UUID for quiz sessions
 */
export function generateQuizSessionUUID(): string {
  return generateUUID();
}

/**
 * Validates and sanitizes UUIDs for database operations
 * @param uuid - UUID to validate
 * @throws Error if UUID is invalid and cannot be fixed
 * @returns string - Valid UUID ready for database insertion
 */
export function validateUUIDForDatabase(uuid: string): string {
  if (!uuid) {
    throw new Error('UUID cannot be null or empty');
  }
  
  if (!isValidUUID(uuid)) {
    throw new Error(`Invalid UUID format: ${uuid}`);
  }
  
  return uuid.toLowerCase(); // Ensure consistent case
}

/**
 * Batch validates multiple UUIDs
 * @param uuids - Array of UUIDs to validate
 * @returns Array of validation results
 */
export function validateMultipleUUIDs(uuids: string[]): Array<{ uuid: string; isValid: boolean; error?: string }> {
  return uuids.map(uuid => {
    try {
      validateUUIDForDatabase(uuid);
      return { uuid, isValid: true };
    } catch (error) {
      return { 
        uuid, 
        isValid: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  });
}