import <PERSON> from 'papa<PERSON><PERSON>';
import { v4 as uuidv4 } from 'uuid';

// Define the expected CSV structure
export interface QuestionCSVRow {
  question_text: string;
  option_a: string;
  option_b: string;
  option_c: string;
  option_d: string;
  correct_answer: string; // Should be A, B, C, or D
  explanation: string;
  difficulty?: string; // Optional, defaults to 'medium'
}

// Enhanced CSV row structure supporting topic information
export interface MultiTopicQuestionCSVRow extends QuestionCSVRow {
  topic_name?: string;
  topic_id?: string;
}

// Define the structure for a validated question ready for database insertion
export interface ValidatedQuestion {
  id: string;
  topic_id: string;
  question_text: string;
  options: {
    A: string;
    B: string;
    C: string;
    D: string;
  };
  correct_answer: string;
  explanation: string;
  difficulty: string;
  created_at: string;
  updated_at: string;
}

// Define the structure for import results
export interface ImportResult {
  success: boolean;
  totalRows: number;
  validQuestions: ValidatedQuestion[];
  errors: { row: number; message: string }[];
}

// Enhanced import result with topic-grouped data
export interface MultiTopicImportResult {
  success: boolean;
  totalRows: number;
  topicResults: Map<string, TopicImportResult>;
  globalErrors: { row: number; message: string }[];
  newTopicsCreated: string[];
}

// Result for a single topic within a multi-topic import
export interface TopicImportResult {
  topicId: string;
  topicName: string;
  validQuestions: ValidatedQuestion[];
  errors: { row: number; message: string }[];
  isNewTopic: boolean;
}

// Import mode configuration
export interface ImportConfig {
  mode: 'single-topic' | 'multi-topic';
  autoCreateTopics: boolean;
  selectedTopicId?: string; // For backward compatibility
}

// Topic resolution result for batch processing
export interface TopicResolutionResult {
  resolved: Map<string, string>; // Maps topic reference to topic_id
  missing: string[];
  created: string[];
  errors: string[];
}

// Import topic service for use in CSV functions
import { topicService, type Topic, type CreateTopicInput } from '@/services/topic-service';

// Import validation utilities
import {
  performCrossTopicValidation,
  validateTopicResolution,
  validateTopicResolutionFailures,
  validateCrossTopicConsistency,
  groupErrorsByTopic,
  generateActionableErrorMessage,
  type ValidationConfig,
  type CrossTopicValidationResult,
  type ValidationError,
  DEFAULT_VALIDATION_CONFIG,
} from './multi-topic-validation';

// Re-export topic service types for convenience
export type { Topic, CreateTopicInput };

// Re-export validation types for convenience
export type { ValidationConfig, CrossTopicValidationResult, ValidationError };

/**
 * Parse a CSV file and validate the questions (legacy single-topic version)
 * @param file The CSV file to parse
 * @param topicId The ID of the topic to associate the questions with
 * @returns A promise that resolves to the import result
 */
export async function parseQuestionCSV(file: File, topicId: string): Promise<ImportResult> {
  // Use the enhanced parser in single-topic mode for backward compatibility
  const config: ImportConfig = {
    mode: 'single-topic',
    autoCreateTopics: false,
    selectedTopicId: topicId,
  };
  
  const multiResult = await parseQuestionCSVEnhanced(file, config);
  
  // Convert multi-topic result to single-topic result for backward compatibility
  const result: ImportResult = {
    success: multiResult.success,
    totalRows: multiResult.totalRows,
    validQuestions: [],
    errors: [...multiResult.globalErrors],
  };
  
  // Aggregate all questions from all topics
  for (const topicResult of multiResult.topicResults.values()) {
    result.validQuestions.push(...topicResult.validQuestions);
    result.errors.push(...topicResult.errors);
  }
  
  return result;
}

/**
 * Enhanced CSV parser that supports both single-topic and multi-topic modes
 * @param file The CSV file to parse
 * @param config Import configuration specifying mode and options
 * @param validationConfig Optional validation configuration
 * @returns A promise that resolves to the multi-topic import result
 */
export async function parseQuestionCSVEnhanced(
  file: File, 
  config: ImportConfig, 
  validationConfig: ValidationConfig = DEFAULT_VALIDATION_CONFIG
): Promise<MultiTopicImportResult> {
  return new Promise((resolve) => {
    const result: MultiTopicImportResult = {
      success: false,
      totalRows: 0,
      topicResults: new Map(),
      globalErrors: [],
      newTopicsCreated: [],
    };

    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: async (results) => {
        try {
          result.totalRows = results.data.length;
          
          if (results.data.length === 0) {
            result.globalErrors.push({ row: 0, message: 'CSV file is empty or contains no valid data rows' });
            resolve(result);
            return;
          }

          // Cast the data to our expected type
          const csvData = results.data as MultiTopicQuestionCSVRow[];
          
          // Detect import mode if not explicitly set
          const detectedMode = detectImportMode(csvData, config);
          const effectiveConfig = { ...config, mode: detectedMode };
          
          if (effectiveConfig.mode === 'single-topic') {
            await processSingleTopicMode(csvData, effectiveConfig, result);
          } else {
            await processMultiTopicMode(csvData, effectiveConfig, result, validationConfig);
          }
          
          // Set overall success flag
          result.success = result.topicResults.size > 0 && 
                          Array.from(result.topicResults.values()).some(tr => tr.validQuestions.length > 0);
          
          resolve(result);
        } catch (error) {
          result.globalErrors.push({
            row: 0,
            message: `Failed to process CSV: ${error instanceof Error ? error.message : String(error)}`,
          });
          resolve(result);
        }
      },
      error: (error) => {
        result.globalErrors.push({ row: 0, message: `CSV parsing error: ${error.message}` });
        resolve(result);
      },
    });
  });
}

/**
 * Detect whether the CSV should be processed in single-topic or multi-topic mode
 * @param csvData Array of CSV rows
 * @param config Import configuration
 * @returns The detected import mode
 */
function detectImportMode(csvData: MultiTopicQuestionCSVRow[], config: ImportConfig): 'single-topic' | 'multi-topic' {
  // If mode is explicitly set, respect it
  if (config.mode && config.mode !== 'single-topic') {
    return config.mode;
  }
  
  // If a specific topic is selected, use single-topic mode
  if (config.selectedTopicId) {
    return 'single-topic';
  }
  
  // Check if any row has topic information
  const hasTopicColumns = csvData.some(row => 
    (row.topic_name && row.topic_name.trim().length > 0) ||
    (row.topic_id && row.topic_id.trim().length > 0)
  );
  
  return hasTopicColumns ? 'multi-topic' : 'single-topic';
}

/**
 * Process CSV in single-topic mode
 * @param csvData Array of CSV rows
 * @param config Import configuration
 * @param result Result object to populate
 */
async function processSingleTopicMode(
  csvData: MultiTopicQuestionCSVRow[],
  config: ImportConfig,
  result: MultiTopicImportResult
): Promise<void> {
  if (!config.selectedTopicId) {
    result.globalErrors.push({ 
      row: 0, 
      message: 'Single-topic mode requires a selected topic ID' 
    });
    return;
  }

  // Get topic information
  let topicName = 'Selected Topic';
  try {
    const topic = await topicService.getTopicById(config.selectedTopicId);
    if (topic) {
      topicName = topic.title;
    }
  } catch (error) {
    // Continue with default name if topic lookup fails
  }

  const topicResult: TopicImportResult = {
    topicId: config.selectedTopicId,
    topicName,
    validQuestions: [],
    errors: [],
    isNewTopic: false,
  };

  // Validate each row for the selected topic
  csvData.forEach((row, index) => {
    try {
      const validatedQuestion = validateQuestionRow(row, config.selectedTopicId!, index);
      topicResult.validQuestions.push(validatedQuestion);
    } catch (error) {
      topicResult.errors.push({
        row: index + 1,
        message: error instanceof Error ? error.message : String(error),
      });
    }
  });

  result.topicResults.set(config.selectedTopicId, topicResult);
}

/**
 * Process CSV in multi-topic mode
 * @param csvData Array of CSV rows
 * @param config Import configuration
 * @param result Result object to populate
 * @param validationConfig Validation configuration
 */
async function processMultiTopicMode(
  csvData: MultiTopicQuestionCSVRow[],
  config: ImportConfig,
  result: MultiTopicImportResult,
  validationConfig: ValidationConfig = DEFAULT_VALIDATION_CONFIG
): Promise<void> {
  // Extract topic references from CSV data
  const topicReferences = extractTopicReferences(csvData);
  
  if (topicReferences.size === 0) {
    result.globalErrors.push({
      row: 0,
      message: 'Multi-topic mode detected but no valid topic references found in CSV data',
    });
    return;
  }

  const topicReferencesArray = Array.from(topicReferences);

  // Perform comprehensive cross-topic validation
  const crossTopicValidation = performCrossTopicValidation(
    csvData,
    topicReferencesArray,
    validationConfig
  );

  // Add cross-topic validation errors to global errors
  crossTopicValidation.globalErrors.forEach(validationError => {
    result.globalErrors.push({
      row: validationError.row,
      message: generateActionableErrorMessage(validationError),
    });
  });

  // Perform additional cross-topic consistency validation
  const consistencyErrors = validateCrossTopicConsistency(csvData, topicReferencesArray, validationConfig);
  consistencyErrors.forEach(validationError => {
    result.globalErrors.push({
      row: validationError.row,
      message: generateActionableErrorMessage(validationError),
    });
  });

  // If cross-topic validation failed critically, stop processing
  if (!crossTopicValidation.isValid && 
      crossTopicValidation.globalErrors.some(e => e.severity === 'error' && e.category === 'topic')) {
    return;
  }

  // Resolve topic references
  const topicResolution = await topicService.resolveTopicReferences(
    topicReferencesArray,
    config.autoCreateTopics
  );

  // Track newly created topics
  result.newTopicsCreated = topicResolution.created;

  // Validate topic resolution and add actionable error messages
  const resolutionErrors = validateTopicResolution(topicResolution, csvData);
  resolutionErrors.forEach(validationError => {
    result.globalErrors.push({
      row: validationError.row,
      message: generateActionableErrorMessage(validationError),
    });
  });

  // Add comprehensive error handling for failed topic resolutions
  if (topicResolution.missing.length > 0) {
    const failureErrors = validateTopicResolutionFailures(
      topicResolution.missing,
      csvData,
      config.autoCreateTopics
    );
    failureErrors.forEach(validationError => {
      result.globalErrors.push({
        row: validationError.row,
        message: generateActionableErrorMessage(validationError),
      });
    });
  }

  // Group questions by resolved topic
  const questionsByTopic = groupQuestionsByTopic(csvData, topicResolution.resolved);

  // Process each topic's questions
  for (const [topicId, questions] of questionsByTopic) {
    const topicName = getTopicNameFromResolution(topicId, topicResolution.resolved);
    const isNewTopic = topicResolution.created.includes(topicName);

    const topicResult: TopicImportResult = {
      topicId,
      topicName,
      validQuestions: [],
      errors: [],
      isNewTopic,
    };

    // Validate questions for this topic with enhanced validation
    questions.forEach(({ row, index }) => {
      try {
        const validatedQuestion = validateQuestionRow(row, topicId, index);
        topicResult.validQuestions.push(validatedQuestion);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        topicResult.errors.push({
          row: index + 1,
          message: errorMessage,
        });
      }
    });

    result.topicResults.set(topicId, topicResult);
  }

  // Handle questions with unresolved topics
  const unresolvedQuestions = getUnresolvedQuestions(csvData, topicResolution.resolved);
  if (unresolvedQuestions.length > 0) {
    unresolvedQuestions.forEach(({ row, index }) => {
      const topicRef = row.topic_name || row.topic_id || 'unknown';
      result.globalErrors.push({
        row: index + 1,
        message: `Cannot resolve topic reference: "${topicRef}"`,
      });
    });
  }
}

/**
 * Extract unique topic references from CSV data
 * @param csvData Array of CSV rows
 * @returns Set of unique topic references
 */
function extractTopicReferences(csvData: MultiTopicQuestionCSVRow[]): Set<string> {
  const references = new Set<string>();
  
  csvData.forEach(row => {
    // Prefer topic_name over topic_id for reference
    let reference: string | undefined;
    
    if (row.topic_name && row.topic_name.trim().length > 0) {
      reference = row.topic_name.trim();
    } else if (row.topic_id && row.topic_id.trim().length > 0) {
      reference = row.topic_id.trim();
    }
    
    if (reference) {
      references.add(reference);
    }
  });
  
  return references;
}

/**
 * Group questions by their resolved topic IDs
 * @param csvData Array of CSV rows
 * @param resolvedTopics Map of topic references to topic IDs
 * @returns Map of topic IDs to their questions with row indices
 */
function groupQuestionsByTopic(
  csvData: MultiTopicQuestionCSVRow[],
  resolvedTopics: Map<string, string>
): Map<string, Array<{ row: MultiTopicQuestionCSVRow; index: number }>> {
  const questionsByTopic = new Map<string, Array<{ row: MultiTopicQuestionCSVRow; index: number }>>();
  
  csvData.forEach((row, index) => {
    // Use same preference logic as extractTopicReferences
    let reference: string | undefined;
    
    if (row.topic_name && row.topic_name.trim().length > 0) {
      reference = row.topic_name.trim();
    } else if (row.topic_id && row.topic_id.trim().length > 0) {
      reference = row.topic_id.trim();
    }
    
    if (reference && resolvedTopics.has(reference)) {
      const topicId = resolvedTopics.get(reference)!;
      
      if (!questionsByTopic.has(topicId)) {
        questionsByTopic.set(topicId, []);
      }
      
      questionsByTopic.get(topicId)!.push({ row, index });
    }
  });
  
  return questionsByTopic;
}

/**
 * Get questions that couldn't be resolved to any topic
 * @param csvData Array of CSV rows
 * @param resolvedTopics Map of resolved topic references
 * @returns Array of unresolved questions with their indices
 */
function getUnresolvedQuestions(
  csvData: MultiTopicQuestionCSVRow[],
  resolvedTopics: Map<string, string>
): Array<{ row: MultiTopicQuestionCSVRow; index: number }> {
  const unresolved: Array<{ row: MultiTopicQuestionCSVRow; index: number }> = [];
  
  csvData.forEach((row, index) => {
    // Use same preference logic as extractTopicReferences
    let reference: string | undefined;
    
    if (row.topic_name && row.topic_name.trim().length > 0) {
      reference = row.topic_name.trim();
    } else if (row.topic_id && row.topic_id.trim().length > 0) {
      reference = row.topic_id.trim();
    }
    
    if (reference && !resolvedTopics.has(reference)) {
      unresolved.push({ row, index });
    }
  });
  
  return unresolved;
}

/**
 * Get topic name from resolution map
 * @param topicId Topic ID to find name for
 * @param resolvedTopics Map of topic references to topic IDs
 * @returns Topic name or fallback
 */
function getTopicNameFromResolution(topicId: string, resolvedTopics: Map<string, string>): string {
  for (const [reference, id] of resolvedTopics) {
    if (id === topicId) {
      return reference;
    }
  }
  return `Topic ${topicId}`;
}

/**
 * Validate a single row from the CSV
 * @param row The row data
 * @param topicId The topic ID
 * @param rowIndex The row index for error reporting
 * @returns A validated question object or throws an error
 */
function validateQuestionRow(
  row: unknown,
  topicId: string,
  rowIndex: number
): ValidatedQuestion {
  // Type guard to ensure row is an object with string properties
  if (!row || typeof row !== 'object') {
    throw new Error(`Row ${rowIndex + 1}: Invalid row data`);
  }

  const rowData = row as Record<string, unknown>;

  // Check required fields
  const requiredFields = [
    'question_text',
    'option_a',
    'option_b',
    'option_c',
    'option_d',
    'correct_answer',
    'explanation',
  ];

  for (const field of requiredFields) {
    const value = rowData[field];
    if (!value || typeof value !== 'string' || value.trim() === '') {
      throw new Error(`Row ${rowIndex + 1}: Missing required field "${field}"`);
    }
  }

  // Validate correct_answer format
  const correctAnswerValue = rowData.correct_answer as string;
  const correctAnswer = correctAnswerValue.trim().toUpperCase();
  if (!['A', 'B', 'C', 'D'].includes(correctAnswer)) {
    throw new Error(
      `Row ${rowIndex + 1}: Invalid correct_answer "${correctAnswerValue}". Must be A, B, C, or D.`
    );
  }

  // Validate difficulty if provided
  let difficulty = 'medium'; // Default
  const difficultyValue = rowData.difficulty;
  if (difficultyValue && typeof difficultyValue === 'string') {
    const validDifficulties = ['easy', 'medium', 'hard'];
    const normalizedDifficulty = difficultyValue.trim().toLowerCase();
    
    if (!validDifficulties.includes(normalizedDifficulty)) {
      throw new Error(
        `Row ${rowIndex + 1}: Invalid difficulty "${difficultyValue}". Must be easy, medium, or hard.`
      );
    }
    
    difficulty = normalizedDifficulty;
  }

  // Create the validated question object
  const now = new Date().toISOString();
  
  return {
    id: uuidv4(),
    topic_id: topicId,
    question_text: (rowData.question_text as string).trim(),
    options: {
      A: (rowData.option_a as string).trim(),
      B: (rowData.option_b as string).trim(),
      C: (rowData.option_c as string).trim(),
      D: (rowData.option_d as string).trim(),
    },
    correct_answer: correctAnswer,
    explanation: (rowData.explanation as string).trim(),
    difficulty,
    created_at: now,
    updated_at: now,
  };
}

/**
 * Resolve topic references from CSV data
 * @param csvData Array of CSV rows with potential topic references
 * @param autoCreateTopics Whether to automatically create missing topics
 * @returns Promise resolving to TopicResolutionResult
 */
export async function resolveTopicsFromCSV(
  csvData: MultiTopicQuestionCSVRow[],
  autoCreateTopics: boolean = false
): Promise<TopicResolutionResult> {
  // Extract unique topic references from CSV data using the same logic as extractTopicReferences
  const topicReferences = extractTopicReferences(csvData);

  // Convert Set to Array for processing
  const referencesArray = Array.from(topicReferences);
  
  // Use the topic service to resolve references
  return await topicService.resolveTopicReferences(referencesArray, autoCreateTopics);
}

/**
 * Generate a CSV template for downloading
 * @returns A CSV string with headers
 */
export function generateCSVTemplate(): string {
  const headers = [
    'question_text',
    'option_a',
    'option_b',
    'option_c',
    'option_d',
    'correct_answer',
    'explanation',
    'difficulty',
  ];
  
  const sampleRow = [
    'What is the primary purpose of a firewall?',
    'To prevent physical access to a network',
    'To control network traffic based on security rules',
    'To encrypt data during transmission',
    'To detect malware on endpoints',
    'B',
    'A firewall controls network traffic flow based on predetermined security rules, allowing or blocking traffic based on these rules.',
    'medium',
  ];
  
  // Create CSV content
  const csvContent = Papa.unparse({
    fields: headers,
    data: [sampleRow],
  });
  
  return csvContent;
}

/**
 * Template format options for multi-topic CSV generation
 */
export type MultiTopicTemplateFormat = 'name-based' | 'id-based' | 'both';

/**
 * Generate a multi-topic CSV template for downloading
 * @param format The format to use for topic identification
 * @returns A CSV string with headers including topic information
 */
export function generateMultiTopicCSVTemplate(format: MultiTopicTemplateFormat = 'name-based'): string {
  let headers: string[];
  let sampleRows: string[][];

  switch (format) {
    case 'id-based':
      headers = [
        'topic_id',
        'question_text',
        'option_a',
        'option_b',
        'option_c',
        'option_d',
        'correct_answer',
        'explanation',
        'difficulty',
      ];
      
      sampleRows = [
        [
          'security-fundamentals-uuid',
          'What is the primary purpose of a firewall?',
          'To prevent physical access to a network',
          'To control network traffic based on security rules',
          'To encrypt data during transmission',
          'To detect malware on endpoints',
          'B',
          'A firewall controls network traffic flow based on predetermined security rules, allowing or blocking traffic based on these rules.',
          'medium',
        ],
        [
          'network-security-uuid',
          'What does VPN stand for?',
          'Virtual Private Network',
          'Very Personal Network',
          'Verified Public Network',
          'Variable Protocol Network',
          'A',
          'VPN stands for Virtual Private Network, which creates a secure tunnel over the internet.',
          'easy',
        ],
        [
          'incident-response-uuid',
          'What is the first step in incident response?',
          'Containment',
          'Identification',
          'Recovery',
          'Lessons learned',
          'B',
          'The first step in incident response is identification - recognizing that a security incident has occurred.',
          'hard',
        ],
      ];
      break;

    case 'both':
      headers = [
        'topic_name',
        'topic_id',
        'question_text',
        'option_a',
        'option_b',
        'option_c',
        'option_d',
        'correct_answer',
        'explanation',
        'difficulty',
      ];
      
      sampleRows = [
        [
          'Security Fundamentals',
          'security-fundamentals-uuid',
          'What is the primary purpose of a firewall?',
          'To prevent physical access to a network',
          'To control network traffic based on security rules',
          'To encrypt data during transmission',
          'To detect malware on endpoints',
          'B',
          'A firewall controls network traffic flow based on predetermined security rules, allowing or blocking traffic based on these rules.',
          'medium',
        ],
        [
          'Network Security',
          'network-security-uuid',
          'What does VPN stand for?',
          'Virtual Private Network',
          'Very Personal Network',
          'Verified Public Network',
          'Variable Protocol Network',
          'A',
          'VPN stands for Virtual Private Network, which creates a secure tunnel over the internet.',
          'easy',
        ],
        [
          'Incident Response',
          'incident-response-uuid',
          'What is the first step in incident response?',
          'Containment',
          'Identification',
          'Recovery',
          'Lessons learned',
          'B',
          'The first step in incident response is identification - recognizing that a security incident has occurred.',
          'hard',
        ],
      ];
      break;

    case 'name-based':
    default:
      headers = [
        'topic_name',
        'question_text',
        'option_a',
        'option_b',
        'option_c',
        'option_d',
        'correct_answer',
        'explanation',
        'difficulty',
      ];
      
      sampleRows = [
        [
          'Security Fundamentals',
          'What is the primary purpose of a firewall?',
          'To prevent physical access to a network',
          'To control network traffic based on security rules',
          'To encrypt data during transmission',
          'To detect malware on endpoints',
          'B',
          'A firewall controls network traffic flow based on predetermined security rules, allowing or blocking traffic based on these rules.',
          'medium',
        ],
        [
          'Network Security',
          'What does VPN stand for?',
          'Virtual Private Network',
          'Very Personal Network',
          'Verified Public Network',
          'Variable Protocol Network',
          'A',
          'VPN stands for Virtual Private Network, which creates a secure tunnel over the internet.',
          'easy',
        ],
        [
          'Incident Response',
          'What is the first step in incident response?',
          'Containment',
          'Identification',
          'Recovery',
          'Lessons learned',
          'B',
          'The first step in incident response is identification - recognizing that a security incident has occurred.',
          'hard',
        ],
      ];
      break;
  }
  
  // Create CSV content
  const csvContent = Papa.unparse({
    fields: headers,
    data: sampleRows,
  });
  
  return csvContent;
}
