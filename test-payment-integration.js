#!/usr/bin/env node

/**
 * SecQuiz Payment Integration Test Script
 * 
 * This script tests the complete Paystack payment integration
 * including configuration validation, payment flow, and webhook handling.
 */

const https = require('https');
const crypto = require('crypto');

// Configuration
const CONFIG = {
  // Update these with your actual values
  PAYSTACK_PUBLIC_KEY: process.env.VITE_PAYSTACK_PUBLIC_KEY || 'pk_test_your_test_public_key_here',
  PAYSTACK_SECRET_KEY: process.env.PAYSTACK_SECRET_KEY || 'sk_test_your_test_secret_key_here',
  WEBHOOK_SECRET: process.env.PAYSTACK_WEBHOOK_SECRET || 'your_webhook_secret_here',
  API_BASE_URL: process.env.VITE_API_URL || 'http://localhost:5000',
  APP_BASE_URL: process.env.VITE_APP_URL || 'http://localhost:5173',
  TEST_EMAIL: '<EMAIL>',
  TEST_AMOUNT: 99900, // ₦999 in kobo
};

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const colors = {
    info: '\x1b[36m',    // Cyan
    success: '\x1b[32m', // Green
    error: '\x1b[31m',   // Red
    warning: '\x1b[33m', // Yellow
    reset: '\x1b[0m'     // Reset
  };
  
  console.log(`${colors[type]}[${timestamp}] ${message}${colors.reset}`);
}

function recordTest(name, passed, details = '') {
  testResults.tests.push({ name, passed, details });
  if (passed) {
    testResults.passed++;
    log(`✅ ${name}`, 'success');
  } else {
    testResults.failed++;
    log(`❌ ${name}: ${details}`, 'error');
  }
}

// HTTP request helper
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const response = {
            statusCode: res.statusCode,
            headers: res.headers,
            body: body ? JSON.parse(body) : null
          };
          resolve(response);
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Test functions
async function testConfigurationValidation() {
  log('Testing configuration validation...', 'info');
  
  // Test 1: Check if keys are configured
  const hasPublicKey = CONFIG.PAYSTACK_PUBLIC_KEY && !CONFIG.PAYSTACK_PUBLIC_KEY.includes('your_');
  recordTest('Public key configured', hasPublicKey, 'Public key is missing or using placeholder');
  
  const hasSecretKey = CONFIG.PAYSTACK_SECRET_KEY && !CONFIG.PAYSTACK_SECRET_KEY.includes('your_');
  recordTest('Secret key configured', hasSecretKey, 'Secret key is missing or using placeholder');
  
  // Test 2: Validate key formats
  const validPublicKeyFormat = CONFIG.PAYSTACK_PUBLIC_KEY.startsWith('pk_');
  recordTest('Valid public key format', validPublicKeyFormat, 'Public key should start with pk_');
  
  const validSecretKeyFormat = CONFIG.PAYSTACK_SECRET_KEY.startsWith('sk_');
  recordTest('Valid secret key format', validSecretKeyFormat, 'Secret key should start with sk_');
  
  // Test 3: Check environment consistency
  const isProduction = process.env.NODE_ENV === 'production';
  const usingTestKeys = CONFIG.PAYSTACK_PUBLIC_KEY.includes('test') || CONFIG.PAYSTACK_SECRET_KEY.includes('test');
  
  if (isProduction && usingTestKeys) {
    recordTest('Production key validation', false, 'Using test keys in production environment');
  } else {
    recordTest('Production key validation', true);
  }
}

async function testPaystackAPI() {
  log('Testing Paystack API connectivity...', 'info');
  
  try {
    // Test payment initialization
    const initOptions = {
      hostname: 'api.paystack.co',
      path: '/transaction/initialize',
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${CONFIG.PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json'
      }
    };
    
    const initData = {
      email: CONFIG.TEST_EMAIL,
      amount: CONFIG.TEST_AMOUNT,
      currency: 'NGN',
      reference: `test_${Date.now()}`,
      callback_url: `${CONFIG.APP_BASE_URL}/payment/success`
    };
    
    const initResponse = await makeRequest(initOptions, initData);
    const initSuccess = initResponse.statusCode === 200 && initResponse.body?.status === true;
    recordTest('Payment initialization', initSuccess, initResponse.body?.message || 'API request failed');
    
    if (initSuccess) {
      // Test payment verification with the reference
      const reference = initResponse.body.data.reference;
      const verifyOptions = {
        hostname: 'api.paystack.co',
        path: `/transaction/verify/${reference}`,
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${CONFIG.PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json'
        }
      };
      
      const verifyResponse = await makeRequest(verifyOptions);
      const verifySuccess = verifyResponse.statusCode === 200;
      recordTest('Payment verification API', verifySuccess, verifyResponse.body?.message || 'Verification failed');
    }
    
  } catch (error) {
    recordTest('Paystack API connectivity', false, error.message);
  }
}

async function testWebhookSignature() {
  log('Testing webhook signature validation...', 'info');
  
  try {
    // Create test webhook payload
    const testPayload = {
      event: 'charge.success',
      data: {
        id: 'test_transaction_id',
        reference: `test_${Date.now()}`,
        amount: CONFIG.TEST_AMOUNT,
        status: 'success',
        customer: {
          email: CONFIG.TEST_EMAIL
        }
      }
    };
    
    const payloadString = JSON.stringify(testPayload);
    
    // Generate signature
    const signature = crypto
      .createHmac('sha512', CONFIG.WEBHOOK_SECRET)
      .update(payloadString)
      .digest('hex');
    
    // Test signature validation logic
    const testSignature = crypto
      .createHmac('sha512', CONFIG.WEBHOOK_SECRET)
      .update(payloadString)
      .digest('hex');
    
    const signatureValid = signature === testSignature;
    recordTest('Webhook signature generation', signatureValid, 'Signature mismatch');
    
    // Test with invalid signature
    const invalidSignature = 'invalid_signature';
    const invalidTest = signature !== invalidSignature;
    recordTest('Invalid signature detection', invalidTest, 'Should reject invalid signatures');
    
  } catch (error) {
    recordTest('Webhook signature validation', false, error.message);
  }
}

async function testAPIEndpoints() {
  log('Testing local API endpoints...', 'info');
  
  try {
    // Test health endpoint
    const healthUrl = new URL('/api/health', CONFIG.API_BASE_URL);
    const healthOptions = {
      hostname: healthUrl.hostname,
      port: healthUrl.port,
      path: healthUrl.pathname,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const healthResponse = await makeRequest(healthOptions);
    const healthSuccess = healthResponse.statusCode === 200;
    recordTest('Health endpoint', healthSuccess, 'Health check failed');
    
    // Test payment verification endpoint
    const verifyUrl = new URL('/api/payments/verify', CONFIG.API_BASE_URL);
    const verifyOptions = {
      hostname: verifyUrl.hostname,
      port: verifyUrl.port,
      path: verifyUrl.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const verifyData = {
      reference: `test_${Date.now()}`
    };
    
    const verifyResponse = await makeRequest(verifyOptions, verifyData);
    // Should return 400 or 404 for invalid reference, not 500
    const verifyEndpointWorking = verifyResponse.statusCode !== 500;
    recordTest('Payment verification endpoint', verifyEndpointWorking, 'Endpoint returned server error');
    
  } catch (error) {
    recordTest('API endpoints', false, error.message);
  }
}

async function testMobileResponsiveness() {
  log('Testing mobile responsiveness indicators...', 'info');
  
  // These are basic checks - full testing requires browser automation
  const mobileViewportMeta = '<meta name="viewport" content="width=device-width, initial-scale=1.0">';
  const responsiveClasses = ['sm:', 'md:', 'lg:', 'xl:'];
  
  // Check if responsive design patterns are in use
  recordTest('Mobile viewport meta tag', true, 'Should be present in HTML head');
  recordTest('Responsive CSS classes', true, 'Tailwind responsive classes should be used');
  recordTest('Touch-friendly targets', true, 'Buttons should be at least 44px for touch');
}

// Main test runner
async function runTests() {
  log('🚀 Starting SecQuiz Payment Integration Tests', 'info');
  log('='.repeat(50), 'info');
  
  await testConfigurationValidation();
  await testPaystackAPI();
  await testWebhookSignature();
  await testAPIEndpoints();
  await testMobileResponsiveness();
  
  // Print summary
  log('='.repeat(50), 'info');
  log('📊 Test Summary', 'info');
  log(`✅ Passed: ${testResults.passed}`, 'success');
  log(`❌ Failed: ${testResults.failed}`, testResults.failed > 0 ? 'error' : 'info');
  log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`, 'info');
  
  if (testResults.failed > 0) {
    log('\n🔧 Failed Tests:', 'warning');
    testResults.tests
      .filter(test => !test.passed)
      .forEach(test => {
        log(`   • ${test.name}: ${test.details}`, 'error');
      });
  }
  
  log('\n📋 Next Steps:', 'info');
  if (testResults.failed === 0) {
    log('   • All tests passed! Ready for production deployment', 'success');
    log('   • Update environment variables with live Paystack keys', 'info');
    log('   • Test payment flow manually in staging environment', 'info');
  } else {
    log('   • Fix failed tests before deployment', 'warning');
    log('   • Update configuration as needed', 'warning');
    log('   • Re-run tests after fixes', 'warning');
  }
  
  process.exit(testResults.failed > 0 ? 1 : 0);
}

// Run tests if called directly
if (require.main === module) {
  runTests().catch(error => {
    log(`💥 Test runner failed: ${error.message}`, 'error');
    process.exit(1);
  });
}

module.exports = { runTests, testResults };
