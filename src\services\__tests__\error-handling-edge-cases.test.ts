/**
 * Error Handling Edge Cases Tests
 * Comprehensive tests for error handling scenarios in quiz randomization system
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock Supabase first
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          not: vi.fn(() => ({ data: [], error: null })),
          limit: vi.fn(() => ({ data: [], error: null })),
          data: [],
          error: null
        })),
        in: vi.fn(() => ({ data: [], error: null })),
        neq: vi.fn(() => ({
          limit: vi.fn(() => ({ data: [], error: null }))
        })),
        data: [],
        error: null
      })),
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(() => ({ data: null, error: null }))
        }))
      })),
      update: vi.fn(() => ({
        eq: vi.fn(() => ({
          eq: vi.fn(() => ({ error: null }))
        }))
      }))
    })),
    rpc: vi.fn(() => ({ data: null, error: null }))
  }
}));

import { errorLogger, ErrorLoggingService } from '../error-logging-service';
import { userFriendlyErrors, UserFriendlyErrorService } from '../user-friendly-errors';
import { systemMonitor, SystemMonitoringService } from '../system-monitoring-service';
import { errorRecovery, ErrorRecoveryService } from '../error-recovery-service';
import QuizRandomizationService from '../quiz-randomization-service';
import { supabase } from '@/integrations/supabase/client';

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};
Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

describe('Error Logging Service Edge Cases', () => {
  beforeEach(() => {
    errorLogger.clearLogs();
    vi.clearAllMocks();
  });

  describe('Memory Management', () => {
    it('should handle memory exhaustion gracefully', () => {
      // Simulate memory pressure by logging many errors
      for (let i = 0; i < 2000; i++) {
        errorLogger.logError(`Test error ${i}`, new Error(`Error ${i}`));
      }

      const metrics = errorLogger.getErrorMetrics();
      expect(metrics.totalErrors).toBeLessThanOrEqual(1000); // Should be trimmed
    });

    it('should handle localStorage quota exceeded', () => {
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('QuotaExceededError');
      });

      expect(() => {
        errorLogger.logError('Test error', new Error('Test'));
      }).not.toThrow();
    });

    it('should handle corrupted localStorage data', () => {
      mockLocalStorage.getItem.mockReturnValue('invalid json {');
      
      expect(() => {
        ErrorLoggingService.getInstance();
      }).not.toThrow();
    });
  });

  describe('Concurrent Error Logging', () => {
    it('should handle rapid concurrent error logging', async () => {
      const promises = Array.from({ length: 100 }, (_, i) =>
        Promise.resolve().then(() => {
          errorLogger.logError(`Concurrent error ${i}`, new Error(`Error ${i}`));
        })
      );

      await Promise.all(promises);
      
      const metrics = errorLogger.getErrorMetrics();
      expect(metrics.totalErrors).toBeGreaterThan(0);
    });

    it('should handle circular reference in error context', () => {
      const circularContext: any = { operation: 'test' };
      circularContext.self = circularContext;

      expect(() => {
        errorLogger.logError('Circular reference test', new Error('Test'), circularContext);
      }).not.toThrow();
    });
  });

  describe('Error Context Edge Cases', () => {
    it('should handle null and undefined context values', () => {
      expect(() => {
        errorLogger.logError('Test', new Error('Test'), {
          userId: null as any,
          topicId: undefined,
          metadata: { value: null, nested: { deep: undefined } }
        });
      }).not.toThrow();
    });

    it('should handle extremely large context objects', () => {
      const largeContext = {
        metadata: {
          largeArray: Array.from({ length: 10000 }, (_, i) => `item_${i}`),
          largeString: 'x'.repeat(100000)
        }
      };

      expect(() => {
        errorLogger.logError('Large context test', new Error('Test'), largeContext);
      }).not.toThrow();
    });
  });
});

describe('User-Friendly Error Service Edge Cases', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Error Message Generation', () => {
    it('should handle errors with no message', () => {
      const error = new Error();
      error.message = '';

      const result = userFriendlyErrors.getRandomizationErrorMessage(error);
      expect(result.title).toBeTruthy();
      expect(result.message).toBeTruthy();
    });

    it('should handle errors with extremely long messages', () => {
      const longMessage = 'x'.repeat(10000);
      const error = new Error(longMessage);

      const result = userFriendlyErrors.getRandomizationErrorMessage(error);
      expect(result.message.length).toBeLessThan(500); // Should be truncated or simplified
    });

    it('should handle non-Error objects', () => {
      const nonError = { message: 'Not an Error object' } as any;

      expect(() => {
        userFriendlyErrors.getRandomizationErrorMessage(nonError);
      }).not.toThrow();
    });

    it('should handle errors with special characters and encoding', () => {
      const specialError = new Error('Error with émojis 🚨 and spëcial chars ñ');

      const result = userFriendlyErrors.getRandomizationErrorMessage(specialError);
      expect(result.message).toBeTruthy();
    });
  });

  describe('Fallback Strategy Edge Cases', () => {
    it('should handle negative question counts', () => {
      const strategy = userFriendlyErrors.getInsufficientQuestionsStrategy(-5, 10, 'Test Topic');
      expect(strategy.type).toBe('redirect');
    });

    it('should handle extremely large question counts', () => {
      const strategy = userFriendlyErrors.getInsufficientQuestionsStrategy(1000000, 10, 'Test Topic');
      expect(strategy.type).toBe('graceful_degradation');
    });

    it('should handle topics with special characters', () => {
      const strategy = userFriendlyErrors.getInsufficientQuestionsStrategy(5, 10, 'Tópic with Spëcial Chars & Symbols!');
      expect(strategy.data.alternativeUrl).toContain(encodeURIComponent('Tópic with Spëcial Chars & Symbols!'));
    });
  });
});

describe('System Monitoring Service Edge Cases', () => {
  beforeEach(() => {
    systemMonitor.clearAlerts();
    vi.clearAllMocks();
  });

  describe('Alert Management', () => {
    it('should handle rapid alert creation', () => {
      for (let i = 0; i < 200; i++) {
        systemMonitor.createAlert('error_rate', 'medium', `Alert ${i}`);
      }

      const alerts = systemMonitor.getActiveAlerts();
      expect(alerts.length).toBeLessThanOrEqual(100); // Should be trimmed
    });

    it('should handle alert creation with invalid parameters', () => {
      expect(() => {
        systemMonitor.createAlert(null as any, 'medium', '');
      }).not.toThrow();

      expect(() => {
        systemMonitor.createAlert('error_rate', null as any, 'Test message');
      }).not.toThrow();
    });

    it('should handle system health check during high error load', () => {
      // Generate many errors
      for (let i = 0; i < 50; i++) {
        errorLogger.logError(`Load test error ${i}`, new Error(`Error ${i}`));
      }

      const health = systemMonitor.getSystemHealth();
      expect(health.status).toBeDefined();
      expect(['healthy', 'degraded', 'critical']).toContain(health.status);
    });
  });

  describe('Configuration Edge Cases', () => {
    it('should handle invalid monitoring configuration', () => {
      expect(() => {
        systemMonitor.updateConfig({
          checkInterval: -1,
          thresholds: {
            errorRateWarning: -5,
            errorRateCritical: null as any,
            criticalErrorThreshold: 'invalid' as any,
            randomizationFailureThreshold: undefined as any,
            sessionFailureThreshold: Infinity
          }
        } as any);
      }).not.toThrow();
    });

    it('should handle monitoring during browser tab visibility changes', () => {
      // Simulate tab becoming hidden
      Object.defineProperty(document, 'hidden', { value: true, configurable: true });
      
      const health = systemMonitor.performHealthCheck();
      expect(health).toBeDefined();

      // Simulate tab becoming visible again
      Object.defineProperty(document, 'hidden', { value: false, configurable: true });
    });
  });
});

describe('Error Recovery Service Edge Cases', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Question Selection Recovery', () => {
    it('should handle recovery when all fallback strategies fail', async () => {
      // Mock all database calls to fail
      (supabase.from as any).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            limit: vi.fn(() => ({ data: null, error: { message: 'Database error' } }))
          })),
          neq: vi.fn(() => ({
            limit: vi.fn(() => ({ data: null, error: { message: 'Database error' } }))
          }))
        }))
      });

      const result = await errorRecovery.recoverFromQuestionSelectionFailure(
        'topic1',
        10,
        new Error('Original error')
      );

      expect(result.success).toBe(false);
      expect(result.userMessage).toBeTruthy();
    });

    it('should handle recovery with corrupted question data', async () => {
      const corruptedQuestions = [
        { id: null, options: 'invalid', correct_answer: undefined },
        { id: 'valid', options: { '0': 'A', '1': 'B' }, correct_answer: '0' }
      ];

      (supabase.from as any).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            limit: vi.fn(() => ({ data: corruptedQuestions, error: null }))
          }))
        }))
      });

      const result = await errorRecovery.recoverFromQuestionSelectionFailure(
        'topic1',
        10,
        new Error('Original error')
      );

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });
  });

  describe('Shuffling Recovery', () => {
    it('should handle questions with completely invalid data', () => {
      const invalidQuestion = {
        id: null,
        topic_id: undefined,
        question_text: '',
        options: 'not an object',
        correct_answer: 'invalid',
        explanation: null,
        difficulty: 'unknown',
        usage_count: -1,
        last_used: 'invalid date',
        correct_answer_rate: 'not a number',
        created_at: null,
        updated_at: undefined,
        created_by: {},
        is_premium: 'not boolean'
      } as any;

      const result = errorRecovery.recoverFromShufflingFailure(
        invalidQuestion,
        new Error('Shuffling failed')
      );

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });

    it('should handle questions with circular references in options', () => {
      const circularOptions: any = { '0': 'Option A' };
      circularOptions['1'] = circularOptions;

      const questionWithCircularRef = {
        id: 'test',
        topic_id: 'topic1',
        question_text: 'Test',
        options: circularOptions,
        correct_answer: '0',
        explanation: 'Test',
        difficulty: 'medium',
        usage_count: 0,
        last_used: null,
        correct_answer_rate: null,
        created_at: '2024-01-01',
        updated_at: '2024-01-01',
        created_by: null,
        is_premium: false
      } as any;

      const result = errorRecovery.recoverFromShufflingFailure(
        questionWithCircularRef,
        new Error('Circular reference error')
      );

      expect(result.success).toBe(true);
    });
  });

  describe('Session Recovery', () => {
    it('should handle session recovery when database is completely unavailable', async () => {
      (supabase.from as any).mockImplementation(() => {
        throw new Error('Database completely unavailable');
      });

      const result = await errorRecovery.recoverFromSessionFailure(
        'user1',
        'topic1',
        { questions: [{ id: 'q1' }] },
        new Error('Session creation failed')
      );

      expect(result.success).toBe(true);
      expect(result.data.temporary).toBe(true);
    });

    it('should handle session recovery with invalid user/topic IDs', async () => {
      const result = await errorRecovery.recoverFromSessionFailure(
        '', // Invalid user ID
        null as any, // Invalid topic ID
        { questions: [] },
        new Error('Invalid IDs')
      );

      expect(result.success).toBe(true); // Should still create temporary session
    });

    it('should handle session recovery with extremely large question data', async () => {
      const largeQuestionsData = {
        questions: Array.from({ length: 10000 }, (_, i) => ({
          id: `q${i}`,
          data: 'x'.repeat(1000) // Large data per question
        }))
      };

      const result = await errorRecovery.recoverFromSessionFailure(
        'user1',
        'topic1',
        largeQuestionsData,
        new Error('Data too large')
      );

      expect(result.success).toBe(true);
    });
  });

  describe('Database Recovery', () => {
    it('should handle database recovery with network timeouts', async () => {
      const timeoutFunction = async () => {
        await new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Network timeout')), 100);
        });
      };

      const result = await errorRecovery.recoverFromDatabaseFailure(
        'test_operation',
        timeoutFunction,
        new Error('Timeout error')
      );

      expect(result.success).toBe(false);
      expect(result.userMessage).toBeTruthy();
    });

    it('should handle database recovery with intermittent failures', async () => {
      let attemptCount = 0;
      const intermittentFunction = async () => {
        attemptCount++;
        if (attemptCount < 3) {
          throw new Error('Intermittent failure');
        }
        return { success: true, data: 'recovered' };
      };

      const result = await errorRecovery.recoverFromDatabaseFailure(
        'intermittent_operation',
        intermittentFunction,
        new Error('Initial failure')
      );

      expect(result.success).toBe(true);
      expect(result.data).toEqual({ success: true, data: 'recovered' });
    });
  });

  describe('Emergency Mode', () => {
    it('should handle emergency mode with no fallback data', () => {
      errorRecovery.updateEmergencyConfig({
        enabled: true,
        fallbackQuestions: [],
        fallbackTopics: []
      });

      const result = errorRecovery.recoverFromShufflingFailure(
        {
          id: 'test',
          options: null,
          correct_answer: 'invalid'
        } as any,
        new Error('No fallback available')
      );

      expect(result.success).toBe(true); // Should still provide default options
    });

    it('should handle emergency mode configuration with invalid values', () => {
      expect(() => {
        errorRecovery.updateEmergencyConfig({
          enabled: null as any,
          maxRetries: -1,
          retryDelay: 'invalid' as any,
          fallbackQuestions: 'not an array' as any
        });
      }).not.toThrow();
    });
  });
});

describe('Integration Error Scenarios', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    errorLogger.clearLogs();
    systemMonitor.clearAlerts();
  });

  it('should handle cascading failures across all services', async () => {
    // Simulate a scenario where multiple systems fail simultaneously
    
    // 1. Database fails
    (supabase.from as any).mockImplementation(() => {
      throw new Error('Database connection lost');
    });

    // 2. localStorage fails
    mockLocalStorage.setItem.mockImplementation(() => {
      throw new Error('Storage quota exceeded');
    });

    // 3. Try to generate a quiz session (should trigger all error handling)
    try {
      await QuizRandomizationService.generateQuizSession('topic1', 'user1', 10);
    } catch (error) {
      // Expected to fail, but should not crash the system
    }

    // System should still be responsive
    const health = systemMonitor.getSystemHealth();
    expect(health).toBeDefined();
    expect(health.status).toBe('critical');

    // Error logging should still work (in memory)
    const metrics = errorLogger.getErrorMetrics();
    expect(metrics.totalErrors).toBeGreaterThan(0);
  });

  it('should handle browser environment edge cases', () => {
    // Simulate browser without localStorage
    Object.defineProperty(window, 'localStorage', { value: undefined });

    expect(() => {
      errorLogger.logError('Test without localStorage', new Error('Test'));
    }).not.toThrow();

    // Simulate browser without console
    const originalConsole = global.console;
    global.console = undefined as any;

    expect(() => {
      errorLogger.logError('Test without console', new Error('Test'));
    }).not.toThrow();

    // Restore console
    global.console = originalConsole;
  });

  it('should handle memory pressure scenarios', () => {
    // Simulate low memory by creating large objects
    const largeObjects = [];
    
    try {
      for (let i = 0; i < 1000; i++) {
        largeObjects.push(new Array(10000).fill(`data_${i}`));
        
        // Log errors during memory pressure
        errorLogger.logError(`Memory pressure test ${i}`, new Error(`Error ${i}`));
        
        // Check system health periodically
        if (i % 100 === 0) {
          const health = systemMonitor.getSystemHealth();
          expect(health).toBeDefined();
        }
      }
    } catch (error) {
      // Memory exhaustion is expected, system should handle gracefully
    }

    // System should still be functional
    const finalHealth = systemMonitor.getSystemHealth();
    expect(finalHealth).toBeDefined();
  });
});

describe('Performance Under Error Conditions', () => {
  it('should maintain performance during high error rates', () => {
    const startTime = Date.now();
    
    // Generate many errors rapidly
    for (let i = 0; i < 1000; i++) {
      errorLogger.logError(`Performance test ${i}`, new Error(`Error ${i}`));
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Should complete within reasonable time (less than 1 second)
    expect(duration).toBeLessThan(1000);
    
    // System should still be responsive
    const health = systemMonitor.getSystemHealth();
    expect(health).toBeDefined();
  });

  it('should handle concurrent error operations efficiently', async () => {
    const operations = Array.from({ length: 100 }, (_, i) => 
      Promise.resolve().then(async () => {
        errorLogger.logError(`Concurrent ${i}`, new Error(`Error ${i}`));
        
        if (i % 10 === 0) {
          systemMonitor.createAlert('error_rate', 'medium', `Alert ${i}`);
        }
        
        if (i % 20 === 0) {
          return errorRecovery.recoverFromShufflingFailure(
            { id: `q${i}`, options: null } as any,
            new Error(`Shuffle error ${i}`)
          );
        }
      })
    );

    const startTime = Date.now();
    await Promise.all(operations);
    const duration = Date.now() - startTime;

    // Should complete efficiently
    expect(duration).toBeLessThan(2000);
  });
});