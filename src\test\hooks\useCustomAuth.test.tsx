import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useCustomAuth } from '@/hooks/use-custom-auth';
import { supabase } from '@/integrations/supabase/client';

// Mock Supabase service
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    auth: {
      signUp: vi.fn(),
      resetPasswordForEmail: vi.fn(),
    },
  },
}));


describe('useCustomAuth hook', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();
    
    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: {
        origin: 'http://localhost:3000',
      },
      writable: true,
    });
  });

  it('initializes with loading=false and error=null', () => {
    const { result } = renderHook(() => useCustomAuth());
    
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
  });

  describe('signUp function', () => {
    it('signs up a user successfully', async () => {
      // Mock successful responses
      vi.mocked(supabase.auth.signUp).mockResolvedValue({
        data: {
          user: {
            id: '123',
            email: '<EMAIL>',
            confirmation_token: 'test-token',
          },
        },
        error: null,
      } as any);
      
      
      const { result } = renderHook(() => useCustomAuth());
      
      let signUpResult;
      await act(async () => {
        signUpResult = await result.current.signUp('<EMAIL>', 'password123', { name: 'Test User' });
      });
      
      // Check loading state during and after operation
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(null);
      
      // Check function was called with correct params
      expect(supabase.auth.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
        options: {
          data: { name: 'Test User' },
          emailRedirectTo: 'http://localhost:3000/auth/callback',
        },
      });
      
      // Verification email is handled by Supabase with Resend
      
      // Check result
      expect(signUpResult).toEqual({
        success: true,
        message: 'Account created! Please check your email to verify your account.',
        data: {
          user: {
            id: '123',
            email: '<EMAIL>',
            confirmation_token: 'test-token',
          },
        },
      });
    });

    it('handles sign up error from Supabase', async () => {
      // Mock error response from Supabase
      vi.mocked(supabase.auth.signUp).mockResolvedValue({
        data: null,
        error: new Error('Email already in use'),
      } as any);
      
      const { result } = renderHook(() => useCustomAuth());
      
      let signUpResult;
      await act(async () => {
        signUpResult = await result.current.signUp('<EMAIL>', 'password123');
      });
      
      // Check error state
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe('Email already in use');
      
      // Check result
      expect(signUpResult).toEqual({
        success: false,
        error: 'Email already in use',
      });
      
      // Verification email is handled by Supabase with Resend
    });

    it('handles email sending error', async () => {
      // Mock successful Supabase response but failed email
      vi.mocked(supabase.auth.signUp).mockResolvedValue({
        data: {
          user: {
            id: '123',
            email: '<EMAIL>',
            confirmation_token: 'test-token',
          },
        },
        error: null,
      } as any);
      
      // Email verification is now handled by Supabase with Resend
      
      const { result } = renderHook(() => useCustomAuth());
      
      let signUpResult;
      await act(async () => {
        signUpResult = await result.current.signUp('<EMAIL>', 'password123');
      });
      
      // Check error state
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe('Failed to send email');
      
      // Check result
      expect(signUpResult).toEqual({
        success: false,
        error: 'Failed to send email',
      });
    });
  });

  describe('resetPassword function', () => {
    it('sends password reset email successfully', async () => {
      // Mock successful responses
      vi.mocked(supabase.auth.resetPasswordForEmail).mockResolvedValue({
        data: {},
        error: null,
      } as any);
      
      // Password reset email is now handled by Supabase with Resend
      
      const { result } = renderHook(() => useCustomAuth());
      
      let resetResult;
      await act(async () => {
        resetResult = await result.current.resetPassword('<EMAIL>');
      });
      
      // Check loading state
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(null);
      
      // Check function was called with correct params
      expect(supabase.auth.resetPasswordForEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        {
          redirectTo: 'http://localhost:3000/auth/reset-password',
        }
      );
      
      // Password reset email is handled by Supabase with Resend
      
      // Check result
      expect(resetResult).toEqual({
        success: true,
        message: 'Password reset instructions sent to your email.',
        data: {},
      });
    });

    it('handles reset password error from Supabase', async () => {
      // Mock error response from Supabase
      vi.mocked(supabase.auth.resetPasswordForEmail).mockResolvedValue({
        data: null,
        error: new Error('User not found'),
      } as any);
      
      const { result } = renderHook(() => useCustomAuth());
      
      let resetResult;
      await act(async () => {
        resetResult = await result.current.resetPassword('<EMAIL>');
      });
      
      // Check error state
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe('User not found');
      
      // Check result
      expect(resetResult).toEqual({
        success: false,
        error: 'User not found',
      });
      
      // Password reset email is handled by Supabase with Resend
    });

    it('handles email sending error for password reset', async () => {
      // Mock successful Supabase response but failed email
      vi.mocked(supabase.auth.resetPasswordForEmail).mockResolvedValue({
        data: {},
        error: null,
      } as any);
      
      // Password reset email is now handled by Supabase with Resend
      
      const { result } = renderHook(() => useCustomAuth());
      
      let resetResult;
      await act(async () => {
        resetResult = await result.current.resetPassword('<EMAIL>');
      });
      
      // Check error state
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe('Failed to send email');
      
      // Check result
      expect(resetResult).toEqual({
        success: false,
        error: 'Failed to send email',
      });
    });
  });
});
