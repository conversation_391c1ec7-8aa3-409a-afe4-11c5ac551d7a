# Requirements Document

## Introduction

This feature enhances the existing CSV import functionality in SecQuiz to support importing questions for multiple topics within a single CSV file. Currently, users must create separate CSV files for each topic and perform multiple import operations. This enhancement will streamline the bulk import process by allowing users to specify topic information directly within the CSV file, enabling efficient mass import of quiz questions across multiple topics in one operation.

## Requirements

### Requirement 1

**User Story:** As an admin user, I want to import quiz questions for multiple topics using a single CSV file, so that I can efficiently bulk-load questions without having to perform separate imports for each topic.

#### Acceptance Criteria

1. WHEN I upload a CSV file with a topic_name or topic_id column THEN the system SHALL parse and validate the topic information for each question
2. WHEN the CSV contains questions for topics that don't exist THEN the system SHALL provide clear error messages indicating which topics are missing
3. WHEN the CSV contains questions for existing topics THEN the system SHALL successfully import the questions to their respective topics
4. WHEN I import a multi-topic CSV THEN the system SHALL display a summary showing how many questions were imported per topic

### Requirement 2

**User Story:** As an admin user, I want the system to automatically create new topics from the CSV file, so that I can set up new quiz categories without having to pre-create them in the admin interface.

#### Acceptance Criteria

1. WHEN the CSV contains a topic_name that doesn't exist in the system THEN the system SHALL offer an option to automatically create the new topic
2. WHEN I choose to auto-create topics THEN the system SHALL create topics with the specified names and default settings
3. WHEN auto-creating topics THEN the system SHALL validate that topic names are unique and follow naming conventions
4. WHEN topic creation fails THEN the system SHALL provide specific error messages and skip questions for failed topics

### Requirement 3

**User Story:** As an admin user, I want to maintain backward compatibility with the existing single-topic CSV format, so that my existing CSV files and workflows continue to work without modification.

#### Acceptance Criteria

1. WHEN I upload a CSV file without topic columns THEN the system SHALL use the existing single-topic import flow
2. WHEN I upload a CSV file with topic columns but select a specific topic in the UI THEN the system SHALL ignore the CSV topic columns and use the selected topic
3. WHEN using the legacy format THEN all existing validation rules and error handling SHALL continue to work as before
4. WHEN switching between single-topic and multi-topic modes THEN the UI SHALL clearly indicate which mode is active

### Requirement 4

**User Story:** As an admin user, I want comprehensive validation and error reporting for multi-topic imports, so that I can quickly identify and fix issues in my CSV files.

#### Acceptance Criteria

1. WHEN the CSV has invalid topic references THEN the system SHALL report specific errors with row numbers and topic names
2. WHEN questions fail validation THEN the system SHALL group errors by topic and question for easy identification
3. WHEN some topics succeed and others fail THEN the system SHALL complete successful imports and report failed ones separately
4. WHEN validation errors occur THEN the system SHALL provide actionable suggestions for fixing the issues

### Requirement 5

**User Story:** As an admin user, I want to preview the import results before committing changes, so that I can verify the questions will be imported to the correct topics.

#### Acceptance Criteria

1. WHEN I upload a multi-topic CSV THEN the system SHALL display a preview showing questions grouped by topic
2. WHEN previewing THEN the system SHALL show topic names, question counts, and any validation warnings
3. WHEN I confirm the preview THEN the system SHALL proceed with the actual import
4. WHEN I cancel the preview THEN the system SHALL discard the parsed data and allow me to upload a different file
