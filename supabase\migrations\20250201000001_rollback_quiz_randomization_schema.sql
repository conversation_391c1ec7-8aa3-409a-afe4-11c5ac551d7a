-- Rollback Migration: Remove Quiz Randomization Schema Enhancements
-- This migration removes all changes made by the quiz randomization schema migration
-- Use this if you need to rollback the changes

-- Drop triggers first (they depend on functions)
DROP TRIGGER IF EXISTS trigger_update_question_usage ON question_analytics;
DROP TRIGGER IF EXISTS trigger_update_correct_answer_rate ON question_analytics;

-- Drop functions
DROP FUNCTION IF EXISTS update_question_usage_stats();
DROP FUNCTION IF EXISTS update_question_correct_answer_rate();
DROP FUNCTION IF EXISTS cleanup_expired_quiz_sessions();
DROP FUNCTION IF EXISTS get_topic_question_stats(UUID);

-- Drop tables (in reverse dependency order)
DROP TABLE IF EXISTS question_analytics CASCADE;
DROP TABLE IF EXISTS quiz_sessions CASCADE;

-- Remove columns from questions table
ALTER TABLE questions 
DROP COLUMN IF EXISTS usage_count,
DROP COLUMN IF EXISTS last_used,
DROP COLUMN IF EXISTS correct_answer_rate;

-- Drop indexes (if they still exist)
DROP INDEX IF EXISTS idx_questions_usage_count;
DROP INDEX IF EXISTS idx_questions_last_used;
DROP INDEX IF EXISTS idx_questions_correct_answer_rate;
DROP INDEX IF EXISTS idx_quiz_sessions_user_id;
DROP INDEX IF EXISTS idx_quiz_sessions_topic_id;
DROP INDEX IF EXISTS idx_quiz_sessions_created_at;
DROP INDEX IF EXISTS idx_quiz_sessions_expires_at;
DROP INDEX IF EXISTS idx_quiz_sessions_completed_at;
DROP INDEX IF EXISTS idx_question_analytics_question_id;
DROP INDEX IF EXISTS idx_question_analytics_user_id;
DROP INDEX IF EXISTS idx_question_analytics_quiz_session_id;
DROP INDEX IF EXISTS idx_question_analytics_created_at;
DROP INDEX IF EXISTS idx_question_analytics_answered_correctly;

-- Note: This rollback script removes all quiz randomization enhancements
-- Make sure to backup any important data before running this rollback