// Simple script to convert your generated questions to CSV format
// Run with: node convert-questions-to-csv.js

const fs = require('fs');
const path = require('path');

function parseQuestionsFromMarkdown(content, topicTitle, difficulty) {
    const questions = [];
    const questionBlocks = content.split('**Question ').slice(1); // Remove first empty element
    
    questionBlocks.forEach(block => {
        try {
            const lines = block.split('\n').filter(line => line.trim());
            
            // Extract question text (first line after question number)
            const questionMatch = lines[0].match(/\d+:\*\* (.+)/);
            if (!questionMatch) return;
            const questionText = questionMatch[1];
            
            // Extract options
            const options = [];
            let correctAnswer = '';
            let explanation = '';
            
            for (let i = 1; i < lines.length; i++) {
                const line = lines[i].trim();
                
                if (line.match(/^[A-D]\)/)) {
                    options.push(line.substring(3).trim());
                } else if (line.startsWith('**Correct Answer:**')) {
                    correctAnswer = line.replace('**Correct Answer:**', '').trim();
                } else if (line.startsWith('**Explanation:**')) {
                    explanation = line.replace('**Explanation:**', '').trim();
                }
            }
            
            // Convert letter to number (A=0, B=1, C=2, D=3)
            const correctIndex = correctAnswer.charCodeAt(0) - 65;
            
            if (options.length === 4 && correctIndex >= 0 && correctIndex < 4) {
                questions.push({
                    topic_title: topicTitle,
                    question_text: questionText,
                    option_a: options[0] || '',
                    option_b: options[1] || '',
                    option_c: options[2] || '',
                    option_d: options[3] || '',
                    correct_answer: correctIndex.toString(),
                    explanation: explanation,
                    difficulty: difficulty
                });
            }
        } catch (error) {
            console.log('Error parsing question block:', error.message);
        }
    });
    
    return questions;
}

function convertToCSV(questions) {
    const headers = [
        'topic_title',
        'question_text', 
        'option_a',
        'option_b',
        'option_c',
        'option_d',
        'correct_answer',
        'explanation',
        'difficulty'
    ];
    
    const csvRows = [headers.join(',')];
    
    questions.forEach(q => {
        const row = [
            `"${q.topic_title.replace(/"/g, '""')}"`,
            `"${q.question_text.replace(/"/g, '""')}"`,
            `"${q.option_a.replace(/"/g, '""')}"`,
            `"${q.option_b.replace(/"/g, '""')}"`,
            `"${q.option_c.replace(/"/g, '""')}"`,
            `"${q.option_d.replace(/"/g, '""')}"`,
            q.correct_answer,
            `"${q.explanation.replace(/"/g, '""')}"`,
            q.difficulty
        ];
        csvRows.push(row.join(','));
    });
    
    return csvRows.join('\n');
}

// Main conversion process
async function convertAllQuestions() {
    const questionsDir = './generated-questions';
    const outputDir = './csv-questions';
    
    // Create output directory if it doesn't exist
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir);
    }
    
    const conversions = [
        {
            file: 'network-security-beginner.md',
            topic: 'Network Security',
            difficulty: 'easy'
        },
        {
            file: 'web-app-security-intermediate.md', 
            topic: 'Web Application Security',
            difficulty: 'medium'
        },
        {
            file: 'cryptography-advanced.md',
            topic: 'Cryptography',
            difficulty: 'hard'
        }
    ];
    
    let allQuestions = [];
    
    for (const conversion of conversions) {
        const filePath = path.join(questionsDir, conversion.file);
        
        if (fs.existsSync(filePath)) {
            console.log(`Converting ${conversion.file}...`);
            
            const content = fs.readFileSync(filePath, 'utf8');
            const questions = parseQuestionsFromMarkdown(content, conversion.topic, conversion.difficulty);
            
            console.log(`  Found ${questions.length} questions`);
            
            // Save individual CSV file
            const csvContent = convertToCSV(questions);
            const csvFileName = conversion.file.replace('.md', '.csv');
            fs.writeFileSync(path.join(outputDir, csvFileName), csvContent);
            
            allQuestions = allQuestions.concat(questions);
        } else {
            console.log(`File not found: ${filePath}`);
        }
    }
    
    // Save combined CSV file
    if (allQuestions.length > 0) {
        const allCsvContent = convertToCSV(allQuestions);
        fs.writeFileSync(path.join(outputDir, 'all-questions.csv'), allCsvContent);
        console.log(`\n✅ Conversion complete!`);
        console.log(`📊 Total questions converted: ${allQuestions.length}`);
        console.log(`📁 Files saved in: ${outputDir}/`);
        console.log(`\nFiles created:`);
        console.log(`  - network-security-beginner.csv`);
        console.log(`  - web-app-security-intermediate.csv`);
        console.log(`  - cryptography-advanced.csv`);
        console.log(`  - all-questions.csv (combined)`);
    }
}

// Run the conversion
convertAllQuestions().catch(console.error);