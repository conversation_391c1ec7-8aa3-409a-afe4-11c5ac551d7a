import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { TopicService, type Topic, type CreateTopicInput } from '../topic-service';

// Mock the supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(),
  },
}));

// Mock uuid
vi.mock('uuid', () => ({
  v4: vi.fn(() => 'mock-uuid-123'),
}));

describe('TopicService', () => {
  let topicService: TopicService;
  let mockSupabase: any;

  beforeEach(async () => {
    // Import the mocked supabase
    const { supabase } = await import('@/integrations/supabase/client');
    mockSupabase = supabase;
    
    topicService = new TopicService();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('findTopic', () => {
    it('should find topic by ID when exact match exists', async () => {
      const mockTopic: Topic = {
        id: 'topic-123',
        title: 'Security Fundamentals',
        description: 'Basic security concepts',
        difficulty: 'medium',
        is_active: true,
        is_premium: false,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        created_by: null,
        icon: null,
      };

      const mockChain = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ data: mockTopic, error: null }),
      };

      mockSupabase.from.mockReturnValue(mockChain);

      const result = await topicService.findTopic('topic-123');

      expect(result).toEqual(mockTopic);
      expect(mockSupabase.from).toHaveBeenCalledWith('topics');
      expect(mockChain.select).toHaveBeenCalledWith('*');
      expect(mockChain.eq).toHaveBeenCalledWith('id', 'topic-123');
      expect(mockChain.eq).toHaveBeenCalledWith('is_active', true);
    });

    it('should find topic by title when ID match fails', async () => {
      const mockTopic: Topic = {
        id: 'topic-456',
        title: 'Network Security',
        description: 'Network security concepts',
        difficulty: 'hard',
        is_active: true,
        is_premium: false,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        created_by: null,
        icon: null,
      };

      const mockChain = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        ilike: vi.fn().mockReturnThis(),
        single: vi.fn()
          .mockResolvedValueOnce({ data: null, error: { code: 'PGRST116' } }) // ID search fails
          .mockResolvedValueOnce({ data: mockTopic, error: null }), // Title search succeeds
      };

      mockSupabase.from.mockReturnValue(mockChain);

      const result = await topicService.findTopic('Network Security');

      expect(result).toEqual(mockTopic);
      expect(mockChain.ilike).toHaveBeenCalledWith('title', 'Network Security');
    });

    it('should return null when topic is not found', async () => {
      const mockChain = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        ilike: vi.fn().mockReturnThis(),
        single: vi.fn()
          .mockResolvedValueOnce({ data: null, error: { code: 'PGRST116' } }) // ID search fails
          .mockResolvedValueOnce({ data: null, error: { code: 'PGRST116' } }), // Title search fails
      };

      mockSupabase.from.mockReturnValue(mockChain);

      const result = await topicService.findTopic('Non-existent Topic');

      expect(result).toBeNull();
    });

    it('should handle database errors gracefully', async () => {
      const mockChain = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockRejectedValue(new Error('Database connection failed')),
      };

      mockSupabase.from.mockReturnValue(mockChain);

      const result = await topicService.findTopic('topic-123');

      expect(result).toBeNull();
    });
  });

  describe('createTopic', () => {
    it('should create a new topic successfully', async () => {
      const input: CreateTopicInput = {
        title: 'New Security Topic',
        description: 'A new topic about security',
        difficulty: 'easy',
        is_premium: false,
      };

      const mockCreatedTopic: Topic = {
        id: 'mock-uuid-123',
        title: 'New Security Topic',
        description: 'A new topic about security',
        difficulty: 'easy',
        is_active: true,
        is_premium: false,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        created_by: null,
        icon: null,
      };

      // Mock findTopic to return null (topic doesn't exist)
      vi.spyOn(topicService, 'findTopic').mockResolvedValue(null);

      const mockChain = {
        insert: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ data: mockCreatedTopic, error: null }),
      };

      mockSupabase.from.mockReturnValue(mockChain);

      const result = await topicService.createTopic(input);

      expect(result).toEqual(mockCreatedTopic);
      expect(mockChain.insert).toHaveBeenCalledWith(expect.objectContaining({
        id: 'mock-uuid-123',
        title: 'New Security Topic',
        description: 'A new topic about security',
        difficulty: 'easy',
        is_active: true,
        is_premium: false,
      }));
    });

    it('should throw error for empty title', async () => {
      const input: CreateTopicInput = {
        title: '',
      };

      await expect(topicService.createTopic(input)).rejects.toThrow('Topic title is required');
    });

    it('should throw error for title that is too long', async () => {
      const input: CreateTopicInput = {
        title: 'A'.repeat(101), // 101 characters
      };

      await expect(topicService.createTopic(input)).rejects.toThrow('Topic title must be 100 characters or less');
    });

    it('should throw error for invalid difficulty', async () => {
      const input: CreateTopicInput = {
        title: 'Valid Title',
        difficulty: 'invalid' as any,
      };

      await expect(topicService.createTopic(input)).rejects.toThrow('Difficulty must be easy, medium, or hard');
    });

    it('should throw error when topic with same title already exists', async () => {
      const input: CreateTopicInput = {
        title: 'Existing Topic',
      };

      const existingTopic: Topic = {
        id: 'existing-123',
        title: 'Existing Topic',
        description: null,
        difficulty: 'medium',
        is_active: true,
        is_premium: false,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        created_by: null,
        icon: null,
      };

      vi.spyOn(topicService, 'findTopic').mockResolvedValue(existingTopic);

      await expect(topicService.createTopic(input)).rejects.toThrow('Topic with title "Existing Topic" already exists');
    });

    it('should handle database errors during creation', async () => {
      const input: CreateTopicInput = {
        title: 'New Topic',
      };

      vi.spyOn(topicService, 'findTopic').mockResolvedValue(null);

      const mockChain = {
        insert: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ 
          data: null, 
          error: { message: 'Database constraint violation' } 
        }),
      };

      mockSupabase.from.mockReturnValue(mockChain);

      await expect(topicService.createTopic(input)).rejects.toThrow('Failed to create topic: Database constraint violation');
    });
  });

  describe('resolveTopicReferences', () => {
    it('should resolve existing topics successfully', async () => {
      const references = ['Security Fundamentals', 'Network Security'];
      
      const mockTopics: Topic[] = [
        {
          id: 'topic-123',
          title: 'Security Fundamentals',
          description: null,
          difficulty: 'medium',
          is_active: true,
          is_premium: false,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
          created_by: null,
          icon: null,
        },
        {
          id: 'topic-456',
          title: 'Network Security',
          description: null,
          difficulty: 'hard',
          is_active: true,
          is_premium: false,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
          created_by: null,
          icon: null,
        },
      ];

      vi.spyOn(topicService, 'findTopic')
        .mockResolvedValueOnce(mockTopics[0])
        .mockResolvedValueOnce(mockTopics[1]);

      const result = await topicService.resolveTopicReferences(references, false);

      expect(result.resolved.size).toBe(2);
      expect(result.resolved.get('Security Fundamentals')).toBe('topic-123');
      expect(result.resolved.get('Network Security')).toBe('topic-456');
      expect(result.missing).toEqual([]);
      expect(result.created).toEqual([]);
      expect(result.errors).toEqual([]);
    });

    it('should identify missing topics when autoCreate is false', async () => {
      const references = ['Existing Topic', 'Missing Topic'];
      
      const existingTopic: Topic = {
        id: 'topic-123',
        title: 'Existing Topic',
        description: null,
        difficulty: 'medium',
        is_active: true,
        is_premium: false,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        created_by: null,
        icon: null,
      };

      vi.spyOn(topicService, 'findTopic')
        .mockResolvedValueOnce(existingTopic)
        .mockResolvedValueOnce(null);

      const result = await topicService.resolveTopicReferences(references, false);

      expect(result.resolved.size).toBe(1);
      expect(result.resolved.get('Existing Topic')).toBe('topic-123');
      expect(result.missing).toEqual(['Missing Topic']);
      expect(result.created).toEqual([]);
      expect(result.errors).toEqual([]);
    });

    it('should create missing topics when autoCreate is true', async () => {
      const references = ['Existing Topic', 'New Topic'];
      
      const existingTopic: Topic = {
        id: 'topic-123',
        title: 'Existing Topic',
        description: null,
        difficulty: 'medium',
        is_active: true,
        is_premium: false,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        created_by: null,
        icon: null,
      };

      const newTopic: Topic = {
        id: 'mock-uuid-123',
        title: 'New Topic',
        description: null,
        difficulty: 'medium',
        is_active: true,
        is_premium: false,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        created_by: null,
        icon: null,
      };

      vi.spyOn(topicService, 'findTopic')
        .mockResolvedValueOnce(existingTopic)
        .mockResolvedValueOnce(null);
      
      vi.spyOn(topicService, 'createTopic').mockResolvedValue(newTopic);

      const result = await topicService.resolveTopicReferences(references, true);

      expect(result.resolved.size).toBe(2);
      expect(result.resolved.get('Existing Topic')).toBe('topic-123');
      expect(result.resolved.get('New Topic')).toBe('mock-uuid-123');
      expect(result.missing).toEqual([]);
      expect(result.created).toEqual(['New Topic']);
      expect(result.errors).toEqual([]);
    });

    it('should handle creation errors gracefully', async () => {
      const references = ['New Topic'];

      vi.spyOn(topicService, 'findTopic').mockResolvedValue(null);
      vi.spyOn(topicService, 'createTopic').mockRejectedValue(new Error('Creation failed'));

      const result = await topicService.resolveTopicReferences(references, true);

      expect(result.resolved.size).toBe(0);
      expect(result.missing).toEqual(['New Topic']);
      expect(result.created).toEqual([]);
      expect(result.errors).toEqual(['Failed to create topic "New Topic": Creation failed']);
    });

    it('should remove duplicates and empty references', async () => {
      const references = ['Topic A', '', 'Topic A', '   ', 'Topic B'];

      const mockTopic: Topic = {
        id: 'topic-123',
        title: 'Topic A',
        description: null,
        difficulty: 'medium',
        is_active: true,
        is_premium: false,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        created_by: null,
        icon: null,
      };

      vi.spyOn(topicService, 'findTopic')
        .mockResolvedValueOnce(mockTopic)
        .mockResolvedValueOnce(null);

      const result = await topicService.resolveTopicReferences(references, false);

      expect(result.resolved.size).toBe(1);
      expect(result.missing).toEqual(['Topic B']);
      expect(topicService.findTopic).toHaveBeenCalledTimes(2); // Only called for unique, non-empty references
    });

    it('should handle findTopic errors', async () => {
      const references = ['Error Topic'];

      vi.spyOn(topicService, 'findTopic').mockRejectedValue(new Error('Database error'));

      const result = await topicService.resolveTopicReferences(references, false);

      expect(result.resolved.size).toBe(0);
      expect(result.missing).toEqual(['Error Topic']);
      expect(result.errors).toEqual(['Error resolving topic "Error Topic": Database error']);
    });
  });

  describe('getAllActiveTopics', () => {
    it('should return all active topics ordered by title', async () => {
      const mockTopics: Topic[] = [
        {
          id: 'topic-1',
          title: 'A Topic',
          description: null,
          difficulty: 'easy',
          is_active: true,
          is_premium: false,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
          created_by: null,
          icon: null,
        },
        {
          id: 'topic-2',
          title: 'B Topic',
          description: null,
          difficulty: 'medium',
          is_active: true,
          is_premium: false,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
          created_by: null,
          icon: null,
        },
      ];

      const mockChain = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockResolvedValue({ data: mockTopics, error: null }),
      };

      mockSupabase.from.mockReturnValue(mockChain);

      const result = await topicService.getAllActiveTopics();

      expect(result).toEqual(mockTopics);
      expect(mockChain.select).toHaveBeenCalledWith('*');
      expect(mockChain.eq).toHaveBeenCalledWith('is_active', true);
      expect(mockChain.order).toHaveBeenCalledWith('title');
    });

    it('should handle database errors', async () => {
      const mockChain = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockResolvedValue({ 
          data: null, 
          error: { message: 'Database error' } 
        }),
      };

      mockSupabase.from.mockReturnValue(mockChain);

      await expect(topicService.getAllActiveTopics()).rejects.toThrow('Failed to fetch topics: Database error');
    });
  });

  describe('getTopicById', () => {
    it('should return topic when found', async () => {
      const mockTopic: Topic = {
        id: 'topic-123',
        title: 'Test Topic',
        description: null,
        difficulty: 'medium',
        is_active: true,
        is_premium: false,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        created_by: null,
        icon: null,
      };

      const mockChain = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ data: mockTopic, error: null }),
      };

      mockSupabase.from.mockReturnValue(mockChain);

      const result = await topicService.getTopicById('topic-123');

      expect(result).toEqual(mockTopic);
      expect(mockChain.eq).toHaveBeenCalledWith('id', 'topic-123');
      expect(mockChain.eq).toHaveBeenCalledWith('is_active', true);
    });

    it('should return null when topic not found', async () => {
      const mockChain = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ 
          data: null, 
          error: { code: 'PGRST116' } 
        }),
      };

      mockSupabase.from.mockReturnValue(mockChain);

      const result = await topicService.getTopicById('non-existent');

      expect(result).toBeNull();
    });

    it('should throw error for database errors', async () => {
      const mockChain = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ 
          data: null, 
          error: { message: 'Database error', code: 'OTHER_ERROR' } 
        }),
      };

      mockSupabase.from.mockReturnValue(mockChain);

      await expect(topicService.getTopicById('topic-123')).rejects.toThrow('Failed to fetch topic: Database error');
    });
  });
});