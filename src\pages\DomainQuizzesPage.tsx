import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import BottomNavigation from "@/components/BottomNavigation";
import Navbar from "@/components/Navbar";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/components/ui/use-toast";
import { 
  ArrowLeft, 
  Zap, 
  Clock, 
  CheckCircle,
  PlayCircle,
  Lock,
  Trophy,
  Target,
  BarChart,
  Star,
  Timer,
  BookOpen
} from "lucide-react";
import { DomainWithDetails } from "@/types/domain";
import { fetchDomainBySlug, getDomainEnrollmentStatus, getDifficultyColor } from "@/utils/domain-utils";
import { supabase } from "@/integrations/supabase/client";
import { motion } from "framer-motion";

interface TopicWithStats {
  id: string;
  title: string;
  description: string;
  difficulty: string;
  is_premium: boolean;
  question_count: number;
  user_best_score?: number;
  user_attempts?: number;
  last_attempt?: string;
}

interface QuizStats {
  totalQuestions: number;
  completedTopics: number;
  averageScore: number;
  totalAttempts: number;
}

const DomainQuizzesPage = () => {
  const { domainSlug } = useParams<{ domainSlug: string }>();
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [domain, setDomain] = useState<DomainWithDetails | null>(null);
  const [topicsWithStats, setTopicsWithStats] = useState<TopicWithStats[]>([]);
  const [quizStats, setQuizStats] = useState<QuizStats>({ totalQuestions: 0, completedTopics: 0, averageScore: 0, totalAttempts: 0 });
  const [hasAccess, setHasAccess] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (domainSlug) {
      loadDomainQuizData();
    }
  }, [domainSlug, user]);

  const loadDomainQuizData = async () => {
    if (!domainSlug) return;
    
    try {
      setLoading(true);
      
      // Load domain details
      const domainData = await fetchDomainBySlug(domainSlug);
      if (!domainData) {
        toast({
          title: "Domain not found",
          description: "The requested domain could not be found.",
          variant: "destructive",
        });
        return;
      }
      
      setDomain(domainData);

      // Check access if user is authenticated
      if (user) {
        const enrollment = await getDomainEnrollmentStatus(user.id, domainData.id);
        setHasAccess(enrollment.hasActiveSubscription || domainData.topics.some(t => !t.is_premium));
        
        // Load user quiz statistics
        await loadUserQuizStats(domainData.topics, user.id);
      } else {
        // For non-authenticated users, just show basic topic info
        const basicTopics = domainData.topics.map(topic => ({
          ...topic,
          question_count: 0 // Will be loaded separately
        }));
        setTopicsWithStats(basicTopics);
        await loadQuestionCounts(basicTopics);
      }
    } catch (error) {
      console.error('Error loading domain quiz data:', error);
      toast({
        title: "Error",
        description: "Failed to load quiz information.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadUserQuizStats = async (topics: any[], userId: string) => {
    try {
      // Get question counts for all topics
      const topicIds = topics.map(t => t.id);
      const { data: questionCounts } = await supabase
        .from("questions")
        .select("topic_id")
        .in("topic_id", topicIds);

      const questionCountMap = questionCounts?.reduce((acc: any, q: any) => {
        acc[q.topic_id] = (acc[q.topic_id] || 0) + 1;
        return acc;
      }, {}) || {};

      // Get user quiz attempts
      const { data: userAttempts } = await supabase
        .from("user_quiz_results")
        .select("topic_id, score, total_questions, created_at")
        .eq("user_id", userId)
        .in("topic_id", topicIds)
        .order("created_at", { ascending: false });

      // Process user statistics
      const userStatsMap = userAttempts?.reduce((acc: any, attempt: any) => {
        const topicId = attempt.topic_id;
        if (!acc[topicId]) {
          acc[topicId] = {
            best_score: 0,
            attempts: 0,
            last_attempt: null
          };
        }
        
        const percentage = (attempt.score / attempt.total_questions) * 100;
        acc[topicId].best_score = Math.max(acc[topicId].best_score, percentage);
        acc[topicId].attempts += 1;
        
        if (!acc[topicId].last_attempt || attempt.created_at > acc[topicId].last_attempt) {
          acc[topicId].last_attempt = attempt.created_at;
        }
        
        return acc;
      }, {}) || {};

      // Combine topic data with user stats
      const topicsWithUserStats = topics.map(topic => ({
        ...topic,
        question_count: questionCountMap[topic.id] || 0,
        user_best_score: userStatsMap[topic.id]?.best_score || 0,
        user_attempts: userStatsMap[topic.id]?.attempts || 0,
        last_attempt: userStatsMap[topic.id]?.last_attempt
      }));

      setTopicsWithStats(topicsWithUserStats);

      // Calculate overall stats
      const totalQuestions = Object.values(questionCountMap).reduce((sum: number, count: any) => sum + count, 0);
      const completedTopics = topicsWithUserStats.filter(t => t.user_best_score > 0).length;
      const totalAttempts = Object.values(userStatsMap).reduce((sum: number, stats: any) => sum + stats.attempts, 0);
      const averageScore = completedTopics > 0 
        ? topicsWithUserStats.reduce((sum, t) => sum + t.user_best_score, 0) / completedTopics 
        : 0;

      setQuizStats({
        totalQuestions,
        completedTopics,
        averageScore,
        totalAttempts
      });
    } catch (error) {
      console.error('Error loading user quiz stats:', error);
    }
  };

  const loadQuestionCounts = async (topics: TopicWithStats[]) => {
    try {
      const topicIds = topics.map(t => t.id);
      const { data: questionCounts } = await supabase
        .from("questions")
        .select("topic_id")
        .in("topic_id", topicIds);

      const questionCountMap = questionCounts?.reduce((acc: any, q: any) => {
        acc[q.topic_id] = (acc[q.topic_id] || 0) + 1;
        return acc;
      }, {}) || {};

      const updatedTopics = topics.map(topic => ({
        ...topic,
        question_count: questionCountMap[topic.id] || 0
      }));

      setTopicsWithStats(updatedTopics);
      
      const totalQuestions = Object.values(questionCountMap).reduce((sum: number, count: any) => sum + count, 0);
      setQuizStats(prev => ({ ...prev, totalQuestions }));
    } catch (error) {
      console.error('Error loading question counts:', error);
    }
  };

  const canAccessTopic = (topic: TopicWithStats) => {
    if (!user) return !topic.is_premium;
    return hasAccess || !topic.is_premium;
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600 bg-green-100";
    if (score >= 60) return "text-yellow-600 bg-yellow-100";
    if (score > 0) return "text-red-600 bg-red-100";
    return "text-gray-600 bg-gray-100";
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-white text-lg">Loading quizzes...</div>
          </div>
        </div>
        <BottomNavigation />
      </div>
    );
  }

  if (!domain) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold text-white mb-4">Domain Not Found</h1>
            <Link to="/domains">
              <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
                Back to Domains
              </Button>
            </Link>
          </div>
        </div>
        <BottomNavigation />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-6">
          <Link to={`/domains/${domain.slug}`}>
            <Button variant="ghost" className="text-white hover:bg-white/10 mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to {domain.name}
            </Button>
          </Link>
          
          <div className="flex items-center gap-4 mb-6">
            <div 
              className="p-3 rounded-lg"
              style={{ backgroundColor: `${domain.colorTheme}20` }}
            >
              <Zap 
                className="h-6 w-6" 
                style={{ color: domain.colorTheme }}
              />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white">
                {domain.name} - Quizzes
              </h1>
              <p className="text-gray-300">
                Test your knowledge with domain-specific quizzes
              </p>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        {user && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            {[
              { 
                icon: BookOpen, 
                label: "Total Questions", 
                value: quizStats.totalQuestions, 
                color: "text-blue-400",
                suffix: ""
              },
              { 
                icon: CheckCircle, 
                label: "Completed Topics", 
                value: quizStats.completedTopics, 
                color: "text-green-400",
                suffix: `/${topicsWithStats.length}`
              },
              { 
                icon: Trophy, 
                label: "Average Score", 
                value: Math.round(quizStats.averageScore), 
                color: "text-yellow-400",
                suffix: "%"
              },
              { 
                icon: Target, 
                label: "Total Attempts", 
                value: quizStats.totalAttempts, 
                color: "text-purple-400",
                suffix: ""
              }
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="bg-white/10 backdrop-blur-sm border-white/20 p-4">
                  <div className="flex items-center gap-3">
                    <stat.icon className={`h-8 w-8 ${stat.color}`} />
                    <div>
                      <div className="text-2xl font-bold text-white">
                        {stat.value}{stat.suffix}
                      </div>
                      <div className="text-gray-400 text-sm">{stat.label}</div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>
        )}

        {/* Quiz Topics */}
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-white mb-4">Available Quizzes</h2>
          
          {topicsWithStats.map((topic, index) => {
            const isAccessible = canAccessTopic(topic);
            const difficultyColor = getDifficultyColor(topic.difficulty || 'medium');
            const scoreColor = getScoreColor(topic.user_best_score || 0);
            
            return (
              <motion.div
                key={topic.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className={`bg-white/15 backdrop-blur-sm border-white/30 p-4 sm:p-6 ${
                  !isAccessible ? 'opacity-70 bg-white/8' : 'hover:bg-white/20 hover:shadow-lg'
                } transition-all duration-200`}>
                  <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                    <div className="flex-1">
                      <div className="flex items-start gap-4 mb-4">
                        <div 
                          className="p-3 rounded-lg flex-shrink-0"
                          style={{ backgroundColor: `${domain.colorTheme}20` }}
                        >
                          <Target 
                            className="h-5 w-5" 
                            style={{ color: domain.colorTheme }}
                          />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-xl font-semibold text-white mb-2 drop-shadow-sm">
                            {topic.title}
                          </h3>
                          <p className="text-gray-200 mb-4 line-clamp-2 font-medium">
                            {topic.description}
                          </p>
                          
                          {/* Badges */}
                          <div className="flex flex-wrap gap-2 mb-4">
                            <Badge className={difficultyColor}>
                              {topic.difficulty}
                            </Badge>
                            <Badge className="text-blue-600 bg-blue-100">
                              <BookOpen className="h-3 w-3 mr-1" />
                              {topic.question_count} Questions
                            </Badge>
                            {topic.is_premium && (
                              <Badge className="text-yellow-600 bg-yellow-100">
                                Premium
                              </Badge>
                            )}
                            {user && topic.user_best_score > 0 && (
                              <Badge className={scoreColor}>
                                <Star className="h-3 w-3 mr-1" />
                                Best: {Math.round(topic.user_best_score)}%
                              </Badge>
                            )}
                          </div>

                          {/* User Progress */}
                          {user && topic.user_attempts > 0 && (
                            <div className="mb-4">
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-gray-400 text-sm">
                                  {topic.user_attempts} attempt{topic.user_attempts !== 1 ? 's' : ''}
                                </span>
                                <span className="text-gray-400 text-sm">
                                  Best Score: {Math.round(topic.user_best_score)}%
                                </span>
                              </div>
                              <Progress 
                                value={topic.user_best_score} 
                                className="h-2"
                              />
                            </div>
                          )}

                          {/* Stats */}
                          <div className="flex items-center gap-4 text-sm text-gray-400">
                            <div className="flex items-center gap-1">
                              <Timer className="h-4 w-4" />
                              <span>~{Math.ceil(topic.question_count * 1.5)} min</span>
                            </div>
                            {user && topic.last_attempt && (
                              <div className="flex items-center gap-1">
                                <Clock className="h-4 w-4" />
                                <span>
                                  Last: {new Date(topic.last_attempt).toLocaleDateString()}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Action Button */}
                    <div className="flex flex-col gap-2 w-full sm:w-auto shrink-0">
                      {isAccessible && topic.question_count > 0 ? (
                        <Link to={`/quiz/${topic.id}`} className="w-full sm:w-auto">
                          <Button 
                            style={{ backgroundColor: domain.colorTheme }}
                            className="w-full sm:min-w-[120px]"
                          >
                            <PlayCircle className="h-4 w-4 mr-2" />
                            Start Quiz
                          </Button>
                        </Link>
                      ) : !isAccessible ? (
                        <Button disabled className="w-full sm:min-w-[120px]">
                          <Lock className="h-4 w-4 mr-2" />
                          Locked
                        </Button>
                      ) : (
                        <Button disabled className="w-full sm:min-w-[120px]">
                          No Questions
                        </Button>
                      )}
                      
                      {user && topic.user_attempts > 0 && (
                        <Button 
                          variant="outline" 
                          size="sm"
                          className="border-white/20 text-white hover:bg-white/10 w-full sm:w-auto"
                        >
                          <BarChart className="h-3 w-3 mr-2" />
                          View Stats
                        </Button>
                      )}
                    </div>
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Empty State */}
        {topicsWithStats.length === 0 && (
          <Card className="bg-white/10 backdrop-blur-sm border-white/20 p-8 text-center">
            <Zap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">
              No Quizzes Available
            </h3>
            <p className="text-gray-400">
              Quizzes for this domain are coming soon.
            </p>
          </Card>
        )}
      </div>

      <BottomNavigation />
    </div>
  );
};

export default DomainQuizzesPage;
