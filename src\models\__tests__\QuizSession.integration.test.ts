/**
 * QuizSession Integration Tests
 * Tests for quiz session lifecycle management with real database operations
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { QuizSession, type QuizSessionCreateParams } from '../QuizSession';
import { supabase } from '@/integrations/supabase/client';

describe('QuizSession Integration Tests', () => {
  let testUserId: string;
  let testTopicId: string;
  let createdSessions: string[] = [];

  beforeEach(async () => {
    // Create test user (in real tests, you might use a test user)
    testUserId = 'test-user-' + Date.now();
    testTopicId = 'test-topic-' + Date.now();
    createdSessions = [];
  });

  afterEach(async () => {
    // Clean up created sessions
    if (createdSessions.length > 0) {
      await supabase
        .from('quiz_sessions')
        .delete()
        .in('id', createdSessions);
    }
  });

  const createTestSessionParams = (): QuizSessionCreateParams => ({
    userId: testUserId,
    topicId: testTopicId,
    questionsData: {
      questions: [
        {
          id: 'q1',
          originalCorrectIndex: 0,
          shuffledCorrectIndex: 2,
          optionMapping: [2, 0, 1, 3]
        },
        {
          id: 'q2',
          originalCorrectIndex: 1,
          shuffledCorrectIndex: 0,
          optionMapping: [1, 3, 0, 2]
        }
      ],
      metadata: {
        totalQuestions: 2,
        topicId: testTopicId,
        createdAt: new Date().toISOString()
      }
    },
    quizLength: 10
  });

  describe('Session Creation and Retrieval', () => {
    it('should create and retrieve a quiz session', async () => {
      const params = createTestSessionParams();
      
      // Create session
      const session = await QuizSession.create(params);
      createdSessions.push(session.id);

      expect(session).toBeInstanceOf(QuizSession);
      expect(session.userId).toBe(testUserId);
      expect(session.topicId).toBe(testTopicId);
      expect(session.quizLength).toBe(10);
      expect(session.isCompleted).toBe(false);
      expect(session.isExpired).toBe(false);

      // Retrieve session
      const retrievedSession = await QuizSession.findById(session.id, testUserId);
      
      expect(retrievedSession).not.toBeNull();
      expect(retrievedSession!.id).toBe(session.id);
      expect(retrievedSession!.userId).toBe(testUserId);
      expect(retrievedSession!.questionsData).toEqual(params.questionsData);
    });

    it('should not retrieve session for different user', async () => {
      const params = createTestSessionParams();
      const session = await QuizSession.create(params);
      createdSessions.push(session.id);

      // Try to retrieve with different user ID
      const retrievedSession = await QuizSession.findById(session.id, 'different-user');
      
      expect(retrievedSession).toBeNull();
    });

    it('should find sessions by user ID', async () => {
      const params1 = createTestSessionParams();
      const params2 = { ...createTestSessionParams(), quizLength: 15 };

      const session1 = await QuizSession.create(params1);
      const session2 = await QuizSession.create(params2);
      
      createdSessions.push(session1.id, session2.id);

      const userSessions = await QuizSession.findByUserId(testUserId);
      
      expect(userSessions.length).toBeGreaterThanOrEqual(2);
      
      const sessionIds = userSessions.map(s => s.id);
      expect(sessionIds).toContain(session1.id);
      expect(sessionIds).toContain(session2.id);
    });

    it('should find only active sessions', async () => {
      const params = createTestSessionParams();
      const session = await QuizSession.create(params);
      createdSessions.push(session.id);

      // Session should be active initially
      const activeSessions = await QuizSession.findActiveSessions(testUserId);
      const activeSessionIds = activeSessions.map(s => s.id);
      
      expect(activeSessionIds).toContain(session.id);

      // Complete the session
      await session.complete(8, 300);

      // Should no longer appear in active sessions
      const activeSessionsAfterCompletion = await QuizSession.findActiveSessions(testUserId);
      const activeSessionIdsAfterCompletion = activeSessionsAfterCompletion.map(s => s.id);
      
      expect(activeSessionIdsAfterCompletion).not.toContain(session.id);
    });
  });

  describe('Session Updates and Completion', () => {
    it('should update session properties', async () => {
      const params = createTestSessionParams();
      const session = await QuizSession.create(params);
      createdSessions.push(session.id);

      // Update session
      const success = await session.update({
        score: 7,
        timeTaken: 250
      });

      expect(success).toBe(true);
      expect(session.score).toBe(7);
      expect(session.timeTaken).toBe(250);
      expect(session.isCompleted).toBe(false); // Not completed yet

      // Verify in database
      const retrievedSession = await QuizSession.findById(session.id, testUserId);
      expect(retrievedSession!.score).toBe(7);
      expect(retrievedSession!.timeTaken).toBe(250);
    });

    it('should complete session successfully', async () => {
      const params = createTestSessionParams();
      const session = await QuizSession.create(params);
      createdSessions.push(session.id);

      // Complete session
      const success = await session.complete(9, 400);

      expect(success).toBe(true);
      expect(session.score).toBe(9);
      expect(session.timeTaken).toBe(400);
      expect(session.isCompleted).toBe(true);

      // Verify in database
      const retrievedSession = await QuizSession.findById(session.id, testUserId);
      expect(retrievedSession!.isCompleted).toBe(true);
      expect(retrievedSession!.score).toBe(9);
      expect(retrievedSession!.timeTaken).toBe(400);
    });

    it('should extend session expiration', async () => {
      const params = createTestSessionParams();
      const session = await QuizSession.create(params);
      createdSessions.push(session.id);

      const originalExpiry = new Date(session.expiresAt);
      
      // Extend expiration by 30 minutes
      const success = await session.extendExpiration(30);

      expect(success).toBe(true);
      
      const newExpiry = new Date(session.expiresAt);
      const timeDifference = newExpiry.getTime() - originalExpiry.getTime();
      
      // Should be approximately 30 minutes (allowing for small timing differences)
      expect(timeDifference).toBeGreaterThan(29 * 60 * 1000); // 29 minutes
      expect(timeDifference).toBeLessThan(31 * 60 * 1000); // 31 minutes
    });
  });

  describe('Session Validation', () => {
    it('should validate active session correctly', async () => {
      const params = createTestSessionParams();
      const session = await QuizSession.create(params);
      createdSessions.push(session.id);

      const validation = session.validate();

      expect(validation.isValid).toBe(true);
      expect(validation.isExpired).toBe(false);
      expect(validation.isCompleted).toBe(false);
      expect(validation.message).toBe('Session is valid and active');
    });

    it('should validate completed session correctly', async () => {
      const params = createTestSessionParams();
      const session = await QuizSession.create(params);
      createdSessions.push(session.id);

      await session.complete(8, 300);

      const validation = session.validate();

      expect(validation.isValid).toBe(false);
      expect(validation.isExpired).toBe(false);
      expect(validation.isCompleted).toBe(true);
      expect(validation.message).toBe('Session is already completed');
    });
  });

  describe('Session Statistics', () => {
    it('should calculate user session statistics correctly', async () => {
      // Create multiple sessions with different states
      const params1 = createTestSessionParams();
      const params2 = { ...createTestSessionParams(), quizLength: 15 };
      const params3 = { ...createTestSessionParams(), quizLength: 20 };

      const session1 = await QuizSession.create(params1);
      const session2 = await QuizSession.create(params2);
      const session3 = await QuizSession.create(params3);

      createdSessions.push(session1.id, session2.id, session3.id);

      // Complete some sessions
      await session1.complete(8, 300);
      await session2.complete(6, 450);
      // Leave session3 incomplete

      const stats = await QuizSession.getUserSessionStats(testUserId);

      expect(stats.totalSessions).toBeGreaterThanOrEqual(3);
      expect(stats.completedSessions).toBeGreaterThanOrEqual(2);
      
      // Check that averages are calculated (exact values depend on other test data)
      expect(stats.averageScore).toBeGreaterThan(0);
      expect(stats.averageTime).toBeGreaterThan(0);
      expect(stats.totalQuestionsAnswered).toBeGreaterThan(0);
      expect(stats.uniqueTopics).toBeGreaterThanOrEqual(1);
    });
  });

  describe('Session Deletion', () => {
    it('should delete session successfully', async () => {
      const params = createTestSessionParams();
      const session = await QuizSession.create(params);

      // Delete session
      const success = await session.delete();
      expect(success).toBe(true);

      // Verify session is deleted
      const retrievedSession = await QuizSession.findById(session.id, testUserId);
      expect(retrievedSession).toBeNull();

      // Remove from cleanup list since it's already deleted
      createdSessions = createdSessions.filter(id => id !== session.id);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid session creation gracefully', async () => {
      const invalidParams = {
        userId: '', // Invalid empty user ID
        topicId: testTopicId,
        questionsData: {
          questions: [],
          metadata: {
            totalQuestions: 0,
            topicId: testTopicId,
            createdAt: new Date().toISOString()
          }
        },
        quizLength: 10
      };

      await expect(QuizSession.create(invalidParams))
        .rejects.toThrow();
    });

    it('should handle non-existent session retrieval gracefully', async () => {
      const session = await QuizSession.findById('non-existent-id', testUserId);
      expect(session).toBeNull();
    });

    it('should handle update failures gracefully', async () => {
      const params = createTestSessionParams();
      const session = await QuizSession.create(params);
      createdSessions.push(session.id);

      // Try to update with invalid data (this might not fail in all cases)
      // The exact behavior depends on database constraints
      const success = await session.update({
        score: -1 // Potentially invalid score
      });

      // The test should handle both success and failure cases
      expect(typeof success).toBe('boolean');
    });
  });
});