import { describe, it, expect, beforeEach, vi } from "vitest";
import {
  batchImportService,
  type BatchImportConfig,
  type BatchImportResult,
  type BatchImportProgress,
} from "@/services/batch-import-service";
import type {
  MultiTopicImportResult,
  TopicImportResult,
  ValidatedQuestion,
} from "@/utils/csv-import";
import { supabase } from "@/integrations/supabase/client";

// Mock Supabase client
vi.mock("@/integrations/supabase/client", () => ({
  supabase: {
    from: vi.fn(() => ({
      insert: vi.fn(),
    })),
  },
}));

// Mock topic service
vi.mock("@/services/topic-service", () => ({
  topicService: {
    findTopic: vi.fn(),
    getTopicById: vi.fn(),
  },
}));

import { topicService } from "@/services/topic-service";

describe("BatchImportService Integration Tests", () => {
  let mockSupabaseInsert: any;
  let progressCallbacks: BatchImportProgress[];

  beforeEach(() => {
    vi.clearAllMocks();
    progressCallbacks = [];

    // Setup Supabase mock
    mockSupabaseInsert = vi.fn();
    (supabase.from as any).mockReturnValue({
      insert: mockSupabaseInsert,
    });

    // Setup topic service mocks
    (topicService.findTopic as any).mockResolvedValue({
      id: "topic-id",
      title: "Topic Name",
    });
    (topicService.getTopicById as any).mockResolvedValue({
      id: "topic-id",
      title: "Topic Name",
    });

    // Mock the delay function to avoid timeouts in tests
    vi.spyOn(batchImportService as any, "delay").mockResolvedValue(undefined);
  });

  const createMockImportResult = (
    topicCount: number = 2,
    questionsPerTopic: number = 5
  ): MultiTopicImportResult => {
    const topicResults = new Map<string, TopicImportResult>();

    for (let i = 1; i <= topicCount; i++) {
      const topicId = `topic-${i}`;
      const topicName = `Topic ${i}`;
      const validQuestions: ValidatedQuestion[] = [];

      for (let j = 1; j <= questionsPerTopic; j++) {
        validQuestions.push({
          id: `question-${i}-${j}`,
          topic_id: topicId,
          question_text: `Question ${j} for ${topicName}`,
          options: {
            A: "Option A",
            B: "Option B",
            C: "Option C",
            D: "Option D",
          },
          correct_answer: "A",
          explanation: `Explanation for question ${j}`,
          difficulty: "medium",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });
      }

      topicResults.set(topicId, {
        topicId,
        topicName,
        validQuestions,
        errors: [],
        isNewTopic: i > 1, // First topic is existing, others are new
      });
    }

    return {
      success: true,
      totalRows: topicCount * questionsPerTopic,
      topicResults,
      globalErrors: [],
      newTopicsCreated: topicCount > 1 ? [`Topic 2`] : [],
    };
  };

  const createProgressCallback = () => (progress: BatchImportProgress) => {
    progressCallbacks.push(progress);
  };

  describe("executeBatchImport", () => {
    it("should successfully import questions across multiple topics", async () => {
      // Arrange
      const importResult = createMockImportResult(2, 3);
      const config: BatchImportConfig = {
        mode: "multi-topic",
        autoCreateTopics: true,
        batchSize: 2,
        maxRetries: 1,
        progressCallback: createProgressCallback(),
      };

      mockSupabaseInsert.mockResolvedValue({ error: null });

      // Act
      const result = await batchImportService.executeBatchImport(
        importResult,
        config
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.totalTopicsProcessed).toBe(2);
      expect(result.totalQuestionsImported).toBe(6);
      expect(result.topicsCreated).toEqual(["Topic 2"]);
      expect(result.errors).toHaveLength(0);

      // Verify progress tracking
      expect(progressCallbacks.length).toBeGreaterThan(0);
      expect(progressCallbacks[0].phase).toBe("topic-creation");
      expect(progressCallbacks[progressCallbacks.length - 1].phase).toBe(
        "complete"
      );
      expect(progressCallbacks[progressCallbacks.length - 1].percentage).toBe(
        100
      );
    });

    it("should handle batch insertion failures with retries", async () => {
      // Arrange
      const importResult = createMockImportResult(1, 4);
      const config: BatchImportConfig = {
        mode: "multi-topic",
        autoCreateTopics: false,
        batchSize: 2,
        maxRetries: 1, // Reduced retries to avoid timeout
        progressCallback: createProgressCallback(),
      };

      // First batch fails completely, second batch succeeds
      mockSupabaseInsert
        .mockRejectedValueOnce(new Error("Database connection error")) // First batch, first try
        .mockRejectedValueOnce(new Error("Database connection error")) // First batch, retry
        .mockRejectedValueOnce(new Error("Database connection error")) // First batch, individual question 1
        .mockRejectedValueOnce(new Error("Database connection error")) // First batch, individual question 1 retry
        .mockRejectedValueOnce(new Error("Database connection error")) // First batch, individual question 2
        .mockRejectedValueOnce(new Error("Database connection error")) // First batch, individual question 2 retry
        .mockResolvedValueOnce({ error: null }); // Second batch succeeds

      // Act
      const result = await batchImportService.executeBatchImport(
        importResult,
        config
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.totalQuestionsImported).toBe(2); // Only second batch succeeded
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0].type).toBe("question-insertion");
      expect(result.errors[0].message).toContain("failed after 1 retries");
    }, 10000); // Increase timeout to 10 seconds

    it("should handle individual question insertion after batch failure", async () => {
      // Arrange
      const importResult = createMockImportResult(1, 3);
      const config: BatchImportConfig = {
        mode: "multi-topic",
        autoCreateTopics: false,
        batchSize: 3, // All questions in one batch
        maxRetries: 1,
      };

      // Batch insert fails twice (initial + retry), then individual inserts succeed
      mockSupabaseInsert
        .mockRejectedValueOnce(new Error("Batch insert failed")) // Initial batch attempt
        .mockRejectedValueOnce(new Error("Batch insert failed")) // Retry batch attempt
        .mockResolvedValueOnce({ error: null }) // First individual question
        .mockResolvedValueOnce({ error: null }) // Second individual question
        .mockResolvedValueOnce({ error: null }); // Third individual question

      // Act
      const result = await batchImportService.executeBatchImport(
        importResult,
        config
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.totalQuestionsImported).toBe(3);
      expect(result.errors.length).toBe(1); // One batch-level error
      expect(result.errors[0].type).toBe("question-insertion");
    });

    it("should handle partial success scenarios with detailed reporting", async () => {
      // Arrange
      const importResult = createMockImportResult(2, 2);
      const config: BatchImportConfig = {
        mode: "multi-topic",
        autoCreateTopics: true,
        batchSize: 1,
        maxRetries: 0, // No retries to avoid timeout
      };

      // First topic succeeds, second topic fails
      mockSupabaseInsert
        .mockResolvedValueOnce({ error: null }) // Topic 1, Question 1
        .mockResolvedValueOnce({ error: null }) // Topic 1, Question 2
        .mockRejectedValueOnce(new Error("Insert failed")) // Topic 2, Question 1 - batch fails
        .mockRejectedValueOnce(new Error("Insert failed")) // Topic 2, Question 1 - individual fails
        .mockRejectedValueOnce(new Error("Insert failed")) // Topic 2, Question 2 - batch fails
        .mockRejectedValueOnce(new Error("Insert failed")); // Topic 2, Question 2 - individual fails

      // Act
      const result = await batchImportService.executeBatchImport(
        importResult,
        config
      );

      // Assert
      expect(result.success).toBe(true); // Partial success
      expect(result.totalQuestionsImported).toBe(2); // Only first topic succeeded
      expect(result.totalTopicsProcessed).toBe(2);
      expect(result.errors.length).toBeGreaterThan(0);

      // Check topic-specific results
      const topic1Result = result.topicResults.get("topic-1");
      const topic2Result = result.topicResults.get("topic-2");

      expect(topic1Result?.questionsImported).toBe(2);
      expect(topic1Result?.questionsFailed).toBe(0);

      expect(topic2Result?.questionsImported).toBe(0);
      expect(topic2Result?.questionsFailed).toBe(2);
    }, 10000); // Increase timeout to 10 seconds

    it("should track progress across multiple topics accurately", async () => {
      // Arrange
      const importResult = createMockImportResult(3, 2);
      const config: BatchImportConfig = {
        mode: "multi-topic",
        autoCreateTopics: true,
        batchSize: 1,
        maxRetries: 0,
        progressCallback: createProgressCallback(),
      };

      mockSupabaseInsert.mockResolvedValue({ error: null });

      // Act
      await batchImportService.executeBatchImport(importResult, config);

      // Assert progress tracking
      expect(progressCallbacks.length).toBeGreaterThan(5);

      // Check that we have topic creation phase
      const topicCreationProgress = progressCallbacks.find(
        (p) => p.phase === "topic-creation"
      );
      expect(topicCreationProgress).toBeDefined();
      expect(topicCreationProgress?.message).toContain("Creating");

      // Check that we have question insertion phases
      const questionInsertionProgress = progressCallbacks.filter(
        (p) => p.phase === "question-insertion"
      );
      expect(questionInsertionProgress.length).toBeGreaterThan(0);

      // Check that progress percentages increase
      const percentages = progressCallbacks.map((p) => p.percentage);
      expect(percentages[0]).toBeLessThan(percentages[percentages.length - 1]);
      expect(percentages[percentages.length - 1]).toBe(100);

      // Check that we track current topic
      const topicSpecificProgress = progressCallbacks.filter(
        (p) => p.currentTopic
      );
      expect(topicSpecificProgress.length).toBeGreaterThan(0);
      expect(topicSpecificProgress[0].currentTopic).toBeDefined();
    });

    it("should validate import result before processing", async () => {
      // Arrange - Create invalid import result
      const invalidImportResult: MultiTopicImportResult = {
        success: false,
        totalRows: 0,
        topicResults: new Map(),
        globalErrors: [],
        newTopicsCreated: [],
      };

      const config: BatchImportConfig = {
        mode: "multi-topic",
        autoCreateTopics: false,
        batchSize: 10,
        maxRetries: 1,
      };

      // Act
      const validationErrors =
        batchImportService.validateImportResult(invalidImportResult);

      // Assert
      expect(validationErrors.length).toBeGreaterThan(0);
      expect(validationErrors[0].type).toBe("validation");
      expect(validationErrors[0].message).toContain("No topics found");
    });

    it("should handle empty topic results gracefully", async () => {
      // Arrange
      const importResult = createMockImportResult(1, 0); // Topic with no questions
      const config: BatchImportConfig = {
        mode: "multi-topic",
        autoCreateTopics: false,
        batchSize: 10,
        maxRetries: 1,
      };

      // Act
      const result = await batchImportService.executeBatchImport(
        importResult,
        config
      );

      // Assert
      expect(result.success).toBe(false); // No questions imported
      expect(result.totalQuestionsImported).toBe(0);
      expect(result.totalTopicsProcessed).toBe(1);
      expect(result.topicResults.size).toBe(1);

      const topicResult = result.topicResults.get("topic-1");
      expect(topicResult?.questionsImported).toBe(0);
      expect(topicResult?.questionsFailed).toBe(0);
    });

    it("should handle system errors gracefully", async () => {
      // Arrange - Create import result with new topics to trigger topic verification
      const importResult = createMockImportResult(1, 2);
      importResult.newTopicsCreated = ["New Topic"]; // This will trigger topic verification

      const config: BatchImportConfig = {
        mode: "multi-topic",
        autoCreateTopics: true,
        batchSize: 10,
        maxRetries: 0,
      };

      // Mock topic service to return null (topic not found) which will generate an error
      (topicService.findTopic as unknown).mockResolvedValue(null);

      // Act
      const result = await batchImportService.executeBatchImport(
        importResult,
        config
      );

      // Assert
      expect(result.success).toBe(false); // No questions imported due to topic creation errors
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors.some((e) => e.type === "topic-creation")).toBe(true);
    });
  });

  describe("validateImportResult", () => {
    it("should validate topics and questions correctly", () => {
      // Arrange
      const validImportResult = createMockImportResult(2, 3);

      // Act
      const errors = batchImportService.validateImportResult(validImportResult);

      // Assert
      expect(errors).toHaveLength(0);
    });

    it("should detect missing topic IDs", () => {
      // Arrange
      const invalidImportResult = createMockImportResult(1, 1);
      const topicResult = invalidImportResult.topicResults.get("topic-1")!;

      // Remove the existing entry and add one with empty topic ID
      invalidImportResult.topicResults.delete("topic-1");
      topicResult.topicId = ""; // Invalid topic ID
      invalidImportResult.topicResults.set("", topicResult); // Set with empty key

      // Act
      const errors =
        batchImportService.validateImportResult(invalidImportResult);

      // Assert
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].type).toBe("validation");
      expect(errors[0].message).toContain("Invalid topic ID");
    });

    it("should detect missing topic names", () => {
      // Arrange
      const invalidImportResult = createMockImportResult(1, 1);
      const topicResult = invalidImportResult.topicResults.get("topic-1")!;
      topicResult.topicName = ""; // Invalid topic name

      // Act
      const errors =
        batchImportService.validateImportResult(invalidImportResult);

      // Assert
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].type).toBe("validation");
      expect(errors[0].message).toContain("Invalid topic name");
    });

    it("should detect invalid questions", () => {
      // Arrange
      const invalidImportResult = createMockImportResult(1, 1);
      const topicResult = invalidImportResult.topicResults.get("topic-1")!;
      topicResult.validQuestions[0].id = ""; // Invalid question ID

      // Act
      const errors =
        batchImportService.validateImportResult(invalidImportResult);

      // Assert
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].type).toBe("validation");
      expect(errors[0].message).toContain("missing required fields");
    });
  });
});
