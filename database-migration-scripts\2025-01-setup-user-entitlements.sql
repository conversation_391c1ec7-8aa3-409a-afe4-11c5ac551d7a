-- User Entitlements: topic-level and domain-level access
-- Safe to run multiple times

CREATE TABLE IF NOT EXISTS public.user_entitlements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES public.user_profiles(user_id) ON DELETE CASCADE,
  entitlement_type TEXT NOT NULL CHECK (entitlement_type IN ('topic','domain','bundle')),
  ref_id UUID NOT NULL, -- topics.id or domains.id or bundles.id
  started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  expires_at TIMESTAMPTZ NULL,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active','expired','revoked','pending')),
  source_payment_id UUID NULL, -- optional link to payments table if present
  provider_ref TEXT NULL, -- Paystack reference
  meta JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Speed up checks
CREATE INDEX IF NOT EXISTS idx_user_entitlements_user ON public.user_entitlements(user_id);
CREATE INDEX IF NOT EXISTS idx_user_entitlements_user_type ON public.user_entitlements(user_id, entitlement_type);
CREATE INDEX IF NOT EXISTS idx_user_entitlements_active ON public.user_entitlements(user_id, entitlement_type, ref_id) WHERE status = 'active' AND (expires_at IS NULL OR expires_at > NOW());

-- Helper view: active entitlements
CREATE OR REPLACE VIEW public.v_active_entitlements AS
SELECT *
FROM public.user_entitlements
WHERE status = 'active' AND (expires_at IS NULL OR expires_at > NOW());


