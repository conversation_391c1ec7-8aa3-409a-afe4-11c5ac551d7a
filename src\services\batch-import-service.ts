import { supabase } from '@/integrations/supabase/client';
import { topicService } from './topic-service';
import type { 
  MultiTopicImportResult, 
  ValidatedQuestion
} from '@/utils/csv-import';

/**
 * Progress callback for batch import operations
 */
export interface BatchImportProgress {
  phase: 'topic-creation' | 'question-insertion' | 'validation' | 'complete';
  currentTopic?: string;
  topicsProcessed: number;
  totalTopics: number;
  questionsProcessed: number;
  totalQuestions: number;
  percentage: number;
  message: string;
}

/**
 * Detailed result for batch import operations
 */
export interface BatchImportResult {
  success: boolean;
  totalTopicsProcessed: number;
  totalQuestionsImported: number;
  topicsCreated: string[];
  topicResults: Map<string, TopicBatchResult>;
  errors: BatchImportError[];
  duration: number; // in milliseconds
}

/**
 * Result for a single topic's batch processing
 */
export interface TopicBatchResult {
  topicId: string;
  topicName: string;
  questionsImported: number;
  questionsFailed: number;
  isNewTopic: boolean;
  errors: string[];
  duration: number; // in milliseconds
}

/**
 * Error information for batch import operations
 */
export interface BatchImportError {
  type: 'topic-creation' | 'question-insertion' | 'validation' | 'system';
  topicId?: string;
  topicName?: string;
  questionIndex?: number;
  message: string;
  details?: unknown;
}

/**
 * Configuration for batch import operations
 */
export interface BatchImportConfig {
  mode: 'single-topic' | 'multi-topic';
  autoCreateTopics: boolean;
  selectedTopicId?: string; // For backward compatibility
  batchSize?: number; // Number of questions to insert per batch
  maxRetries?: number; // Maximum retry attempts for failed operations
  progressCallback?: (progress: BatchImportProgress) => void;
}

/**
 * Service for handling batch import operations with enhanced multi-topic support
 */
export class BatchImportService {
  private readonly DEFAULT_BATCH_SIZE = 10;
  private readonly DEFAULT_MAX_RETRIES = 3;

  /**
   * Execute a complete multi-topic batch import
   * @param importResult The parsed import result from CSV processing
   * @param config Batch import configuration
   * @returns Promise resolving to BatchImportResult
   */
  async executeBatchImport(
    importResult: MultiTopicImportResult,
    config: BatchImportConfig
  ): Promise<BatchImportResult> {
    const startTime = Date.now();
    const batchSize = config.batchSize || this.DEFAULT_BATCH_SIZE;
    const maxRetries = config.maxRetries || this.DEFAULT_MAX_RETRIES;
    
    const result: BatchImportResult = {
      success: false,
      totalTopicsProcessed: 0,
      totalQuestionsImported: 0,
      topicsCreated: [...importResult.newTopicsCreated],
      topicResults: new Map(),
      errors: [],
      duration: 0,
    };

    // Calculate totals for progress tracking
    const totalTopics = importResult.topicResults.size;
    const totalQuestions = Array.from(importResult.topicResults.values())
      .reduce((sum, tr) => sum + tr.validQuestions.length, 0);

    let topicsProcessed = 0;
    let questionsProcessed = 0;

    try {
      // Phase 1: Topic Creation (ensure all topics exist before question insertion)
      if (importResult.newTopicsCreated.length > 0) {
        this.reportProgress(config.progressCallback, {
          phase: 'topic-creation',
          topicsProcessed: 0,
          totalTopics,
          questionsProcessed: 0,
          totalQuestions,
          percentage: 5,
          message: `Creating ${importResult.newTopicsCreated.length} new topics...`,
        });

        // Verify and create any missing topics before proceeding with questions
        const topicCreationResult = await this.ensureTopicsExist(
          importResult.newTopicsCreated,
          config.autoCreateTopics
        );
        
        // Add any topic creation errors to the result
        result.errors.push(...topicCreationResult.errors);
        
        // Update the created topics list with actually created topics
        result.topicsCreated = topicCreationResult.created;
        
        // If critical topics failed to create, we might need to skip their questions
        if (topicCreationResult.failed.length > 0) {
          this.reportProgress(config.progressCallback, {
            phase: 'topic-creation',
            topicsProcessed: 0,
            totalTopics,
            questionsProcessed: 0,
            totalQuestions,
            percentage: 8,
            message: `Warning: ${topicCreationResult.failed.length} topics failed to create`,
          });
        }
      }

      // Phase 2: Question Insertion
      this.reportProgress(config.progressCallback, {
        phase: 'question-insertion',
        topicsProcessed: 0,
        totalTopics,
        questionsProcessed: 0,
        totalQuestions,
        percentage: 10,
        message: 'Starting question import...',
      });

      // Process each topic sequentially to maintain data integrity
      for (const [topicId, topicResult] of importResult.topicResults) {
        const topicStartTime = Date.now();
        
        this.reportProgress(config.progressCallback, {
          phase: 'question-insertion',
          currentTopic: topicResult.topicName,
          topicsProcessed,
          totalTopics,
          questionsProcessed,
          totalQuestions,
          percentage: 10 + Math.floor((topicsProcessed / totalTopics) * 80),
          message: `Processing topic: ${topicResult.topicName}`,
        });

        const topicBatchResult: TopicBatchResult = {
          topicId,
          topicName: topicResult.topicName,
          questionsImported: 0,
          questionsFailed: 0,
          isNewTopic: topicResult.isNewTopic,
          errors: [],
          duration: 0,
        };

        // Check if topic exists before processing questions
        const topicExists = await this.verifyTopicExists(topicId, topicResult.topicName);
        if (!topicExists) {
          topicBatchResult.questionsFailed = topicResult.validQuestions.length;
          topicBatchResult.errors.push(`Topic "${topicResult.topicName}" does not exist, skipping ${topicResult.validQuestions.length} questions`);
          result.errors.push({
            type: 'topic-creation',
            topicId,
            topicName: topicResult.topicName,
            message: `Cannot import questions: topic "${topicResult.topicName}" does not exist`,
          });
          
          topicBatchResult.duration = Date.now() - topicStartTime;
          result.topicResults.set(topicId, topicBatchResult);
          topicsProcessed++;
          continue;
        }

        // Skip topics with no valid questions
        if (topicResult.validQuestions.length === 0) {
          topicBatchResult.duration = Date.now() - topicStartTime;
          result.topicResults.set(topicId, topicBatchResult);
          topicsProcessed++;
          continue;
        }

        // Process questions in batches with enhanced error handling
        const batches = this.createBatches(topicResult.validQuestions, batchSize);
        
        for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
          const batch = batches[batchIndex];
          let retryCount = 0;
          let batchSuccess = false;

          // Update progress for current batch
          this.reportProgress(config.progressCallback, {
            phase: 'question-insertion',
            currentTopic: topicResult.topicName,
            topicsProcessed,
            totalTopics,
            questionsProcessed,
            totalQuestions,
            percentage: 10 + Math.floor((questionsProcessed / totalQuestions) * 80),
            message: `Processing batch ${batchIndex + 1}/${batches.length} for ${topicResult.topicName}`,
          });

          while (retryCount <= maxRetries && !batchSuccess) {
            try {
              const { error } = await supabase
                .from('questions')
                .insert(batch);

              if (error) {
                throw error;
              }

              // Batch succeeded
              topicBatchResult.questionsImported += batch.length;
              questionsProcessed += batch.length;
              batchSuccess = true;

              // Update progress
              this.reportProgress(config.progressCallback, {
                phase: 'question-insertion',
                currentTopic: topicResult.topicName,
                topicsProcessed,
                totalTopics,
                questionsProcessed,
                totalQuestions,
                percentage: 10 + Math.floor((questionsProcessed / totalQuestions) * 80),
                message: `Imported ${questionsProcessed}/${totalQuestions} questions`,
              });

            } catch (error) {
              retryCount++;
              
              if (retryCount > maxRetries) {
                // All retries exhausted, handle individual questions
                const individualResults = await this.handleFailedBatch(
                  batch, 
                  topicId, 
                  topicResult.topicName,
                  maxRetries
                );
                
                topicBatchResult.questionsImported += individualResults.imported;
                topicBatchResult.questionsFailed += individualResults.failed;
                topicBatchResult.errors.push(...individualResults.errors);
                questionsProcessed += batch.length;

                // Add batch-level error with more context
                result.errors.push({
                  type: 'question-insertion',
                  topicId,
                  topicName: topicResult.topicName,
                  message: `Batch ${batchIndex + 1}/${batches.length} failed after ${maxRetries} retries: ${error instanceof Error ? error.message : String(error)}`,
                  details: error,
                });

                batchSuccess = true; // Exit retry loop
              } else {
                // Wait before retry (exponential backoff)
                const delayMs = Math.min(Math.pow(2, retryCount) * 1000, 10000); // Cap at 10 seconds
                await this.delay(delayMs);
                
                this.reportProgress(config.progressCallback, {
                  phase: 'question-insertion',
                  currentTopic: topicResult.topicName,
                  topicsProcessed,
                  totalTopics,
                  questionsProcessed,
                  totalQuestions,
                  percentage: 10 + Math.floor((questionsProcessed / totalQuestions) * 80),
                  message: `Retrying batch ${batchIndex + 1} (attempt ${retryCount + 1}/${maxRetries + 1})`,
                });
              }
            }
          }
        }

        topicBatchResult.duration = Date.now() - topicStartTime;
        result.topicResults.set(topicId, topicBatchResult);
        result.totalQuestionsImported += topicBatchResult.questionsImported;
        topicsProcessed++;
      }

      result.totalTopicsProcessed = topicsProcessed;
      result.success = result.totalQuestionsImported > 0;

      // Phase 3: Complete
      this.reportProgress(config.progressCallback, {
        phase: 'complete',
        topicsProcessed,
        totalTopics,
        questionsProcessed,
        totalQuestions,
        percentage: 100,
        message: `Import complete: ${result.totalQuestionsImported} questions imported across ${result.totalTopicsProcessed} topics`,
      });

    } catch (error) {
      result.errors.push({
        type: 'system',
        message: `Batch import failed: ${error instanceof Error ? error.message : String(error)}`,
        details: error,
      });
    }

    result.duration = Date.now() - startTime;
    return result;
  }

  /**
   * Handle a failed batch by attempting to insert questions individually
   * @param batch Array of questions that failed as a batch
   * @param topicId Topic ID for error reporting
   * @param topicName Topic name for error reporting
   * @param maxRetries Maximum retry attempts per question
   * @returns Object with import statistics and errors
   */
  private async handleFailedBatch(
    batch: ValidatedQuestion[],
    topicId: string,
    topicName: string,
    maxRetries: number
  ): Promise<{ imported: number; failed: number; errors: string[] }> {
    let imported = 0;
    let failed = 0;
    const errors: string[] = [];

    for (let i = 0; i < batch.length; i++) {
      const question = batch[i];
      let retryCount = 0;
      let questionSuccess = false;

      while (retryCount <= maxRetries && !questionSuccess) {
        try {
          const { error } = await supabase
            .from('questions')
            .insert([question]);

          if (error) {
            throw error;
          }

          imported++;
          questionSuccess = true;
        } catch (error) {
          retryCount++;
          
          if (retryCount > maxRetries) {
            failed++;
            errors.push(`Question ${i + 1}: ${error instanceof Error ? error.message : String(error)}`);
          } else {
            await this.delay(500); // Short delay between individual retries
          }
        }
      }
    }

    return { imported, failed, errors };
  }

  /**
   * Create batches from an array of questions
   * @param questions Array of validated questions
   * @param batchSize Size of each batch
   * @returns Array of question batches
   */
  private createBatches<T>(questions: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    
    for (let i = 0; i < questions.length; i += batchSize) {
      batches.push(questions.slice(i, i + batchSize));
    }
    
    return batches;
  }

  /**
   * Report progress to callback if provided
   * @param callback Progress callback function
   * @param progress Progress information
   */
  private reportProgress(
    callback: ((progress: BatchImportProgress) => void) | undefined,
    progress: BatchImportProgress
  ): void {
    if (callback) {
      callback(progress);
    }
  }

  /**
   * Delay execution for specified milliseconds
   * @param ms Milliseconds to delay
   * @returns Promise that resolves after delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Ensure all required topics exist before question insertion
   * @param topicNames Array of topic names that should exist
   * @param autoCreate Whether to create missing topics
   * @returns Promise resolving to topic creation result
   */
  private async ensureTopicsExist(
    topicNames: string[],
    autoCreate: boolean
  ): Promise<{
    created: string[];
    failed: string[];
    errors: BatchImportError[];
  }> {
    const result = {
      created: [] as string[],
      failed: [] as string[],
      errors: [] as BatchImportError[],
    };

    for (const topicName of topicNames) {
      try {
        // Check if topic already exists
        const existingTopic = await topicService.findTopic(topicName);
        
        if (existingTopic) {
          // Topic already exists, no need to create
          continue;
        }

        if (autoCreate) {
          // Attempt to create the topic
          try {
            await topicService.createTopic({
              title: topicName,
              difficulty: 'medium',
              is_premium: false,
            });
            result.created.push(topicName);
          } catch (createError) {
            result.failed.push(topicName);
            result.errors.push({
              type: 'topic-creation',
              topicName,
              message: `Failed to create topic "${topicName}": ${createError instanceof Error ? createError.message : String(createError)}`,
              details: createError,
            });
          }
        } else {
          // Auto-create is disabled, but topic doesn't exist
          result.failed.push(topicName);
          result.errors.push({
            type: 'topic-creation',
            topicName,
            message: `Topic "${topicName}" does not exist and auto-create is disabled`,
          });
        }
      } catch (error) {
        result.failed.push(topicName);
        result.errors.push({
          type: 'topic-creation',
          topicName,
          message: `Error checking topic "${topicName}": ${error instanceof Error ? error.message : String(error)}`,
          details: error,
        });
      }
    }

    return result;
  }

  /**
   * Verify that a topic exists before processing its questions
   * @param topicId Topic ID to verify
   * @param topicName Topic name for error reporting
   * @returns Promise resolving to boolean indicating if topic exists
   */
  private async verifyTopicExists(topicId: string, topicName: string): Promise<boolean> {
    try {
      const topic = await topicService.getTopicById(topicId);
      return topic !== null;
    } catch (error) {
      console.error(`Error verifying topic existence for ${topicName} (${topicId}):`, error);
      return false;
    }
  }

  /**
   * Validate import result before processing
   * @param importResult Multi-topic import result to validate
   * @returns Array of validation errors
   */
  validateImportResult(importResult: MultiTopicImportResult): BatchImportError[] {
    const errors: BatchImportError[] = [];

    // Check if there are any topics to process
    if (importResult.topicResults.size === 0) {
      errors.push({
        type: 'validation',
        message: 'No topics found in import result',
      });
    }

    // Check if there are any questions to import
    const totalQuestions = Array.from(importResult.topicResults.values())
      .reduce((sum, tr) => sum + tr.validQuestions.length, 0);

    if (totalQuestions === 0) {
      errors.push({
        type: 'validation',
        message: 'No valid questions found in import result',
      });
    }

    // Validate each topic result
    for (const [topicId, topicResult] of importResult.topicResults) {
      if (!topicId || topicId.trim().length === 0) {
        errors.push({
          type: 'validation',
          topicName: topicResult.topicName,
          message: `Invalid topic ID for topic "${topicResult.topicName}"`,
        });
      }

      if (!topicResult.topicName || topicResult.topicName.trim().length === 0) {
        errors.push({
          type: 'validation',
          topicId,
          message: `Invalid topic name for topic ID "${topicId}"`,
        });
      }

      // Validate questions have required fields
      topicResult.validQuestions.forEach((question, index) => {
        if (!question.id || !question.topic_id || !question.question_text) {
          errors.push({
            type: 'validation',
            topicId,
            topicName: topicResult.topicName,
            questionIndex: index,
            message: `Question ${index + 1} is missing required fields`,
          });
        }
      });
    }

    return errors;
  }
}

// Export singleton instance
export const batchImportService = new BatchImportService();