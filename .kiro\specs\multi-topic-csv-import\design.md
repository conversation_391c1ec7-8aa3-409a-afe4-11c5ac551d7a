# Design Document: Multi-Topic CSV Import Enhancement

## Overview

This design enhances the existing CSV import functionality to support importing quiz questions for multiple topics within a single CSV file. The solution extends the current single-topic import system while maintaining full backward compatibility. The design introduces topic identification within CSV files, automatic topic creation capabilities, and enhanced validation and preview functionality.

## Architecture

### High-Level Architecture

The enhancement follows the existing architecture pattern with these key components:

1. **Enhanced CSV Parser** - Extends `parseQuestionCSV` to handle topic information
2. **Topic Resolution Service** - New service to handle topic lookup and creation
3. **Multi-Topic Import Component** - Enhanced UI component with preview capabilities
4. **Batch Import Engine** - Improved batch processing for cross-topic imports

### Data Flow

```mermaid
graph TD
    A[CSV File Upload] --> B[Enhanced CSV Parser]
    B --> C[Topic Resolution Service]
    C --> D[Question Validation]
    D --> E[Import Preview]
    E --> F[User Confirmation]
    F --> G[Batch Import Engine]
    G --> H[Database Insert]
    H --> I[Import Results]
```

## Components and Interfaces

### 1. Enhanced CSV Parser (`csv-import.ts`)

#### New Interfaces

```typescript
// Enhanced CSV row structure supporting topic information
export interface MultiTopicQuestionCSVRow extends QuestionCSVRow {
  topic_name?: string;
  topic_id?: string;
}

// Enhanced import result with topic-grouped data
export interface MultiTopicImportResult {
  success: boolean;
  totalRows: number;
  topicResults: Map<string, TopicImportResult>;
  globalErrors: { row: number; message: string }[];
  newTopicsCreated: string[];
}

export interface TopicImportResult {
  topicId: string;
  topicName: string;
  validQuestions: ValidatedQuestion[];
  errors: { row: number; message: string }[];
  isNewTopic: boolean;
}

// Import mode configuration
export interface ImportConfig {
  mode: 'single-topic' | 'multi-topic';
  autoCreateTopics: boolean;
  selectedTopicId?: string; // For backward compatibility
}
```

#### Enhanced Functions

```typescript
// Main parsing function with mode detection
export async function parseQuestionCSV(
  file: File, 
  config: ImportConfig
): Promise<MultiTopicImportResult>

// Topic resolution and creation
export async function resolveTopics(
  topicReferences: Set<string>,
  autoCreate: boolean
): Promise<Map<string, string>> // Maps topic reference to topic ID
```

### 2. Topic Resolution Service (`topic-service.ts`)

```typescript
export interface TopicService {
  // Find existing topic by name or ID
  findTopic(reference: string): Promise<Topic | null>;
  
  // Create new topic with validation
  createTopic(name: string): Promise<Topic>;
  
  // Batch resolve multiple topic references
  resolveTopicReferences(
    references: string[], 
    autoCreate: boolean
  ): Promise<TopicResolutionResult>;
}

export interface TopicResolutionResult {
  resolved: Map<string, string>; // reference -> topic_id
  missing: string[];
  created: string[];
  errors: string[];
}
```

### 3. Enhanced Import Component (`MultiTopicCSVImport.tsx`)

#### Component Structure

```typescript
export interface MultiTopicCSVImportProps {
  topics: Topic[];
  onSuccess?: (result: MultiTopicImportResult) => void;
  onCancel?: () => void;
}

// Internal state management
interface ImportState {
  mode: 'single-topic' | 'multi-topic';
  selectedFile: File | null;
  selectedTopic: string; // For single-topic mode
  autoCreateTopics: boolean;
  previewData: MultiTopicImportResult | null;
  importing: boolean;
  showPreview: boolean;
}
```

#### UI Flow States

1. **File Selection** - User uploads CSV, system detects mode
2. **Configuration** - User sets import options (auto-create, topic selection)
3. **Preview** - System shows parsed results grouped by topic
4. **Confirmation** - User reviews and confirms import
5. **Import** - Batch processing with progress tracking
6. **Results** - Summary of successful/failed imports per topic

## Data Models

### Enhanced CSV Format

The CSV format is extended to support topic identification while maintaining backward compatibility:

#### Multi-Topic Format (New)
```csv
topic_name,question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty
Security Fundamentals,What is a firewall?,Physical barrier,Network traffic controller,Data encryptor,Malware detector,B,Controls network traffic flow,medium
Network Security,What is a VPN?,Virtual Private Network,Very Personal Network,Verified Public Network,Variable Protocol Network,A,Creates secure tunnel over internet,easy
Incident Response,What is a security incident?,Hardware failure,Unauthorized access event,Software update,Routine maintenance,B,Any event that compromises security,hard
```

#### Alternative ID-Based Format
```csv
topic_id,question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty
uuid-123,What is a firewall?,Physical barrier,Network traffic controller,Data encryptor,Malware detector,B,Controls network traffic flow,medium
uuid-456,What is a VPN?,Virtual Private Network,Very Personal Network,Verified Public Network,Variable Protocol Network,A,Creates secure tunnel over internet,easy
```

#### Legacy Single-Topic Format (Unchanged)
```csv
question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty
What is a firewall?,Physical barrier,Network traffic controller,Data encryptor,Malware detector,B,Controls network traffic flow,medium
```

### Database Schema Extensions

No database schema changes are required. The existing `topics` and `questions` tables support the enhanced functionality:

- `topics.title` - Used for topic name matching
- `topics.id` - Used for direct topic ID references
- `questions.topic_id` - Links questions to topics as before

## Error Handling

### Validation Hierarchy

1. **File Format Validation** - CSV structure and encoding
2. **Mode Detection** - Single vs multi-topic format
3. **Topic Resolution** - Existing topic lookup and creation validation
4. **Question Validation** - Existing question field validation per topic
5. **Batch Import Validation** - Database constraint validation

### Error Categories

#### Topic-Level Errors
- Invalid topic names (empty, too long, special characters)
- Topic creation failures (duplicate names, database errors)
- Permission errors for topic creation

#### Question-Level Errors
- Standard validation errors grouped by topic
- Cross-topic duplicate question detection (optional)

#### System-Level Errors
- File parsing errors
- Database connection issues
- Batch processing failures

### Error Recovery

- **Partial Success**: Import successful topics, report failed ones
- **Rollback Option**: Allow users to undo imports within a session
- **Retry Mechanism**: Re-attempt failed topic/question imports

## Testing Strategy

### Unit Tests

1. **CSV Parser Tests**
   - Mode detection accuracy
   - Topic reference extraction
   - Backward compatibility validation
   - Error handling for malformed CSV

2. **Topic Service Tests**
   - Topic resolution logic
   - Auto-creation functionality
   - Duplicate handling
   - Permission validation

3. **Validation Tests**
   - Multi-topic question validation
   - Cross-topic consistency checks
   - Error message accuracy

### Integration Tests

1. **End-to-End Import Flow**
   - Complete multi-topic import process
   - Preview functionality
   - Batch processing with progress tracking

2. **Database Integration**
   - Topic creation and question insertion
   - Transaction handling and rollback
   - Concurrent import handling

3. **UI Component Tests**
   - Mode switching behavior
   - Preview display accuracy
   - Error state handling

### Performance Tests

1. **Large File Handling**
   - CSV files with 1000+ questions across 10+ topics
   - Memory usage during parsing and preview
   - Import speed and progress accuracy

2. **Concurrent Usage**
   - Multiple users importing simultaneously
   - Topic creation race conditions
   - Database performance under load

### Compatibility Tests

1. **Backward Compatibility**
   - Existing single-topic CSV files
   - Legacy import workflows
   - UI behavior with old format

2. **Browser Compatibility**
   - File upload handling across browsers
   - CSV parsing performance
   - Progress tracking accuracy

## Implementation Phases

### Phase 1: Core Infrastructure
- Enhanced CSV parser with mode detection
- Topic resolution service
- Basic multi-topic validation

### Phase 2: UI Enhancement
- Enhanced import component with mode switching
- Preview functionality
- Progress tracking for multi-topic imports

### Phase 3: Advanced Features
- Auto-topic creation with validation
- Enhanced error reporting and recovery
- Performance optimizations

### Phase 4: Polish and Testing
- Comprehensive test coverage
- Documentation updates
- Performance tuning and optimization