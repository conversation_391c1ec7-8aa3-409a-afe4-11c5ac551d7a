import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { isUserAdmin } from '@/utils/auth-helpers';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import EnvDebug from './EnvDebug';
import SupabaseConnectionTest from './SupabaseConnectionTest';
import SimpleAuthTest from './SimpleAuthTest';

const AdminDebug: React.FC = () => {
  const { user } = useAuth();
  const [adminStatus, setAdminStatus] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [debugInfo, setDebugInfo] = useState<Record<string, unknown>>({});

  const checkAdminStatus = async () => {
    if (!user) {
      setAdminStatus(null);
      setDebugInfo({ error: 'No user logged in' });
      return;
    }

    setIsLoading(true);
    try {
      const isAdmin = await isUserAdmin(user);
      setAdminStatus(isAdmin);
      
      // Collect debug information
      const debug = {
        userId: user.id,
        userEmail: user.email,
        isAdmin,
        adminEmails: ['<EMAIL>', '<EMAIL>'],
        emailMatch: user.email ? ['<EMAIL>', '<EMAIL>'].some(
          email => email.toLowerCase() === user.email!.toLowerCase()
        ) : false,
        envVars: {
          adminFeatures: import.meta.env.VITE_ENABLE_ADMIN_FEATURES,
          debugMode: import.meta.env.VITE_ENABLE_DEBUG_MODE,
          nodeEnv: import.meta.env.NODE_ENV
        }
      };
      
      setDebugInfo(debug);
      console.log('Admin Debug Info:', debug);
    } catch (error) {
      console.error('Error checking admin status:', error);
      setDebugInfo({ error: error instanceof Error ? error.message : 'Unknown error' });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkAdminStatus();
  }, [user, checkAdminStatus]);

  if (!user) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>🔐 Admin Debug Panel</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">Please sign in to test admin authentication.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <EnvDebug />
      <SupabaseConnectionTest />
      <SimpleAuthTest />

      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔐 Admin Debug Panel
            {adminStatus === true && <Badge variant="default" className="bg-green-500">ADMIN</Badge>}
            {adminStatus === false && <Badge variant="destructive">NOT ADMIN</Badge>}
            {adminStatus === null && <Badge variant="secondary">UNKNOWN</Badge>}
          </CardTitle>
        </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="font-semibold mb-2">User Information</h3>
            <div className="text-sm space-y-1">
              <p><strong>ID:</strong> {debugInfo.userId}</p>
              <p><strong>Email:</strong> {debugInfo.userEmail}</p>
              <p><strong>Admin Status:</strong> {
                adminStatus === true ? '✅ Admin' : 
                adminStatus === false ? '❌ Not Admin' : 
                '⏳ Checking...'
              }</p>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold mb-2">Configuration</h3>
            <div className="text-sm space-y-1">
              <p><strong>Admin Features:</strong> {debugInfo.envVars?.adminFeatures || 'undefined'}</p>
              <p><strong>Debug Mode:</strong> {debugInfo.envVars?.debugMode || 'undefined'}</p>
              <p><strong>Environment:</strong> {debugInfo.envVars?.nodeEnv || 'undefined'}</p>
            </div>
          </div>
        </div>

        <div>
          <h3 className="font-semibold mb-2">Admin Email Check</h3>
          <div className="text-sm space-y-1">
            <p><strong>Admin Emails:</strong> {debugInfo.adminEmails?.join(', ')}</p>
            <p><strong>Email Match:</strong> {debugInfo.emailMatch ? '✅ Yes' : '❌ No'}</p>
          </div>
        </div>

        {debugInfo.error && (
          <div className="bg-red-50 border border-red-200 rounded p-3">
            <p className="text-red-700 text-sm"><strong>Error:</strong> {debugInfo.error}</p>
          </div>
        )}

        <div className="flex gap-2">
          <Button 
            onClick={checkAdminStatus} 
            disabled={isLoading}
            size="sm"
          >
            {isLoading ? 'Checking...' : 'Recheck Admin Status'}
          </Button>
          
          <Button 
            onClick={() => console.log('Debug Info:', debugInfo)} 
            variant="outline"
            size="sm"
          >
            Log Debug Info
          </Button>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded p-3">
          <p className="text-blue-700 text-sm">
            <strong>Troubleshooting:</strong> Check the browser console for detailed logs from the admin authentication process.
          </p>
        </div>
      </CardContent>
    </Card>
    </div>
  );
};

export default AdminDebug;
