import { describe, it, expect } from 'vitest';

// Test the UI text changes made to AdminDashboard
describe('AdminDashboard UI Integration - Text Changes', () => {
  it('should have updated CSV import menu item text', () => {
    // Test that the CSV import menu item text includes multi-topic support indication
    const expectedText = 'CSV Format (Single/Multi-Topic)';
    
    // This verifies that the menu item text was updated to indicate both modes are supported
    expect(expectedText).toContain('Single/Multi-Topic');
    expect(expectedText).toContain('CSV Format');
  });

  it('should have enhanced dialog title and description', () => {
    // Test the enhanced dialog title
    const dialogTitle = 'Import Questions from CSV';
    expect(dialogTitle).toBe('Import Questions from CSV');

    // Test the enhanced dialog description
    const dialogDescription = `Import quiz questions from CSV files. Supports both single-topic and multi-topic import modes.
      Choose single-topic to import all questions to one topic, or multi-topic to import questions 
      to multiple topics specified in the CSV file.`;
    
    expect(dialogDescription).toContain('Supports both single-topic and multi-topic import modes');
    expect(dialogDescription).toContain('Choose single-topic to import all questions to one topic');
    expect(dialogDescription).toContain('multi-topic to import questions');
  });

  it('should maintain backward compatibility indicators', () => {
    // Verify that the text indicates backward compatibility
    const menuText = 'CSV Format (Single/Multi-Topic)';
    
    // The presence of "Single" in the text indicates backward compatibility
    expect(menuText).toContain('Single');
    
    // The presence of "Multi-Topic" indicates the new functionality
    expect(menuText).toContain('Multi-Topic');
  });
});