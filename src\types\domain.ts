// Domain-related type definitions

export interface Domain {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  icon: string | null;
  color_theme: string | null;
  difficulty_level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  estimated_duration_weeks: number;
  prerequisites: string[];
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface DomainSubscriptionPlan {
  id: string;
  domain_id: string;
  plan_id: string;
  name: string;
  amount: number;
  interval: 'weekly' | 'monthly' | 'one-time';
  features: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface DomainLearningPath {
  id: string;
  domain_id: string;
  name: string;
  description: string | null;
  difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  estimated_hours: number;
  sort_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface PathTopic {
  id: string;
  learning_path_id: string;
  topic_id: string;
  sort_order: number;
  is_required: boolean;
  created_at: string;
}

export interface UserDomainProgress {
  id: string;
  user_id: string;
  domain_id: string;
  learning_path_id: string;
  completed_topics: number;
  total_topics: number;
  completion_percentage: number;
  started_at: string;
  last_activity_at: string;
  completed_at: string | null;
  created_at: string;
  updated_at: string;
}

// UI-friendly domain interface
export interface DomainForUI {
  id: string;
  name: string;
  slug: string;
  description: string;
  icon: string;
  colorTheme: string;
  difficultyLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  estimatedDurationWeeks: number;
  prerequisites: string[];
  isActive: boolean;
  sortOrder: number;
  topicCount?: number;
  learningPathCount?: number;
  userHasDomainPass?: boolean;
  subscriptionPlan?: DomainSubscriptionPlan;
  userProgress?: UserDomainProgress;
}

// Domain with learning paths and topics
export interface DomainWithDetails extends DomainForUI {
  learningPaths: DomainLearningPath[];
  topics: Array<{
    id: string;
    title: string;
    description: string;
    difficulty: string;
    is_premium: boolean;
    question_count: number;
  }>;
}

// Domain statistics
export interface DomainStats {
  totalDomains: number;
  activeDomains: number;
  totalTopics: number;
  totalQuestions: number;
  totalLearningMaterials: number;
  userEnrollments: number;
}

// Domain filter options
export interface DomainFilters {
  difficulty?: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  duration?: 'short' | 'medium' | 'long'; // weeks: short (1-4), medium (5-8), long (9+)
  hasPrerequisites?: boolean;
  searchQuery?: string;
}

// Domain enrollment status
export interface DomainEnrollment {
  domainId: string;
  isEnrolled: boolean;
  hasActiveSubscription: boolean;
  subscriptionPlanId?: string;
  enrollmentDate?: string;
  progress?: UserDomainProgress;
}
