# Supabase CLI Troubleshooting Guide

## Common Issues and Solutions

### 1. "bash: supabase: command not found"

This is the most common issue and indicates that the Supabase CLI is either not installed or not in your system's PATH.

#### Quick Diagnosis:
```bash
# Check if supabase is installed anywhere
which supabase
whereis supabase

# Check your PATH
echo $PATH

# Try different installation methods
npm list -g supabase
brew list | grep supabase
```

#### Solutions:

**Option A: Install via Package Manager**
```bash
# macOS with Homebrew
brew install supabase/tap/supabase

# Linux with apt
curl -fsSL https://supabase.com/dist/gpg | sudo gpg --dearmor -o /usr/share/keyrings/supabase-archive-keyring.gpg
echo "deb [signed-by=/usr/share/keyrings/supabase-archive-keyring.gpg] https://supabase.com/dist/apt/ stable main" | sudo tee /etc/apt/sources.list.d/supabase.list
sudo apt update && sudo apt install supabase

# Universal with npm
npm install -g supabase
```

**Option B: Fix PATH Issues**
```bash
# Find where supabase is installed
find / -name "supabase" -type f 2>/dev/null

# Add to PATH (replace /path/to/supabase with actual path)
echo 'export PATH="/path/to/supabase:$PATH"' >> ~/.bashrc
source ~/.bashrc

# For zsh users
echo 'export PATH="/path/to/supabase:$PATH"' >> ~/.zshrc
source ~/.zshrc
```

**Option C: Manual Installation**
```bash
# Download latest release
curl -fsSL https://github.com/supabase/cli/releases/latest/download/supabase_linux_amd64.tar.gz | tar -xz

# Move to system directory
sudo mv supabase /usr/local/bin/
sudo chmod +x /usr/local/bin/supabase

# Verify installation
supabase --version
```

### 2. "Docker daemon not running"

#### Error Message:
```
Error: Cannot connect to the Docker daemon at unix:///var/run/docker.sock
```

#### Solutions:

**Linux:**
```bash
# Start Docker service
sudo systemctl start docker
sudo systemctl enable docker

# Add user to docker group
sudo usermod -aG docker $USER
newgrp docker

# Verify Docker is running
docker info
```

**macOS:**
```bash
# Start Docker Desktop
open -a Docker

# Or install via Homebrew
brew install --cask docker
```

**Windows:**
- Start Docker Desktop from Start Menu
- Or install from https://www.docker.com/products/docker-desktop

### 3. "Port already in use"

#### Error Message:
```
Error: Port 54321 is already in use
```

#### Solutions:

**Option A: Find and Kill Process**
```bash
# Find what's using the port
lsof -i :54321
netstat -tulpn | grep :54321

# Kill the process (replace PID with actual process ID)
kill -9 PID
```

**Option B: Change Supabase Ports**
Edit `supabase/config.toml`:
```toml
[api]
port = 54325  # Changed from 54321

[db]
port = 54326  # Changed from 54322

[studio]
port = 54327  # Changed from 54323

[inbucket]
port = 54328  # Changed from 54324
```

**Option C: Stop Conflicting Services**
```bash
# Stop existing Supabase instance
supabase stop

# Stop other services that might conflict
sudo systemctl stop postgresql  # If you have local PostgreSQL
```

### 4. "Permission denied" Errors

#### Error Message:
```
Error: permission denied while trying to connect to Docker daemon
```

#### Solutions:

**Linux:**
```bash
# Add user to docker group
sudo usermod -aG docker $USER

# Apply group changes
newgrp docker

# Or run with sudo (not recommended for regular use)
sudo supabase start
```

**macOS/Windows:**
- Ensure Docker Desktop is running with proper permissions
- Run terminal as administrator (Windows) or with sudo (macOS)

### 5. "Failed to pull Docker images"

#### Error Message:
```
Error: failed to pull image "supabase/postgres:15.1.0.147"
```

#### Solutions:

**Check Internet Connection:**
```bash
# Test connectivity
ping google.com
curl -I https://hub.docker.com

# Test Docker Hub access
docker pull hello-world
```

**Clear Docker Cache:**
```bash
# Clean up Docker
docker system prune -a

# Remove Supabase containers and try again
docker container prune
docker image prune -a
```

**Use Different Registry:**
```bash
# Pull images manually
docker pull supabase/postgres:15.1.0.147
docker pull supabase/gotrue:v2.99.0
docker pull supabase/realtime:v2.25.50
```

### 6. "Database connection failed"

#### Error Message:
```
Error: failed to connect to database
```

#### Solutions:

**Check Database Status:**
```bash
# Check Supabase status
supabase status

# Check Docker containers
docker ps

# Check database logs
docker logs supabase_db_secquiz
```

**Reset Database:**
```bash
# Stop and restart
supabase stop
supabase start

# Or reset completely
supabase db reset
```

**Check Connection String:**
```bash
# Verify connection details
supabase status | grep "DB URL"

# Test connection manually
psql "postgresql://postgres:postgres@localhost:54322/postgres"
```

### 7. VS Code Extension Issues

#### Problem: Extension doesn't connect to local instance

**Solutions:**

1. **Use CLI instead of extension for local development**
   - The VS Code extension is primarily for remote Supabase projects
   - Use `supabase start` and access Studio at http://localhost:54323

2. **Configure extension for local use:**
   ```json
   // In VS Code settings.json
   {
     "supabase.projectUrl": "http://localhost:54321",
     "supabase.anonKey": "your-local-anon-key"
   }
   ```

3. **Access Supabase Studio directly:**
   - Open http://localhost:54323 in your browser
   - This provides the same functionality as the VS Code extension

### 8. Environment Variable Issues

#### Problem: Wrong Supabase URL/Key in application

**Check Current Configuration:**
```bash
# Check environment variables
env | grep SUPABASE

# Check .env file
cat .env
```

**Fix Configuration:**
```bash
# Get local credentials
supabase status

# Update .env file
echo "VITE_SUPABASE_URL=http://localhost:54321" > .env.local
echo "VITE_SUPABASE_ANON_KEY=your-local-anon-key" >> .env.local
```

### 9. Migration Issues

#### Problem: Migrations fail to apply

**Solutions:**

```bash
# Check migration status
supabase migration list

# Apply specific migration
supabase migration up --target 20231201000000

# Reset and reapply all migrations
supabase db reset

# Create new migration for fixes
supabase migration new fix_schema_issue
```

### 10. Performance Issues

#### Problem: Slow startup or high resource usage

**Solutions:**

**Optimize Docker:**
```bash
# Increase Docker resources in Docker Desktop settings
# Recommended: 4GB RAM, 2 CPUs minimum

# Clean up unused resources
docker system prune -a
```

**Check System Resources:**
```bash
# Monitor resource usage
docker stats

# Check available disk space
df -h
```

## Platform-Specific Issues

### Windows-Specific

**WSL Issues:**
```bash
# If using WSL, ensure Docker Desktop integration is enabled
# In Docker Desktop: Settings > Resources > WSL Integration

# Use WSL 2 for better performance
wsl --set-default-version 2
```

**PowerShell Execution Policy:**
```powershell
# Allow script execution
Set-ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### macOS-Specific

**M1/M2 Mac Issues:**
```bash
# Use Rosetta if needed
arch -x86_64 supabase start

# Or install native ARM version
brew install supabase/tap/supabase
```

**Permission Issues:**
```bash
# Fix Homebrew permissions
sudo chown -R $(whoami) /usr/local/share/zsh /usr/local/share/zsh/site-functions
```

### Linux-Specific

**Systemd Issues:**
```bash
# Enable Docker service
sudo systemctl enable docker
sudo systemctl start docker

# Check service status
sudo systemctl status docker
```

**Firewall Issues:**
```bash
# Allow Docker ports
sudo ufw allow 54321:54324/tcp

# Or disable firewall temporarily
sudo ufw disable
```

## Diagnostic Commands

### System Information
```bash
# OS and architecture
uname -a

# Docker version and info
docker --version
docker info

# Available disk space
df -h

# Memory usage
free -h

# Network connectivity
ping -c 4 google.com
```

### Supabase-Specific Diagnostics
```bash
# Supabase version and status
supabase --version
supabase status

# Docker containers
docker ps -a | grep supabase

# Docker images
docker images | grep supabase

# Container logs
docker logs supabase_db_secquiz
docker logs supabase_api_secquiz
```

## Getting Help

### Community Resources
- **GitHub Issues**: https://github.com/supabase/cli/issues
- **Discord**: https://discord.supabase.com
- **Documentation**: https://supabase.com/docs/guides/cli

### Creating Bug Reports
Include this information:
```bash
# System info
uname -a
supabase --version
docker --version

# Error logs
supabase start --debug
docker logs supabase_db_secquiz

# Configuration
cat supabase/config.toml
```

### Emergency Workarounds

**If CLI completely fails:**
1. Use remote Supabase instance for development
2. Use Docker Compose directly with Supabase images
3. Set up PostgreSQL locally and use Supabase client libraries

**Quick Reset:**
```bash
# Nuclear option - reset everything
supabase stop
docker system prune -a
rm -rf supabase/.temp
supabase start
```
