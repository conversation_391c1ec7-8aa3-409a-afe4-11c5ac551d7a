import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Crown, FileText, Shield } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { useAdminStatus } from "@/hooks/use-admin-status";
import { fixDeleteUserFunction } from "@/utils/execute-sql";
import { fixIsAdminFunction } from "@/utils/fix-is-admin-function";
import { useNavigate } from "react-router-dom";
import React from "react";
import AdminDebug from "@/components/AdminDebug";

// You may need to move fixUserPaymentIssues here or import it if it's in a shared utils file
import { useAdminUsers } from "@/hooks/use-admin-users";

const DeveloperTools = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const { isAdmin } = useAdminStatus(user);
  const { updateUserSubscription } = useAdminUsers();
  const navigate = useNavigate();

  const isDevUser = user && (user.email || '').toLowerCase() === '<EMAIL>';
  // Only allow access if admin and in development mode or isDevUser
  if (!isAdmin || (!(process.env.NODE_ENV === 'development' || isDevUser))) {
    navigate("/");
    return null;
  }

  // Helper for making a user premium (copied from AdminDashboard)
  const fixUserPaymentIssues = async (email: string, planId: string = 'pro') => {
    try {
      // This is a simplified version; you may want to move the full logic here if needed
      await updateUserSubscription(email, true, undefined, planId);
      toast({
        title: "User made premium",
        description: `User ${email} has been successfully made a premium user with the ${planId} plan`,
      });
    } catch (error: any) {
      toast({
        title: "Error making user premium",
        description: error.message || "An unknown error occurred",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="p-4 max-w-2xl mx-auto">
      <Card className="cyber-card p-6">
        <h2 className="text-lg font-bold mb-4">Developer Tools</h2>
        <div className="flex flex-wrap gap-2 mb-2">
          <Button
            variant="outline"
            size="sm"
            className="border-red-500 text-red-600 hover:bg-red-50 font-medium"
            onClick={async () => {
              try {
                toast({
                  title: "Fixing delete user function",
                  description: "Please wait while we fix the delete user function...",
                });
                const result = await fixDeleteUserFunction();
                if (result.success) {
                  toast({
                    title: "Delete user function fixed",
                    description: "The delete user function has been fixed successfully. You should now be able to delete users.",
                    duration: 5000,
                  });
                } else {
                  toast({
                    title: "Error fixing delete user function",
                    description: result.error || "An unknown error occurred",
                    variant: "destructive",
                  });
                }
              } catch (error: any) {
                toast({
                  title: "Error fixing delete user function",
                  description: error.message || "An unknown error occurred",
                  variant: "destructive",
                });
              }
            }}
          >
            <FileText className="h-3 w-3 mr-1" />
            Fix Delete User Function
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="border-blue-500 text-blue-600 hover:bg-blue-50 font-medium"
            onClick={async () => {
              try {
                toast({
                  title: "Fixing admin check function",
                  description: "Please wait while we fix the is_admin function...",
                });
                const result = await fixIsAdminFunction();
                if (result.success) {
                  toast({
                    title: "Admin check function fixed",
                    description: "The is_admin function has been fixed successfully. You should now be able to delete users and perform other admin actions.",
                    duration: 5000,
                  });
                } else {
                  toast({
                    title: "Error fixing admin check function",
                    description: result.error || "An unknown error occurred",
                    variant: "destructive",
                  });
                }
              } catch (error: any) {
                toast({
                  title: "Error fixing admin check function",
                  description: error.message || "An unknown error occurred",
                  variant: "destructive",
                });
              }
            }}
          >
            <Shield className="h-3 w-3 mr-1" />
            Fix Admin Check Function
          </Button>
        </div>
        <p className="text-xs text-muted-foreground mt-4">These tools are only available in development mode and for admin users.</p>
      </Card>

      {/* Admin Debug Panel */}
      <AdminDebug />
    </div>
  );
};

export default DeveloperTools;