# Network Security - Beginner Level Questions

## 50 Multiple Choice Questions

**Question 1:** What is the primary purpose of a firewall in network security?
A) To speed up internet connection
B) To block unauthorized network traffic
C) To encrypt all data
D) To backup network files
**Correct Answer:** B
**Explanation:** Firewalls are designed to monitor and control network traffic based on security rules, blocking unauthorized access while allowing legitimate traffic through.

**Question 2:** What does VPN stand for?
A) Virtual Private Network
B) Very Private Network
C) Verified Protection Network
D) Visual Protocol Network
**Correct Answer:** A
**Explanation:** VPN stands for Virtual Private Network, which creates a secure, encrypted connection over a less secure network like the internet.

**Question 3:** Which protocol is considered more secure for web browsing?
A) HTTP
B) FTP
C) HTTPS
D) SMTP
**Correct Answer:** C
**Explanation:** HTTPS (HTTP Secure) encrypts data between your browser and the website, making it much safer than regular HTTP which sends data in plain text.

**Question 4:** What is the main difference between WPA and WEP wireless security?
A) WPA is older than WEP
B) WEP is more secure than WPA
C) WPA provides stronger encryption than WEP
D) They are exactly the same
**Correct Answer:** C
**Explanation:** WPA (Wi-Fi Protected Access) uses much stronger encryption methods than the older WEP (Wired Equivalent Privacy), which has known security vulnerabilities.

**Question 5:** What is a DDoS attack?
A) A type of virus
B) An attack that overwhelms a server with traffic
C) A password cracking method
D) A firewall configuration
**Correct Answer:** B
**Explanation:** DDoS (Distributed Denial of Service) attacks flood a target server or network with massive amounts of traffic from multiple sources, making it unavailable to legitimate users.

**Question 6:** Which device typically sits between your internal network and the internet?
A) Switch
B) Hub
C) Router
D) Modem only
**Correct Answer:** C
**Explanation:** A router connects your internal network to the internet and often includes firewall capabilities to protect your network from external threats.

**Question 7:** What is port scanning used for?
A) To speed up network connections
B) To find open ports on a target system
C) To encrypt network traffic
D) To backup network data
**Correct Answer:** B
**Explanation:** Port scanning is a technique used to discover which ports are open on a target system, often used by both security professionals and attackers to find potential entry points.

**Question 8:** What should you do if you connect to an unsecured public Wi-Fi network?
A) Use it normally for all activities
B) Avoid accessing sensitive information
C) Share your passwords with others
D) Turn off your firewall
**Correct Answer:** B
**Explanation:** Unsecured public Wi-Fi networks can be monitored by attackers, so you should avoid accessing sensitive information like banking or personal accounts without additional protection like a VPN.

**Question 9:** What is network monitoring primarily used for?
A) To slow down the network
B) To watch for suspicious activity and performance issues
C) To delete network files
D) To create new user accounts
**Correct Answer:** B
**Explanation:** Network monitoring involves continuously observing network traffic and performance to detect suspicious activities, security threats, and performance problems.

**Question 10:** Which of these is a strong network security practice?
A) Using the same password for all devices
B) Disabling all security features for convenience
C) Regularly updating firmware and software
D) Sharing network passwords publicly
**Correct Answer:** C
**Explanation:** Regularly updating firmware and software patches security vulnerabilities and ensures you have the latest security features and protections.

**Question 11:** What does SSH stand for?
A) Secure Shell
B) Simple Security Host
C) System Security Hub
D) Safe Server Host
**Correct Answer:** A
**Explanation:** SSH (Secure Shell) is a cryptographic network protocol that provides a secure way to access and manage network devices and servers remotely.

**Question 12:** What is the purpose of network segmentation?
A) To make networks slower
B) To isolate different parts of a network for security
C) To combine all networks together
D) To delete network data
**Correct Answer:** B
**Explanation:** Network segmentation divides a network into smaller, isolated segments to limit the spread of attacks and control access to sensitive resources.

**Question 13:** Which port is commonly used for HTTPS traffic?
A) Port 80
B) Port 443
C) Port 21
D) Port 25
**Correct Answer:** B
**Explanation:** Port 443 is the standard port for HTTPS (secure web) traffic, while port 80 is used for regular HTTP traffic.

**Question 14:** What is an Intrusion Detection System (IDS)?
A) A system that creates intrusions
B) A system that monitors for suspicious network activity
C) A system that deletes files
D) A system that speeds up networks
**Correct Answer:** B
**Explanation:** An IDS monitors network traffic and system activities for malicious activities or policy violations and alerts administrators when threats are detected.

**Question 15:** What should you look for to verify a website is using HTTPS?
A) The website loads quickly
B) A lock icon in the browser address bar
C) The website has many images
D) The website asks for your password
**Correct Answer:** B
**Explanation:** A lock icon in the browser's address bar indicates that the website is using HTTPS encryption to protect your data during transmission.

**Question 16:** What is the main purpose of a network switch?
A) To connect devices within a local network
B) To connect to the internet
C) To encrypt all data
D) To block all network traffic
**Correct Answer:** A
**Explanation:** A network switch connects multiple devices within a local network, allowing them to communicate with each other efficiently.

**Question 17:** Which of these is considered a weak wireless security setting?
A) WPA3
B) WPA2
C) Open (no security)
D) WPA
**Correct Answer:** C
**Explanation:** An open wireless network with no security allows anyone to connect and potentially intercept traffic, making it the weakest security option.

**Question 18:** What does DNS stand for?
A) Domain Name System
B) Data Network Security
C) Digital Network Service
D) Direct Network System
**Correct Answer:** A
**Explanation:** DNS (Domain Name System) translates human-readable domain names (like google.com) into IP addresses that computers use to communicate.

**Question 19:** What is a common sign that your network might be compromised?
A) Fast internet speeds
B) Unusual network traffic or slow performance
C) All devices working perfectly
D) Low electricity bills
**Correct Answer:** B
**Explanation:** Unusual network traffic patterns, slow performance, or unexpected network activity can indicate that your network has been compromised by attackers.

**Question 20:** What is the purpose of MAC address filtering?
A) To speed up network connections
B) To allow only specific devices to connect to the network
C) To encrypt all network data
D) To delete network logs
**Correct Answer:** B
**Explanation:** MAC address filtering allows network administrators to specify which devices can connect to the network based on their unique MAC addresses.

**Question 21:** Which protocol is commonly used for secure file transfer?
A) FTP
B) HTTP
C) SFTP
D) SMTP
**Correct Answer:** C
**Explanation:** SFTP (Secure File Transfer Protocol) provides encrypted file transfer capabilities, unlike regular FTP which sends data in plain text.

**Question 22:** What is a honeypot in network security?
A) A sweet snack for IT workers
B) A decoy system designed to attract attackers
C) A type of firewall
D) A network cable
**Correct Answer:** B
**Explanation:** A honeypot is a security mechanism that creates a decoy system to attract and detect attackers, helping security teams understand attack methods.

**Question 23:** What should you do before connecting a new device to your network?
A) Nothing, just plug it in
B) Update its firmware and change default passwords
C) Share its password with everyone
D) Disable all security features
**Correct Answer:** B
**Explanation:** Before connecting new devices, you should update firmware to patch security vulnerabilities and change default passwords to prevent unauthorized access.

**Question 24:** What is the main risk of using default passwords on network devices?
A) The device will work too fast
B) Attackers can easily guess and access the device
C) The device will consume more power
D) The device will be too secure
**Correct Answer:** B
**Explanation:** Default passwords are widely known and published online, making it easy for attackers to gain unauthorized access to devices that still use them.

**Question 25:** What does DHCP do in a network?
A) Encrypts all network traffic
B) Automatically assigns IP addresses to devices
C) Blocks all internet access
D) Deletes network files
**Correct Answer:** B
**Explanation:** DHCP (Dynamic Host Configuration Protocol) automatically assigns IP addresses and other network configuration information to devices joining the network.

**Question 26:** Which of these is a best practice for wireless network security?
A) Broadcasting your network name loudly
B) Using a strong, unique password
C) Allowing everyone to connect
D) Never changing the password
**Correct Answer:** B
**Explanation:** Using a strong, unique password for your wireless network prevents unauthorized users from connecting and accessing your network resources.

**Question 27:** What is the purpose of a DMZ in network security?
A) To make networks completely open
B) To create a buffer zone between internal and external networks
C) To delete all network data
D) To speed up internet connections
**Correct Answer:** B
**Explanation:** A DMZ (Demilitarized Zone) is a network segment that sits between the internal network and external networks, providing an additional layer of security.

**Question 28:** What should you do if you suspect a security breach on your network?
A) Ignore it and hope it goes away
B) Disconnect affected systems and investigate
C) Share your passwords with more people
D) Turn off all security systems
**Correct Answer:** B
**Explanation:** If you suspect a security breach, you should immediately isolate affected systems to prevent further damage and begin investigating the incident.

**Question 29:** What is bandwidth throttling?
A) A type of network attack
B) Limiting the amount of data that can be transmitted
C) Encrypting network traffic
D) Deleting network logs
**Correct Answer:** B
**Explanation:** Bandwidth throttling is the intentional limiting of data transmission rates, often used to manage network performance or enforce usage policies.

**Question 30:** Which of these makes a password stronger for network access?
A) Using only lowercase letters
B) Making it very short
C) Including numbers, symbols, and mixed case letters
D) Using common dictionary words
**Correct Answer:** C
**Explanation:** Strong passwords include a mix of uppercase and lowercase letters, numbers, and symbols, making them much harder for attackers to guess or crack.

**Question 31:** What is the main purpose of network access control (NAC)?
A) To make networks slower
B) To control which devices and users can access the network
C) To delete all network data
D) To share passwords publicly
**Correct Answer:** B
**Explanation:** Network Access Control (NAC) systems verify that devices and users meet security requirements before allowing them to access network resources.

**Question 32:** What does it mean when a network port is "closed"?
A) The port is physically damaged
B) The port is not accepting connections
C) The port is working perfectly
D) The port needs to be replaced
**Correct Answer:** B
**Explanation:** A closed network port means that no service is listening on that port and it will not accept incoming connections, which is often a security measure.

**Question 33:** What is a common method attackers use to intercept wireless communications?
A) Packet sniffing
B) Cable cutting
C) Power outages
D) Loud noises
**Correct Answer:** A
**Explanation:** Packet sniffing involves capturing and analyzing network traffic, which can be used by attackers to intercept sensitive information on wireless networks.

**Question 34:** What should you do with unused network ports on switches?
A) Leave them open for convenience
B) Disable them to prevent unauthorized access
C) Connect them to the internet
D) Use them for storage
**Correct Answer:** B
**Explanation:** Unused network ports should be disabled to prevent attackers from physically connecting unauthorized devices to your network.

**Question 35:** What is the benefit of using a guest network?
A) It makes your main network slower
B) It isolates visitor traffic from your main network
C) It gives guests access to all your files
D) It costs more money
**Correct Answer:** B
**Explanation:** A guest network provides internet access to visitors while keeping them separated from your main network and sensitive resources.

**Question 36:** What is ARP spoofing?
A) A legitimate network function
B) An attack that redirects network traffic by faking MAC addresses
C) A way to speed up networks
D) A type of network cable
**Correct Answer:** B
**Explanation:** ARP spoofing is an attack where an attacker sends fake ARP messages to redirect network traffic through their device, allowing them to intercept communications.

**Question 37:** Which of these is a sign of a possible man-in-the-middle attack?
A) Fast internet speeds
B) Unexpected certificate warnings when browsing
C) All websites loading normally
D) Low network usage
**Correct Answer:** B
**Explanation:** Unexpected certificate warnings can indicate that someone is intercepting your connection and presenting their own certificate instead of the legitimate one.

**Question 38:** What is the purpose of network logging?
A) To slow down the network
B) To record network activities for security analysis
C) To delete network data
D) To make networks more expensive
**Correct Answer:** B
**Explanation:** Network logging records network activities and events, providing valuable information for security monitoring, troubleshooting, and forensic analysis.

**Question 39:** What should you do before disposing of old network equipment?
A) Throw it away immediately
B) Securely wipe all data and configuration settings
C) Sell it with all data intact
D) Give it away without checking it
**Correct Answer:** B
**Explanation:** Before disposing of network equipment, you should securely wipe all stored data and configuration settings to prevent sensitive information from falling into the wrong hands.

**Question 40:** What is a VLAN?
A) A very long area network
B) A virtual LAN that segments network traffic
C) A type of network cable
D) A network virus
**Correct Answer:** B
**Explanation:** A VLAN (Virtual Local Area Network) logically segments a physical network into separate broadcast domains, improving security and network management.

**Question 41:** What is the main security risk of peer-to-peer (P2P) networks?
A) They are too fast
B) They can expose your computer to malware and unauthorized access
C) They use too little bandwidth
D) They are too secure
**Correct Answer:** B
**Explanation:** P2P networks can expose your computer to malware, unauthorized access, and legal issues since you're directly connecting to other users' computers.

**Question 42:** What does "network hardening" mean?
A) Making network cables stronger
B) Securing a network by reducing vulnerabilities
C) Making networks more expensive
D) Slowing down network speeds
**Correct Answer:** B
**Explanation:** Network hardening involves implementing security measures to reduce vulnerabilities and strengthen the overall security posture of a network.

**Question 43:** What is a common indicator of network congestion?
A) Very fast data transfer
B) Slow response times and timeouts
C) Perfect connectivity
D) Low CPU usage
**Correct Answer:** B
**Explanation:** Network congestion typically results in slow response times, timeouts, and degraded performance as too much traffic competes for limited bandwidth.

**Question 44:** What should you do if you receive a suspicious email asking for network credentials?
A) Reply with your username and password
B) Delete it and report it as phishing
C) Forward it to all your contacts
D) Click all the links to investigate
**Correct Answer:** B
**Explanation:** Suspicious emails asking for credentials are likely phishing attempts and should be deleted and reported to help protect yourself and others.

**Question 45:** What is the purpose of network redundancy?
A) To make networks more complicated
B) To ensure network availability if one component fails
C) To slow down network traffic
D) To increase network costs
**Correct Answer:** B
**Explanation:** Network redundancy provides backup paths and components so that if one part of the network fails, traffic can still flow through alternative routes.

**Question 46:** What is a botnet?
A) A legitimate network tool
B) A network of infected computers controlled by criminals
C) A type of network cable
D) A network monitoring software
**Correct Answer:** B
**Explanation:** A botnet is a network of compromised computers that are remotely controlled by cybercriminals to perform malicious activities like DDoS attacks or spam distribution.

**Question 47:** What should you check when connecting to a public Wi-Fi network?
A) Nothing, all public Wi-Fi is safe
B) The network name and avoid suspicious networks
C) Only the internet speed
D) The number of other users
**Correct Answer:** B
**Explanation:** You should verify the legitimate network name with the venue and avoid connecting to suspicious or unofficial networks that might be set up by attackers.

**Question 48:** What is the main purpose of encryption in network communications?
A) To slow down data transmission
B) To protect data from being read by unauthorized parties
C) To make data larger
D) To delete data automatically
**Correct Answer:** B
**Explanation:** Encryption scrambles data so that even if it's intercepted during transmission, it cannot be read by unauthorized parties without the proper decryption key.

**Question 49:** What is a common mistake people make with home router security?
A) Updating firmware regularly
B) Never changing the default admin password
C) Using strong Wi-Fi passwords
D) Enabling security features
**Correct Answer:** B
**Explanation:** Many people never change the default administrator password on their home routers, leaving them vulnerable to attacks since these passwords are widely known.

**Question 50:** What should you do if your network performance suddenly degrades significantly?
A) Ignore it, it will fix itself
B) Investigate for possible security issues or hardware problems
C) Immediately share all your passwords
D) Turn off all security systems
**Correct Answer:** B
**Explanation:** Sudden network performance degradation could indicate security issues like malware, DDoS attacks, or hardware problems that need immediate investigation and resolution.