-- Verify Migration Compatibility Script
-- Run this BEFORE the main migration to check for any issues

-- 1. Check current table structures
SELECT 'profiles table columns:' as info;
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'profiles'
ORDER BY ordinal_position;

SELECT 'user_profiles table columns:' as info;
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'user_profiles'
ORDER BY ordinal_position;

-- 2. Check data counts
SELECT 'Data counts:' as info;
SELECT 
  'profiles' as table_name, 
  count(*) as row_count 
FROM public.profiles
UNION ALL
SELECT 
  'user_profiles' as table_name, 
  count(*) as row_count 
FROM public.user_profiles
UNION ALL
SELECT 
  'auth.users' as table_name, 
  count(*) as row_count 
FROM auth.users;

-- 3. Check for users in auth.users but not in either profile table
SELECT 'Users missing profiles:' as info;
SELECT 
  au.id, 
  au.email,
  CASE WHEN p.id IS NOT NULL THEN 'has_profile' ELSE 'no_profile' END as profile_status,
  CASE WHEN up.user_id IS NOT NULL THEN 'has_user_profile' ELSE 'no_user_profile' END as user_profile_status
FROM auth.users au
LEFT JOIN public.profiles p ON au.id = p.id
LEFT JOIN public.user_profiles up ON au.id = up.user_id
WHERE p.id IS NULL AND up.user_id IS NULL;

-- 4. Sample data from profiles table (to see actual column values)
SELECT 'Sample profiles data:' as info;
SELECT 
  id,
  full_name,
  is_admin,
  subscription_status,
  subscription_ends_at,
  created_at
FROM public.profiles 
LIMIT 3;

-- 5. Sample data from user_profiles table
SELECT 'Sample user_profiles data:' as info;
SELECT 
  user_id,
  is_subscribed,
  is_admin,
  subscription_expires_at,
  created_at
FROM public.user_profiles 
LIMIT 3;

-- 6. Check for any subscription data
SELECT 'Active subscriptions:' as info;
SELECT count(*) as active_subscriptions
FROM public.subscriptions 
WHERE is_active = true;

-- 7. Check admin users
SELECT 'Admin users:' as info;
SELECT 
  'profiles' as source,
  count(*) as admin_count
FROM public.profiles 
WHERE is_admin = true
UNION ALL
SELECT 
  'user_profiles' as source,
  count(*) as admin_count
FROM public.user_profiles 
WHERE is_admin = true
UNION ALL
SELECT 
  'admin_users' as source,
  count(*) as admin_count
FROM public.admin_users 
WHERE is_admin = true;
