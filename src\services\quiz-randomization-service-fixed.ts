/**
 * Fixed Quiz Randomization Service
 * Handles question selection, answer option shuffling, and quiz session management
 * with improved error handling and fallback mechanisms
 */

import { supabase } from '@/integrations/supabase/client';
import { parseCorrectAnswer, parseQuestionOptions } from '@/utils/answer-validation';
import type { Tables } from '@/types/supabase';

// Type definitions for randomization
export interface Question extends Tables<'questions'> {}

export interface RandomizedQuestion extends Question {
  originalCorrectIndex: number;
  shuffledCorrectIndex: number;
  optionMapping: number[]; // Maps shuffled positions to original positions
  shuffledOptions: Record<string, string>;
}

export interface QuizSessionResult {
  questions: RandomizedQuestion[];
  sessionId: string;
  topicId: string;
  userId: string;
  quizLength: number;
}

export interface QuestionSelectionOptions {
  topicId: string;
  count: number;
  excludeQuestionIds?: string[];
}

/**
 * Fixed Quiz Randomization Service Class
 * Provides methods for question selection, shuffling, and session management
 * with robust error handling and fallback mechanisms
 */
export class QuizRandomizationServiceFixed {
  
  /**
   * Selects random questions from the available question pool for a topic
   * @param options - Question selection options
   * @returns Promise<Question[]> - Array of selected questions
   */
  static async selectRandomQuestions(options: QuestionSelectionOptions): Promise<Question[]> {
    const { topicId, count, excludeQuestionIds = [] } = options;

    try {
      // Validate inputs
      if (!topicId) {
        throw new Error('Topic ID is required for question selection');
      }

      if (count <= 0 || count > 100) {
        throw new Error(`Invalid question count: ${count}. Must be between 1 and 100`);
      }

      console.log(`Selecting ${count} questions for topic ${topicId}`);

      // Build the query
      let query = supabase
        .from('questions')
        .select('*')
        .eq('topic_id', topicId);

      // Exclude specific questions if provided
      if (excludeQuestionIds.length > 0) {
        query = query.not('id', 'in', `(${excludeQuestionIds.join(',')})`);
      }

      // Execute query
      const { data: allQuestions, error } = await query;

      if (error) {
        console.error('Database error selecting questions:', error);
        throw new Error(`Failed to fetch questions: ${error.message}`);
      }

      if (!allQuestions || allQuestions.length === 0) {
        throw new Error(`No questions found for topic ${topicId}`);
      }

      console.log(`Found ${allQuestions.length} questions for topic ${topicId}`);

      // Handle insufficient questions with graceful fallback
      if (allQuestions.length < count) {
        console.warn(`Topic ${topicId} has only ${allQuestions.length} questions, less than requested ${count}`);
        // Return all available questions
        return this.shuffleArray(allQuestions);
      }

      // Select random questions
      const selectedQuestions = this.shuffleArray([...allQuestions]).slice(0, count);

      // Validate question data integrity
      const validQuestions = selectedQuestions.filter(q => 
        q.question_text && q.options && q.correct_answer !== null && q.correct_answer !== undefined
      );

      if (validQuestions.length === 0) {
        throw new Error('All selected questions have invalid data');
      }

      if (validQuestions.length < selectedQuestions.length) {
        console.warn(`Filtered out ${selectedQuestions.length - validQuestions.length} invalid questions`);
      }

      return validQuestions;

    } catch (error) {
      console.error('Error in selectRandomQuestions:', error);
      throw error;
    }
  }

  /**
   * Shuffles answer options while maintaining correct answer mapping
   * @param question - The question to shuffle options for
   * @returns RandomizedQuestion with shuffled options and updated mapping
   */
  static shuffleAnswerOptions(question: Question): RandomizedQuestion {
    try {
      // Validate question data
      if (!question.options) {
        throw new Error('Question has no options to shuffle');
      }

      // Parse the original options and correct answer
      let originalOptions: string[];
      try {
        originalOptions = parseQuestionOptions(question.options);
      } catch (parseError) {
        console.error(`Failed to parse question options:`, parseError);
        throw new Error('Invalid question options format');
      }

      if (originalOptions.length < 2) {
        console.warn(`Question has insufficient options (${originalOptions.length})`);
        
        // Don't shuffle if there are fewer than 2 options
        const parsedAnswer = parseCorrectAnswer(question.correct_answer, originalOptions.length);
        return {
          ...question,
          originalCorrectIndex: parsedAnswer.correctIndex,
          shuffledCorrectIndex: parsedAnswer.correctIndex,
          optionMapping: Array.from({ length: originalOptions.length }, (_, i) => i),
          shuffledOptions: question.options as Record<string, string>
        };
      }

      const parsedAnswer = parseCorrectAnswer(question.correct_answer, originalOptions.length);
      
      if (!parsedAnswer.isValid) {
        console.warn(`Question ${question.id}: Invalid correct answer (${parsedAnswer.originalValue}), using fallback index ${parsedAnswer.correctIndex}`);
      }

      const originalCorrectIndex = parsedAnswer.correctIndex;

      // Create array of indices for shuffling
      const indices = Array.from({ length: originalOptions.length }, (_, i) => i);
      
      // Perform shuffling
      const shuffledIndices = this.shuffleArray(indices);

      // Create the option mapping (shuffled position -> original position)
      const optionMapping = shuffledIndices;

      // Create shuffled options object
      const shuffledOptions: Record<string, string> = {};
      shuffledIndices.forEach((originalIndex, shuffledPosition) => {
        if (originalIndex >= 0 && originalIndex < originalOptions.length) {
          shuffledOptions[shuffledPosition.toString()] = originalOptions[originalIndex];
        } else {
          console.warn(`Invalid original index ${originalIndex} during shuffling`);
          shuffledOptions[shuffledPosition.toString()] = `Option ${shuffledPosition + 1}`;
        }
      });

      // Find where the correct answer ended up after shuffling
      const shuffledCorrectIndex = shuffledIndices.indexOf(originalCorrectIndex);

      if (shuffledCorrectIndex === -1) {
        console.error(`Correct answer index ${originalCorrectIndex} not found in shuffled indices`);
        
        // Emergency fallback: use original order
        return {
          ...question,
          originalCorrectIndex,
          shuffledCorrectIndex: originalCorrectIndex,
          optionMapping: Array.from({ length: originalOptions.length }, (_, i) => i),
          shuffledOptions: question.options as Record<string, string>
        };
      }

      console.log(`Successfully shuffled options for question ${question.id}`);

      return {
        ...question,
        originalCorrectIndex,
        shuffledCorrectIndex,
        optionMapping,
        shuffledOptions
      };

    } catch (error) {
      console.error('Error shuffling answer options:', error);
      
      // Comprehensive fallback: return question with original options and safe defaults
      try {
        const originalOptions = parseQuestionOptions(question.options || {});
        const parsedAnswer = parseCorrectAnswer(question.correct_answer, originalOptions.length);
        
        console.warn(`Using fallback for question ${question.id} due to shuffling error`);
        
        return {
          ...question,
          originalCorrectIndex: parsedAnswer.correctIndex,
          shuffledCorrectIndex: parsedAnswer.correctIndex,
          optionMapping: Array.from({ length: originalOptions.length }, (_, i) => i),
          shuffledOptions: question.options as Record<string, string> || {}
        };
      } catch (fallbackError) {
        console.error('Fallback also failed:', fallbackError);
        
        // Ultimate fallback with minimal data
        return {
          ...question,
          originalCorrectIndex: 0,
          shuffledCorrectIndex: 0,
          optionMapping: [0, 1, 2, 3],
          shuffledOptions: { '0': 'Option A', '1': 'Option B', '2': 'Option C', '3': 'Option D' }
        };
      }
    }
  }

  /**
   * Generates a complete quiz session with randomized questions and options
   * @param topicId - ID of the topic for the quiz
   * @param userId - ID of the user taking the quiz
   * @param quizLength - Number of questions in the quiz
   * @returns Promise<QuizSessionResult> - Complete quiz session data
   */
  static async generateQuizSession(
    topicId: string,
    userId: string,
    quizLength: number = 15
  ): Promise<QuizSessionResult> {
    try {
      console.log('Starting quiz session generation:', { topicId, userId, quizLength });

      // Input validation
      if (!topicId || typeof topicId !== 'string' || topicId.trim() === '') {
        throw new Error('Valid Topic ID is required');
      }

      if (!userId || typeof userId !== 'string' || userId.trim() === '') {
        throw new Error('Valid User ID is required');
      }

      if (typeof quizLength !== 'number' || quizLength < 1 || quizLength > 50 || !Number.isInteger(quizLength)) {
        throw new Error(`Invalid quiz length: ${quizLength}. Must be an integer between 1 and 50`);
      }

      // Check question availability
      const { data: questionCount, error: countError } = await supabase
        .from('questions')
        .select('id', { count: 'exact' })
        .eq('topic_id', topicId);

      if (countError) {
        throw new Error(`Unable to check question availability: ${countError.message}`);
      }

      const totalQuestions = questionCount?.length || 0;
      
      if (totalQuestions === 0) {
        throw new Error(`No questions available for topic ${topicId}`);
      }

      // Adjust quiz length if necessary
      const effectiveQuizLength = Math.min(quizLength, totalQuestions);
      
      if (effectiveQuizLength < quizLength) {
        console.warn(`Requested ${quizLength} questions but only ${totalQuestions} available. Adjusting to ${effectiveQuizLength}`);
      }

      // Select random questions
      const selectedQuestions = await this.selectRandomQuestions({
        topicId,
        count: effectiveQuizLength
      });

      if (selectedQuestions.length === 0) {
        throw new Error(`No questions could be selected for topic ${topicId}`);
      }

      // Shuffle answer options for each question
      const randomizedQuestions: RandomizedQuestion[] = [];
      const shufflingErrors: string[] = [];

      for (const question of selectedQuestions) {
        try {
          const randomizedQuestion = this.shuffleAnswerOptions(question);
          randomizedQuestions.push(randomizedQuestion);
        } catch (shuffleError) {
          shufflingErrors.push(question.id);
          console.warn(`Failed to shuffle options for question ${question.id}, using original order`);
          
          // Add question with original options as fallback
          const originalOptions = parseQuestionOptions(question.options);
          const parsedAnswer = parseCorrectAnswer(question.correct_answer, originalOptions.length);
          
          randomizedQuestions.push({
            ...question,
            originalCorrectIndex: parsedAnswer.correctIndex,
            shuffledCorrectIndex: parsedAnswer.correctIndex,
            optionMapping: Array.from({ length: originalOptions.length }, (_, idx) => idx),
            shuffledOptions: question.options as Record<string, string>
          });
        }
      }

      if (shufflingErrors.length > 0) {
        console.warn(`Shuffling failed for ${shufflingErrors.length} questions, used fallback`);
      }

      if (randomizedQuestions.length === 0) {
        throw new Error('No questions could be processed for the quiz session');
      }

      // Generate proper UUID for session ID
      const sessionId = crypto.randomUUID();

      console.log(`Successfully created quiz session with ${randomizedQuestions.length} questions`);

      return {
        questions: randomizedQuestions,
        sessionId,
        topicId,
        userId,
        quizLength: randomizedQuestions.length
      };

    } catch (error) {
      console.error('Error generating quiz session:', error);
      throw error;
    }
  }

  /**
   * Records question analytics (simplified version)
   * @param questionId - ID of the question
   * @param userId - ID of the user
   * @param sessionId - ID of the quiz session
   * @param answeredCorrectly - Whether the question was answered correctly
   * @param selectedOption - The option selected by the user
   * @param timeToAnswer - Time taken to answer in seconds
   */
  static async recordQuestionAnalytics(
    questionId: string,
    userId: string,
    sessionId: string,
    answeredCorrectly: boolean,
    selectedOption: number,
    timeToAnswer?: number
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('question_analytics')
        .insert({
          question_id: questionId,
          user_id: userId,
          quiz_session_id: sessionId,
          answered_correctly: answeredCorrectly,
          selected_option: selectedOption,
          time_to_answer: timeToAnswer
        });

      if (error) {
        console.warn('Could not record question analytics:', error.message);
        // Don't throw error, just log it
      } else {
        console.log('Question analytics recorded successfully');
      }
    } catch (error) {
      console.warn('Error recording question analytics:', error);
      // Don't throw error, analytics is not critical
    }
  }

  /**
   * Completes a quiz session (simplified version)
   * @param sessionId - ID of the quiz session
   * @param userId - ID of the user
   * @param score - Final score
   * @param timeTaken - Total time taken in seconds
   */
  static async completeQuizSession(
    sessionId: string,
    userId: string,
    score: number,
    timeTaken?: number
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('quiz_sessions')
        .update({
          completed_at: new Date().toISOString(),
          score,
          time_taken: timeTaken
        })
        .eq('id', sessionId)
        .eq('user_id', userId);

      if (error) {
        console.warn('Could not complete quiz session:', error.message);
        // Don't throw error, just log it
      } else {
        console.log('Quiz session completed successfully');
      }
    } catch (error) {
      console.warn('Error completing quiz session:', error);
      // Don't throw error, session completion is not critical for quiz functionality
    }
  }

  /**
   * Gets topic question statistics (simplified version)
   * @param topicId - ID of the topic
   * @returns Promise with topic question statistics
   */
  static async getTopicQuestionStats(topicId: string) {
    try {
      const { data: questions, error } = await supabase
        .from('questions')
        .select('id')
        .eq('topic_id', topicId);

      if (error) {
        console.error('Error fetching topic questions:', error);
        throw error;
      }

      const totalQuestions = questions?.length || 0;

      return {
        total_questions: totalQuestions,
        avg_usage_count: 0,
        avg_correct_rate: 0,
        questions_never_used: totalQuestions,
        questions_low_performance: 0
      };

    } catch (error) {
      console.error('Error in getTopicQuestionStats:', error);
      throw error;
    }
  }

  /**
   * Utility method to shuffle an array using Fisher-Yates algorithm
   * @param array - Array to shuffle
   * @returns Shuffled copy of the array
   */
  private static shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }
}

export default QuizRandomizationServiceFixed;