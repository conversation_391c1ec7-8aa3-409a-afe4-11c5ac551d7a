import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { BatchImportService, type BatchImportConfig, type BatchImportProgress } from '../batch-import-service';
import type { MultiTopicImportResult, TopicImportResult, ValidatedQuestion } from '@/utils/csv-import';

// Mock the supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(),
  },
}));

// Mock the topic service
vi.mock('../topic-service', () => ({
  topicService: {
    findTopic: vi.fn(),
    createTopic: vi.fn(),
    getTopicById: vi.fn(),
    resolveTopicReferences: vi.fn(),
  },
}));

// Mock uuid
vi.mock('uuid', () => ({
  v4: vi.fn(() => 'mock-uuid-123'),
}));

describe('BatchImportService', () => {
  let batchImportService: BatchImportService;
  let mockSupabase: any;
  let mockTopicService: any;

  beforeEach(async () => {
    // Import the mocked modules
    const { supabase } = await import('@/integrations/supabase/client');
    const { topicService } = await import('../topic-service');
    
    mockSupabase = supabase;
    mockTopicService = topicService;
    
    batchImportService = new BatchImportService();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  // Helper function to create mock validated questions
  const createMockQuestion = (id: string, topicId: string, questionText: string): ValidatedQuestion => ({
    id,
    topic_id: topicId,
    question_text: questionText,
    options: {
      A: 'Option A',
      B: 'Option B',
      C: 'Option C',
      D: 'Option D',
    },
    correct_answer: 'A',
    explanation: 'Test explanation',
    difficulty: 'medium',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  });

  // Helper function to create mock import result
  const createMockImportResult = (
    topics: Array<{ id: string; name: string; questions: ValidatedQuestion[]; isNew?: boolean }>
  ): MultiTopicImportResult => {
    const topicResults = new Map<string, TopicImportResult>();
    const newTopicsCreated: string[] = [];

    topics.forEach(topic => {
      topicResults.set(topic.id, {
        topicId: topic.id,
        topicName: topic.name,
        validQuestions: topic.questions,
        errors: [],
        isNewTopic: topic.isNew || false,
      });

      if (topic.isNew) {
        newTopicsCreated.push(topic.name);
      }
    });

    return {
      success: true,
      totalRows: topics.reduce((sum, topic) => sum + topic.questions.length, 0),
      topicResults,
      globalErrors: [],
      newTopicsCreated,
    };
  };

  describe('executeBatchImport', () => {
    it('should successfully import questions for single topic', async () => {
      const questions = [
        createMockQuestion('q1', 'topic-1', 'Question 1'),
        createMockQuestion('q2', 'topic-1', 'Question 2'),
      ];

      const importResult = createMockImportResult([
        { id: 'topic-1', name: 'Security Fundamentals', questions },
      ]);

      const config: BatchImportConfig = {
        mode: 'single-topic',
        autoCreateTopics: false,
        selectedTopicId: 'topic-1',
        batchSize: 2,
      };

      // Mock topic exists
      mockTopicService.getTopicById.mockResolvedValue({
        id: 'topic-1',
        title: 'Security Fundamentals',
      });

      // Mock successful question insertion
      const mockChain = {
        insert: vi.fn().mockResolvedValue({ error: null }),
      };
      mockSupabase.from.mockReturnValue(mockChain);

      const result = await batchImportService.executeBatchImport(importResult, config);

      expect(result.success).toBe(true);
      expect(result.totalQuestionsImported).toBe(2);
      expect(result.totalTopicsProcessed).toBe(1);
      expect(result.errors).toHaveLength(0);
      expect(mockChain.insert).toHaveBeenCalledWith(questions);
    });

    it('should successfully import questions for multiple topics', async () => {
      const topic1Questions = [
        createMockQuestion('q1', 'topic-1', 'Question 1'),
        createMockQuestion('q2', 'topic-1', 'Question 2'),
      ];

      const topic2Questions = [
        createMockQuestion('q3', 'topic-2', 'Question 3'),
      ];

      const importResult = createMockImportResult([
        { id: 'topic-1', name: 'Security Fundamentals', questions: topic1Questions },
        { id: 'topic-2', name: 'Network Security', questions: topic2Questions },
      ]);

      const config: BatchImportConfig = {
        mode: 'multi-topic',
        autoCreateTopics: false,
        batchSize: 2,
      };

      // Mock topics exist
      mockTopicService.getTopicById
        .mockResolvedValueOnce({ id: 'topic-1', title: 'Security Fundamentals' })
        .mockResolvedValueOnce({ id: 'topic-2', title: 'Network Security' });

      // Mock successful question insertion
      const mockChain = {
        insert: vi.fn().mockResolvedValue({ error: null }),
      };
      mockSupabase.from.mockReturnValue(mockChain);

      const result = await batchImportService.executeBatchImport(importResult, config);

      expect(result.success).toBe(true);
      expect(result.totalQuestionsImported).toBe(3);
      expect(result.totalTopicsProcessed).toBe(2);
      expect(result.errors).toHaveLength(0);
      expect(mockChain.insert).toHaveBeenCalledTimes(2);
    });

    it('should create new topics when autoCreateTopics is enabled', async () => {
      const questions = [
        createMockQuestion('q1', 'topic-1', 'Question 1'),
      ];

      const importResult = createMockImportResult([
        { id: 'topic-1', name: 'New Topic', questions, isNew: true },
      ]);

      const config: BatchImportConfig = {
        mode: 'multi-topic',
        autoCreateTopics: true,
        batchSize: 1,
      };

      // Mock topic doesn't exist initially, then exists after creation
      mockTopicService.findTopic.mockResolvedValue(null);
      mockTopicService.createTopic.mockResolvedValue({
        id: 'topic-1',
        title: 'New Topic',
      });
      mockTopicService.getTopicById.mockResolvedValue({
        id: 'topic-1',
        title: 'New Topic',
      });

      // Mock successful question insertion
      const mockChain = {
        insert: vi.fn().mockResolvedValue({ error: null }),
      };
      mockSupabase.from.mockReturnValue(mockChain);

      const result = await batchImportService.executeBatchImport(importResult, config);

      expect(result.success).toBe(true);
      expect(result.totalQuestionsImported).toBe(1);
      expect(result.topicsCreated).toContain('New Topic');
      expect(mockTopicService.createTopic).toHaveBeenCalledWith({
        title: 'New Topic',
        difficulty: 'medium',
        is_premium: false,
      });
    });

    it('should handle partial success when some topics fail', async () => {
      const topic1Questions = [
        createMockQuestion('q1', 'topic-1', 'Question 1'),
      ];

      const topic2Questions = [
        createMockQuestion('q2', 'topic-2', 'Question 2'),
      ];

      const importResult = createMockImportResult([
        { id: 'topic-1', name: 'Existing Topic', questions: topic1Questions },
        { id: 'topic-2', name: 'Missing Topic', questions: topic2Questions },
      ]);

      const config: BatchImportConfig = {
        mode: 'multi-topic',
        autoCreateTopics: false,
        batchSize: 1,
      };

      // Mock first topic exists, second doesn't
      mockTopicService.getTopicById
        .mockResolvedValueOnce({ id: 'topic-1', title: 'Existing Topic' })
        .mockResolvedValueOnce(null);

      // Mock successful question insertion for first topic
      const mockChain = {
        insert: vi.fn().mockResolvedValue({ error: null }),
      };
      mockSupabase.from.mockReturnValue(mockChain);

      const result = await batchImportService.executeBatchImport(importResult, config);

      expect(result.success).toBe(true); // Partial success
      expect(result.totalQuestionsImported).toBe(1);
      expect(result.totalTopicsProcessed).toBe(2);
      expect(result.errors.length).toBeGreaterThan(0);
      
      // Check that error mentions missing topic
      const topicError = result.errors.find(e => e.type === 'topic-creation');
      expect(topicError).toBeDefined();
      expect(topicError?.message).toContain('Missing Topic');
    });

    it('should retry failed batches with exponential backoff', async () => {
      const questions = [
        createMockQuestion('q1', 'topic-1', 'Question 1'),
        createMockQuestion('q2', 'topic-1', 'Question 2'),
      ];

      const importResult = createMockImportResult([
        { id: 'topic-1', name: 'Test Topic', questions },
      ]);

      const config: BatchImportConfig = {
        mode: 'single-topic',
        autoCreateTopics: false,
        selectedTopicId: 'topic-1',
        batchSize: 2,
        maxRetries: 2,
      };

      // Mock topic exists
      mockTopicService.getTopicById.mockResolvedValue({
        id: 'topic-1',
        title: 'Test Topic',
      });

      // Mock failed insertion that succeeds on retry
      const mockChain = {
        insert: vi.fn()
          .mockRejectedValueOnce(new Error('Database timeout'))
          .mockRejectedValueOnce(new Error('Database timeout'))
          .mockResolvedValueOnce({ error: null }),
      };
      mockSupabase.from.mockReturnValue(mockChain);

      const result = await batchImportService.executeBatchImport(importResult, config);

      expect(result.success).toBe(true);
      expect(result.totalQuestionsImported).toBe(2);
      expect(mockChain.insert).toHaveBeenCalledTimes(3); // Initial + 2 retries
    }, 10000); // Increase timeout to 10 seconds

    it('should handle individual question failures after batch retry exhaustion', async () => {
      const questions = [
        createMockQuestion('q1', 'topic-1', 'Question 1'),
        createMockQuestion('q2', 'topic-1', 'Question 2'),
      ];

      const importResult = createMockImportResult([
        { id: 'topic-1', name: 'Test Topic', questions },
      ]);

      const config: BatchImportConfig = {
        mode: 'single-topic',
        autoCreateTopics: false,
        selectedTopicId: 'topic-1',
        batchSize: 2,
        maxRetries: 1,
      };

      // Mock topic exists
      mockTopicService.getTopicById.mockResolvedValue({
        id: 'topic-1',
        title: 'Test Topic',
      });

      // Mock batch failures, then individual successes
      const mockChain = {
        insert: vi.fn()
          .mockRejectedValueOnce(new Error('Batch failed'))
          .mockRejectedValueOnce(new Error('Batch failed'))
          .mockResolvedValueOnce({ error: null }) // First individual question
          .mockResolvedValueOnce({ error: null }), // Second individual question
      };
      mockSupabase.from.mockReturnValue(mockChain);

      const result = await batchImportService.executeBatchImport(importResult, config);

      // Should succeed because individual questions were imported successfully
      expect(result.totalQuestionsImported).toBe(2);
      expect(result.errors.length).toBeGreaterThan(0); // Should have batch error
      expect(mockChain.insert).toHaveBeenCalledTimes(4); // 2 batch attempts + 2 individual
      
      // Check that we have at least some success
      expect(result.totalQuestionsImported).toBeGreaterThan(0);
    }, 10000); // Increase timeout

    it('should track progress correctly throughout import', async () => {
      const questions = [
        createMockQuestion('q1', 'topic-1', 'Question 1'),
        createMockQuestion('q2', 'topic-1', 'Question 2'),
      ];

      const importResult = createMockImportResult([
        { id: 'topic-1', name: 'Test Topic', questions },
      ]);

      const progressUpdates: BatchImportProgress[] = [];
      const config: BatchImportConfig = {
        mode: 'single-topic',
        autoCreateTopics: false,
        selectedTopicId: 'topic-1',
        batchSize: 1,
        progressCallback: (progress) => progressUpdates.push({ ...progress }),
      };

      // Mock topic exists
      mockTopicService.getTopicById.mockResolvedValue({
        id: 'topic-1',
        title: 'Test Topic',
      });

      // Mock successful question insertion
      const mockChain = {
        insert: vi.fn().mockResolvedValue({ error: null }),
      };
      mockSupabase.from.mockReturnValue(mockChain);

      await batchImportService.executeBatchImport(importResult, config);

      expect(progressUpdates.length).toBeGreaterThan(0);
      
      // Check that progress goes from 0 to 100
      const percentages = progressUpdates.map(p => p.percentage);
      expect(Math.min(...percentages)).toBeLessThanOrEqual(10);
      expect(Math.max(...percentages)).toBe(100);
      
      // Check that final progress indicates completion
      const finalProgress = progressUpdates[progressUpdates.length - 1];
      expect(finalProgress.phase).toBe('complete');
      expect(finalProgress.percentage).toBe(100);
    });

    it('should handle empty import result gracefully', async () => {
      const importResult: MultiTopicImportResult = {
        success: false,
        totalRows: 0,
        topicResults: new Map(),
        globalErrors: [],
        newTopicsCreated: [],
      };

      const config: BatchImportConfig = {
        mode: 'multi-topic',
        autoCreateTopics: false,
      };

      const result = await batchImportService.executeBatchImport(importResult, config);

      expect(result.success).toBe(false);
      expect(result.totalQuestionsImported).toBe(0);
      expect(result.totalTopicsProcessed).toBe(0);
    });
  });

  describe('validateImportResult', () => {
    it('should validate successful import result', async () => {
      const questions = [createMockQuestion('q1', 'topic-1', 'Question 1')];
      const importResult = createMockImportResult([
        { id: 'topic-1', name: 'Test Topic', questions },
      ]);

      const errors = batchImportService.validateImportResult(importResult);

      expect(errors).toHaveLength(0);
    });

    it('should detect missing topics', async () => {
      const importResult: MultiTopicImportResult = {
        success: false,
        totalRows: 0,
        topicResults: new Map(),
        globalErrors: [],
        newTopicsCreated: [],
      };

      const errors = batchImportService.validateImportResult(importResult);

      expect(errors.length).toBeGreaterThan(0);
      expect(errors.some(e => e.message.includes('No topics found'))).toBe(true);
    });

    it('should detect missing questions', async () => {
      const importResult = createMockImportResult([
        { id: 'topic-1', name: 'Empty Topic', questions: [] },
      ]);

      const errors = batchImportService.validateImportResult(importResult);

      expect(errors.length).toBeGreaterThan(0);
      expect(errors.some(e => e.message.includes('No valid questions found'))).toBe(true);
    });

    it('should detect invalid topic data', async () => {
      const importResult: MultiTopicImportResult = {
        success: false,
        totalRows: 1,
        topicResults: new Map([
          ['', {
            topicId: '',
            topicName: '',
            validQuestions: [createMockQuestion('q1', '', 'Question 1')],
            errors: [],
            isNewTopic: false,
          }],
        ]),
        globalErrors: [],
        newTopicsCreated: [],
      };

      const errors = batchImportService.validateImportResult(importResult);

      expect(errors.length).toBeGreaterThan(0);
      expect(errors.some(e => e.message.includes('Invalid topic ID'))).toBe(true);
      expect(errors.some(e => e.message.includes('Invalid topic name'))).toBe(true);
    });

    it('should detect invalid question data', async () => {
      const invalidQuestion: ValidatedQuestion = {
        id: '',
        topic_id: '',
        question_text: '',
        options: { A: '', B: '', C: '', D: '' },
        correct_answer: 'A',
        explanation: '',
        difficulty: 'medium',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      const importResult = createMockImportResult([
        { id: 'topic-1', name: 'Test Topic', questions: [invalidQuestion] },
      ]);

      const errors = batchImportService.validateImportResult(importResult);

      expect(errors.length).toBeGreaterThan(0);
      expect(errors.some(e => e.message.includes('missing required fields'))).toBe(true);
    });
  });

  describe('error handling and recovery', () => {
    it('should handle database connection errors gracefully', async () => {
      const questions = [createMockQuestion('q1', 'topic-1', 'Question 1')];
      const importResult = createMockImportResult([
        { id: 'topic-1', name: 'Test Topic', questions },
      ]);

      const config: BatchImportConfig = {
        mode: 'single-topic',
        autoCreateTopics: false,
        selectedTopicId: 'topic-1',
      };

      // Mock topic exists
      mockTopicService.getTopicById.mockResolvedValue({
        id: 'topic-1',
        title: 'Test Topic',
      });

      // Mock database connection error that throws immediately
      const mockChain = {
        insert: vi.fn().mockRejectedValue(new Error('Database connection failed')),
      };
      mockSupabase.from.mockReturnValue(mockChain);

      const result = await batchImportService.executeBatchImport(importResult, config);

      expect(result.success).toBe(false);
      expect(result.totalQuestionsImported).toBe(0);
      expect(result.errors.length).toBeGreaterThan(0);
    }, 15000); // Increase timeout to 15 seconds

    it('should handle topic creation failures when autoCreate is enabled', async () => {
      const questions = [createMockQuestion('q1', 'topic-1', 'Question 1')];
      const importResult = createMockImportResult([
        { id: 'topic-1', name: 'New Topic', questions, isNew: true },
      ]);

      const config: BatchImportConfig = {
        mode: 'multi-topic',
        autoCreateTopics: true,
      };

      // Mock topic doesn't exist and creation fails
      mockTopicService.findTopic.mockResolvedValue(null);
      mockTopicService.createTopic.mockRejectedValue(new Error('Topic creation failed'));
      mockTopicService.getTopicById.mockResolvedValue(null);

      const result = await batchImportService.executeBatchImport(importResult, config);

      expect(result.success).toBe(false);
      expect(result.totalQuestionsImported).toBe(0);
      expect(result.errors.some(e => e.type === 'topic-creation')).toBe(true);
    });

    it('should continue processing other topics when one topic fails', async () => {
      const topic1Questions = [createMockQuestion('q1', 'topic-1', 'Question 1')];
      const topic2Questions = [createMockQuestion('q2', 'topic-2', 'Question 2')];

      const importResult = createMockImportResult([
        { id: 'topic-1', name: 'Good Topic', questions: topic1Questions },
        { id: 'topic-2', name: 'Bad Topic', questions: topic2Questions },
      ]);

      const config: BatchImportConfig = {
        mode: 'multi-topic',
        autoCreateTopics: false,
      };

      // Mock first topic exists, second doesn't
      mockTopicService.getTopicById
        .mockResolvedValueOnce({ id: 'topic-1', title: 'Good Topic' })
        .mockResolvedValueOnce(null);

      // Mock successful insertion for first topic
      const mockChain = {
        insert: vi.fn().mockResolvedValue({ error: null }),
      };
      mockSupabase.from.mockReturnValue(mockChain);

      const result = await batchImportService.executeBatchImport(importResult, config);

      expect(result.success).toBe(true); // Partial success
      expect(result.totalQuestionsImported).toBe(1);
      expect(result.totalTopicsProcessed).toBe(2);
      
      // Should have processed the good topic
      const goodTopicResult = result.topicResults.get('topic-1');
      expect(goodTopicResult?.questionsImported).toBe(1);
      
      // Should have failed the bad topic
      const badTopicResult = result.topicResults.get('topic-2');
      expect(badTopicResult?.questionsFailed).toBe(1);
    });
  });

  describe('performance and concurrency', () => {
    it('should handle large batches efficiently', async () => {
      // Create a large number of questions
      const questions = Array.from({ length: 100 }, (_, i) =>
        createMockQuestion(`q${i}`, 'topic-1', `Question ${i}`)
      );

      const importResult = createMockImportResult([
        { id: 'topic-1', name: 'Large Topic', questions },
      ]);

      const config: BatchImportConfig = {
        mode: 'single-topic',
        autoCreateTopics: false,
        selectedTopicId: 'topic-1',
        batchSize: 10, // Process in smaller batches
      };

      // Mock topic exists
      mockTopicService.getTopicById.mockResolvedValue({
        id: 'topic-1',
        title: 'Large Topic',
      });

      // Mock successful question insertion
      const mockChain = {
        insert: vi.fn().mockResolvedValue({ error: null }),
      };
      mockSupabase.from.mockReturnValue(mockChain);

      const startTime = Date.now();
      const result = await batchImportService.executeBatchImport(importResult, config);
      const duration = Date.now() - startTime;

      expect(result.success).toBe(true);
      expect(result.totalQuestionsImported).toBe(100);
      expect(mockChain.insert).toHaveBeenCalledTimes(10); // 100 questions / 10 batch size
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should respect batch size configuration', async () => {
      const questions = Array.from({ length: 25 }, (_, i) =>
        createMockQuestion(`q${i}`, 'topic-1', `Question ${i}`)
      );

      const importResult = createMockImportResult([
        { id: 'topic-1', name: 'Test Topic', questions },
      ]);

      const config: BatchImportConfig = {
        mode: 'single-topic',
        autoCreateTopics: false,
        selectedTopicId: 'topic-1',
        batchSize: 7, // Should create 4 batches: 7, 7, 7, 4
      };

      // Mock topic exists
      mockTopicService.getTopicById.mockResolvedValue({
        id: 'topic-1',
        title: 'Test Topic',
      });

      // Mock successful question insertion
      const insertCalls: unknown[] = [];
      const mockChain = {
        insert: vi.fn().mockImplementation((data) => {
          insertCalls.push(data);
          return Promise.resolve({ error: null });
        }),
      };
      mockSupabase.from.mockReturnValue(mockChain);

      const result = await batchImportService.executeBatchImport(importResult, config);

      expect(result.success).toBe(true);
      expect(result.totalQuestionsImported).toBe(25);
      expect(mockChain.insert).toHaveBeenCalledTimes(4);
      
      // Check batch sizes
      expect(insertCalls[0]).toHaveLength(7);
      expect(insertCalls[1]).toHaveLength(7);
      expect(insertCalls[2]).toHaveLength(7);
      expect(insertCalls[3]).toHaveLength(4);
    });
  });
});