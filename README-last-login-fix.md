# User Last Login Fix

## Issue
The "user last login" feature is not working because the `last_login_at` column is missing from the `user_profiles` table in the database. The code in `src/hooks/use-admin-users.ts` is trying to access this column, but it doesn't exist in the database schema.

## Solution

### 1. Database Changes
Run the SQL script `fix-last-login-at.sql` on your production database. This script will:

- Add the `last_login_at` column to the `user_profiles` table if it doesn't exist
- Populate the column with data from `auth.users.last_sign_in_at`
- Create a trigger to keep the column in sync with `auth.users.last_sign_in_at`

```bash
# Connect to your Supabase database and run the script
psql -h your-supabase-db-host -U postgres -d postgres -f fix-last-login-at.sql
```

### 2. Code Changes
The code changes have already been made in the `src/hooks/use-admin-users.ts` file to use the `last_login_at` column from the `user_profiles` table.

### 3. Migration File
A migration file `20240801000001_add_last_login_at_column.sql` has been added to the `supabase/migrations` directory. This will ensure that the column is added to the database when running migrations in development or when deploying to new environments.

## Verification
After applying the fix, you should be able to see the last login time for users in the admin dashboard.

## Technical Details

### Database Schema Change
```sql
ALTER TABLE public.user_profiles ADD COLUMN last_login_at TIMESTAMPTZ;
```

### Data Sync
The trigger `sync_auth_user_last_login` will keep the `last_login_at` column in sync with `auth.users.last_sign_in_at` whenever a user logs in.

### Code Reference
The `use-admin-users.ts` file maps the `last_login_at` column from the `user_profiles` table to the `last_sign_in_at` field in the `User` interface:

```typescript
return {
  id: profile.user_id,
  email: profile.email || '',
  full_name: profile.full_name,
  created_at: profile.created_at,
  last_sign_in_at: profile.last_login_at || null, // Use last_login_at from user_profiles table
  is_subscribed: isSubscribed,
  is_admin: profile.is_admin || false,
  subscription_expires_at: profile.subscription_expires_at || null,
  subscription_status: subscriptionStatus,
  subscription_plan: profile.subscription_plan
};
```