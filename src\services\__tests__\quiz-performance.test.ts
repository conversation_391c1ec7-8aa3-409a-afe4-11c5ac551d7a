/**
 * Quiz Performance Tests
 * Comprehensive performance testing and benchmarking for quiz randomization
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { QuizPerformanceOptimizer } from '../quiz-performance-optimizer';
import { QuizRandomizationService } from '../quiz-randomization-service';

// Mock Supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          order: vi.fn(() => ({
            data: [],
            error: null
          }))
        }))
      }))
    }))
  }
}));

describe('Quiz Performance Optimizer', () => {
  beforeEach(() => {
    QuizPerformanceOptimizer.clearCache();
    QuizPerformanceOptimizer.clearMetrics();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Caching Performance', () => {
    it('should cache question pools effectively', async () => {
      const mockQuestions = [
        {
          id: '1',
          topic_id: 'topic1',
          question_text: 'Test question 1',
          options: { '0': 'A', '1': 'B', '2': 'C', '3': 'D' },
          correct_answer: '0',
          explanation: 'Test explanation'
        },
        {
          id: '2',
          topic_id: 'topic1',
          question_text: 'Test question 2',
          options: { '0': 'A', '1': 'B', '2': 'C', '3': 'D' },
          correct_answer: '1',
          explanation: 'Test explanation'
        }
      ];

      // Mock successful database response
      const mockSupabase = await import('@/integrations/supabase/client');
      vi.mocked(mockSupabase.supabase.from).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            order: vi.fn(() => Promise.resolve({
              data: mockQuestions,
              error: null
            }))
          }))
        }))
      } as any);

      // First call should hit database
      const start1 = performance.now();
      const result1 = await QuizPerformanceOptimizer.getCachedQuestionPool('topic1');
      const duration1 = performance.now() - start1;

      expect(result1).toEqual(mockQuestions);

      // Second call should hit cache (should be faster)
      const start2 = performance.now();
      const result2 = await QuizPerformanceOptimizer.getCachedQuestionPool('topic1');
      const duration2 = performance.now() - start2;

      expect(result2).toEqual(mockQuestions);
      expect(duration2).toBeLessThan(duration1);

      // Verify cache stats
      const cacheStats = QuizPerformanceOptimizer.getCacheStats();
      expect(cacheStats.totalEntries).toBe(1);
      expect(cacheStats.validEntries).toBe(1);
    });

    it('should handle cache expiration correctly', async () => {
      // Mock a cache entry that's expired
      const mockQuestions = [{ id: '1', topic_id: 'topic1' }];
      
      const mockSupabase = await import('@/integrations/supabase/client');
      vi.mocked(mockSupabase.supabase.from).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            order: vi.fn(() => Promise.resolve({
              data: mockQuestions,
              error: null
            }))
          }))
        }))
      } as any);

      // Manually set an expired cache entry
      (QuizPerformanceOptimizer as any).questionPoolCache['topic1'] = {
        data: mockQuestions,
        timestamp: Date.now() - 10 * 60 * 1000, // 10 minutes ago
        ttl: 5 * 60 * 1000 // 5 minute TTL
      };

      // Should fetch fresh data due to expiration
      const result = await QuizPerformanceOptimizer.getCachedQuestionPool('topic1');
      expect(result).toEqual(mockQuestions);
    });
  });

  describe('Optimized Question Selection', () => {
    it('should select questions efficiently with large pools', async () => {
      // Create a large mock question pool
      const largeQuestionPool = Array.from({ length: 1000 }, (_, i) => ({
        id: `question_${i}`,
        topic_id: 'large_topic',
        question_text: `Question ${i}`,
        options: { '0': 'A', '1': 'B', '2': 'C', '3': 'D' },
        correct_answer: '0',
        explanation: 'Test explanation'
      }));

      const mockSupabase = await import('@/integrations/supabase/client');
      vi.mocked(mockSupabase.supabase.from).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            order: vi.fn(() => Promise.resolve({
              data: largeQuestionPool,
              error: null
            }))
          }))
        }))
      } as any);

      const start = performance.now();
      const selected = await QuizPerformanceOptimizer.selectOptimizedQuestions('large_topic', 20);
      const duration = performance.now() - start;

      expect(selected).toHaveLength(20);
      expect(duration).toBeLessThan(100); // Should complete in under 100ms

      // Verify no duplicates
      const ids = selected.map(q => q.id);
      const uniqueIds = new Set(ids);
      expect(uniqueIds.size).toBe(ids.length);
    });

    it('should handle exclusion lists efficiently', async () => {
      const mockQuestions = Array.from({ length: 50 }, (_, i) => ({
        id: `question_${i}`,
        topic_id: 'topic1',
        question_text: `Question ${i}`,
        options: { '0': 'A', '1': 'B', '2': 'C', '3': 'D' },
        correct_answer: '0',
        explanation: 'Test explanation'
      }));

      const mockSupabase = await import('@/integrations/supabase/client');
      vi.mocked(mockSupabase.supabase.from).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            order: vi.fn(() => Promise.resolve({
              data: mockQuestions,
              error: null
            }))
          }))
        }))
      } as any);

      const excludeIds = ['question_0', 'question_1', 'question_2'];
      const selected = await QuizPerformanceOptimizer.selectOptimizedQuestions('topic1', 10, excludeIds);

      expect(selected).toHaveLength(10);
      
      // Verify excluded questions are not in results
      const selectedIds = selected.map(q => q.id);
      for (const excludeId of excludeIds) {
        expect(selectedIds).not.toContain(excludeId);
      }
    });
  });

  describe('Batch Processing', () => {
    it('should process multiple quiz sessions efficiently', async () => {
      const mockQuestions = Array.from({ length: 20 }, (_, i) => ({
        id: `question_${i}`,
        topic_id: 'batch_topic',
        question_text: `Question ${i}`,
        options: { '0': 'A', '1': 'B', '2': 'C', '3': 'D' },
        correct_answer: '0',
        explanation: 'Test explanation'
      }));

      const mockSupabase = await import('@/integrations/supabase/client');
      vi.mocked(mockSupabase.supabase.from).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            order: vi.fn(() => Promise.resolve({
              data: mockQuestions,
              error: null
            }))
          }))
        }))
      } as any);

      const requests = Array.from({ length: 10 }, (_, i) => ({
        topicId: 'batch_topic',
        userId: `user_${i}`,
        quizLength: 5
      }));

      const start = performance.now();
      const results = await QuizPerformanceOptimizer.batchProcessQuizSessions(requests);
      const duration = performance.now() - start;

      expect(results).toHaveLength(10);
      expect(results.every(r => r.success)).toBe(true);
      expect(duration).toBeLessThan(1000); // Should complete in under 1 second

      // Verify each result has the expected structure
      results.forEach(result => {
        expect(result.success).toBe(true);
        expect(result.data).toBeDefined();
        expect(result.data.questions).toHaveLength(5);
        expect(result.data.sessionId).toBeDefined();
      });
    });

    it('should handle concurrent requests with controlled concurrency', async () => {
      const mockQuestions = [
        { id: '1', topic_id: 'concurrent_topic', question_text: 'Q1', options: {}, correct_answer: '0', explanation: '' }
      ];

      const mockSupabase = await import('@/integrations/supabase/client');
      vi.mocked(mockSupabase.supabase.from).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            order: vi.fn(() => Promise.resolve({
              data: mockQuestions,
              error: null
            }))
          }))
        }))
      } as any);

      // Create many concurrent requests
      const requests = Array.from({ length: 20 }, (_, i) => ({
        topicId: 'concurrent_topic',
        userId: `user_${i}`,
        quizLength: 1
      }));

      const results = await QuizPerformanceOptimizer.batchProcessQuizSessions(requests);

      expect(results).toHaveLength(20);
      expect(results.every(r => r.success)).toBe(true);
    });
  });

  describe('Performance Monitoring', () => {
    it('should record and report performance metrics', async () => {
      const mockQuestions = [
        { id: '1', topic_id: 'metrics_topic', question_text: 'Q1', options: {}, correct_answer: '0', explanation: '' }
      ];

      const mockSupabase = await import('@/integrations/supabase/client');
      vi.mocked(mockSupabase.supabase.from).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            order: vi.fn(() => Promise.resolve({
              data: mockQuestions,
              error: null
            }))
          }))
        }))
      } as any);

      // Perform some operations
      await QuizPerformanceOptimizer.getCachedQuestionPool('metrics_topic');
      await QuizPerformanceOptimizer.selectOptimizedQuestions('metrics_topic', 1);

      const stats = QuizPerformanceOptimizer.getPerformanceStats();

      expect(stats.totalOperations).toBeGreaterThan(0);
      expect(stats.successRate).toBe(100);
      expect(stats.averageDuration).toBeGreaterThan(0);
      expect(stats.minDuration).toBeGreaterThanOrEqual(0);
      expect(stats.maxDuration).toBeGreaterThanOrEqual(stats.minDuration);
    });

    it('should track cache hit rates', async () => {
      const mockQuestions = [
        { id: '1', topic_id: 'hitrate_topic', question_text: 'Q1', options: {}, correct_answer: '0', explanation: '' }
      ];

      const mockSupabase = await import('@/integrations/supabase/client');
      vi.mocked(mockSupabase.supabase.from).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            order: vi.fn(() => Promise.resolve({
              data: mockQuestions,
              error: null
            }))
          }))
        }))
      } as any);

      // First call (cache miss)
      await QuizPerformanceOptimizer.getCachedQuestionPool('hitrate_topic');
      
      // Multiple cache hits
      for (let i = 0; i < 5; i++) {
        await QuizPerformanceOptimizer.getCachedQuestionPool('hitrate_topic');
      }

      const cacheStats = QuizPerformanceOptimizer.getCacheStats();
      expect(cacheStats.hitRate).toBeGreaterThan(0);
    });
  });

  describe('Memory Management', () => {
    it('should estimate cache memory usage', () => {
      const cacheStats = QuizPerformanceOptimizer.getCacheStats();
      expect(typeof cacheStats.memoryUsage).toBe('number');
      expect(cacheStats.memoryUsage).toBeGreaterThanOrEqual(0);
    });

    it('should clear cache when requested', async () => {
      const mockQuestions = [
        { id: '1', topic_id: 'clear_topic', question_text: 'Q1', options: {}, correct_answer: '0', explanation: '' }
      ];

      const mockSupabase = await import('@/integrations/supabase/client');
      vi.mocked(mockSupabase.supabase.from).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            order: vi.fn(() => Promise.resolve({
              data: mockQuestions,
              error: null
            }))
          }))
        }))
      } as any);

      // Add something to cache
      await QuizPerformanceOptimizer.getCachedQuestionPool('clear_topic');
      
      let cacheStats = QuizPerformanceOptimizer.getCacheStats();
      expect(cacheStats.totalEntries).toBe(1);

      // Clear cache
      QuizPerformanceOptimizer.clearCache();
      
      cacheStats = QuizPerformanceOptimizer.getCacheStats();
      expect(cacheStats.totalEntries).toBe(0);
    });
  });
});

describe('Performance Benchmarks', () => {
  it('should benchmark question selection performance', async () => {
    const sizes = [10, 50, 100, 500, 1000];
    const results: { size: number; duration: number }[] = [];

    for (const size of sizes) {
      const mockQuestions = Array.from({ length: size }, (_, i) => ({
        id: `question_${i}`,
        topic_id: `benchmark_topic_${size}`,
        question_text: `Question ${i}`,
        options: { '0': 'A', '1': 'B', '2': 'C', '3': 'D' },
        correct_answer: '0',
        explanation: 'Test explanation'
      }));

      const mockSupabase = await import('@/integrations/supabase/client');
      vi.mocked(mockSupabase.supabase.from).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            order: vi.fn(() => Promise.resolve({
              data: mockQuestions,
              error: null
            }))
          }))
        }))
      } as any);

      const start = performance.now();
      await QuizPerformanceOptimizer.selectOptimizedQuestions(`benchmark_topic_${size}`, Math.min(20, size));
      const duration = performance.now() - start;

      results.push({ size, duration });
    }

    // Performance should scale reasonably
    console.log('Performance benchmark results:', results);
    
    // Verify that performance doesn't degrade exponentially
    const maxDuration = Math.max(...results.map(r => r.duration));
    expect(maxDuration).toBeLessThan(200); // Should complete in under 200ms even for large datasets
  });
});