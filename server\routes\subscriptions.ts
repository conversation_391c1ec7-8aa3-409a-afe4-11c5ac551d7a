import express, { Request, Response } from 'express';
import { checkAndUpdateExpiredSubscriptions, activateFreeTier } from '../services/subscription.js';

const router = express.Router();

// Endpoint to check for expired subscriptions
router.post('/check-expired', async (_req: Request, res: Response) => {
  try {
    const result = await checkAndUpdateExpiredSubscriptions();
    
    if (result.success) {
      const expiredCount = result.data?.expiredCount || 0;
      const cleanedUsers = result.data?.cleanedUsers || [];
      return res.status(200).json({
        success: true,
        message: `Successfully checked for expired subscriptions. Updated ${expiredCount} subscriptions.`,
        updated: expiredCount,
        cleanedUsers: cleanedUsers
      });
    } else {
      return res.status(500).json({
        success: false,
        message: 'Failed to check for expired subscriptions',
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error checking expired subscriptions:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during subscription check',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Endpoint to activate free tier for authenticated users
router.post('/activate-free-tier', async (req: Request, res: Response) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email is required'
      });
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email format'
      });
    }

    const result = await activateFreeTier(email);

    if (result.success) {
      return res.status(200).json({
        success: true,
        message: 'Free tier activated successfully',
        data: result.data
      });
    } else {
      return res.status(400).json({
        success: false,
        message: result.error || 'Failed to activate free tier'
      });
    }
  } catch (error) {
    console.error('Error activating free tier:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during free tier activation',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
