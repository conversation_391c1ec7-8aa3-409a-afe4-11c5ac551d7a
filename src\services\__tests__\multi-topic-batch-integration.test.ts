import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import {
  batchImportService,
  type BatchImportConfig,
} from "../batch-import-service";
import {
  parseQuestionCSVEnhanced,
  type ImportConfig,
} from "@/utils/csv-import";

// Mock the supabase client
vi.mock("@/integrations/supabase/client", () => ({
  supabase: {
    from: vi.fn(),
  },
}));

// Mock the topic service
vi.mock("../topic-service", () => ({
  topicService: {
    findTopic: vi.fn(),
    createTopic: vi.fn(),
    getTopicById: vi.fn(),
    resolveTopicReferences: vi.fn(),
  },
}));

describe("Multi-Topic Batch Import Integration", () => {
  let mockSupabase: any;
  let mockTopicService: any;

  beforeEach(async () => {
    const { supabase } = await import("@/integrations/supabase/client");
    const { topicService } = await import("../topic-service");

    mockSupabase = supabase;
    mockTopicService = topicService;

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  // Helper to create a mock CSV file
  const createMockCSVFile = (content: string): File => {
    const blob = new Blob([content], { type: "text/csv" });
    return new File([blob], "test.csv", { type: "text/csv" });
  };

  it("should handle complete multi-topic import flow with topic creation", async () => {
    // Create CSV content with multiple topics
    const csvContent = `topic_name,question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty
Security Fundamentals,What is a firewall?,Physical barrier,Network traffic controller,Data encryptor,Malware detector,B,Controls network traffic flow,medium
Network Security,What is a VPN?,Virtual Private Network,Very Personal Network,Verified Public Network,Variable Protocol Network,A,Creates secure tunnel over internet,easy
Security Fundamentals,What is encryption?,Data hiding,Data transformation,Data deletion,Data compression,B,Process of encoding data,hard`;

    const csvFile = createMockCSVFile(csvContent);

    // Mock topic resolution - Security Fundamentals exists, Network Security is new
    mockTopicService.resolveTopicReferences.mockResolvedValue({
      resolved: new Map([
        ["Security Fundamentals", "existing-topic-id"],
        ["Network Security", "new-topic-id"],
      ]),
      missing: [],
      created: ["Network Security"],
      errors: [],
    });

    // Mock topics exist after resolution
    mockTopicService.getTopicById
      .mockResolvedValueOnce({
        id: "existing-topic-id",
        title: "Security Fundamentals",
      })
      .mockResolvedValueOnce({ id: "new-topic-id", title: "Network Security" });

    // Mock successful question insertion
    const mockChain = {
      insert: vi.fn().mockResolvedValue({ error: null }),
    };
    mockSupabase.from.mockReturnValue(mockChain);

    // Step 1: Parse CSV
    const parseConfig: ImportConfig = {
      mode: "multi-topic",
      autoCreateTopics: true,
    };

    const parseResult = await parseQuestionCSVEnhanced(csvFile, parseConfig);

    expect(parseResult.success).toBe(true);
    expect(parseResult.topicResults.size).toBe(2);
    expect(parseResult.newTopicsCreated).toContain("Network Security");

    // Step 2: Execute batch import
    const batchConfig: BatchImportConfig = {
      mode: "multi-topic",
      autoCreateTopics: true,
      batchSize: 5,
    };

    const batchResult = await batchImportService.executeBatchImport(
      parseResult,
      batchConfig
    );

    expect(batchResult.success).toBe(true);
    expect(batchResult.totalTopicsProcessed).toBe(2);
    expect(batchResult.totalQuestionsImported).toBe(3);
    expect(batchResult.topicsCreated).toContain("Network Security");

    // Verify questions were inserted for both topics
    expect(mockChain.insert).toHaveBeenCalledTimes(2); // One batch per topic

    // Check topic results
    const securityFundamentalsResult =
      batchResult.topicResults.get("existing-topic-id");
    expect(securityFundamentalsResult?.questionsImported).toBe(2);
    expect(securityFundamentalsResult?.isNewTopic).toBe(false);

    const networkSecurityResult = batchResult.topicResults.get("new-topic-id");
    expect(networkSecurityResult?.questionsImported).toBe(1);
    expect(networkSecurityResult?.isNewTopic).toBe(true);
  });

  it("should handle partial success when some topics fail", async () => {
    const csvContent = `topic_name,question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty
Good Topic,What is security?,Protection,Destruction,Confusion,Ignorance,A,Security provides protection,easy
Bad Topic,What is risk?,Danger,Safety,Comfort,Peace,A,Risk represents potential danger,medium`;

    const csvFile = createMockCSVFile(csvContent);

    // Mock topic resolution - both topics resolve but Bad Topic will fail during processing
    mockTopicService.resolveTopicReferences.mockResolvedValue({
      resolved: new Map([
        ["Good Topic", "good-topic-id"],
        ["Bad Topic", "bad-topic-id"],
      ]),
      missing: [],
      created: [],
      errors: [],
    });

    // Mock good topic exists, bad topic doesn't exist during verification
    mockTopicService.getTopicById
      .mockResolvedValueOnce({ id: "good-topic-id", title: "Good Topic" })
      .mockResolvedValueOnce(null); // Bad topic doesn't exist during verification

    // Mock successful insertion for good topic
    const mockChain = {
      insert: vi.fn().mockResolvedValue({ error: null }),
    };
    mockSupabase.from.mockReturnValue(mockChain);

    // Parse and import
    const parseConfig: ImportConfig = {
      mode: "multi-topic",
      autoCreateTopics: false,
    };

    const parseResult = await parseQuestionCSVEnhanced(csvFile, parseConfig);

    const batchConfig: BatchImportConfig = {
      mode: "multi-topic",
      autoCreateTopics: false,
    };

    const batchResult = await batchImportService.executeBatchImport(
      parseResult,
      batchConfig
    );

    // Should have partial success - only good topic questions imported
    expect(batchResult.totalQuestionsImported).toBe(1);
    expect(batchResult.totalTopicsProcessed).toBe(2); // Both topics were processed

    // Good topic should succeed
    const goodTopicResult = batchResult.topicResults.get("good-topic-id");
    expect(goodTopicResult?.questionsImported).toBe(1);
    expect(goodTopicResult?.questionsFailed).toBe(0);

    // Bad topic should fail
    const badTopicResult = batchResult.topicResults.get("bad-topic-id");
    expect(badTopicResult?.questionsImported).toBe(0);
    expect(badTopicResult?.questionsFailed).toBe(1);
  });

  it("should track progress accurately across multiple topics", async () => {
    const csvContent = `topic_name,question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty
Topic A,Question 1,A,B,C,D,A,Explanation 1,easy
Topic A,Question 2,A,B,C,D,B,Explanation 2,medium
Topic B,Question 3,A,B,C,D,C,Explanation 3,hard
Topic B,Question 4,A,B,C,D,D,Explanation 4,easy
Topic C,Question 5,A,B,C,D,A,Explanation 5,medium`;

    const csvFile = createMockCSVFile(csvContent);

    // Mock all topics exist
    mockTopicService.resolveTopicReferences.mockResolvedValue({
      resolved: new Map([
        ["Topic A", "topic-a-id"],
        ["Topic B", "topic-b-id"],
        ["Topic C", "topic-c-id"],
      ]),
      missing: [],
      created: [],
      errors: [],
    });

    mockTopicService.getTopicById
      .mockResolvedValue({ id: "topic-a-id", title: "Topic A" })
      .mockResolvedValue({ id: "topic-b-id", title: "Topic B" })
      .mockResolvedValue({ id: "topic-c-id", title: "Topic C" });

    const mockChain = {
      insert: vi.fn().mockResolvedValue({ error: null }),
    };
    mockSupabase.from.mockReturnValue(mockChain);

    // Track progress updates
    const progressUpdates: unknown[] = [];

    const parseConfig: ImportConfig = {
      mode: "multi-topic",
      autoCreateTopics: false,
    };

    const parseResult = await parseQuestionCSVEnhanced(csvFile, parseConfig);

    const batchConfig: BatchImportConfig = {
      mode: "multi-topic",
      autoCreateTopics: false,
      batchSize: 2, // Small batches to see more progress updates
      progressCallback: (progress) => progressUpdates.push({ ...progress }),
    };

    const batchResult = await batchImportService.executeBatchImport(
      parseResult,
      batchConfig
    );

    expect(batchResult.success).toBe(true);
    expect(batchResult.totalQuestionsImported).toBe(5);
    expect(batchResult.totalTopicsProcessed).toBe(3);

    // Check progress tracking
    expect(progressUpdates.length).toBeGreaterThan(0);

    // Should have progress updates for different phases
    const phases = progressUpdates.map((p) => p.phase);
    expect(phases).toContain("question-insertion");
    expect(phases).toContain("complete");

    // Progress should go from low to 100
    const percentages = progressUpdates.map((p) => p.percentage);
    expect(Math.max(...percentages)).toBe(100);

    // Final progress should indicate completion
    const finalProgress = progressUpdates[progressUpdates.length - 1];
    expect(finalProgress.phase).toBe("complete");
    expect(finalProgress.percentage).toBe(100);
  });

  it("should handle large multi-topic imports efficiently", async () => {
    // Generate CSV with many questions across multiple topics
    const topics = ["Topic A", "Topic B", "Topic C", "Topic D", "Topic E"];
    const questionsPerTopic = 20;

    let csvContent =
      "topic_name,question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty\n";

    topics.forEach((topic) => {
      for (let i = 1; i <= questionsPerTopic; i++) {
        csvContent += `${topic},Question ${i} for ${topic},A,B,C,D,A,Explanation ${i},medium\n`;
      }
    });

    const csvFile = createMockCSVFile(csvContent);

    // Mock all topics exist
    const topicMap = new Map();
    topics.forEach((topic, index) => {
      topicMap.set(topic, `topic-${index}-id`);
    });

    mockTopicService.resolveTopicReferences.mockResolvedValue({
      resolved: topicMap,
      missing: [],
      created: [],
      errors: [],
    });

    mockTopicService.getTopicById.mockResolvedValue({
      id: "mock-id",
      title: "Mock Topic",
    });

    const mockChain = {
      insert: vi.fn().mockResolvedValue({ error: null }),
    };
    mockSupabase.from.mockReturnValue(mockChain);

    const parseConfig: ImportConfig = {
      mode: "multi-topic",
      autoCreateTopics: false,
    };

    const parseResult = await parseQuestionCSVEnhanced(csvFile, parseConfig);

    const batchConfig: BatchImportConfig = {
      mode: "multi-topic",
      autoCreateTopics: false,
      batchSize: 10, // Process in batches of 10
    };

    const startTime = Date.now();
    const batchResult = await batchImportService.executeBatchImport(
      parseResult,
      batchConfig
    );
    const duration = Date.now() - startTime;

    expect(batchResult.success).toBe(true);
    expect(batchResult.totalQuestionsImported).toBe(
      topics.length * questionsPerTopic
    );
    expect(batchResult.totalTopicsProcessed).toBe(topics.length);
    expect(duration).toBeLessThan(10000); // Should complete within 10 seconds

    // Verify batching worked correctly
    const expectedBatches = Math.ceil(questionsPerTopic / 10) * topics.length;
    expect(mockChain.insert).toHaveBeenCalledTimes(expectedBatches);
  });

  it("should validate import results before processing", async () => {
    // Test validation with invalid data
    const invalidImportResult = {
      success: false,
      totalRows: 0,
      topicResults: new Map(),
      globalErrors: [],
      newTopicsCreated: [],
    };

    const validationErrors =
      batchImportService.validateImportResult(invalidImportResult);

    expect(validationErrors.length).toBeGreaterThan(0);
    expect(
      validationErrors.some((e) => e.message.includes("No topics found"))
    ).toBe(true);
    expect(
      validationErrors.some((e) =>
        e.message.includes("No valid questions found")
      )
    ).toBe(true);
  });
});
