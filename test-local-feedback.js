// Test local storage feedback functionality
function testLocalFeedback() {
  console.log('Testing local feedback storage...');
  
  // Create test feedback items
  const testFeedback = [
    {
      id: 'test-1',
      name: '<PERSON>',
      email: '<EMAIL>',
      subject: 'Test Feedback 1',
      message: 'This is a test feedback message to verify the local storage functionality works.',
      user_id: null,
      status: 'new',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'test-2',
      name: '<PERSON>',
      email: '<EMAIL>',
      subject: 'Another Test Message',
      message: 'This is another test message to ensure multiple feedback items are displayed correctly.',
      user_id: null,
      status: 'new',
      created_at: new Date(Date.now() - 60000).toISOString(), // 1 minute ago
      updated_at: new Date(Date.now() - 60000).toISOString()
    },
    {
      id: 'test-3',
      name: '<PERSON>',
      email: '<EMAIL>',
      subject: 'Feature Request',
      message: 'I would like to request a new feature for the quiz application. It would be great to have more detailed explanations.',
      user_id: null,
      status: 'new',
      created_at: new Date(Date.now() - 120000).toISOString(), // 2 minutes ago
      updated_at: new Date(Date.now() - 120000).toISOString()
    }
  ];
  
  // Store in localStorage
  localStorage.setItem('pendingFeedback', JSON.stringify(testFeedback));
  
  console.log('✅ Test feedback stored in localStorage');
  console.log('📝 Test feedback items:', testFeedback);
  
  // Verify storage
  const stored = localStorage.getItem('pendingFeedback');
  if (stored) {
    const parsed = JSON.parse(stored);
    console.log('✅ Verification: Found', parsed.length, 'feedback items in localStorage');
  } else {
    console.log('❌ Verification failed: No feedback found in localStorage');
  }
  
  console.log('🔄 Now refresh the admin dashboard feedback section to see these items');
}

// Run the test
testLocalFeedback();
