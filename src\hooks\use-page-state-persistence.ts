import { useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { 
  savePageState, 
  getSavedPageState, 
  clearPageState, 
  shouldPersistRoute 
} from '@/utils/page-state-persistence';

/**
 * Hook to handle page state persistence across browser refreshes
 * This ensures users stay on the same page after refreshing the browser
 */
export function usePageStatePersistence() {
  const location = useLocation();
  const navigate = useNavigate();
  const hasRestoredRef = useRef(false);
  const isInitialLoadRef = useRef(true);

  // Restore saved page state on initial load
  useEffect(() => {
    // Only run on the very first load
    if (!isInitialLoadRef.current || hasRestoredRef.current) return;
    
    isInitialLoadRef.current = false;
    
    // Check if we're on the home page and there's a saved state to restore
    if (location.pathname === '/' || location.pathname === '') {
      const savedState = getSavedPageState();
      
      if (savedState && shouldPersistRoute(savedState.pathname)) {
        console.log('Restoring page state:', savedState);
        hasRestoredRef.current = true;
        
        // Navigate to the saved route
        const fullPath = savedState.pathname + savedState.search;
        navigate(fullPath, { replace: true });
        
        // Clear the saved state after restoration
        clearPageState();
        return;
      }
    }
    
    // If we're not restoring, clear any old saved state
    clearPageState();
  }, [location.pathname, navigate]);

  // Save current page state when location changes (but not on initial restore)
  useEffect(() => {
    // Don't save state during the initial restoration
    if (hasRestoredRef.current && isInitialLoadRef.current) return;
    
    // Only save state for routes that should be persisted
    if (shouldPersistRoute(location.pathname)) {
      savePageState(location.pathname, location.search);
    }
  }, [location.pathname, location.search]);

  // Clear saved state when user explicitly navigates to home or auth pages
  useEffect(() => {
    if (location.pathname === '/' || location.pathname.startsWith('/auth')) {
      // Small delay to ensure this doesn't interfere with restoration
      const timer = setTimeout(() => {
        if (!hasRestoredRef.current) {
          clearPageState();
        }
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [location.pathname]);
}
