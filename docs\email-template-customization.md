# [DEPRECATED] Email Template Customization Guide

**NOTE: This guide is deprecated. The application now uses Supabase with Resend for email functionality. Email templates are managed through the Supabase dashboard.**

~~This guide explains how to customize the email templates for your SecQuiz application to match your brand identity.~~

## Managing Email Templates with Supabase and Resend

Email templates are now managed through the Supabase dashboard with Resend as the email provider. This provides a more streamlined and maintainable approach to email template management.

## How to Customize Email Templates in Supabase

1. **Access Email Templates**:
   - Log in to your [Supabase Dashboard](https://app.supabase.io)
   - Navigate to Authentication > Email Templates
   - You'll find templates for:
     - Confirmation (Email Verification)
     - Invite
     - Magic Link
     - Recovery (Password Reset)

2. **Customization Options**:
   - **Subject Line**: Customize the email subject
   - **HTML Body**: Edit the HTML content of your email
   - **Text Body**: Provide a plain text alternative
   - **Variables**: Use dynamic variables like `{{ .ConfirmationURL }}` for links

3. **Brand Elements to Consider**:
   - **Colors**: Update background, text, and button colors to match your brand
   - **Logo**: Add your company logo
   - **Typography**: Choose fonts that align with your brand identity
   - **Button Style**: Customize call-to-action buttons
   - **Footer**: Add your company information and links

4. **Best Practices for Email Templates**:
   - **Keep it Simple**: Email clients have limited CSS support
   - **Mobile Responsive**: Ensure templates work well on mobile devices
   - **Clear Call-to-Action**: Make buttons and links prominent
   - **Alt Text for Images**: Always include alt text for accessibility
   - **Test Thoroughly**: Check templates in various email clients

## Testing Your Email Templates

To test your customized email templates:

1. **Use Supabase's Test Feature**:
   - In the Email Templates section, use the "Send test email" option
   - This allows you to verify how your template appears in real email clients

2. **Test the User Flow**:
   - Try the sign-up process in your application
   - Request a password reset
   - Check how the emails appear in your inbox

3. **Check Different Email Clients**:
   - Test in Gmail, Outlook, Apple Mail, etc.
   - Verify mobile appearance on iOS and Android devices

## Additional Resources

- [Supabase Auth Email Templates Documentation](https://supabase.com/docs/guides/auth/auth-email-templates)
- [Resend Email Best Practices](https://resend.com/docs/dashboard/emails/overview)
- [HTML Email Design Guidelines](https://www.litmus.com/blog/email-design-best-practices/)

5. Find any text with the green color and change it: `color: #4CAF50;` to `color: #2196F3;`
6. Save the file and preview the changes: `npm run preview:emails`

## Advanced Customization

For more advanced customization:

1. **Custom Fonts**: You can use web-safe fonts or link to Google Fonts (though support varies by email client)
2. **Images and Graphics**: Add custom graphics or icons to enhance the visual appeal
3. **Personalization**: Use dynamic content like the user's name for personalization
4. **Responsive Design**: Add media queries for better mobile responsiveness (though support varies)

Remember to test thoroughly after making changes to ensure compatibility across different email clients.
