/**
 * Environment Configuration Checker
 * Validates that all required environment variables are properly set
 */

interface EnvCheckResult {
  isValid: boolean;
  missing: string[];
  warnings: string[];
  suggestions: string[];
}

/**
 * Required environment variables for the application to function
 */
const REQUIRED_ENV_VARS = [
  'VITE_SUPABASE_URL',
  'VITE_SUPABASE_ANON_KEY'
];

/**
 * Optional environment variables with their default values
 */
const OPTIONAL_ENV_VARS = {
  'VITE_APP_NAME': 'SecQuiz',
  'VITE_APP_DESCRIPTION': 'A cybersecurity education platform',
  'VITE_APP_URL': 'http://localhost:5173',
  'VITE_API_URL': 'http://localhost:3001',
  'VITE_ENABLE_DEBUG_MODE': 'true',
  'VITE_ENABLE_ADMIN_FEATURES': 'false',
  'VITE_ENABLE_ANALYTICS': 'false'
};

/**
 * Validates environment configuration
 */
export function checkEnvironment(): EnvCheckResult {
  const missing: string[] = [];
  const warnings: string[] = [];
  const suggestions: string[] = [];

  // Check required variables
  for (const envVar of REQUIRED_ENV_VARS) {
    const value = import.meta.env[envVar];
    if (!value || value === 'your_supabase_url_here' || value === 'your_supabase_anon_key_here') {
      missing.push(envVar);
    }
  }

  // Check optional variables and provide warnings
  for (const [envVar, defaultValue] of Object.entries(OPTIONAL_ENV_VARS)) {
    const value = import.meta.env[envVar];
    if (!value) {
      warnings.push(`${envVar} not set, using default: ${defaultValue}`);
    }
  }

  // Validate Supabase URL format
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  if (supabaseUrl && !supabaseUrl.startsWith('https://') && !supabaseUrl.includes('.supabase.co')) {
    warnings.push('VITE_SUPABASE_URL format looks incorrect. Expected: https://your-project.supabase.co');
  }

  // Check for placeholder values
  const paystackKey = import.meta.env.VITE_PAYSTACK_PUBLIC_KEY;
  if (paystackKey === 'your_paystack_public_key_here') {
    warnings.push('VITE_PAYSTACK_PUBLIC_KEY is still using placeholder value. Payment features may not work.');
  }

  // Generate suggestions
  if (missing.length > 0) {
    suggestions.push('Run "npm run setup:env" to configure environment variables interactively');
    suggestions.push('Or manually create a .env file based on .env.example');
    suggestions.push('Get Supabase credentials from: https://app.supabase.com → Settings → API');
  }

  if (warnings.length > 0 && missing.length === 0) {
    suggestions.push('Consider setting optional environment variables for better configuration');
  }

  return {
    isValid: missing.length === 0,
    missing,
    warnings,
    suggestions
  };
}

/**
 * Logs environment check results to console
 */
export function logEnvironmentStatus(): void {
  const result = checkEnvironment();

  if (result.isValid) {
    console.log('✅ Environment configuration is valid');
    
    if (result.warnings.length > 0) {
      console.warn('⚠️  Environment warnings:');
      result.warnings.forEach(warning => console.warn(`  - ${warning}`));
    }
  } else {
    console.error('❌ Environment configuration is invalid');
    console.error('Missing required variables:');
    result.missing.forEach(missing => console.error(`  - ${missing}`));
    
    if (result.suggestions.length > 0) {
      console.log('\n💡 Suggestions:');
      result.suggestions.forEach(suggestion => console.log(`  - ${suggestion}`));
    }
  }
}

/**
 * Gets a safe environment value with fallback
 */
export function getEnvValue(key: string, fallback: string = ''): string {
  return import.meta.env[key] || fallback;
}

/**
 * Checks if we're in development mode
 */
export function isDevelopment(): boolean {
  return import.meta.env.DEV;
}

/**
 * Checks if we're in production mode
 */
export function isProduction(): boolean {
  return import.meta.env.PROD;
}

/**
 * Gets the current environment mode
 */
export function getEnvironmentMode(): 'development' | 'production' | 'test' {
  if (import.meta.env.DEV) return 'development';
  if (import.meta.env.PROD) return 'production';
  return 'test';
}

// Auto-check environment in development mode
if (isDevelopment()) {
  // Delay the check to avoid blocking app startup
  setTimeout(() => {
    logEnvironmentStatus();
  }, 1000);
}
