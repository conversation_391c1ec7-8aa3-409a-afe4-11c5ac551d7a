import { supabase } from "@/integrations/supabase/client";

/**
 * Test Supabase connection and domain data access
 */
export async function testSupabaseConnection() {
  console.log('🧪 Testing Supabase connection...');
  
  try {
    // Test 1: Basic connection - simplified test
    console.log('📡 Testing basic Supabase connection...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('domains')
      .select('id')
      .limit(1);

    if (connectionError) {
      console.error('❌ Connection test failed:', connectionError);
      console.error('Error details:', connectionError.message, connectionError.code);
      return false;
    }
    
    console.log('✅ Basic connection successful');
    
    // Test 2: Count domains
    console.log('🔢 Counting domains...');
    const { count, error: countError } = await supabase
      .from('domains')
      .select('*', { count: 'exact', head: true });
    
    if (countError) {
      console.error('❌ Count query failed:', countError);
      return false;
    }
    
    console.log(`✅ Found ${count} domains in database`);
    
    // Test 3: Fetch sample domains
    console.log('📋 Fetching sample domains...');
    const { data: sampleDomains, error: fetchError } = await supabase
      .from('domains')
      .select('id, name, slug, is_active')
      .eq('is_active', true)
      .limit(3);
    
    if (fetchError) {
      console.error('❌ Fetch query failed:', fetchError);
      return false;
    }
    
    console.log('✅ Sample domains:', sampleDomains);
    
    // Test 4: Check RLS policies
    console.log('🔒 Testing RLS policies...');
    const { data: allDomains, error: rlsError } = await supabase
      .from('domains')
      .select('*')
      .eq('is_active', true);
    
    if (rlsError) {
      console.error('❌ RLS policy test failed:', rlsError);
      return false;
    }
    
    console.log(`✅ RLS test passed - fetched ${allDomains?.length || 0} domains`);
    
    return true;
  } catch (error) {
    console.error('💥 Supabase connection test failed:', error);
    return false;
  }
}

/**
 * Run comprehensive diagnostics
 */
export async function runDomainDiagnostics() {
  console.log('🔍 Running domain diagnostics...');
  
  const connectionOk = await testSupabaseConnection();
  
  if (!connectionOk) {
    console.error('❌ Supabase connection failed - check your environment variables');
    return;
  }
  
  console.log('✅ All diagnostics passed!');
}
