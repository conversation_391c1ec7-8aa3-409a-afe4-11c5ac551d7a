/**
 * Centralized Error Logging Service
 * Provides comprehensive error logging, monitoring, and alerting for quiz randomization failures
 */

export interface ErrorContext {
  userId?: string;
  sessionId?: string;
  topicId?: string;
  questionId?: string;
  operation?: string;
  metadata?: Record<string, any>;
}

export interface ErrorLog {
  id: string;
  timestamp: Date;
  level: 'error' | 'warning' | 'info';
  category: 'randomization' | 'validation' | 'session' | 'database' | 'user_action';
  message: string;
  error?: Error;
  context: ErrorContext;
  stackTrace?: string;
  userAgent?: string;
  url?: string;
}

export interface ErrorMetrics {
  totalErrors: number;
  errorsByCategory: Record<string, number>;
  errorsByLevel: Record<string, number>;
  recentErrors: ErrorLog[];
  criticalErrors: ErrorLog[];
}

export interface AlertConfig {
  enabled: boolean;
  thresholds: {
    errorRate: number; // errors per minute
    criticalErrorCount: number; // critical errors in time window
    timeWindow: number; // minutes
  };
  channels: {
    console: boolean;
    localStorage: boolean;
    // Future: webhook, email, etc.
  };
}

/**
 * Error Logging Service Class
 * Handles error logging, metrics collection, and alerting
 */
export class ErrorLoggingService {
  private static instance: ErrorLoggingService;
  private errorLogs: ErrorLog[] = [];
  private maxLogSize = 1000; // Maximum number of logs to keep in memory
  private alertConfig: AlertConfig = {
    enabled: true,
    thresholds: {
      errorRate: 10, // 10 errors per minute
      criticalErrorCount: 3, // 3 critical errors in 5 minutes
      timeWindow: 5
    },
    channels: {
      console: true,
      localStorage: true
    }
  };

  private constructor() {
    this.loadStoredLogs();
    this.setupPeriodicCleanup();
  }

  static getInstance(): ErrorLoggingService {
    if (!ErrorLoggingService.instance) {
      ErrorLoggingService.instance = new ErrorLoggingService();
    }
    return ErrorLoggingService.instance;
  }

  /**
   * Logs an error with comprehensive context
   */
  logError(
    message: string,
    error?: Error,
    context: ErrorContext = {},
    level: 'error' | 'warning' | 'info' = 'error',
    category: ErrorLog['category'] = 'randomization'
  ): void {
    const errorLog: ErrorLog = {
      id: this.generateId(),
      timestamp: new Date(),
      level,
      category,
      message,
      error,
      context,
      stackTrace: error?.stack,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    this.errorLogs.push(errorLog);
    this.trimLogs();
    this.persistLogs();
    this.outputToChannels(errorLog);
    this.checkAlerts();
  }

  /**
   * Logs randomization-specific errors
   */
  logRandomizationError(
    operation: string,
    error: Error,
    context: ErrorContext = {}
  ): void {
    this.logError(
      `Randomization failure in ${operation}: ${error.message}`,
      error,
      { ...context, operation },
      'error',
      'randomization'
    );
  }

  /**
   * Logs validation errors
   */
  logValidationError(
    message: string,
    context: ErrorContext = {},
    level: 'error' | 'warning' = 'warning'
  ): void {
    this.logError(
      message,
      undefined,
      context,
      level,
      'validation'
    );
  }

  /**
   * Logs session management errors
   */
  logSessionError(
    operation: string,
    error: Error,
    context: ErrorContext = {}
  ): void {
    this.logError(
      `Session management error in ${operation}: ${error.message}`,
      error,
      { ...context, operation },
      'error',
      'session'
    );
  }

  /**
   * Logs database operation errors
   */
  logDatabaseError(
    operation: string,
    error: Error,
    context: ErrorContext = {}
  ): void {
    this.logError(
      `Database operation failed in ${operation}: ${error.message}`,
      error,
      { ...context, operation },
      'error',
      'database'
    );
  }

  /**
   * Logs user action errors (for UX improvements)
   */
  logUserActionError(
    action: string,
    message: string,
    context: ErrorContext = {}
  ): void {
    this.logError(
      `User action error in ${action}: ${message}`,
      undefined,
      { ...context, operation: action },
      'info',
      'user_action'
    );
  }

  /**
   * Gets error metrics for monitoring
   */
  getErrorMetrics(timeWindow: number = 60): ErrorMetrics {
    const cutoffTime = new Date(Date.now() - timeWindow * 60 * 1000);
    const recentLogs = this.errorLogs.filter(log => log.timestamp >= cutoffTime);

    const errorsByCategory: Record<string, number> = {};
    const errorsByLevel: Record<string, number> = {};

    recentLogs.forEach(log => {
      errorsByCategory[log.category] = (errorsByCategory[log.category] || 0) + 1;
      errorsByLevel[log.level] = (errorsByLevel[log.level] || 0) + 1;
    });

    const criticalErrors = recentLogs.filter(log => 
      log.level === 'error' && 
      (log.category === 'randomization' || log.category === 'database')
    );

    return {
      totalErrors: recentLogs.length,
      errorsByCategory,
      errorsByLevel,
      recentErrors: recentLogs.slice(-10), // Last 10 errors
      criticalErrors
    };
  }

  /**
   * Gets formatted error summary for user display
   */
  getUserFriendlyErrorSummary(context: ErrorContext = {}): string {
    const recentErrors = this.errorLogs
      .filter(log => {
        if (context.userId && log.context.userId !== context.userId) return false;
        if (context.topicId && log.context.topicId !== context.topicId) return false;
        return log.timestamp >= new Date(Date.now() - 5 * 60 * 1000); // Last 5 minutes
      })
      .slice(-5);

    if (recentErrors.length === 0) {
      return "No recent issues detected.";
    }

    const errorCounts = recentErrors.reduce((acc, log) => {
      acc[log.category] = (acc[log.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const summaryParts: string[] = [];
    
    if (errorCounts.randomization) {
      summaryParts.push(`${errorCounts.randomization} question selection issue(s)`);
    }
    if (errorCounts.validation) {
      summaryParts.push(`${errorCounts.validation} answer validation issue(s)`);
    }
    if (errorCounts.session) {
      summaryParts.push(`${errorCounts.session} session management issue(s)`);
    }
    if (errorCounts.database) {
      summaryParts.push(`${errorCounts.database} data access issue(s)`);
    }

    return `Recent issues: ${summaryParts.join(', ')}.`;
  }

  /**
   * Clears error logs (for testing or maintenance)
   */
  clearLogs(): void {
    this.errorLogs = [];
    this.persistLogs();
  }

  /**
   * Updates alert configuration
   */
  updateAlertConfig(config: Partial<AlertConfig>): void {
    this.alertConfig = { ...this.alertConfig, ...config };
  }

  /**
   * Gets current alert configuration
   */
  getAlertConfig(): AlertConfig {
    return { ...this.alertConfig };
  }

  /**
   * Exports error logs for analysis
   */
  exportLogs(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      const headers = ['timestamp', 'level', 'category', 'message', 'userId', 'topicId', 'operation'];
      const rows = this.errorLogs.map(log => [
        log.timestamp.toISOString(),
        log.level,
        log.category,
        log.message.replace(/,/g, ';'), // Escape commas
        log.context.userId || '',
        log.context.topicId || '',
        log.context.operation || ''
      ]);
      
      return [headers, ...rows].map(row => row.join(',')).join('\n');
    }
    
    return JSON.stringify(this.errorLogs, null, 2);
  }

  // Private methods

  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private trimLogs(): void {
    if (this.errorLogs.length > this.maxLogSize) {
      this.errorLogs = this.errorLogs.slice(-this.maxLogSize);
    }
  }

  private persistLogs(): void {
    if (this.alertConfig.channels.localStorage) {
      try {
        const logsToStore = this.errorLogs.slice(-100); // Store only last 100 logs
        localStorage.setItem('quiz_error_logs', JSON.stringify(logsToStore));
      } catch (error) {
        console.warn('Failed to persist error logs to localStorage:', error);
      }
    }
  }

  private loadStoredLogs(): void {
    if (this.alertConfig.channels.localStorage) {
      try {
        const stored = localStorage.getItem('quiz_error_logs');
        if (stored) {
          const logs = JSON.parse(stored);
          this.errorLogs = logs.map((log: any) => ({
            ...log,
            timestamp: new Date(log.timestamp)
          }));
        }
      } catch (error) {
        console.warn('Failed to load stored error logs:', error);
      }
    }
  }

  private outputToChannels(errorLog: ErrorLog): void {
    if (this.alertConfig.channels.console) {
      const logMethod = errorLog.level === 'error' ? console.error : 
                       errorLog.level === 'warning' ? console.warn : console.log;
      
      logMethod(`[${errorLog.category.toUpperCase()}] ${errorLog.message}`, {
        context: errorLog.context,
        error: errorLog.error,
        timestamp: errorLog.timestamp
      });
    }
  }

  private checkAlerts(): void {
    if (!this.alertConfig.enabled) return;

    const metrics = this.getErrorMetrics(this.alertConfig.thresholds.timeWindow);
    
    // Check error rate threshold
    const errorRate = metrics.totalErrors / this.alertConfig.thresholds.timeWindow;
    if (errorRate >= this.alertConfig.thresholds.errorRate) {
      this.triggerAlert('high_error_rate', {
        currentRate: errorRate,
        threshold: this.alertConfig.thresholds.errorRate,
        timeWindow: this.alertConfig.thresholds.timeWindow
      });
    }

    // Check critical error threshold
    if (metrics.criticalErrors.length >= this.alertConfig.thresholds.criticalErrorCount) {
      this.triggerAlert('critical_errors', {
        criticalErrorCount: metrics.criticalErrors.length,
        threshold: this.alertConfig.thresholds.criticalErrorCount,
        timeWindow: this.alertConfig.thresholds.timeWindow
      });
    }
  }

  private triggerAlert(type: string, data: any): void {
    console.warn(`🚨 ALERT [${type.toUpperCase()}]:`, data);
    
    // Future: Send to external monitoring services
    // this.sendToWebhook(type, data);
    // this.sendEmail(type, data);
  }

  private setupPeriodicCleanup(): void {
    // Clean up old logs every hour
    setInterval(() => {
      const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
      this.errorLogs = this.errorLogs.filter(log => log.timestamp >= cutoffTime);
      this.persistLogs();
    }, 60 * 60 * 1000); // Every hour
  }
}

// Export singleton instance
export const errorLogger = ErrorLoggingService.getInstance();

// Convenience functions for common error types
export const logRandomizationError = (operation: string, error: Error, context?: ErrorContext) => {
  errorLogger.logRandomizationError(operation, error, context);
};

export const logValidationError = (message: string, context?: ErrorContext, level?: 'error' | 'warning') => {
  errorLogger.logValidationError(message, context, level);
};

export const logSessionError = (operation: string, error: Error, context?: ErrorContext) => {
  errorLogger.logSessionError(operation, error, context);
};

export const logDatabaseError = (operation: string, error: Error, context?: ErrorContext) => {
  errorLogger.logDatabaseError(operation, error, context);
};

export const logUserActionError = (action: string, message: string, context?: ErrorContext) => {
  errorLogger.logUserActionError(action, message, context);
};