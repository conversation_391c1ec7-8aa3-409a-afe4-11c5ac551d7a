/**
 * Quiz Length Selector Component
 * Allows users to select quiz length and validates against available questions
 */

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

export interface QuizLengthOption {
  value: number;
  label: string;
  description: string;
  estimatedTime: string;
}

export const QUIZ_LENGTH_OPTIONS: QuizLengthOption[] = [
  {
    value: 10,
    label: '10 Questions',
    description: 'Quick practice session',
    estimatedTime: '10-15 min'
  },
  {
    value: 15,
    label: '15 Questions',
    description: 'Standard practice',
    estimatedTime: '15-20 min'
  },
  {
    value: 20,
    label: '20 Questions',
    description: 'Comprehensive review',
    estimatedTime: '20-25 min'
  },
  {
    value: 25,
    label: '25 Questions',
    description: 'Extended practice',
    estimatedTime: '25-30 min'
  }
];

interface QuizLengthSelectorProps {
  availableQuestions: number;
  selectedLength: number;
  onLengthChange: (length: number) => void;
  onStartQuiz: () => void;
  loading?: boolean;
  disabled?: boolean;
}

export const QuizLengthSelector: React.FC<QuizLengthSelectorProps> = ({
  availableQuestions,
  selectedLength,
  onLengthChange,
  onStartQuiz,
  loading = false,
  disabled = false
}) => {
  const [validationMessage, setValidationMessage] = useState<string | null>(null);
  const [validationSeverity, setValidationSeverity] = useState<'info' | 'warning' | 'error'>('info');

  // Validate selected length against available questions
  useEffect(() => {
    if (availableQuestions === 0) {
      setValidationMessage('No questions available for this topic.');
      setValidationSeverity('error');
      return;
    }

    if (selectedLength > availableQuestions) {
      setValidationMessage(
        `Only ${availableQuestions} questions available. Quiz will be adjusted to ${availableQuestions} questions.`
      );
      setValidationSeverity('warning');
      return;
    }

    if (availableQuestions < 20) {
      setValidationMessage(
        `This topic has limited questions (${availableQuestions}). Consider adding more questions for better variety.`
      );
      setValidationSeverity('info');
      return;
    }

    setValidationMessage(null);
  }, [selectedLength, availableQuestions]);

  const getEffectiveQuizLength = () => {
    return Math.min(selectedLength, availableQuestions);
  };

  const isLengthAvailable = (length: number) => {
    return length <= availableQuestions;
  };

  const canStartQuiz = () => {
    return availableQuestions > 0 && !loading && !disabled;
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">Choose Quiz Length</h3>
        <p className="text-muted-foreground text-sm mb-4">
          Select how many questions you'd like in your quiz. Available questions: {availableQuestions}
        </p>
      </div>

      {/* Quiz Length Options */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        {QUIZ_LENGTH_OPTIONS.map((option) => {
          const isSelected = selectedLength === option.value;
          const isAvailable = isLengthAvailable(option.value);
          const effectiveLength = Math.min(option.value, availableQuestions);

          return (
            <Card
              key={option.value}
              className={`p-4 cursor-pointer transition-all border-2 ${
                isSelected
                  ? 'border-cyber-primary bg-cyber-primary/5'
                  : isAvailable
                  ? 'border-border hover:border-cyber-primary/50'
                  : 'border-border opacity-50 cursor-not-allowed'
              }`}
              onClick={() => {
                if (isAvailable && !disabled) {
                  onLengthChange(option.value);
                }
              }}
            >
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center gap-2">
                  <h4 className="font-medium">
                    {effectiveLength !== option.value ? `${effectiveLength} Questions` : option.label}
                  </h4>
                  {isSelected && (
                    <CheckCircle className="h-4 w-4 text-cyber-primary" />
                  )}
                </div>
                {!isAvailable && (
                  <Badge variant="secondary" className="text-xs">
                    Limited
                  </Badge>
                )}
              </div>
              
              <p className="text-sm text-muted-foreground mb-2">
                {effectiveLength !== option.value ? 'Adjusted length' : option.description}
              </p>
              
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <Clock className="h-3 w-3" />
                <span>
                  {effectiveLength !== option.value 
                    ? `~${Math.ceil(effectiveLength * 1.2)} min`
                    : option.estimatedTime
                  }
                </span>
              </div>
            </Card>
          );
        })}
      </div>

      {/* Validation Message */}
      {validationMessage && (
        <Alert className={validationSeverity === 'error' ? 'border-destructive' : ''}>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{validationMessage}</AlertDescription>
        </Alert>
      )}

      {/* Quiz Summary */}
      <Card className="p-4 bg-muted/50">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium">Quiz Summary</h4>
            <p className="text-sm text-muted-foreground">
              {getEffectiveQuizLength()} questions • ~{Math.ceil(getEffectiveQuizLength() * 1.2)} minutes
            </p>
          </div>
          <Button
            onClick={onStartQuiz}
            disabled={!canStartQuiz()}
            className="bg-cyber-primary hover:bg-cyber-primary/90"
          >
            {loading ? 'Starting...' : 'Start Quiz'}
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default QuizLengthSelector;