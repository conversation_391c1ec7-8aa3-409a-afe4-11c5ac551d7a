import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://agdyycknlxojiwhlqicq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFnZHl5Y2tubHhvaml3aGxxaWNxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyMjkzOTgsImV4cCI6MjA1OTgwNTM5OH0.lWZLRByfsyRqkK7XZfi21qSeEuOZHJKkFJGC_2ojQR8';
const supabase = createClient(supabaseUrl, supabaseKey);

async function testFeedbackSubmission() {
  console.log('Testing feedback submission...');
  
  try {
    // Test inserting feedback
    const testFeedback = {
      name: 'Test User',
      email: '<EMAIL>',
      subject: 'Test Subject',
      message: 'This is a test message to verify feedback submission works.',
      status: 'new'
    };
    
    console.log('Attempting to insert feedback:', testFeedback);
    
    const { data, error } = await supabase
      .from('feedback')
      .insert(testFeedback)
      .select();
    
    if (error) {
      console.error('Error inserting feedback:', error);
      return false;
    }
    
    console.log('Successfully inserted feedback:', data);
    
    // Test retrieving feedback
    console.log('Testing feedback retrieval...');
    const { data: allFeedback, error: retrieveError } = await supabase
      .from('feedback')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (retrieveError) {
      console.error('Error retrieving feedback:', retrieveError);
      return false;
    }
    
    console.log('Successfully retrieved feedback:', allFeedback);
    console.log(`Total feedback items: ${allFeedback.length}`);
    
    return true;
  } catch (e) {
    console.error('Exception during test:', e);
    return false;
  }
}

testFeedbackSubmission();
