import { createClient } from '@supabase/supabase-js';

// Using the connection details from the MCP configuration
const supabaseUrl = 'https://agdyycknlxojiwhlqicq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFnZHl5Y2tubHhvaml3aGxxaWNxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NzI5NzAsImV4cCI6MjA1MDU0ODk3MH0.hhpNJaNHitFpRi0l';

const supabase = createClient(supabaseUrl, supabaseKey);

async function showTopicCounts() {
  console.log('🔍 Checking current topic counts for all domains...\n');

  try {
    // First, let's check if we can connect to the database
    console.log('🔗 Testing database connection...');
    
    // Get all domains with their topic counts
    const { data: domains, error } = await supabase
      .from('domains')
      .select(`
        name,
        slug,
        difficulty_level,
        estimated_duration_weeks,
        sort_order
      `)
      .eq('is_active', true)
      .order('sort_order');

    if (error) {
      console.error('❌ Error fetching domains:', error);
      return;
    }

    console.log(`✅ Found ${domains.length} active domains\n`);

    // Get topic counts for each domain
    console.log('📊 Current Topic Counts by Domain:');
    console.log('=====================================');
    
    for (const domain of domains) {
      // Get topics for this domain
      const { data: topics, error: topicError } = await supabase
        .from('topics')
        .select('id, title, difficulty, is_premium')
        .eq('domain_id', domain.id);

      if (topicError) {
        console.error(`❌ Error fetching topics for ${domain.name}:`, topicError);
        continue;
      }

      const topicCount = topics?.length || 0;
      const status = topicCount === 0 ? '❌' : '✅';
      
      console.log(`${status} ${domain.name}: ${topicCount} topics`);
      console.log(`   Slug: ${domain.slug}`);
      console.log(`   Difficulty: ${domain.difficulty_level}`);
      console.log(`   Duration: ${domain.estimated_duration_weeks} weeks`);
      
      if (topicCount > 0) {
        console.log(`   Topics: ${topics.map(t => t.title).join(', ')}`);
      }
      console.log('');
    }

    // Show specific topics for Cybersecurity Foundations
    console.log('🔍 Cybersecurity Foundations Topics:');
    console.log('=====================================');
    
    const { data: cybersecDomain } = await supabase
      .from('domains')
      .select('id')
      .eq('slug', 'cybersecurity-foundations')
      .single();

    if (cybersecDomain) {
      const { data: cybersecTopics, error: cybersecError } = await supabase
        .from('topics')
        .select('title, difficulty, is_premium')
        .eq('domain_id', cybersecDomain.id)
        .order('title');

      if (cybersecError) {
        console.error('❌ Error fetching Cybersecurity Foundations topics:', cybersecError);
      } else {
        if (cybersecTopics && cybersecTopics.length > 0) {
          cybersecTopics.forEach(topic => {
            console.log(`   - ${topic.title} (${topic.difficulty}${topic.is_premium ? ', Premium' : ''})`);
          });
        } else {
          console.log('   ❌ No topics found in Cybersecurity Foundations domain');
        }
      }
    } else {
      console.log('   ❌ Cybersecurity Foundations domain not found');
    }

    console.log('\n🔍 Governance & Compliance Topics:');
    console.log('=====================================');
    
    const { data: govDomain } = await supabase
      .from('domains')
      .select('id')
      .eq('slug', 'governance-compliance')
      .single();

    if (govDomain) {
      const { data: govTopics, error: govError } = await supabase
        .from('topics')
        .select('title, difficulty, is_premium')
        .eq('domain_id', govDomain.id)
        .order('title');

      if (govError) {
        console.error('❌ Error fetching Governance & Compliance topics:', govError);
      } else {
        if (govTopics && govTopics.length > 0) {
          govTopics.forEach(topic => {
            console.log(`   - ${topic.title} (${topic.difficulty}${topic.is_premium ? ', Premium' : ''})`);
          });
        } else {
          console.log('   ❌ No topics found in Governance & Compliance domain');
        }
      }
    } else {
      console.log('   ❌ Governance & Compliance domain not found');
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

showTopicCounts();
