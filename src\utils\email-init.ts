/**
 * Email notification initialization
 * Call this once when your app starts
 */

/**
 * Initialize all email services
 */
export function initializeEmailServices() {
  // Supabase with Resend handles email services
  console.log('📧 Email services handled by Supabase with <PERSON>send');
}

/**
 * Check if email notifications are properly configured
 */
export function checkEmailConfiguration(): { configured: boolean; services: string[] } {
  const services: string[] = [];
  let configured = false;

  // Supabase with Resend is used for email services
  services.push('Supabase with Resend');
  configured = true;

  // Check admin email
  if (import.meta.env.VITE_ADMIN_EMAIL) {
    services.push('Admin Email Set');
  }

  return { configured, services };
}

/**
 * Display configuration status in console (development only)
 */
export function logEmailConfigurationStatus() {
  if (import.meta.env.DEV) {
    const { configured, services } = checkEmailConfiguration();
    
    if (configured) {
      console.log('📧 Email notifications configured with:', services.join(', '));
    } else {
      console.warn('⚠️ Email notifications not fully configured. Check Supabase configuration.');
      console.warn('- VITE_ADMIN_EMAIL (recommended)');
    }
  }
}