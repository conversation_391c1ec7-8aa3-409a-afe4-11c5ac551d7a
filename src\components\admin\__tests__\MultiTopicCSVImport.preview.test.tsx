import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { MultiTopicCSVImport } from '../MultiTopicCSVImport';
import { useToast } from '@/hooks/use-toast';
import * as csvImportUtils from '@/utils/csv-import';
import type { MultiTopicImportResult, TopicImportResult } from '@/utils/csv-import';

// Mock the toast hook
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(),
}));

// Mock the CSV import utilities
vi.mock('@/utils/csv-import', () => ({
  parseQuestionCSVEnhanced: vi.fn(),
  generateCSVTemplate: vi.fn(),
  generateMultiTopicCSVTemplate: vi.fn(),
}));

// Mock Supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      insert: vi.fn(),
    })),
  },
}));

const mockToast = vi.fn();
const mockTopics = [
  { 
    id: '1', 
    title: 'Security Fundamentals',
    created_at: '2023-01-01',
    created_by: 'admin',
    description: 'Basic security concepts',
    difficulty: 'medium',
    icon: 'shield',
    is_active: true,
    is_premium: false,
    updated_at: '2023-01-01',
  },
  { 
    id: '2', 
    title: 'Network Security',
    created_at: '2023-01-01',
    created_by: 'admin',
    description: 'Network security concepts',
    difficulty: 'medium',
    icon: 'network',
    is_active: true,
    is_premium: false,
    updated_at: '2023-01-01',
  },
];

describe('MultiTopicCSVImport - Preview Functionality', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useToast as any).mockReturnValue({ toast: mockToast });
    (csvImportUtils.generateCSVTemplate as any).mockReturnValue('mock,csv,content');
    (csvImportUtils.generateMultiTopicCSVTemplate as any).mockReturnValue('topic_name,mock,csv,content');
  });

  describe('Preview Display - Questions Grouped by Topic', () => {
    it('displays questions grouped by topic with correct counts', async () => {
      const mockResult: MultiTopicImportResult = {
        success: true,
        totalRows: 5,
        topicResults: new Map([
          ['topic1', {
            topicId: 'topic1',
            topicName: 'Security Fundamentals',
            validQuestions: [
              { id: '1', topic_id: 'topic1', question_text: 'Q1', options: { A: 'A1', B: 'B1', C: 'C1', D: 'D1' }, correct_answer: 'A', explanation: 'E1', difficulty: 'medium', created_at: '2023-01-01', updated_at: '2023-01-01' },
              { id: '2', topic_id: 'topic1', question_text: 'Q2', options: { A: 'A2', B: 'B2', C: 'C2', D: 'D2' }, correct_answer: 'B', explanation: 'E2', difficulty: 'easy', created_at: '2023-01-01', updated_at: '2023-01-01' },
            ],
            errors: [],
            isNewTopic: false,
          }],
          ['topic2', {
            topicId: 'topic2',
            topicName: 'Network Security',
            validQuestions: [
              { id: '3', topic_id: 'topic2', question_text: 'Q3', options: { A: 'A3', B: 'B3', C: 'C3', D: 'D3' }, correct_answer: 'C', explanation: 'E3', difficulty: 'hard', created_at: '2023-01-01', updated_at: '2023-01-01' },
            ],
            errors: [
              { row: 4, message: 'Invalid answer format' },
              { row: 5, message: 'Missing explanation' },
            ],
            isNewTopic: true,
          }],
        ]),
        globalErrors: [],
        newTopicsCreated: ['Network Security'],
      };
      
      (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
      
      render(<MultiTopicCSVImport topics={mockTopics} />);
      
      // Switch to multi-topic mode and add file
      fireEvent.click(screen.getByText('Multi Topic'));
      const fileInput = screen.getByLabelText('Upload CSV File');
      const mockFile = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
      fireEvent.change(fileInput, { target: { files: [mockFile] } });
      
      const previewButton = screen.getByText('Preview Import');
      fireEvent.click(previewButton);
      
      await waitFor(() => {
        // Check preview header
        expect(screen.getByText('Import Preview')).toBeInTheDocument();
        
        // Check topic groupings (using getAllByText since they appear in multiple places)
        expect(screen.getAllByText('Security Fundamentals').length).toBeGreaterThan(0);
        expect(screen.getAllByText('Network Security').length).toBeGreaterThan(0);
        
        // Check question counts
        expect(screen.getByText('2 questions')).toBeInTheDocument();
        expect(screen.getByText('1 questions')).toBeInTheDocument();
        
        // Check error counts
        expect(screen.getByText('2 errors')).toBeInTheDocument();
        
        // Check new topic badge
        expect(screen.getByText('New')).toBeInTheDocument();
      });
    });

    it('displays topic validation status correctly', async () => {
      const mockResult: MultiTopicImportResult = {
        success: true,
        totalRows: 3,
        topicResults: new Map([
          ['topic1', {
            topicId: 'topic1',
            topicName: 'Valid Topic',
            validQuestions: [
              { id: '1', topic_id: 'topic1', question_text: 'Q1', options: { A: 'A1', B: 'B1', C: 'C1', D: 'D1' }, correct_answer: 'A', explanation: 'E1', difficulty: 'medium', created_at: '2023-01-01', updated_at: '2023-01-01' },
            ],
            errors: [],
            isNewTopic: false,
          }],
          ['topic2', {
            topicId: 'topic2',
            topicName: 'Topic with Errors',
            validQuestions: [],
            errors: [
              { row: 2, message: 'Invalid question format' },
              { row: 3, message: 'Missing required field' },
            ],
            isNewTopic: false,
          }],
        ]),
        globalErrors: [],
        newTopicsCreated: [],
      };
      
      (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
      
      render(<MultiTopicCSVImport topics={mockTopics} />);
      
      // Add file and preview
      const fileInput = screen.getByLabelText('Upload CSV File');
      const mockFile = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
      fireEvent.change(fileInput, { target: { files: [mockFile] } });
      
      // Select topic for single-topic mode
      const topicSelect = screen.getByRole('combobox');
      fireEvent.click(topicSelect);
      fireEvent.click(screen.getByText('Security Fundamentals'));
      
      const previewButton = screen.getByText('Preview Import');
      fireEvent.click(previewButton);
      
      await waitFor(() => {
        // Check that valid topic shows question count
        expect(screen.getByText('Valid Topic')).toBeInTheDocument();
        expect(screen.getByText('1 questions')).toBeInTheDocument();
        
        // Check that topic with errors shows error count and details
        expect(screen.getByText('Topic with Errors')).toBeInTheDocument();
        expect(screen.getByText('2 errors')).toBeInTheDocument();
        expect(screen.getByText('Row 2: Invalid question format')).toBeInTheDocument();
        expect(screen.getByText('Row 3: Missing required field')).toBeInTheDocument();
      });
    });
  });

  describe('New Topics Display', () => {
    it('shows new topics that will be created', async () => {
      const mockResult: MultiTopicImportResult = {
        success: true,
        totalRows: 2,
        topicResults: new Map([
          ['new-topic-1', {
            topicId: 'new-topic-1',
            topicName: 'Cybersecurity Basics',
            validQuestions: [
              { id: '1', topic_id: 'new-topic-1', question_text: 'Q1', options: { A: 'A1', B: 'B1', C: 'C1', D: 'D1' }, correct_answer: 'A', explanation: 'E1', difficulty: 'medium', created_at: '2023-01-01', updated_at: '2023-01-01' },
            ],
            errors: [],
            isNewTopic: true,
          }],
          ['new-topic-2', {
            topicId: 'new-topic-2',
            topicName: 'Advanced Threats',
            validQuestions: [
              { id: '2', topic_id: 'new-topic-2', question_text: 'Q2', options: { A: 'A2', B: 'B2', C: 'C2', D: 'D2' }, correct_answer: 'B', explanation: 'E2', difficulty: 'hard', created_at: '2023-01-01', updated_at: '2023-01-01' },
            ],
            errors: [],
            isNewTopic: true,
          }],
        ]),
        globalErrors: [],
        newTopicsCreated: ['Cybersecurity Basics', 'Advanced Threats'],
      };
      
      (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
      
      render(<MultiTopicCSVImport topics={mockTopics} />);
      
      // Switch to multi-topic mode and enable auto-create
      fireEvent.click(screen.getByText('Multi Topic'));
      const autoCreateSwitch = screen.getByRole('switch');
      fireEvent.click(autoCreateSwitch);
      
      const fileInput = screen.getByLabelText('Upload CSV File');
      const mockFile = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
      fireEvent.change(fileInput, { target: { files: [mockFile] } });
      
      const previewButton = screen.getByText('Preview Import');
      fireEvent.click(previewButton);
      
      await waitFor(() => {
        // Check new topics alert
        expect(screen.getByText('New Topics Will Be Created')).toBeInTheDocument();
        
        // Check individual new topic badges (using getAllByText since they appear multiple times)
        expect(screen.getAllByText('Cybersecurity Basics').length).toBeGreaterThan(0);
        expect(screen.getAllByText('Advanced Threats').length).toBeGreaterThan(0);
        
        // Check "New" badges for new topics
        const newBadges = screen.getAllByText('New');
        expect(newBadges).toHaveLength(2);
      });
    });

    it('does not show new topics alert when no new topics are created', async () => {
      const mockResult: MultiTopicImportResult = {
        success: true,
        totalRows: 1,
        topicResults: new Map([
          ['existing-topic', {
            topicId: 'existing-topic',
            topicName: 'Existing Topic',
            validQuestions: [
              { id: '1', topic_id: 'existing-topic', question_text: 'Q1', options: { A: 'A1', B: 'B1', C: 'C1', D: 'D1' }, correct_answer: 'A', explanation: 'E1', difficulty: 'medium', created_at: '2023-01-01', updated_at: '2023-01-01' },
            ],
            errors: [],
            isNewTopic: false,
          }],
        ]),
        globalErrors: [],
        newTopicsCreated: [],
      };
      
      (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
      
      render(<MultiTopicCSVImport topics={mockTopics} />);
      
      const fileInput = screen.getByLabelText('Upload CSV File');
      const mockFile = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
      fireEvent.change(fileInput, { target: { files: [mockFile] } });
      
      // Select topic for single-topic mode
      const topicSelect = screen.getByRole('combobox');
      fireEvent.click(topicSelect);
      fireEvent.click(screen.getByText('Security Fundamentals'));
      
      const previewButton = screen.getByText('Preview Import');
      fireEvent.click(previewButton);
      
      await waitFor(() => {
        expect(screen.getByText('Import Preview')).toBeInTheDocument();
        expect(screen.queryByText('New Topics Will Be Created')).not.toBeInTheDocument();
        expect(screen.queryByText('New')).not.toBeInTheDocument();
      });
    });
  });

  describe('Preview Confirmation and Cancellation Flow', () => {
    it('shows confirmation and cancellation buttons in preview mode', async () => {
      const mockResult: MultiTopicImportResult = {
        success: true,
        totalRows: 1,
        topicResults: new Map([
          ['topic1', {
            topicId: 'topic1',
            topicName: 'Test Topic',
            validQuestions: [
              { id: '1', topic_id: 'topic1', question_text: 'Q1', options: { A: 'A1', B: 'B1', C: 'C1', D: 'D1' }, correct_answer: 'A', explanation: 'E1', difficulty: 'medium', created_at: '2023-01-01', updated_at: '2023-01-01' },
            ],
            errors: [],
            isNewTopic: false,
          }],
        ]),
        globalErrors: [],
        newTopicsCreated: [],
      };
      
      (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
      
      render(<MultiTopicCSVImport topics={mockTopics} />);
      
      const fileInput = screen.getByLabelText('Upload CSV File');
      const mockFile = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
      fireEvent.change(fileInput, { target: { files: [mockFile] } });
      
      // Select topic for single-topic mode
      const topicSelect = screen.getByRole('combobox');
      fireEvent.click(topicSelect);
      fireEvent.click(screen.getByText('Security Fundamentals'));
      
      // Initially shows preview button
      expect(screen.getByText('Preview Import')).toBeInTheDocument();
      expect(screen.queryByText('Confirm Import')).not.toBeInTheDocument();
      expect(screen.queryByText('Back to Edit')).not.toBeInTheDocument();
      
      const previewButton = screen.getByText('Preview Import');
      fireEvent.click(previewButton);
      
      await waitFor(() => {
        // After preview, shows confirmation and cancellation buttons
        expect(screen.queryByText('Preview Import')).not.toBeInTheDocument();
        expect(screen.getByText('Confirm Import')).toBeInTheDocument();
        expect(screen.getByText('Back to Edit')).toBeInTheDocument();
      });
    });

    it('returns to edit mode when cancellation is clicked', async () => {
      const mockResult: MultiTopicImportResult = {
        success: true,
        totalRows: 1,
        topicResults: new Map([
          ['topic1', {
            topicId: 'topic1',
            topicName: 'Test Topic',
            validQuestions: [
              { id: '1', topic_id: 'topic1', question_text: 'Q1', options: { A: 'A1', B: 'B1', C: 'C1', D: 'D1' }, correct_answer: 'A', explanation: 'E1', difficulty: 'medium', created_at: '2023-01-01', updated_at: '2023-01-01' },
            ],
            errors: [],
            isNewTopic: false,
          }],
        ]),
        globalErrors: [],
        newTopicsCreated: [],
      };
      
      (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
      
      render(<MultiTopicCSVImport topics={mockTopics} />);
      
      const fileInput = screen.getByLabelText('Upload CSV File');
      const mockFile = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
      fireEvent.change(fileInput, { target: { files: [mockFile] } });
      
      // Select topic for single-topic mode
      const topicSelect = screen.getByRole('combobox');
      fireEvent.click(topicSelect);
      fireEvent.click(screen.getByText('Security Fundamentals'));
      
      const previewButton = screen.getByText('Preview Import');
      fireEvent.click(previewButton);
      
      await waitFor(() => {
        expect(screen.getByText('Import Preview')).toBeInTheDocument();
      });
      
      // Click back to edit
      const backButton = screen.getByText('Back to Edit');
      fireEvent.click(backButton);
      
      // Should return to edit mode
      expect(screen.queryByText('Import Preview')).not.toBeInTheDocument();
      expect(screen.getByText('Preview Import')).toBeInTheDocument();
      expect(screen.queryByText('Confirm Import')).not.toBeInTheDocument();
      expect(screen.queryByText('Back to Edit')).not.toBeInTheDocument();
    });

    it('shows confirm import button when preview is ready', async () => {
      const mockResult: MultiTopicImportResult = {
        success: true,
        totalRows: 1,
        topicResults: new Map([
          ['topic1', {
            topicId: 'topic1',
            topicName: 'Test Topic',
            validQuestions: [
              { id: '1', topic_id: 'topic1', question_text: 'Q1', options: { A: 'A1', B: 'B1', C: 'C1', D: 'D1' }, correct_answer: 'A', explanation: 'E1', difficulty: 'medium', created_at: '2023-01-01', updated_at: '2023-01-01' },
            ],
            errors: [],
            isNewTopic: false,
          }],
        ]),
        globalErrors: [],
        newTopicsCreated: [],
      };
      
      (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
      
      render(<MultiTopicCSVImport topics={mockTopics} />);
      
      const fileInput = screen.getByLabelText('Upload CSV File');
      const mockFile = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
      fireEvent.change(fileInput, { target: { files: [mockFile] } });
      
      // Select topic for single-topic mode
      const topicSelect = screen.getByRole('combobox');
      fireEvent.click(topicSelect);
      fireEvent.click(screen.getByText('Security Fundamentals'));
      
      const previewButton = screen.getByText('Preview Import');
      fireEvent.click(previewButton);
      
      await waitFor(() => {
        expect(screen.getByText('Confirm Import')).toBeInTheDocument();
        expect(screen.getByText('Confirm Import')).not.toBeDisabled();
      });
    });
  });

  describe('Preview Summary Information', () => {
    it('displays accurate summary with total rows, questions, and topics', async () => {
      const mockResult: MultiTopicImportResult = {
        success: true,
        totalRows: 10,
        topicResults: new Map([
          ['topic1', {
            topicId: 'topic1',
            topicName: 'Security Fundamentals',
            validQuestions: [
              { id: '1', topic_id: 'topic1', question_text: 'Q1', options: { A: 'A1', B: 'B1', C: 'C1', D: 'D1' }, correct_answer: 'A', explanation: 'E1', difficulty: 'medium', created_at: '2023-01-01', updated_at: '2023-01-01' },
              { id: '2', topic_id: 'topic1', question_text: 'Q2', options: { A: 'A2', B: 'B2', C: 'C2', D: 'D2' }, correct_answer: 'B', explanation: 'E2', difficulty: 'easy', created_at: '2023-01-01', updated_at: '2023-01-01' },
            ],
            errors: [
              { row: 3, message: 'Invalid format' },
            ],
            isNewTopic: false,
          }],
          ['topic2', {
            topicId: 'topic2',
            topicName: 'Network Security',
            validQuestions: [
              { id: '3', topic_id: 'topic2', question_text: 'Q3', options: { A: 'A3', B: 'B3', C: 'C3', D: 'D3' }, correct_answer: 'C', explanation: 'E3', difficulty: 'hard', created_at: '2023-01-01', updated_at: '2023-01-01' },
            ],
            errors: [],
            isNewTopic: true,
          }],
        ]),
        globalErrors: [
          { row: 8, message: 'Global parsing error' },
        ],
        newTopicsCreated: ['Network Security'],
      };
      
      (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
      
      render(<MultiTopicCSVImport topics={mockTopics} />);
      
      // Switch to multi-topic mode
      fireEvent.click(screen.getByText('Multi Topic'));
      
      const fileInput = screen.getByLabelText('Upload CSV File');
      const mockFile = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
      fireEvent.change(fileInput, { target: { files: [mockFile] } });
      
      const previewButton = screen.getByText('Preview Import');
      fireEvent.click(previewButton);
      
      await waitFor(() => {
        // Check summary information
        expect(screen.getByText(/Summary:/)).toBeInTheDocument();
        expect(screen.getByText(/10 total rows/)).toBeInTheDocument();
        expect(screen.getByText(/3 valid questions/)).toBeInTheDocument();
        expect(screen.getByText(/2 topic\(s\)/)).toBeInTheDocument();
        expect(screen.getByText(/1 new topic\(s\)/)).toBeInTheDocument();
      });
    });

    it('displays summary without new topics when none are created', async () => {
      const mockResult: MultiTopicImportResult = {
        success: true,
        totalRows: 5,
        topicResults: new Map([
          ['topic1', {
            topicId: 'topic1',
            topicName: 'Existing Topic',
            validQuestions: [
              { id: '1', topic_id: 'topic1', question_text: 'Q1', options: { A: 'A1', B: 'B1', C: 'C1', D: 'D1' }, correct_answer: 'A', explanation: 'E1', difficulty: 'medium', created_at: '2023-01-01', updated_at: '2023-01-01' },
            ],
            errors: [],
            isNewTopic: false,
          }],
        ]),
        globalErrors: [],
        newTopicsCreated: [],
      };
      
      (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
      
      render(<MultiTopicCSVImport topics={mockTopics} />);
      
      const fileInput = screen.getByLabelText('Upload CSV File');
      const mockFile = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
      fireEvent.change(fileInput, { target: { files: [mockFile] } });
      
      // Select topic for single-topic mode
      const topicSelect = screen.getByRole('combobox');
      fireEvent.click(topicSelect);
      fireEvent.click(screen.getByText('Security Fundamentals'));
      
      const previewButton = screen.getByText('Preview Import');
      fireEvent.click(previewButton);
      
      await waitFor(() => {
        // Check summary without new topics
        expect(screen.getByText(/Summary:/)).toBeInTheDocument();
        expect(screen.getByText(/5 total rows/)).toBeInTheDocument();
        expect(screen.getByText(/1 valid questions/)).toBeInTheDocument();
        expect(screen.getByText(/1 topic\(s\)/)).toBeInTheDocument();
        expect(screen.queryByText(/new topic\(s\)/)).not.toBeInTheDocument();
      });
    });
  });

  describe('Global Error Display', () => {
    it('displays global errors with proper formatting', async () => {
      const mockResult: MultiTopicImportResult = {
        success: false,
        totalRows: 3,
        topicResults: new Map(),
        globalErrors: [
          { row: 1, message: 'Invalid CSV header format' },
          { row: 2, message: 'Missing required column: topic_name' },
          { row: 0, message: 'File encoding issue detected' },
        ],
        newTopicsCreated: [],
      };
      
      (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
      
      render(<MultiTopicCSVImport topics={mockTopics} />);
      
      const fileInput = screen.getByLabelText('Upload CSV File');
      const mockFile = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
      fireEvent.change(fileInput, { target: { files: [mockFile] } });
      
      // Select topic for single-topic mode
      const topicSelect = screen.getByRole('combobox');
      fireEvent.click(topicSelect);
      fireEvent.click(screen.getByText('Security Fundamentals'));
      
      const previewButton = screen.getByText('Preview Import');
      fireEvent.click(previewButton);
      
      await waitFor(() => {
        // Check global errors alert
        expect(screen.getByText('Global Issues Found')).toBeInTheDocument();
        
        // Check individual error messages
        expect(screen.getByText('Row 1: Invalid CSV header format')).toBeInTheDocument();
        expect(screen.getByText('Row 2: Missing required column: topic_name')).toBeInTheDocument();
        expect(screen.getByText('Row 0: File encoding issue detected')).toBeInTheDocument();
      });
    });
  });

  describe('Preview Data Accuracy', () => {
    it('accurately reflects parsed CSV data structure', async () => {
      const validQuestion1 = {
        id: '1',
        topic_id: 'topic1',
        question_text: 'What is cybersecurity?',
        options: { A: 'Protection of systems', B: 'Network monitoring', C: 'Data backup', D: 'User training' },
        correct_answer: 'A',
        explanation: 'Cybersecurity involves protecting digital systems from threats.',
        difficulty: 'easy',
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
      };

      const validQuestion2 = {
        id: '2',
        topic_id: 'topic2',
        question_text: 'What is a VPN?',
        options: { A: 'Virtual Private Network', B: 'Very Personal Network', C: 'Verified Public Network', D: 'Variable Protocol Network' },
        correct_answer: 'A',
        explanation: 'VPN creates a secure tunnel over the internet.',
        difficulty: 'medium',
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
      };

      const mockResult: MultiTopicImportResult = {
        success: true,
        totalRows: 4,
        topicResults: new Map([
          ['topic1', {
            topicId: 'topic1',
            topicName: 'Cybersecurity Fundamentals',
            validQuestions: [validQuestion1],
            errors: [
              { row: 2, message: 'Invalid difficulty level: "super-hard"' },
            ],
            isNewTopic: true,
          }],
          ['topic2', {
            topicId: 'topic2',
            topicName: 'Network Security',
            validQuestions: [validQuestion2],
            errors: [],
            isNewTopic: false,
          }],
        ]),
        globalErrors: [
          { row: 4, message: 'Unrecognized topic reference: "Unknown Topic"' },
        ],
        newTopicsCreated: ['Cybersecurity Fundamentals'],
      };
      
      (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
      
      render(<MultiTopicCSVImport topics={mockTopics} />);
      
      // Switch to multi-topic mode and enable auto-create
      fireEvent.click(screen.getByText('Multi Topic'));
      const autoCreateSwitch = screen.getByRole('switch');
      fireEvent.click(autoCreateSwitch);
      
      const fileInput = screen.getByLabelText('Upload CSV File');
      const mockFile = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
      fireEvent.change(fileInput, { target: { files: [mockFile] } });
      
      const previewButton = screen.getByText('Preview Import');
      fireEvent.click(previewButton);
      
      await waitFor(() => {
        // Verify all data is accurately displayed
        
        // Check topics and their properties (using getAllByText since they appear in multiple places)
        expect(screen.getAllByText('Cybersecurity Fundamentals').length).toBeGreaterThan(0);
        expect(screen.getAllByText('Network Security').length).toBeGreaterThan(0);
        
        // Check question counts match the data
        const questionBadges = screen.getAllByText(/\d+ questions/);
        expect(questionBadges).toHaveLength(2);
        
        // Check error counts and messages
        expect(screen.getByText('1 errors')).toBeInTheDocument();
        expect(screen.getByText('Row 2: Invalid difficulty level: "super-hard"')).toBeInTheDocument();
        
        // Check new topic indication
        expect(screen.getByText('New')).toBeInTheDocument();
        expect(screen.getByText('New Topics Will Be Created')).toBeInTheDocument();
        expect(screen.getAllByText('Cybersecurity Fundamentals').length).toBeGreaterThan(0);
        
        // Check global errors
        expect(screen.getByText('Global Issues Found')).toBeInTheDocument();
        expect(screen.getByText('Row 4: Unrecognized topic reference: "Unknown Topic"')).toBeInTheDocument();
        
        // Check summary accuracy
        expect(screen.getByText(/4 total rows/)).toBeInTheDocument();
        expect(screen.getByText(/2 valid questions/)).toBeInTheDocument();
        expect(screen.getByText(/2 topic\(s\)/)).toBeInTheDocument();
        expect(screen.getByText(/1 new topic\(s\)/)).toBeInTheDocument();
      });
    });

    it('handles empty preview data correctly', async () => {
      const mockResult: MultiTopicImportResult = {
        success: false,
        totalRows: 0,
        topicResults: new Map(),
        globalErrors: [
          { row: 0, message: 'CSV file is empty or contains no valid data rows' },
        ],
        newTopicsCreated: [],
      };
      
      (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
      
      render(<MultiTopicCSVImport topics={mockTopics} />);
      
      const fileInput = screen.getByLabelText('Upload CSV File');
      const mockFile = new File([''], 'empty.csv', { type: 'text/csv' });
      fireEvent.change(fileInput, { target: { files: [mockFile] } });
      
      // Select topic for single-topic mode
      const topicSelect = screen.getByRole('combobox');
      fireEvent.click(topicSelect);
      fireEvent.click(screen.getByText('Security Fundamentals'));
      
      const previewButton = screen.getByText('Preview Import');
      fireEvent.click(previewButton);
      
      await waitFor(() => {
        // Check that empty state is handled properly
        expect(screen.getByText('Import Preview')).toBeInTheDocument();
        expect(screen.getByText('Global Issues Found')).toBeInTheDocument();
        expect(screen.getByText('Row 0: CSV file is empty or contains no valid data rows')).toBeInTheDocument();
        
        // Check summary shows zero values
        expect(screen.getByText(/0 total rows/)).toBeInTheDocument();
        expect(screen.getByText(/0 valid questions/)).toBeInTheDocument();
        expect(screen.getByText(/0 topic\(s\)/)).toBeInTheDocument();
        
        // Confirm button should be disabled for empty data
        const confirmButton = screen.getByRole('button', { name: /confirm import/i });
        expect(confirmButton).toBeDisabled();
      });
    });
  });
});