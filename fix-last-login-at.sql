-- Fix for user last login feature

-- First check if the column already exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'user_profiles' 
    AND column_name = 'last_login_at'
  ) THEN
    -- Add the column if it doesn't exist
    ALTER TABLE public.user_profiles ADD COLUMN last_login_at TIMESTAMPTZ;
    
    -- Update the column with data from auth.users
    UPDATE public.user_profiles
    SET last_login_at = au.last_sign_in_at
    FROM auth.users au
    WHERE public.user_profiles.user_id = au.id;
    
    -- Create a function to sync last_login_at from auth.users
    CREATE OR REPLACE FUNCTION public.sync_user_last_login()
    RETURNS TRIGGER AS $$
    BEGIN
      -- Update last_login_at in user_profiles when auth.users last_sign_in_at changes
      UPDATE public.user_profiles 
      SET last_login_at = NEW.last_sign_in_at, updated_at = now()
      WHERE user_id = NEW.id;
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;
    
    -- <PERSON><PERSON> trigger to sync last_login_at changes
    DROP TRIGGER IF EXISTS sync_auth_user_last_login ON auth.users;
    CREATE TRIGGER sync_auth_user_last_login
      AFTER UPDATE OF last_sign_in_at ON auth.users
      FOR EACH ROW EXECUTE FUNCTION public.sync_user_last_login();
    
    RAISE NOTICE 'Added last_login_at column to user_profiles table and created sync trigger';
  ELSE
    RAISE NOTICE 'last_login_at column already exists in user_profiles table';
  END IF;
END $$;

-- Verify the column exists and has data
SELECT 
  COUNT(*) as total_profiles,
  COUNT(last_login_at) as profiles_with_last_login
FROM public.user_profiles;

-- Sample of user profiles with last_login_at
SELECT 
  up.id,
  up.email,
  up.last_login_at,
  au.last_sign_in_at
FROM public.user_profiles up
JOIN auth.users au ON up.user_id = au.id
LIMIT 10;