import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ImportQuestionsPage from '@/pages/ImportQuestionsPage';
import { useAdminTopics } from '@/hooks/use-admin';
import { useToast } from '@/hooks/use-toast';
import * as csvImportUtils from '@/utils/csv-import';
import { batchImportService } from '@/services/batch-import-service';
import { importErrorReportingService } from '@/services/import-error-reporting-service';

// Mock dependencies
vi.mock('@/hooks/use-admin');
vi.mock('@/hooks/use-toast');
vi.mock('@/utils/csv-import');
vi.mock('@/services/batch-import-service');
vi.mock('@/services/import-error-reporting-service');
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => vi.fn(),
  };
});

const mockTopics = [
  { id: '1', title: 'JavaScript Basics', slug: 'javascript-basics' },
  { id: '2', title: 'React Fundamentals', slug: 'react-fundamentals' },
];

const mockToast = vi.fn();

const createMockImportResultWithErrors = () => ({
  success: false,
  totalRows: 5,
  globalErrors: [
    { row: 2, message: 'Missing required field: question' },
    { row: 4, message: 'Invalid correct_answer: must be A, B, C, or D' },
  ],
  newTopicsCreated: ['New Topic'],
  topicResults: new Map([
    [
      'topic-1',
      {
        topicId: 'topic-1',
        topicName: 'JavaScript Basics',
        isNewTopic: false,
        validQuestions: [
          {
            question: 'Valid question 1',
            option_a: 'Option A',
            option_b: 'Option B',
            option_c: 'Option C',
            option_d: 'Option D',
            correct_answer: 'A',
            explanation: 'Test explanation',
            difficulty: 'medium' as const,
          },
        ],
        errors: [
          { row: 3, message: 'Question text too short (minimum 10 characters)' },
          { row: 5, message: 'Missing explanation' },
        ],
      },
    ],
  ]),
});

const renderImportPage = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return render(
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <ImportQuestionsPage />
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('ImportQuestionsPage - Error Handling', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useAdminTopics as any).mockReturnValue({
      data: mockTopics,
      isLoading: false,
    });
    (useToast as any).mockReturnValue({ toast: mockToast });
    (csvImportUtils.generateCSVTemplate as any).mockReturnValue('mock,csv,content');
    (csvImportUtils.generateMultiTopicCSVTemplate as any).mockReturnValue('topic_name,mock,csv,content');
    (importErrorReportingService.generateErrorReport as any).mockReturnValue({
      summary: {
        totalErrors: 4,
        globalErrors: 2,
        topicErrors: 2,
        affectedTopics: 1,
      },
      details: [],
    });
  });

  it('displays global errors in preview', async () => {
    const mockResult = createMockImportResultWithErrors();
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
    
    renderImportPage();
    
    // Select topic and file
    const topicSelect = screen.getByRole('combobox');
    fireEvent.click(topicSelect);
    fireEvent.click(screen.getByText('JavaScript Basics'));
    
    const fileInput = screen.getByLabelText('CSV File');
    const file = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      expect(screen.getByText('Issues Found (2)')).toBeInTheDocument();
      expect(screen.getByText('Row 2: Missing required field: question')).toBeInTheDocument();
      expect(screen.getByText('Row 4: Invalid correct_answer: must be A, B, C, or D')).toBeInTheDocument();
    });
    
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Preview completed with warnings',
        description: 'Found 2 issues. Please review before importing.',
        variant: 'destructive',
      });
    });
  });

  it('displays topic-level errors in preview', async () => {
    const mockResult = createMockImportResultWithErrors();
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
    
    renderImportPage();
    
    // Select topic and file
    const topicSelect = screen.getByRole('combobox');
    fireEvent.click(topicSelect);
    fireEvent.click(screen.getByText('JavaScript Basics'));
    
    const fileInput = screen.getByLabelText('CSV File');
    const file = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      expect(screen.getByText('❌ Errors in this topic (2):')).toBeInTheDocument();
      expect(screen.getByText('Row 3: Question text too short (minimum 10 characters)')).toBeInTheDocument();
      expect(screen.getByText('Row 5: Missing explanation')).toBeInTheDocument();
    });
  });

  it('shows new topics that will be created', async () => {
    const mockResult = createMockImportResultWithErrors();
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
    
    renderImportPage();
    
    // Switch to multi-topic mode
    const multiTopicButton = screen.getByText('Multi Topic');
    fireEvent.click(multiTopicButton);
    
    const fileInput = screen.getByLabelText('CSV File');
    const file = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      expect(screen.getByText('New Topics Will Be Created (1)')).toBeInTheDocument();
      expect(screen.getByText('New Topic')).toBeInTheDocument();
    });
  });

  it('disables import button when there are global errors', async () => {
    const mockResult = createMockImportResultWithErrors();
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
    
    renderImportPage();
    
    // Select topic and file
    const topicSelect = screen.getByRole('combobox');
    fireEvent.click(topicSelect);
    fireEvent.click(screen.getByText('JavaScript Basics'));
    
    const fileInput = screen.getByLabelText('CSV File');
    const file = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      const confirmButton = screen.getByText('Confirm Import');
      expect(confirmButton).toBeDisabled();
    });
  });

  it('handles validation errors before batch import', async () => {
    const mockResult = {
      success: true,
      totalRows: 2,
      globalErrors: [],
      newTopicsCreated: [],
      topicResults: new Map([
        [
          'topic-1',
          {
            topicId: 'topic-1',
            topicName: 'JavaScript Basics',
            isNewTopic: false,
            validQuestions: [
              {
                question: 'Valid question',
                option_a: 'Option A',
                option_b: 'Option B',
                option_c: 'Option C',
                option_d: 'Option D',
                correct_answer: 'A',
                explanation: 'Test explanation',
                difficulty: 'medium' as const,
              },
            ],
            errors: [],
          },
        ],
      ]),
    };
    
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
    (batchImportService.validateImportResult as any).mockReturnValue([
      'Invalid topic configuration',
      'Missing required permissions',
    ]);
    
    renderImportPage();
    
    // Go through preview
    const topicSelect = screen.getByRole('combobox');
    fireEvent.click(topicSelect);
    fireEvent.click(screen.getByText('JavaScript Basics'));
    
    const fileInput = screen.getByLabelText('CSV File');
    const file = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      const confirmButton = screen.getByText('Confirm Import');
      fireEvent.click(confirmButton);
    });
    
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Validation failed',
        description: 'Found 2 validation errors. Please check your data.',
        variant: 'destructive',
      });
    });
  });

  it('displays batch import errors in results', async () => {
    const mockPreviewResult = {
      success: true,
      totalRows: 3,
      globalErrors: [],
      newTopicsCreated: [],
      topicResults: new Map(),
    };
    
    const mockBatchResult = {
      success: false,
      totalQuestionsImported: 1,
      totalTopicsProcessed: 1,
      topicsCreated: [],
      errors: [
        { type: 'DATABASE_ERROR', message: 'Connection timeout', topicName: 'JavaScript Basics' },
        { type: 'VALIDATION_ERROR', message: 'Duplicate question detected' },
      ],
      duration: 2000,
      topicResults: new Map([
        [
          'topic-1',
          {
            topicId: 'topic-1',
            topicName: 'JavaScript Basics',
            isNewTopic: false,
            questionsImported: 1,
            questionsFailed: 2,
            duration: 1500,
            errors: ['Failed to save question 2', 'Failed to save question 3'],
          },
        ],
      ]),
    };
    
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockPreviewResult);
    (batchImportService.validateImportResult as any).mockReturnValue([]);
    (batchImportService.executeBatchImport as any).mockResolvedValue(mockBatchResult);
    
    renderImportPage();
    
    // Complete the import process
    const topicSelect = screen.getByRole('combobox');
    fireEvent.click(topicSelect);
    fireEvent.click(screen.getByText('JavaScript Basics'));
    
    const fileInput = screen.getByLabelText('CSV File');
    const file = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      const confirmButton = screen.getByText('Confirm Import');
      fireEvent.click(confirmButton);
    });
    
    await waitFor(() => {
      expect(screen.getByText('Import Failed')).toBeInTheDocument();
      expect(screen.getByText('Import Errors (2)')).toBeInTheDocument();
      expect(screen.getByText('DATABASE_ERROR: Connection timeout (Topic: JavaScript Basics)')).toBeInTheDocument();
      expect(screen.getByText('VALIDATION_ERROR: Duplicate question detected')).toBeInTheDocument();
    });
    
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Import failed',
        description: 'Failed to import questions. Encountered 2 errors.',
        variant: 'destructive',
      });
    });
  });

  it('handles partial success with mixed results', async () => {
    const mockPreviewResult = {
      success: true,
      totalRows: 5,
      globalErrors: [],
      newTopicsCreated: [],
      topicResults: new Map(),
    };
    
    const mockBatchResult = {
      success: true,
      totalQuestionsImported: 3,
      totalTopicsProcessed: 2,
      topicsCreated: ['New Topic'],
      errors: [
        { type: 'VALIDATION_ERROR', message: 'Question format invalid for row 4' },
        { type: 'PERMISSION_ERROR', message: 'Cannot create topic: insufficient permissions' },
      ],
      duration: 3000,
      topicResults: new Map(),
    };
    
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockPreviewResult);
    (batchImportService.validateImportResult as any).mockReturnValue([]);
    (batchImportService.executeBatchImport as any).mockResolvedValue(mockBatchResult);
    
    renderImportPage();
    
    // Complete the import process
    const topicSelect = screen.getByRole('combobox');
    fireEvent.click(topicSelect);
    fireEvent.click(screen.getByText('JavaScript Basics'));
    
    const fileInput = screen.getByLabelText('CSV File');
    const file = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      const confirmButton = screen.getByText('Confirm Import');
      fireEvent.click(confirmButton);
    });
    
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Import completed with errors',
        description: 'Imported 3 questions, but encountered 2 errors. Check the results below for details.',
        variant: 'destructive',
      });
    });
  });

  it('shows detailed error report when available', async () => {
    const mockPreviewResult = {
      success: true,
      totalRows: 2,
      globalErrors: [],
      newTopicsCreated: [],
      topicResults: new Map(),
    };
    
    const mockBatchResult = {
      success: false,
      totalQuestionsImported: 0,
      totalTopicsProcessed: 1,
      topicsCreated: [],
      errors: [{ type: 'CRITICAL_ERROR', message: 'System failure' }],
      duration: 500,
      topicResults: new Map(),
    };
    
    const mockErrorReport = {
      summary: {
        totalErrors: 3,
        globalErrors: 1,
        topicErrors: 2,
        affectedTopics: 1,
      },
      details: [
        {
          category: 'VALIDATION',
          count: 2,
          examples: ['Invalid format', 'Missing data'],
        },
        {
          category: 'SYSTEM',
          count: 1,
          examples: ['Database error'],
        },
      ],
    };
    
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockPreviewResult);
    (batchImportService.validateImportResult as any).mockReturnValue([]);
    (batchImportService.executeBatchImport as any).mockResolvedValue(mockBatchResult);
    (importErrorReportingService.generateErrorReport as any).mockReturnValue(mockErrorReport);
    
    renderImportPage();
    
    // Complete the import process
    const topicSelect = screen.getByRole('combobox');
    fireEvent.click(topicSelect);
    fireEvent.click(screen.getByText('JavaScript Basics'));
    
    const fileInput = screen.getByLabelText('CSV File');
    const file = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      const confirmButton = screen.getByText('Confirm Import');
      fireEvent.click(confirmButton);
    });
    
    await waitFor(() => {
      expect(screen.getByText('Detailed Error Report')).toBeInTheDocument();
    });
  });

  it('handles network errors during import', async () => {
    const mockPreviewResult = {
      success: true,
      totalRows: 2,
      globalErrors: [],
      newTopicsCreated: [],
      topicResults: new Map(),
    };
    
    const networkError = new Error('Network request failed');
    
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockPreviewResult);
    (batchImportService.validateImportResult as any).mockReturnValue([]);
    (batchImportService.executeBatchImport as any).mockRejectedValue(networkError);
    
    renderImportPage();
    
    // Complete the import process
    const topicSelect = screen.getByRole('combobox');
    fireEvent.click(topicSelect);
    fireEvent.click(screen.getByText('JavaScript Basics'));
    
    const fileInput = screen.getByLabelText('CSV File');
    const file = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      const confirmButton = screen.getByText('Confirm Import');
      fireEvent.click(confirmButton);
    });
    
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Import error',
        description: 'Network request failed',
        variant: 'destructive',
      });
    });
  });
});