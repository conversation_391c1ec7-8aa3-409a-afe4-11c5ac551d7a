import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogContent,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { AlertTriangle, Mail, Search, Zap } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

interface PaymentFixDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onFixUser: (email: string, planId: string) => Promise<void>;
  onDetectIssues: () => Promise<void>;
}

export const PaymentFixDialog: React.FC<PaymentFixDialogProps> = ({
  open,
  onOpenChange,
  onFixUser,
  onDetectIssues,
}) => {
  const [userEmail, setUserEmail] = useState('');
  const [selectedPlan, setSelectedPlan] = useState('basic');
  const [isFixing, setIsFixing] = useState(false);
  const [isDetecting, setIsDetecting] = useState(false);
  const { toast } = useToast();

  const handleFixSpecificUser = async () => {
    if (!userEmail.trim()) {
      toast({
        title: "Email Required",
        description: "Please enter a user email address",
        variant: "destructive",
      });
      return;
    }

    if (!userEmail.includes('@')) {
      toast({
        title: "Invalid Email",
        description: "Please enter a valid email address",
        variant: "destructive",
      });
      return;
    }

    setIsFixing(true);
    try {
      await onFixUser(userEmail.trim(), selectedPlan);
      setUserEmail('');
      toast({
        title: "Payment Issue Fixed",
        description: `Successfully granted premium access to ${userEmail}`,
      });
    } catch (error) {
      console.error('Error fixing user payment:', error);
      toast({
        title: "Fix Failed",
        description: error.message || "Failed to fix payment issue",
        variant: "destructive",
      });
    } finally {
      setIsFixing(false);
    }
  };

  const handleDetectAndFix = async () => {
    setIsDetecting(true);
    try {
      await onDetectIssues();
    } catch (error) {
      console.error('Error detecting issues:', error);
    } finally {
      setIsDetecting(false);
    }
  };

  const handleClose = () => {
    setUserEmail('');
    setSelectedPlan('basic');
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-orange-500" />
          Fix Payment Issues
        </DialogTitle>
        <DialogDescription>
          Resolve payment processing issues for users who completed payment but didn't receive premium access.
        </DialogDescription>

        <div className="space-y-6">
          {/* Bulk Detection Section */}
          <div className="space-y-3">
            <h4 className="font-medium flex items-center gap-2">
              <Search className="h-4 w-4" />
              Detect & Fix All Issues
            </h4>
            <p className="text-sm text-muted-foreground">
              Automatically detect users with payment discrepancies and fix them.
            </p>
            <Button
              onClick={handleDetectAndFix}
              disabled={isDetecting}
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              {isDetecting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Detecting Issues...
                </>
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" />
                  Detect & Fix Payment Issues
                </>
              )}
            </Button>
          </div>

          <Separator />

          {/* Manual Fix Section */}
          <div className="space-y-3">
            <h4 className="font-medium flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Fix Specific User
            </h4>
            <p className="text-sm text-muted-foreground">
              Manually grant premium access to a specific user by email.
            </p>
            
            <div className="space-y-3">
              <div>
                <Label htmlFor="userEmail">User Email</Label>
                <Input
                  id="userEmail"
                  type="email"
                  placeholder="<EMAIL>"
                  value={userEmail}
                  onChange={(e) => setUserEmail(e.target.value)}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="planSelect">Plan Type</Label>
                <Select value={selectedPlan} onValueChange={setSelectedPlan}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select plan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="basic">Basic Plan (₦998/week)</SelectItem>
                    <SelectItem value="pro">Pro Plan (₦1,979/week)</SelectItem>
                    <SelectItem value="elite">Elite Plan (₦5,000 one-time)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button
                onClick={handleFixSpecificUser}
                disabled={isFixing || !userEmail.trim()}
                className="w-full bg-green-600 hover:bg-green-700"
              >
                {isFixing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Fixing Payment...
                  </>
                ) : (
                  <>
                    <Zap className="h-4 w-4 mr-2" />
                    Grant Premium Access
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={handleClose}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
