import { describe, it, expect } from 'vitest';
import { 
  parseCorrectAnswer, 
  validateAnswer, 
  parseQuestionOptions, 
  safeValidateAnswer 
} from './answer-validation';

describe('Answer Validation Integration Tests', () => {
  describe('Real-world scenarios', () => {
    it('should handle typical database question format', () => {
      // Simulate a typical question from the database
      const dbQuestion = {
        id: 'q1',
        question_text: 'What is the CIA triad?',
        options: {
          '0': 'Confidentiality, Integrity, Availability',
          '1': 'Control, Integration, Access',
          '2': 'Compliance, Information, Authentication',
          '3': 'Cryptography, Identity, Authorization'
        },
        correct_answer: '0'
      };

      // Parse options
      const options = parseQuestionOptions(dbQuestion.options);
      expect(options).toHaveLength(4);
      expect(options[0]).toBe('Confidentiality, Integrity, Availability');

      // Parse correct answer
      const parsedAnswer = parseCorrectAnswer(dbQuestion.correct_answer, options.length);
      expect(parsedAnswer.isValid).toBe(true);
      expect(parsedAnswer.correctIndex).toBe(0);

      // Validate user selection (correct)
      const correctValidation = safeValidateAnswer(0, dbQuestion.correct_answer, parsedAnswer.correctIndex, options.length);
      expect(correctValidation.isCorrect).toBe(true);
      expect(correctValidation.confidence).toBe('high');

      // Validate user selection (incorrect)
      const incorrectValidation = safeValidateAnswer(1, dbQuestion.correct_answer, parsedAnswer.correctIndex, options.length);
      expect(incorrectValidation.isCorrect).toBe(false);
      expect(incorrectValidation.confidence).toBe('high');
    });

    it('should handle malformed database data gracefully', () => {
      // Simulate malformed question data
      const malformedQuestion = {
        id: 'q2',
        question_text: 'What is malware?',
        options: null, // Malformed options
        correct_answer: 'invalid_answer' // Invalid correct answer
      };

      // Parse options with fallback
      const options = parseQuestionOptions(malformedQuestion.options);
      expect(options).toEqual(['Option not available']);

      // Parse correct answer with fallback
      const parsedAnswer = parseCorrectAnswer(malformedQuestion.correct_answer, options.length);
      expect(parsedAnswer.isValid).toBe(false);
      expect(parsedAnswer.correctIndex).toBe(0); // Falls back to 0

      // Safe validation should still work
      const validation = safeValidateAnswer(0, malformedQuestion.correct_answer, parsedAnswer.correctIndex, options.length);
      expect(validation.isCorrect).toBe(true); // Matches fallback
      expect(validation.warnings.length).toBeGreaterThan(0);
    });

    it('should handle array-format options', () => {
      const arrayQuestion = {
        id: 'q3',
        question_text: 'Which is a type of attack?',
        options: ['Phishing', 'Encryption', 'Firewall', 'Antivirus'],
        correct_answer: 0
      };

      const options = parseQuestionOptions(arrayQuestion.options);
      expect(options).toEqual(['Phishing', 'Encryption', 'Firewall', 'Antivirus']);

      const parsedAnswer = parseCorrectAnswer(arrayQuestion.correct_answer, options.length);
      expect(parsedAnswer.isValid).toBe(true);
      expect(parsedAnswer.correctIndex).toBe(0);

      const validation = safeValidateAnswer(0, arrayQuestion.correct_answer, parsedAnswer.correctIndex, options.length);
      expect(validation.isCorrect).toBe(true);
      expect(validation.confidence).toBe('high');
    });

    it('should handle JSON string options', () => {
      const jsonQuestion = {
        id: 'q4',
        question_text: 'What is SQL injection?',
        options: '["A database attack", "A network protocol", "An encryption method", "A firewall rule"]',
        correct_answer: '0'
      };

      const options = parseQuestionOptions(jsonQuestion.options);
      expect(options).toEqual(['A database attack', 'A network protocol', 'An encryption method', 'A firewall rule']);

      const parsedAnswer = parseCorrectAnswer(jsonQuestion.correct_answer, options.length);
      expect(parsedAnswer.isValid).toBe(true);
      expect(parsedAnswer.correctIndex).toBe(0);

      const validation = safeValidateAnswer(0, jsonQuestion.correct_answer, parsedAnswer.correctIndex, options.length);
      expect(validation.isCorrect).toBe(true);
      expect(validation.confidence).toBe('high');
    });

    it('should handle edge cases in user selection', () => {
      const question = {
        options: ['Option A', 'Option B', 'Option C', 'Option D'],
        correct_answer: 2
      };

      const options = parseQuestionOptions(question.options);
      const parsedAnswer = parseCorrectAnswer(question.correct_answer, options.length);

      // Test null selection
      const nullValidation = safeValidateAnswer(null, question.correct_answer, parsedAnswer.correctIndex, options.length);
      expect(nullValidation.isCorrect).toBe(false);
      expect(nullValidation.selectedIndex).toBe(-1);

      // Test out of range selection
      const outOfRangeValidation = safeValidateAnswer(5, question.correct_answer, parsedAnswer.correctIndex, options.length);
      expect(outOfRangeValidation.isCorrect).toBe(false);
      expect(outOfRangeValidation.warnings.length).toBeGreaterThan(0);

      // Test negative selection
      const negativeValidation = safeValidateAnswer(-1, question.correct_answer, parsedAnswer.correctIndex, options.length);
      expect(negativeValidation.isCorrect).toBe(false);
      expect(negativeValidation.warnings.length).toBeGreaterThan(0);
    });
  });

  describe('Performance and reliability', () => {
    it('should handle large numbers of validations efficiently', () => {
      const question = {
        options: ['A', 'B', 'C', 'D'],
        correct_answer: 1
      };

      const options = parseQuestionOptions(question.options);
      const parsedAnswer = parseCorrectAnswer(question.correct_answer, options.length);

      const startTime = performance.now();
      
      // Perform 1000 validations
      for (let i = 0; i < 1000; i++) {
        const selectedIndex = i % 4;
        safeValidateAnswer(selectedIndex, question.correct_answer, parsedAnswer.correctIndex, options.length);
      }

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should complete 1000 validations in reasonable time (less than 100ms)
      expect(duration).toBeLessThan(100);
    });

    it('should be consistent across multiple calls', () => {
      const question = {
        options: ['Option 1', 'Option 2', 'Option 3', 'Option 4'],
        correct_answer: '2'
      };

      const options = parseQuestionOptions(question.options);
      const parsedAnswer = parseCorrectAnswer(question.correct_answer, options.length);

      // Run the same validation multiple times
      const results = [];
      for (let i = 0; i < 10; i++) {
        const result = safeValidateAnswer(2, question.correct_answer, parsedAnswer.correctIndex, options.length);
        results.push(result);
      }

      // All results should be identical
      const firstResult = results[0];
      results.forEach(result => {
        expect(result.isCorrect).toBe(firstResult.isCorrect);
        expect(result.selectedIndex).toBe(firstResult.selectedIndex);
        expect(result.correctIndex).toBe(firstResult.correctIndex);
        expect(result.confidence).toBe(firstResult.confidence);
      });
    });
  });
});