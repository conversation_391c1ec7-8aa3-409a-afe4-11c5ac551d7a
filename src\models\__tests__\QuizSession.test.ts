/**
 * QuizSession Model Tests
 * Tests for quiz session lifecycle management, validation, and database operations
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { QuizSession, type QuizSessionCreateParams } from '../QuizSession';
import { supabase } from '@/integrations/supabase/client';

// Mock Supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn()
        }))
      })),
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(),
          order: vi.fn(() => ({
            limit: vi.fn()
          })),
          is: vi.fn(() => ({
            gt: vi.fn(() => ({
              order: vi.fn()
            }))
          }))
        }))
      })),
      update: vi.fn(() => ({
        eq: vi.fn()
      })),
      delete: vi.fn(() => ({
        eq: vi.fn()
      }))
    })),
    rpc: vi.fn()
  }
}));

describe('QuizSession Model', () => {
  const mockSessionData = {
    id: 'session-123',
    user_id: 'user-456',
    topic_id: 'topic-789',
    questions_data: {
      questions: [
        {
          id: 'q1',
          originalCorrectIndex: 0,
          shuffledCorrectIndex: 2,
          optionMapping: [2, 0, 1, 3]
        }
      ],
      metadata: {
        totalQuestions: 1,
        topicId: 'topic-789',
        createdAt: '2025-01-01T10:00:00Z'
      }
    },
    quiz_length: 10,
    created_at: '2025-01-01T10:00:00Z',
    expires_at: '2025-01-01T12:00:00Z',
    completed_at: null,
    score: null,
    total_questions: 10,
    time_taken: null
  };

  const mockCreateParams: QuizSessionCreateParams = {
    userId: 'user-456',
    topicId: 'topic-789',
    questionsData: {
      questions: [
        {
          id: 'q1',
          originalCorrectIndex: 0,
          shuffledCorrectIndex: 2,
          optionMapping: [2, 0, 1, 3]
        }
      ],
      metadata: {
        totalQuestions: 1,
        topicId: 'topic-789',
        createdAt: '2025-01-01T10:00:00Z'
      }
    },
    quizLength: 10
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Constructor and Getters', () => {
    it('should create a QuizSession instance with correct properties', () => {
      const session = new QuizSession(mockSessionData);

      expect(session.id).toBe('session-123');
      expect(session.userId).toBe('user-456');
      expect(session.topicId).toBe('topic-789');
      expect(session.quizLength).toBe(10);
      expect(session.isCompleted).toBe(false);
      expect(session.score).toBeNull();
      expect(session.timeTaken).toBeNull();
    });

    it('should correctly identify completed sessions', () => {
      const completedSessionData = {
        ...mockSessionData,
        completed_at: '2025-01-01T11:00:00Z',
        score: 8
      };
      const session = new QuizSession(completedSessionData);

      expect(session.isCompleted).toBe(true);
      expect(session.score).toBe(8);
    });

    it('should correctly identify expired sessions', () => {
      // Mock current time to be after expiration
      const pastExpiryData = {
        ...mockSessionData,
        expires_at: '2024-12-31T12:00:00Z' // Past date
      };
      const session = new QuizSession(pastExpiryData);

      expect(session.isExpired).toBe(true);
    });
  });

  describe('Static Methods - Create', () => {
    it('should create a new quiz session successfully', async () => {
      const mockSupabaseResponse = {
        data: mockSessionData,
        error: null
      };

      const mockInsert = vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn().mockResolvedValue(mockSupabaseResponse)
        }))
      }));

      const mockFrom = vi.fn(() => ({
        insert: mockInsert
      }));

      (supabase.from as any).mockImplementation(mockFrom);

      const session = await QuizSession.create(mockCreateParams);

      expect(session).toBeInstanceOf(QuizSession);
      expect(session.id).toBe('session-123');
      expect(mockFrom).toHaveBeenCalledWith('quiz_sessions');
      expect(mockInsert).toHaveBeenCalledWith({
        user_id: 'user-456',
        topic_id: 'topic-789',
        questions_data: mockCreateParams.questionsData,
        quiz_length: 10,
        total_questions: 1
      });
    });

    it('should throw error when creation fails', async () => {
      const mockSupabaseResponse = {
        data: null,
        error: { message: 'Database error' }
      };

      const mockInsert = vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn().mockResolvedValue(mockSupabaseResponse)
        }))
      }));

      (supabase.from as any).mockImplementation(() => ({
        insert: mockInsert
      }));

      await expect(QuizSession.create(mockCreateParams))
        .rejects.toThrow('Failed to create quiz session: Database error');
    });
  });

  describe('Static Methods - Find', () => {
    it('should find session by ID successfully', async () => {
      const mockSupabaseResponse = {
        data: mockSessionData,
        error: null
      };

      const mockSingle = vi.fn().mockResolvedValue(mockSupabaseResponse);
      const mockEq2 = vi.fn(() => ({ single: mockSingle }));
      const mockEq1 = vi.fn(() => ({ eq: mockEq2 }));
      const mockSelect = vi.fn(() => ({ eq: mockEq1 }));

      (supabase.from as any).mockImplementation(() => ({
        select: mockSelect
      }));

      const session = await QuizSession.findById('session-123', 'user-456');

      expect(session).toBeInstanceOf(QuizSession);
      expect(session?.id).toBe('session-123');
      expect(mockSelect).toHaveBeenCalledWith('*');
      expect(mockEq1).toHaveBeenCalledWith('id', 'session-123');
      expect(mockEq2).toHaveBeenCalledWith('user_id', 'user-456');
    });

    it('should return null when session not found', async () => {
      const mockSupabaseResponse = {
        data: null,
        error: { code: 'PGRST116' } // Not found error
      };

      const mockSingle = vi.fn().mockResolvedValue(mockSupabaseResponse);
      const mockEq = vi.fn(() => ({ single: mockSingle }));
      const mockSelect = vi.fn(() => ({ eq: mockEq }));

      (supabase.from as any).mockImplementation(() => ({
        select: mockSelect
      }));

      const session = await QuizSession.findById('nonexistent-id');

      expect(session).toBeNull();
    });

    it('should find sessions by user ID', async () => {
      const mockSupabaseResponse = {
        data: [mockSessionData],
        error: null
      };

      const mockLimit = vi.fn().mockResolvedValue(mockSupabaseResponse);
      const mockOrder = vi.fn(() => ({ limit: mockLimit }));
      const mockEq = vi.fn(() => ({ order: mockOrder }));
      const mockSelect = vi.fn(() => ({ eq: mockEq }));

      (supabase.from as any).mockImplementation(() => ({
        select: mockSelect
      }));

      const sessions = await QuizSession.findByUserId('user-456');

      expect(sessions).toHaveLength(1);
      expect(sessions[0]).toBeInstanceOf(QuizSession);
      expect(sessions[0].userId).toBe('user-456');
    });

    it('should find active sessions for user', async () => {
      const mockSupabaseResponse = {
        data: [mockSessionData],
        error: null
      };

      const mockOrder = vi.fn().mockResolvedValue(mockSupabaseResponse);
      const mockGt = vi.fn(() => ({ order: mockOrder }));
      const mockIs = vi.fn(() => ({ gt: mockGt }));
      const mockEq = vi.fn(() => ({ is: mockIs }));
      const mockSelect = vi.fn(() => ({ eq: mockEq }));

      (supabase.from as any).mockImplementation(() => ({
        select: mockSelect
      }));

      const sessions = await QuizSession.findActiveSessions('user-456');

      expect(sessions).toHaveLength(1);
      expect(sessions[0].isCompleted).toBe(false);
    });
  });

  describe('Instance Methods - Update', () => {
    it('should update session successfully', async () => {
      const mockSupabaseResponse = {
        error: null
      };

      const mockEq = vi.fn().mockResolvedValue(mockSupabaseResponse);
      const mockUpdate = vi.fn(() => ({ eq: mockEq }));

      (supabase.from as any).mockImplementation(() => ({
        update: mockUpdate
      }));

      const session = new QuizSession(mockSessionData);
      const success = await session.update({
        score: 8,
        timeTaken: 300,
        completedAt: '2025-01-01T11:00:00Z'
      });

      expect(success).toBe(true);
      expect(session.score).toBe(8);
      expect(session.timeTaken).toBe(300);
      expect(session.isCompleted).toBe(true);
    });

    it('should complete session successfully', async () => {
      const mockSupabaseResponse = {
        error: null
      };

      const mockEq = vi.fn().mockResolvedValue(mockSupabaseResponse);
      const mockUpdate = vi.fn(() => ({ eq: mockEq }));

      (supabase.from as any).mockImplementation(() => ({
        update: mockUpdate
      }));

      const session = new QuizSession(mockSessionData);
      const success = await session.complete(8, 300);

      expect(success).toBe(true);
      expect(session.score).toBe(8);
      expect(session.timeTaken).toBe(300);
      expect(session.isCompleted).toBe(true);
    });

    it('should extend expiration successfully', async () => {
      const mockSupabaseResponse = {
        error: null
      };

      const mockEq = vi.fn().mockResolvedValue(mockSupabaseResponse);
      const mockUpdate = vi.fn(() => ({ eq: mockEq }));

      (supabase.from as any).mockImplementation(() => ({
        update: mockUpdate
      }));

      const session = new QuizSession(mockSessionData);
      const originalExpiry = new Date(session.expiresAt);
      
      const success = await session.extendExpiration(30);

      expect(success).toBe(true);
      
      // Verify the expiration was extended
      const newExpiry = new Date(session.expiresAt);
      expect(newExpiry.getTime()).toBeGreaterThan(originalExpiry.getTime());
    });
  });

  describe('Validation', () => {
    it('should validate active session as valid', () => {
      // Create session with future expiry
      const futureSessionData = {
        ...mockSessionData,
        expires_at: new Date(Date.now() + 3600000).toISOString(), // 1 hour from now
        completed_at: null // Ensure not completed
      };
      const session = new QuizSession(futureSessionData);

      const validation = session.validate();

      expect(validation.isValid).toBe(true);
      expect(validation.isExpired).toBe(false);
      expect(validation.isCompleted).toBe(false);
      expect(validation.message).toBe('Session is valid and active');
    });

    it('should validate expired session as invalid', () => {
      const expiredSessionData = {
        ...mockSessionData,
        expires_at: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        completed_at: null // Ensure not completed
      };
      const session = new QuizSession(expiredSessionData);

      const validation = session.validate();

      expect(validation.isValid).toBe(false);
      expect(validation.isExpired).toBe(true);
      expect(validation.isCompleted).toBe(false);
      expect(validation.message).toBe('Session has expired');
    });

    it('should validate completed session as invalid', () => {
      const completedSessionData = {
        ...mockSessionData,
        completed_at: '2025-01-01T11:00:00Z',
        expires_at: new Date(Date.now() + 3600000).toISOString() // Future expiry to isolate completion check
      };
      const session = new QuizSession(completedSessionData);

      const validation = session.validate();

      expect(validation.isValid).toBe(false);
      expect(validation.isExpired).toBe(false);
      expect(validation.isCompleted).toBe(true);
      expect(validation.message).toBe('Session is already completed');
    });
  });

  describe('Cleanup and Statistics', () => {
    it('should cleanup expired sessions', async () => {
      const mockSupabaseResponse = {
        data: 5,
        error: null
      };

      (supabase.rpc as any).mockResolvedValue(mockSupabaseResponse);

      const cleanedCount = await QuizSession.cleanupExpired();

      expect(cleanedCount).toBe(5);
      expect(supabase.rpc).toHaveBeenCalledWith('cleanup_expired_quiz_sessions');
    });

    it('should get user session statistics', async () => {
      const mockSessionsData = [
        {
          completed_at: '2025-01-01T11:00:00Z',
          score: 8,
          total_questions: 10,
          time_taken: 300,
          topic_id: 'topic-1'
        },
        {
          completed_at: '2025-01-01T12:00:00Z',
          score: 6,
          total_questions: 10,
          time_taken: 400,
          topic_id: 'topic-2'
        },
        {
          completed_at: null,
          score: null,
          total_questions: 10,
          time_taken: null,
          topic_id: 'topic-1'
        }
      ];

      const mockSupabaseResponse = {
        data: mockSessionsData,
        error: null
      };

      const mockEq = vi.fn().mockResolvedValue(mockSupabaseResponse);
      const mockSelect = vi.fn(() => ({ eq: mockEq }));

      (supabase.from as any).mockImplementation(() => ({
        select: mockSelect
      }));

      const stats = await QuizSession.getUserSessionStats('user-456');

      expect(stats.totalSessions).toBe(3);
      expect(stats.completedSessions).toBe(2);
      expect(stats.averageScore).toBe(7); // (8 + 6) / 2
      expect(stats.averageTime).toBe(350); // (300 + 400) / 2
      expect(stats.totalQuestionsAnswered).toBe(20); // 10 + 10
      expect(stats.uniqueTopics).toBe(2);
    });

    it('should delete session successfully', async () => {
      const mockSupabaseResponse = {
        error: null
      };

      const mockEq = vi.fn().mockResolvedValue(mockSupabaseResponse);
      const mockDelete = vi.fn(() => ({ eq: mockEq }));

      (supabase.from as any).mockImplementation(() => ({
        delete: mockDelete
      }));

      const session = new QuizSession(mockSessionData);
      const success = await session.delete();

      expect(success).toBe(true);
      expect(mockDelete).toHaveBeenCalled();
      expect(mockEq).toHaveBeenCalledWith('id', 'session-123');
    });
  });

  describe('JSON Serialization', () => {
    it('should convert session to JSON correctly', () => {
      const session = new QuizSession(mockSessionData);
      const json = session.toJSON();

      expect(json).toEqual(mockSessionData);
      expect(json.id).toBe('session-123');
      expect(json.user_id).toBe('user-456');
    });
  });
});