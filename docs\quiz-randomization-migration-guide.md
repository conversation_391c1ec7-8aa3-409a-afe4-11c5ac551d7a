# Quiz Randomization Migration Guide

This document explains the database schema enhancements for quiz randomization and analytics features.

## Overview

The quiz randomization migration adds support for:
- Question usage tracking and analytics
- Randomized quiz sessions with question and answer shuffling
- Performance monitoring and question pool analysis
- Automated cleanup of expired quiz sessions

## Migration Files

### Primary Migration
- **File**: `supabase/migrations/20250201000000_add_quiz_randomization_schema.sql`
- **Purpose**: Adds all new tables, columns, functions, and triggers for quiz randomization

### Rollback Migration
- **File**: `supabase/migrations/20250201000001_rollback_quiz_randomization_schema.sql`
- **Purpose**: Removes all changes if rollback is needed

## Schema Changes

### 1. Questions Table Enhancements

New columns added to the `questions` table:

```sql
-- Usage tracking columns
usage_count INTEGER DEFAULT 0              -- Number of times question was used
last_used TIMESTAMP WITH TIME ZONE         -- When question was last used
correct_answer_rate DECIMAL(5,2) DEFAULT 0.00  -- Percentage of correct answers
```

### 2. New Tables

#### quiz_sessions
Tracks randomized quiz instances with question order and answer mappings:

```sql
CREATE TABLE quiz_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  topic_id UUID REFERENCES topics(id),
  questions_data JSONB NOT NULL,  -- Randomized question order and mappings
  quiz_length INTEGER NOT NULL DEFAULT 10,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() + INTERVAL '2 hours',
  completed_at TIMESTAMP WITH TIME ZONE,
  score INTEGER,
  total_questions INTEGER,
  time_taken INTEGER  -- in seconds
);
```

#### question_analytics
Stores individual question performance data:

```sql
CREATE TABLE question_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  question_id UUID REFERENCES questions(id) NOT NULL,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  quiz_session_id UUID REFERENCES quiz_sessions(id),
  answered_correctly BOOLEAN NOT NULL,
  time_to_answer INTEGER,  -- in seconds
  selected_option INTEGER, -- the option index selected by user
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 3. Database Functions

#### update_question_usage_stats()
Automatically updates question usage count and last_used timestamp when analytics are inserted.

#### update_question_correct_answer_rate()
Calculates and updates the correct answer rate for questions based on user responses.

#### cleanup_expired_quiz_sessions()
Removes expired quiz sessions that haven't been completed.

#### get_topic_question_stats(topic_uuid UUID)
Returns comprehensive statistics about question pools for a given topic:
- Total questions
- Average usage count
- Average correct answer rate
- Questions never used
- Questions with low performance

### 4. Triggers

- **trigger_update_question_usage**: Fires after question_analytics insertion to update usage stats
- **trigger_update_correct_answer_rate**: Fires after question_analytics insertion to update correct answer rates

## Security (RLS Policies)

All new tables have Row Level Security enabled with appropriate policies:

### quiz_sessions
- Users can view/insert/update their own quiz sessions
- Service role can manage all quiz sessions
- Admins have full access

### question_analytics
- Users can view/insert their own analytics data
- Service role can manage all analytics data
- Admins can view all analytics for reporting

## Performance Optimizations

### Indexes Created
- Questions table: usage_count, last_used, correct_answer_rate
- Quiz sessions: user_id, topic_id, created_at, expires_at, completed_at
- Question analytics: question_id, user_id, quiz_session_id, created_at, answered_correctly

## Testing

### Running Migration Tests

```bash
# Run the comprehensive test suite
cd server
npm test -- --run test/integration/quiz-randomization-schema.test.ts

# Run the migration validation script (requires database connection)
node scripts/test-quiz-randomization-migration.js
```

### Test Coverage

The tests verify:
- ✅ Table creation and structure
- ✅ Column additions and default values
- ✅ RLS policy creation
- ✅ Function and trigger creation
- ✅ Index creation for performance
- ✅ Foreign key constraints
- ✅ Data type validation
- ✅ Concurrent update handling
- ✅ Migration rollback capability

## Usage Examples

### Creating a Quiz Session

```javascript
const { data: session } = await supabase
  .from('quiz_sessions')
  .insert({
    user_id: userId,
    topic_id: topicId,
    questions_data: {
      questions: randomizedQuestions,
      mappings: answerMappings
    },
    quiz_length: 20
  })
  .select()
  .single();
```

### Recording Question Analytics

```javascript
const { data } = await supabase
  .from('question_analytics')
  .insert({
    question_id: questionId,
    user_id: userId,
    quiz_session_id: sessionId,
    answered_correctly: isCorrect,
    time_to_answer: timeInSeconds,
    selected_option: selectedIndex
  });
```

### Getting Topic Statistics

```javascript
const { data: stats } = await supabase
  .rpc('get_topic_question_stats', { 
    topic_uuid: topicId 
  });

console.log(`Total questions: ${stats.total_questions}`);
console.log(`Average usage: ${stats.avg_usage_count}`);
console.log(`Average correct rate: ${stats.avg_correct_rate}%`);
```

### Cleaning Up Expired Sessions

```javascript
const { data: deletedCount } = await supabase
  .rpc('cleanup_expired_quiz_sessions');

console.log(`Cleaned up ${deletedCount} expired sessions`);
```

## Migration Rollback

If you need to rollback the migration:

```sql
-- Run the rollback migration
-- This will remove all quiz randomization enhancements
-- WARNING: This will delete all quiz_sessions and question_analytics data
```

**⚠️ Important**: Make sure to backup any important data before running the rollback migration.

## Monitoring and Maintenance

### Regular Cleanup
Consider setting up a scheduled job to run `cleanup_expired_quiz_sessions()` periodically to keep the database clean.

### Performance Monitoring
Use `get_topic_question_stats()` to monitor question pool health and identify topics that need more questions or have poorly performing questions.

### Analytics Insights
Query the `question_analytics` table to gain insights into:
- Question difficulty (low correct answer rates)
- User engagement (time to answer patterns)
- Content quality (questions that consistently confuse users)

## Requirements Satisfied

This migration satisfies the following requirements from the specification:

- **Requirement 6.1**: Question usage tracking with statistics
- **Requirement 6.2**: Question performance monitoring and flagging
- **Requirement 6.3**: Topic-level question pool analysis and alerts

The schema provides a solid foundation for implementing the quiz randomization features while maintaining data integrity and performance.