import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { MultiTopicCSVImport } from '../MultiTopicCSVImport';
import { useToast } from '@/hooks/use-toast';
import * as csvImportUtils from '@/utils/csv-import';

// Mock the toast hook
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(),
}));

// Mock the CSV import utilities
vi.mock('@/utils/csv-import', () => ({
  parseQuestionCSVEnhanced: vi.fn(),
  generateCSVTemplate: vi.fn(),
  generateMultiTopicCSVTemplate: vi.fn(),
}));

// Mock Supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      insert: vi.fn(),
    })),
  },
}));

const mockToast = vi.fn();
const mockTopics = [
  { id: '1', title: 'Security Fundamentals' },
  { id: '2', title: 'Network Security' },
  { id: '3', title: 'Incident Response' },
];

describe('MultiTopicCSVImport', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useToast as any).mockReturnValue({ toast: mockToast });
    (csvImportUtils.generateCSVTemplate as any).mockReturnValue('mock,csv,content');
    (csvImportUtils.generateMultiTopicCSVTemplate as any).mockReturnValue('topic_name,mock,csv,content');
  });

  it('renders with default single-topic mode', () => {
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    expect(screen.getByText('Import Questions from CSV')).toBeInTheDocument();
    expect(screen.getByText('Single Topic')).toBeInTheDocument();
    expect(screen.getByText('Multi Topic')).toBeInTheDocument();
    expect(screen.getByText('Select Topic')).toBeInTheDocument();
    expect(screen.getByText('Upload CSV File')).toBeInTheDocument();
  });

  it('switches between single-topic and multi-topic modes', () => {
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    // Initially in single-topic mode
    expect(screen.getByText('Select Topic')).toBeInTheDocument();
    expect(screen.queryByText('Auto-create Topics')).not.toBeInTheDocument();
    
    // Switch to multi-topic mode
    fireEvent.click(screen.getByText('Multi Topic'));
    
    expect(screen.getByText('Auto-create Topics')).toBeInTheDocument();
    expect(screen.queryByText('Select Topic')).not.toBeInTheDocument();
  });

  it('shows topic selection in single-topic mode', () => {
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    const topicSelect = screen.getByRole('combobox');
    expect(topicSelect).toBeInTheDocument();
    
    // Click to open dropdown
    fireEvent.click(topicSelect);
    
    // Check that topics are available
    expect(screen.getByText('Security Fundamentals')).toBeInTheDocument();
    expect(screen.getByText('Network Security')).toBeInTheDocument();
    expect(screen.getByText('Incident Response')).toBeInTheDocument();
  });

  it('shows auto-create toggle in multi-topic mode', () => {
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    // Switch to multi-topic mode
    fireEvent.click(screen.getByText('Multi Topic'));
    
    const autoCreateSwitch = screen.getByRole('switch');
    expect(autoCreateSwitch).toBeInTheDocument();
    expect(autoCreateSwitch).not.toBeChecked();
    
    // Toggle the switch
    fireEvent.click(autoCreateSwitch);
    expect(autoCreateSwitch).toBeChecked();
  });

  it('handles file selection', () => {
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    const fileInput = screen.getByLabelText('Upload CSV File');
    const mockFile = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    
    fireEvent.change(fileInput, { target: { files: [mockFile] } });
    
    expect(fileInput.files?.[0]).toBe(mockFile);
  });

  it('downloads single-topic template by default', () => {
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    // Mock URL.createObjectURL and related DOM methods
    const mockCreateObjectURL = vi.fn(() => 'mock-url');
    const mockRevokeObjectURL = vi.fn();
    global.URL.createObjectURL = mockCreateObjectURL;
    global.URL.revokeObjectURL = mockRevokeObjectURL;
    
    const mockClick = vi.fn();
    const mockAppendChild = vi.fn();
    const mockRemoveChild = vi.fn();
    
    document.createElement = vi.fn(() => ({
      href: '',
      setAttribute: vi.fn(),
      click: mockClick,
    })) as any;
    document.body.appendChild = mockAppendChild;
    document.body.removeChild = mockRemoveChild;
    
    const downloadButton = screen.getByTitle('Download Single-Topic CSV Template');
    fireEvent.click(downloadButton);
    
    expect(csvImportUtils.generateCSVTemplate).toHaveBeenCalled();
    expect(mockCreateObjectURL).toHaveBeenCalled();
    expect(mockClick).toHaveBeenCalled();
  });

  it('downloads multi-topic template in multi-topic mode', () => {
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    // Switch to multi-topic mode
    fireEvent.click(screen.getByText('Multi Topic'));
    
    // Mock URL.createObjectURL and related DOM methods
    const mockCreateObjectURL = vi.fn(() => 'mock-url');
    global.URL.createObjectURL = mockCreateObjectURL;
    
    const mockClick = vi.fn();
    document.createElement = vi.fn(() => ({
      href: '',
      setAttribute: vi.fn(),
      click: mockClick,
    })) as any;
    document.body.appendChild = vi.fn();
    document.body.removeChild = vi.fn();
    
    const downloadButton = screen.getByTitle('Download Multi-Topic CSV Template');
    fireEvent.click(downloadButton);
    
    expect(csvImportUtils.generateMultiTopicCSVTemplate).toHaveBeenCalled();
  });

  it('validates required fields before preview in single-topic mode', async () => {
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    const previewButton = screen.getByText('Preview Import');
    
    // Try to preview without file
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: "Missing file",
        description: "Please select a CSV file to preview.",
        variant: "destructive",
      });
    });
    
    // Add file but no topic
    const fileInput = screen.getByLabelText('Upload CSV File');
    const mockFile = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [mockFile] } });
    
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: "Missing topic",
        description: "Please select a topic for single-topic import.",
        variant: "destructive",
      });
    });
  });

  it('allows preview in multi-topic mode with just a file', async () => {
    const mockResult = {
      success: true,
      totalRows: 2,
      topicResults: new Map([
        ['topic1', {
          topicId: 'topic1',
          topicName: 'Test Topic',
          validQuestions: [{ id: '1' }],
          errors: [],
          isNewTopic: false,
        }],
      ]),
      globalErrors: [],
      newTopicsCreated: [],
    };
    
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
    
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    // Switch to multi-topic mode
    fireEvent.click(screen.getByText('Multi Topic'));
    
    // Add file
    const fileInput = screen.getByLabelText('Upload CSV File');
    const mockFile = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [mockFile] } });
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      expect(csvImportUtils.parseQuestionCSVEnhanced).toHaveBeenCalledWith(
        mockFile,
        {
          mode: 'multi-topic',
          autoCreateTopics: false,
          selectedTopicId: undefined,
        }
      );
    });
  });

  it('displays preview results correctly', async () => {
    const mockResult = {
      success: true,
      totalRows: 3,
      topicResults: new Map([
        ['topic1', {
          topicId: 'topic1',
          topicName: 'Security Fundamentals',
          validQuestions: [{ id: '1' }, { id: '2' }],
          errors: [],
          isNewTopic: false,
        }],
        ['topic2', {
          topicId: 'topic2',
          topicName: 'Network Security',
          validQuestions: [{ id: '3' }],
          errors: [{ row: 3, message: 'Invalid answer format' }],
          isNewTopic: true,
        }],
      ]),
      globalErrors: [],
      newTopicsCreated: ['Network Security'],
    };
    
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
    
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    // Switch to multi-topic mode and add file
    fireEvent.click(screen.getByText('Multi Topic'));
    const fileInput = screen.getByLabelText('Upload CSV File');
    const mockFile = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [mockFile] } });
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      expect(screen.getByText('Import Preview')).toBeInTheDocument();
      expect(screen.getByText('Security Fundamentals')).toBeInTheDocument();
      expect(screen.getByText('Network Security')).toBeInTheDocument();
      expect(screen.getByText('2 questions')).toBeInTheDocument();
      expect(screen.getByText('1 questions')).toBeInTheDocument();
      expect(screen.getByText('1 errors')).toBeInTheDocument();
      expect(screen.getByText('New')).toBeInTheDocument(); // Badge for new topic
      expect(screen.getByText('New Topics Will Be Created')).toBeInTheDocument();
    });
  });

  it('shows global errors in preview', async () => {
    const mockResult = {
      success: false,
      totalRows: 2,
      topicResults: new Map(),
      globalErrors: [
        { row: 1, message: 'Invalid CSV format' },
        { row: 2, message: 'Missing required column' },
      ],
      newTopicsCreated: [],
    };
    
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
    
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    // Add file and preview
    const fileInput = screen.getByLabelText('Upload CSV File');
    const mockFile = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [mockFile] } });
    
    // Select topic for single-topic mode
    const topicSelect = screen.getByRole('combobox');
    fireEvent.click(topicSelect);
    fireEvent.click(screen.getByText('Security Fundamentals'));
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      expect(screen.getByText('Global Issues Found')).toBeInTheDocument();
      expect(screen.getByText('Row 1: Invalid CSV format')).toBeInTheDocument();
      expect(screen.getByText('Row 2: Missing required column')).toBeInTheDocument();
    });
  });

  it('handles preview cancellation', async () => {
    const mockResult = {
      success: true,
      totalRows: 1,
      topicResults: new Map([
        ['topic1', {
          topicId: 'topic1',
          topicName: 'Test Topic',
          validQuestions: [{ id: '1' }],
          errors: [],
          isNewTopic: false,
        }],
      ]),
      globalErrors: [],
      newTopicsCreated: [],
    };
    
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
    
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    // Add file and topic, then preview
    const fileInput = screen.getByLabelText('Upload CSV File');
    const mockFile = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [mockFile] } });
    
    const topicSelect = screen.getByRole('combobox');
    fireEvent.click(topicSelect);
    fireEvent.click(screen.getByText('Security Fundamentals'));
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      expect(screen.getByText('Import Preview')).toBeInTheDocument();
    });
    
    // Cancel preview
    const backButton = screen.getByText('Back to Edit');
    fireEvent.click(backButton);
    
    expect(screen.queryByText('Import Preview')).not.toBeInTheDocument();
    expect(screen.getByText('Preview Import')).toBeInTheDocument();
  });

  it('calls onSuccess callback after successful import', async () => {
    const mockOnSuccess = vi.fn();
    const mockResult = {
      success: true,
      totalRows: 1,
      topicResults: new Map([
        ['topic1', {
          topicId: 'topic1',
          topicName: 'Test Topic',
          validQuestions: [{ 
            id: '1',
            topic_id: 'topic1',
            question_text: 'Test question',
            options: { A: 'A', B: 'B', C: 'C', D: 'D' },
            correct_answer: 'A',
            explanation: 'Test explanation',
            difficulty: 'medium',
            created_at: '2023-01-01',
            updated_at: '2023-01-01',
          }],
          errors: [],
          isNewTopic: false,
        }],
      ]),
      globalErrors: [],
      newTopicsCreated: [],
    };
    
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockResolvedValue(mockResult);
    
    // Mock successful Supabase insert
    const mockInsert = vi.fn().mockResolvedValue({ error: null });
    const mockFrom = vi.fn().mockReturnValue({ insert: mockInsert });
    vi.doMock('@/integrations/supabase/client', () => ({
      supabase: { from: mockFrom },
    }));
    
    render(<MultiTopicCSVImport topics={mockTopics} onSuccess={mockOnSuccess} />);
    
    // Add file and topic, then preview
    const fileInput = screen.getByLabelText('Upload CSV File');
    const mockFile = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [mockFile] } });
    
    const topicSelect = screen.getByRole('combobox');
    fireEvent.click(topicSelect);
    fireEvent.click(screen.getByText('Security Fundamentals'));
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      expect(screen.getByText('Confirm Import')).toBeInTheDocument();
    });
    
    // Confirm import
    const confirmButton = screen.getByText('Confirm Import');
    fireEvent.click(confirmButton);
    
    await waitFor(() => {
      expect(mockOnSuccess).toHaveBeenCalledWith(mockResult);
    });
  });

  it('calls onCancel callback when cancel button is clicked', () => {
    const mockOnCancel = vi.fn();
    
    render(<MultiTopicCSVImport topics={mockTopics} onCancel={mockOnCancel} />);
    
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);
    
    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('disables buttons during import process', async () => {
    const mockResult = {
      success: true,
      totalRows: 1,
      topicResults: new Map([
        ['topic1', {
          topicId: 'topic1',
          topicName: 'Test Topic',
          validQuestions: [{ id: '1' }],
          errors: [],
          isNewTopic: false,
        }],
      ]),
      globalErrors: [],
      newTopicsCreated: [],
    };
    
    // Mock a delayed response to test loading state
    (csvImportUtils.parseQuestionCSVEnhanced as any).mockImplementation(
      () => new Promise(resolve => setTimeout(() => resolve(mockResult), 100))
    );
    
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    // Add file and topic
    const fileInput = screen.getByLabelText('Upload CSV File');
    const mockFile = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    fireEvent.change(fileInput, { target: { files: [mockFile] } });
    
    const topicSelect = screen.getByRole('combobox');
    fireEvent.click(topicSelect);
    fireEvent.click(screen.getByText('Security Fundamentals'));
    
    const previewButton = screen.getByText('Preview Import');
    fireEvent.click(previewButton);
    
    // Check that buttons are disabled during processing
    expect(screen.getByText('Processing...')).toBeInTheDocument();
    expect(fileInput).toBeDisabled();
    
    await waitFor(() => {
      expect(screen.getByText('Confirm Import')).toBeInTheDocument();
    });
  });
});