/**
 * Quiz Performance Optimizer
 * Implements caching, query optimization, and performance monitoring
 * for the quiz randomization system
 */

import { supabase } from '@/integrations/supabase/client';
import type { Tables } from '@/types/supabase';

// Type definitions
export interface Question extends Tables<'questions'> {}

export interface RandomizedQuestion extends Question {
  originalCorrectIndex: number;
  shuffledCorrectIndex: number;
  optionMapping: number[];
  shuffledOptions: Record<string, string>;
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

interface QuestionPoolCache {
  [topicId: string]: CacheEntry<Question[]>;
}

interface PerformanceMetrics {
  operationName: string;
  duration: number;
  timestamp: number;
  success: boolean;
  errorMessage?: string;
}

/**
 * Quiz Performance Optimizer Class
 * Provides caching, optimization, and monitoring for quiz operations
 */
export class QuizPerformanceOptimizer {
  private static questionPoolCache: QuestionPoolCache = {};
  private static performanceMetrics: PerformanceMetrics[] = [];
  private static readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private static readonly MAX_METRICS = 1000; // Keep last 1000 metrics

  /**
   * Cached question pool retrieval with performance monitoring
   * @param topicId - ID of the topic
   * @returns Promise<Question[]> - Cached or fresh question pool
   */
  static async getCachedQuestionPool(topicId: string): Promise<Question[]> {
    const startTime = performance.now();
    let success = true;
    let errorMessage: string | undefined;

    try {
      // Check cache first
      const cacheKey = topicId;
      const cachedEntry = this.questionPoolCache[cacheKey];
      
      if (cachedEntry && this.isCacheValid(cachedEntry)) {
        console.log(`Cache hit for topic ${topicId}`);
        this.recordMetric('getCachedQuestionPool', performance.now() - startTime, true);
        return cachedEntry.data;
      }

      console.log(`Cache miss for topic ${topicId}, fetching from database`);

      // Fetch from database with optimized query
      const { data: questions, error } = await supabase
        .from('questions')
        .select('*')
        .eq('topic_id', topicId)
        .order('id'); // Consistent ordering for better caching

      if (error) {
        success = false;
        errorMessage = error.message;
        throw new Error(`Failed to fetch questions: ${error.message}`);
      }

      const questionPool = questions || [];

      // Cache the result
      this.questionPoolCache[cacheKey] = {
        data: questionPool,
        timestamp: Date.now(),
        ttl: this.CACHE_TTL
      };

      console.log(`Cached ${questionPool.length} questions for topic ${topicId}`);
      return questionPool;

    } catch (error) {
      success = false;
      errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw error;
    } finally {
      this.recordMetric('getCachedQuestionPool', performance.now() - startTime, success, errorMessage);
    }
  }

  /**
   * Optimized question selection with batch processing
   * @param topicId - ID of the topic
   * @param count - Number of questions to select
   * @param excludeIds - Question IDs to exclude
   * @returns Promise<Question[]> - Selected questions
   */
  static async selectOptimizedQuestions(
    topicId: string,
    count: number,
    excludeIds: string[] = []
  ): Promise<Question[]> {
    const startTime = performance.now();
    let success = true;
    let errorMessage: string | undefined;

    try {
      // Get cached question pool
      const questionPool = await this.getCachedQuestionPool(topicId);

      if (questionPool.length === 0) {
        throw new Error(`No questions available for topic ${topicId}`);
      }

      // Filter out excluded questions
      const availableQuestions = excludeIds.length > 0
        ? questionPool.filter(q => !excludeIds.includes(q.id))
        : questionPool;

      if (availableQuestions.length === 0) {
        throw new Error('No questions available after filtering');
      }

      // Optimized selection using Fisher-Yates shuffle
      const selectedCount = Math.min(count, availableQuestions.length);
      const selected = this.optimizedShuffle(availableQuestions, selectedCount);

      console.log(`Selected ${selected.length} questions from pool of ${availableQuestions.length}`);
      return selected;

    } catch (error) {
      success = false;
      errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw error;
    } finally {
      this.recordMetric('selectOptimizedQuestions', performance.now() - startTime, success, errorMessage);
    }
  }

  /**
   * Batch process multiple quiz sessions for concurrent users
   * @param requests - Array of quiz session requests
   * @returns Promise<Array> - Array of quiz session results
   */
  static async batchProcessQuizSessions(
    requests: Array<{
      topicId: string;
      userId: string;
      quizLength: number;
    }>
  ): Promise<Array<{ success: boolean; data?: any; error?: string }>> {
    const startTime = performance.now();
    
    try {
      console.log(`Processing ${requests.length} quiz session requests in batch`);

      // Group requests by topic for better cache utilization
      const requestsByTopic = new Map<string, typeof requests>();
      
      for (const request of requests) {
        if (!requestsByTopic.has(request.topicId)) {
          requestsByTopic.set(request.topicId, []);
        }
        requestsByTopic.get(request.topicId)!.push(request);
      }

      // Pre-warm cache for all topics
      const cachePromises = Array.from(requestsByTopic.keys()).map(topicId =>
        this.getCachedQuestionPool(topicId).catch(error => {
          console.warn(`Failed to pre-warm cache for topic ${topicId}:`, error);
          return [];
        })
      );

      await Promise.all(cachePromises);

      // Process requests with controlled concurrency
      const results = await this.processWithConcurrencyLimit(
        requests,
        async (request) => {
          try {
            const questions = await this.selectOptimizedQuestions(
              request.topicId,
              request.quizLength
            );

            return {
              success: true,
              data: {
                questions,
                sessionId: `batch_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
                topicId: request.topicId,
                userId: request.userId,
                quizLength: questions.length
              }
            };
          } catch (error) {
            return {
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            };
          }
        },
        5 // Max 5 concurrent operations
      );

      this.recordMetric('batchProcessQuizSessions', performance.now() - startTime, true);
      return results;

    } catch (error) {
      this.recordMetric('batchProcessQuizSessions', performance.now() - startTime, false, 
        error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  /**
   * Optimized shuffle algorithm that only shuffles the needed amount
   * @param array - Array to shuffle
   * @param count - Number of items needed
   * @returns Shuffled subset of the array
   */
  private static optimizedShuffle<T>(array: T[], count: number): T[] {
    const result = [...array];
    const shuffleCount = Math.min(count, array.length);

    // Only shuffle the first 'count' positions for better performance
    for (let i = 0; i < shuffleCount; i++) {
      const j = i + Math.floor(Math.random() * (result.length - i));
      [result[i], result[j]] = [result[j], result[i]];
    }

    return result.slice(0, shuffleCount);
  }

  /**
   * Process array with controlled concurrency
   * @param items - Items to process
   * @param processor - Processing function
   * @param concurrency - Max concurrent operations
   * @returns Promise<Array> - Results array
   */
  private static async processWithConcurrencyLimit<T, R>(
    items: T[],
    processor: (item: T) => Promise<R>,
    concurrency: number
  ): Promise<R[]> {
    const results: R[] = [];
    const executing: Promise<void>[] = [];

    for (let i = 0; i < items.length; i++) {
      const promise = processor(items[i]).then(result => {
        results[i] = result;
      });

      executing.push(promise);

      if (executing.length >= concurrency) {
        await Promise.race(executing);
        executing.splice(executing.findIndex(p => p === promise), 1);
      }
    }

    await Promise.all(executing);
    return results;
  }

  /**
   * Check if cache entry is still valid
   * @param entry - Cache entry to check
   * @returns boolean - Whether the cache entry is valid
   */
  private static isCacheValid<T>(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.timestamp < entry.ttl;
  }

  /**
   * Record performance metric
   * @param operationName - Name of the operation
   * @param duration - Duration in milliseconds
   * @param success - Whether the operation succeeded
   * @param errorMessage - Error message if failed
   */
  private static recordMetric(
    operationName: string,
    duration: number,
    success: boolean,
    errorMessage?: string
  ): void {
    const metric: PerformanceMetrics = {
      operationName,
      duration,
      timestamp: Date.now(),
      success,
      errorMessage
    };

    this.performanceMetrics.push(metric);

    // Keep only the last MAX_METRICS entries
    if (this.performanceMetrics.length > this.MAX_METRICS) {
      this.performanceMetrics.splice(0, this.performanceMetrics.length - this.MAX_METRICS);
    }

    // Log slow operations
    if (duration > 1000) { // More than 1 second
      console.warn(`Slow operation detected: ${operationName} took ${duration.toFixed(2)}ms`);
    }
  }

  /**
   * Get performance statistics
   * @param operationName - Optional operation name to filter by
   * @returns Performance statistics
   */
  static getPerformanceStats(operationName?: string) {
    const metrics = operationName
      ? this.performanceMetrics.filter(m => m.operationName === operationName)
      : this.performanceMetrics;

    if (metrics.length === 0) {
      return {
        totalOperations: 0,
        successRate: 0,
        averageDuration: 0,
        minDuration: 0,
        maxDuration: 0,
        recentErrors: []
      };
    }

    const successfulMetrics = metrics.filter(m => m.success);
    const durations = metrics.map(m => m.duration);
    const recentErrors = metrics
      .filter(m => !m.success && m.errorMessage)
      .slice(-10) // Last 10 errors
      .map(m => ({
        operation: m.operationName,
        error: m.errorMessage,
        timestamp: new Date(m.timestamp).toISOString()
      }));

    return {
      totalOperations: metrics.length,
      successRate: (successfulMetrics.length / metrics.length) * 100,
      averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      recentErrors
    };
  }

  /**
   * Clear all caches
   */
  static clearCache(): void {
    this.questionPoolCache = {};
    console.log('Question pool cache cleared');
  }

  /**
   * Clear performance metrics
   */
  static clearMetrics(): void {
    this.performanceMetrics = [];
    console.log('Performance metrics cleared');
  }

  /**
   * Get cache statistics
   * @returns Cache statistics
   */
  static getCacheStats() {
    const cacheKeys = Object.keys(this.questionPoolCache);
    const validEntries = cacheKeys.filter(key => 
      this.isCacheValid(this.questionPoolCache[key])
    );

    return {
      totalEntries: cacheKeys.length,
      validEntries: validEntries.length,
      hitRate: this.calculateCacheHitRate(),
      memoryUsage: this.estimateCacheMemoryUsage()
    };
  }

  /**
   * Calculate cache hit rate from recent metrics
   * @returns Cache hit rate percentage
   */
  private static calculateCacheHitRate(): number {
    const cacheMetrics = this.performanceMetrics
      .filter(m => m.operationName === 'getCachedQuestionPool')
      .slice(-100); // Last 100 cache operations

    if (cacheMetrics.length === 0) return 0;

    // Assume cache hits are faster operations (< 50ms)
    const cacheHits = cacheMetrics.filter(m => m.duration < 50).length;
    return (cacheHits / cacheMetrics.length) * 100;
  }

  /**
   * Estimate cache memory usage
   * @returns Estimated memory usage in KB
   */
  private static estimateCacheMemoryUsage(): number {
    const cacheString = JSON.stringify(this.questionPoolCache);
    return Math.round(cacheString.length / 1024); // Rough estimate in KB
  }
}

export default QuizPerformanceOptimizer;