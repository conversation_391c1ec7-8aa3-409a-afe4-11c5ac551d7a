import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://agdyycknlxojiwhlqicq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFnZHl5Y2tubHhvaml3aGxxaWNxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyMjkzOTgsImV4cCI6MjA1OTgwNTM5OH0.lWZLRByfsyRqkK7XZfi21qSeEuOZHJKkFJGC_2ojQR8';
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkFeedbackTableStructure() {
  console.log('Checking feedback table structure...');
  
  try {
    // Try to get table information using SQL
    const { data, error } = await supabase.rpc('execute_sql', {
      query: `
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_name = 'feedback' 
        AND table_schema = 'public'
        ORDER BY ordinal_position;
      `
    });
    
    if (error) {
      console.error('Error checking table structure:', error);
      
      // Try alternative approach - check if table exists
      console.log('Trying alternative approach...');
      const { data: tableCheck, error: tableError } = await supabase.rpc('execute_sql', {
        query: `
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'feedback'
          );
        `
      });
      
      if (tableError) {
        console.error('Error checking if table exists:', tableError);
      } else {
        console.log('Table exists check:', tableCheck);
      }
      
      return false;
    }
    
    console.log('Feedback table structure:', data);
    return true;
  } catch (e) {
    console.error('Exception during check:', e);
    return false;
  }
}

async function createFeedbackTableIfNeeded() {
  console.log('Creating feedback table...');
  
  try {
    const { data, error } = await supabase.rpc('execute_sql', {
      query: `
        CREATE TABLE IF NOT EXISTS public.feedback (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name TEXT NOT NULL,
          email TEXT NOT NULL,
          subject TEXT NOT NULL,
          message TEXT NOT NULL,
          user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          status TEXT DEFAULT 'new',
          created_at TIMESTAMPTZ DEFAULT now(),
          updated_at TIMESTAMPTZ DEFAULT now()
        );
        
        -- Enable Row Level Security
        ALTER TABLE public.feedback ENABLE ROW LEVEL SECURITY;
        
        -- Create policies for feedback
        DROP POLICY IF EXISTS "Anyone can submit feedback" ON public.feedback;
        CREATE POLICY "Anyone can submit feedback"
          ON public.feedback
          FOR INSERT
          WITH CHECK (true);
        
        DROP POLICY IF EXISTS "Admins can view all feedback" ON public.feedback;
        CREATE POLICY "Admins can view all feedback"
          ON public.feedback
          FOR SELECT
          TO authenticated
          USING (
            auth.uid() IN (
              SELECT id FROM auth.users WHERE raw_user_meta_data->>'is_admin' = 'true'
            )
          );
      `
    });
    
    if (error) {
      console.error('Error creating feedback table:', error);
      return false;
    }
    
    console.log('Feedback table created successfully');
    return true;
  } catch (e) {
    console.error('Exception creating table:', e);
    return false;
  }
}

async function main() {
  const structureExists = await checkFeedbackTableStructure();
  
  if (!structureExists) {
    console.log('Table structure not found, attempting to create...');
    await createFeedbackTableIfNeeded();
    
    // Check again
    await checkFeedbackTableStructure();
  }
}

main();
