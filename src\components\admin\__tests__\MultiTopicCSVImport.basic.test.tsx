import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { MultiTopicCSVImport } from '../MultiTopicCSVImport';
import { useToast } from '@/hooks/use-toast';

// Mock the toast hook
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(),
}));

// Mock the CSV import utilities
vi.mock('@/utils/csv-import', () => ({
  parseQuestionCSVEnhanced: vi.fn(),
  generateCSVTemplate: vi.fn(() => 'mock,csv,content'),
  generateMultiTopicCSVTemplate: vi.fn(() => 'topic_name,mock,csv,content'),
}));

// Mock Supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      insert: vi.fn(),
    })),
  },
}));

const mockToast = vi.fn();
const mockTopics = [
  { id: '1', title: 'Security Fundamentals' },
  { id: '2', title: 'Network Security' },
  { id: '3', title: 'Incident Response' },
];

describe('MultiTopicCSVImport - Basic Functionality', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useToast as any).mockReturnValue({ toast: mockToast });
  });

  it('renders with default single-topic mode', () => {
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    expect(screen.getByText('Import Questions from CSV')).toBeInTheDocument();
    expect(screen.getByText('Single Topic')).toBeInTheDocument();
    expect(screen.getByText('Multi Topic')).toBeInTheDocument();
    expect(screen.getByText('Select Topic')).toBeInTheDocument();
    expect(screen.getByText('Upload CSV File')).toBeInTheDocument();
  });

  it('switches between single-topic and multi-topic modes', () => {
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    // Initially in single-topic mode
    expect(screen.getByText('Select Topic')).toBeInTheDocument();
    expect(screen.queryByText('Auto-create Topics')).not.toBeInTheDocument();
    
    // Switch to multi-topic mode
    fireEvent.click(screen.getByText('Multi Topic'));
    
    expect(screen.getByText('Auto-create Topics')).toBeInTheDocument();
    expect(screen.queryByText('Select Topic')).not.toBeInTheDocument();
  });

  it('shows topic selection in single-topic mode', () => {
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    const topicSelect = screen.getByRole('combobox');
    expect(topicSelect).toBeInTheDocument();
    
    // Click to open dropdown
    fireEvent.click(topicSelect);
    
    // Check that topics are available
    expect(screen.getByText('Security Fundamentals')).toBeInTheDocument();
    expect(screen.getByText('Network Security')).toBeInTheDocument();
    expect(screen.getByText('Incident Response')).toBeInTheDocument();
  });

  it('shows auto-create toggle in multi-topic mode', () => {
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    // Switch to multi-topic mode
    fireEvent.click(screen.getByText('Multi Topic'));
    
    const autoCreateSwitch = screen.getByRole('switch');
    expect(autoCreateSwitch).toBeInTheDocument();
    expect(autoCreateSwitch).not.toBeChecked();
    
    // Toggle the switch
    fireEvent.click(autoCreateSwitch);
    expect(autoCreateSwitch).toBeChecked();
  });

  it('handles file selection', () => {
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    const fileInput = screen.getByLabelText('Upload CSV File');
    const mockFile = new File(['test,csv,content'], 'test.csv', { type: 'text/csv' });
    
    fireEvent.change(fileInput, { target: { files: [mockFile] } });
    
    expect(fileInput.files?.[0]).toBe(mockFile);
  });

  it('calls onCancel callback when cancel button is clicked', () => {
    const mockOnCancel = vi.fn();
    
    render(<MultiTopicCSVImport topics={mockTopics} onCancel={mockOnCancel} />);
    
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);
    
    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('shows correct description text for each mode', () => {
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    // Single-topic mode description
    expect(screen.getByText('Import all questions to a single selected topic')).toBeInTheDocument();
    
    // Switch to multi-topic mode
    fireEvent.click(screen.getByText('Multi Topic'));
    
    // Multi-topic mode description
    expect(screen.getByText('Import questions to multiple topics specified in the CSV file')).toBeInTheDocument();
  });

  it('shows correct CSV format instructions for each mode', () => {
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    // Single-topic mode instructions
    expect(screen.getByText(/CSV must include: question_text, option_a, option_b, option_c, option_d, correct_answer, explanation, and optional difficulty\./)).toBeInTheDocument();
    
    // Switch to multi-topic mode
    fireEvent.click(screen.getByText('Multi Topic'));
    
    // Multi-topic mode instructions - check for the new format
    expect(screen.getByText('Multi-topic CSV formats supported:')).toBeInTheDocument();
    expect(screen.getByText(/Name-based:/)).toBeInTheDocument();
    expect(screen.getByText(/ID-based:/)).toBeInTheDocument();
    expect(screen.getByText(/Both columns:/)).toBeInTheDocument();
    expect(screen.getByText(/Required columns: question_text, option_a, option_b, option_c, option_d, correct_answer, explanation, and optional difficulty\./)).toBeInTheDocument();
  });

  it('shows auto-create topics description', () => {
    render(<MultiTopicCSVImport topics={mockTopics} />);
    
    // Switch to multi-topic mode
    fireEvent.click(screen.getByText('Multi Topic'));
    
    // Initially shows "only import" description
    expect(screen.getByText('Only import questions for existing topics')).toBeInTheDocument();
    
    // Toggle auto-create
    const autoCreateSwitch = screen.getByRole('switch');
    fireEvent.click(autoCreateSwitch);
    
    // Now shows "automatically create" description
    expect(screen.getByText('Automatically create new topics if they don\'t exist')).toBeInTheDocument();
  });
});