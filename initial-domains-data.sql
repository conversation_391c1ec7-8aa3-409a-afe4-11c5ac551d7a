-- Initial Domain Data for Multi-Domain Cybersecurity Platform

-- Insert cybersecurity domains (with existence checking)
INSERT INTO public.domains (name, slug, description, icon, color_theme, difficulty_level, estimated_duration_weeks, prerequisites, sort_order)
SELECT 'Cybersecurity Foundations', 'cybersecurity-foundations', 'Fundamental cybersecurity concepts, principles, and basic security practices', 'shield', '#6366F1', 'beginner', 4, '{}', 1
WHERE NOT EXISTS (SELECT 1 FROM public.domains WHERE name = 'Cybersecurity Foundations');

INSERT INTO public.domains (name, slug, description, icon, color_theme, difficulty_level, estimated_duration_weeks, prerequisites, sort_order)
SELECT 'Network Security', 'network-security', 'Comprehensive network security concepts, protocols, and defense mechanisms', 'network', '#3B82F6', 'intermediate', 6, '{}', 2
WHERE NOT EXISTS (SELECT 1 FROM public.domains WHERE name = 'Network Security');

INSERT INTO public.domains (name, slug, description, icon, color_theme, difficulty_level, estimated_duration_weeks, prerequisites, sort_order)
SELECT 'Cloud Security', 'cloud-security', 'Cloud computing security, AWS/Azure/GCP security, and cloud-native protection', 'cloud', '#10B981', 'intermediate', 8, '{"network-security"}', 3
WHERE NOT EXISTS (SELECT 1 FROM public.domains WHERE name = 'Cloud Security');

INSERT INTO public.domains (name, slug, description, icon, color_theme, difficulty_level, estimated_duration_weeks, prerequisites, sort_order)
SELECT 'Incident Response', 'incident-response', 'Cybersecurity incident handling, forensics, and emergency response procedures', 'alert-triangle', '#EF4444', 'advanced', 4, '{"network-security"}', 4
WHERE NOT EXISTS (SELECT 1 FROM public.domains WHERE name = 'Incident Response');

INSERT INTO public.domains (name, slug, description, icon, color_theme, difficulty_level, estimated_duration_weeks, prerequisites, sort_order)
SELECT 'Ethical Hacking', 'ethical-hacking', 'Ethical hacking, vulnerability assessment, and penetration testing methodologies', 'shield-check', '#8B5CF6', 'advanced', 10, '{"network-security", "incident-response"}', 5
WHERE NOT EXISTS (SELECT 1 FROM public.domains WHERE name = 'Ethical Hacking');

INSERT INTO public.domains (name, slug, description, icon, color_theme, difficulty_level, estimated_duration_weeks, prerequisites, sort_order)
SELECT 'Governance & Compliance', 'governance-compliance', 'Cybersecurity frameworks, compliance standards, and risk management', 'file-text', '#F59E0B', 'intermediate', 6, '{}', 6
WHERE NOT EXISTS (SELECT 1 FROM public.domains WHERE name = 'Governance & Compliance');

INSERT INTO public.domains (name, slug, description, icon, color_theme, difficulty_level, estimated_duration_weeks, prerequisites, sort_order)
SELECT 'Cryptography', 'cryptography', 'Encryption, digital signatures, PKI, and cryptographic protocols', 'key', '#EC4899', 'advanced', 8, '{"network-security"}', 7
WHERE NOT EXISTS (SELECT 1 FROM public.domains WHERE name = 'Cryptography');

INSERT INTO public.domains (name, slug, description, icon, color_theme, difficulty_level, estimated_duration_weeks, prerequisites, sort_order)
SELECT 'Security Awareness', 'security-awareness', 'Basic cybersecurity concepts and best practices for all users', 'users', '#06B6D4', 'beginner', 2, '{}', 8
WHERE NOT EXISTS (SELECT 1 FROM public.domains WHERE name = 'Security Awareness');

INSERT INTO public.domains (name, slug, description, icon, color_theme, difficulty_level, estimated_duration_weeks, prerequisites, sort_order)
SELECT 'CISSP Preparation', 'cissp-preparation', 'Comprehensive CISSP certification preparation and study materials', 'award', '#7C3AED', 'expert', 12, '{"network-security", "governance-compliance", "cryptography"}', 9
WHERE NOT EXISTS (SELECT 1 FROM public.domains WHERE name = 'CISSP Preparation');

INSERT INTO public.domains (name, slug, description, icon, color_theme, difficulty_level, estimated_duration_weeks, prerequisites, sort_order)
SELECT 'Digital Forensics', 'digital-forensics', 'Digital evidence collection, analysis, and forensic investigation techniques', 'search', '#DC2626', 'advanced', 8, '{"incident-response"}', 10
WHERE NOT EXISTS (SELECT 1 FROM public.domains WHERE name = 'Digital Forensics');

INSERT INTO public.domains (name, slug, description, icon, color_theme, difficulty_level, estimated_duration_weeks, prerequisites, sort_order)
SELECT 'Application Security', 'application-security', 'Secure coding, OWASP Top 10, and application vulnerability management', 'code', '#059669', 'intermediate', 6, '{"network-security"}', 11
WHERE NOT EXISTS (SELECT 1 FROM public.domains WHERE name = 'Application Security');

-- Create domain-specific subscription plans (with existence checking)
INSERT INTO public.domain_subscription_plans (domain_id, plan_id, name, amount, interval, features)
SELECT d.id, 'domain-cybersecurity-foundations', 'Cybersecurity Foundations Focus', 49900, 'weekly', 
 ARRAY['Cybersecurity fundamentals content', 'Security awareness materials', 'CISSP preparation content', 'Basic security concepts']
FROM public.domains d
WHERE d.slug = 'cybersecurity-foundations'
  AND NOT EXISTS (SELECT 1 FROM public.domain_subscription_plans WHERE plan_id = 'domain-cybersecurity-foundations');

INSERT INTO public.domain_subscription_plans (domain_id, plan_id, name, amount, interval, features)
SELECT d.id, 'domain-network-security', 'Network Security Focus', 59900, 'weekly', 
 ARRAY['Network security content only', 'Unlimited network security questions', 'Network security labs', 'Firewall configuration guides']
FROM public.domains d
WHERE d.slug = 'network-security'
  AND NOT EXISTS (SELECT 1 FROM public.domain_subscription_plans WHERE plan_id = 'domain-network-security');

INSERT INTO public.domain_subscription_plans (domain_id, plan_id, name, amount, interval, features)
SELECT d.id, 'domain-cloud-security', 'Cloud Security Focus', 69900, 'weekly',
 ARRAY['Cloud security content only', 'AWS/Azure/GCP security', 'Cloud compliance guides', 'Hands-on cloud labs']
FROM public.domains d
WHERE d.slug = 'cloud-security'
  AND NOT EXISTS (SELECT 1 FROM public.domain_subscription_plans WHERE plan_id = 'domain-cloud-security');

INSERT INTO public.domain_subscription_plans (domain_id, plan_id, name, amount, interval, features)
SELECT d.id, 'domain-incident-response', 'Incident Response Focus', 64900, 'weekly',
 ARRAY['Incident response content only', 'IR playbooks and procedures', 'Forensics fundamentals', 'Case study scenarios']
FROM public.domains d
WHERE d.slug = 'incident-response'
  AND NOT EXISTS (SELECT 1 FROM public.domain_subscription_plans WHERE plan_id = 'domain-incident-response');

INSERT INTO public.domain_subscription_plans (domain_id, plan_id, name, amount, interval, features)
SELECT d.id, 'domain-ethical-hacking', 'Ethical Hacking Focus', 79900, 'weekly',
 ARRAY['Ethical hacking content only', 'Penetration testing techniques', 'Tool tutorials and labs', 'Real-world scenarios']
FROM public.domains d
WHERE d.slug = 'ethical-hacking'
  AND NOT EXISTS (SELECT 1 FROM public.domain_subscription_plans WHERE plan_id = 'domain-ethical-hacking');

INSERT INTO public.domain_subscription_plans (domain_id, plan_id, name, amount, interval, features)
SELECT d.id, 'domain-governance-compliance', 'Governance & Compliance Focus', 59900, 'weekly',
 ARRAY['GRC content only', 'Framework comparisons', 'Compliance checklists', 'Risk assessment tools']
FROM public.domains d
WHERE d.slug = 'governance-compliance'
  AND NOT EXISTS (SELECT 1 FROM public.domain_subscription_plans WHERE plan_id = 'domain-governance-compliance');

INSERT INTO public.domain_subscription_plans (domain_id, plan_id, name, amount, interval, features)
SELECT d.id, 'domain-cryptography', 'Cryptography Focus', 64900, 'weekly',
 ARRAY['Cryptography content only', 'Encryption algorithms', 'PKI implementation', 'Cryptographic protocols']
FROM public.domains d
WHERE d.slug = 'cryptography'
  AND NOT EXISTS (SELECT 1 FROM public.domain_subscription_plans WHERE plan_id = 'domain-cryptography');

INSERT INTO public.domain_subscription_plans (domain_id, plan_id, name, amount, interval, features)
SELECT d.id, 'domain-security-awareness', 'Security Awareness Focus', 39900, 'weekly',
 ARRAY['Security awareness content', 'Basic cybersecurity concepts', 'Beginner-friendly materials', 'Interactive scenarios']
FROM public.domains d
WHERE d.slug = 'security-awareness'
  AND NOT EXISTS (SELECT 1 FROM public.domain_subscription_plans WHERE plan_id = 'domain-security-awareness');

INSERT INTO public.domain_subscription_plans (domain_id, plan_id, name, amount, interval, features)
SELECT d.id, 'domain-cissp-preparation', 'CISSP Preparation Focus', 79900, 'weekly',
 ARRAY['CISSP-specific content only', 'All 8 CISSP domains', 'Practice exams', 'Study roadmap']
FROM public.domains d
WHERE d.slug = 'cissp-preparation'
  AND NOT EXISTS (SELECT 1 FROM public.domain_subscription_plans WHERE plan_id = 'domain-cissp-preparation');

INSERT INTO public.domain_subscription_plans (domain_id, plan_id, name, amount, interval, features)
SELECT d.id, 'domain-digital-forensics', 'Digital Forensics Focus', 69900, 'weekly',
 ARRAY['Digital forensics content', 'Evidence collection techniques', 'Forensic tools training', 'Legal considerations']
FROM public.domains d
WHERE d.slug = 'digital-forensics'
  AND NOT EXISTS (SELECT 1 FROM public.domain_subscription_plans WHERE plan_id = 'domain-digital-forensics');

INSERT INTO public.domain_subscription_plans (domain_id, plan_id, name, amount, interval, features)
SELECT d.id, 'domain-application-security', 'Application Security Focus', 64900, 'weekly',
 ARRAY['Application security content', 'OWASP Top 10 coverage', 'Secure coding practices', 'Code review techniques']
FROM public.domains d
WHERE d.slug = 'application-security'
  AND NOT EXISTS (SELECT 1 FROM public.domain_subscription_plans WHERE plan_id = 'domain-application-security');

-- Create learning paths for each domain (with existence checking)
INSERT INTO public.domain_learning_paths (domain_id, name, description, difficulty_level, estimated_hours, sort_order)
SELECT d.id, 'Security Fundamentals', 'Basic cybersecurity concepts and principles', 'beginner', 20, 1
FROM public.domains d
WHERE d.slug = 'cybersecurity-foundations'
  AND NOT EXISTS (SELECT 1 FROM public.domain_learning_paths WHERE domain_id = d.id AND name = 'Security Fundamentals');

INSERT INTO public.domain_learning_paths (domain_id, name, description, difficulty_level, estimated_hours, sort_order)
SELECT d.id, 'Security Awareness & Best Practices', 'Security awareness and best practices for everyone', 'beginner', 15, 2
FROM public.domains d
WHERE d.slug = 'cybersecurity-foundations'
  AND NOT EXISTS (SELECT 1 FROM public.domain_learning_paths WHERE domain_id = d.id AND name = 'Security Awareness & Best Practices');

INSERT INTO public.domain_learning_paths (domain_id, name, description, difficulty_level, estimated_hours, sort_order)
SELECT d.id, 'CISSP Preparation', 'CISSP certification preparation materials', 'advanced', 60, 3
FROM public.domains d
WHERE d.slug = 'cybersecurity-foundations'
  AND NOT EXISTS (SELECT 1 FROM public.domain_learning_paths WHERE domain_id = d.id AND name = 'CISSP Preparation');

INSERT INTO public.domain_learning_paths (domain_id, name, description, difficulty_level, estimated_hours, sort_order)
SELECT d.id, 'Network Fundamentals', 'Basic networking and security concepts', 'beginner', 20, 1
FROM public.domains d
WHERE d.slug = 'network-security'
  AND NOT EXISTS (SELECT 1 FROM public.domain_learning_paths WHERE domain_id = d.id AND name = 'Network Fundamentals');

INSERT INTO public.domain_learning_paths (domain_id, name, description, difficulty_level, estimated_hours, sort_order)
SELECT d.id, 'Advanced Network Defense', 'Advanced network security techniques', 'advanced', 30, 2
FROM public.domains d
WHERE d.slug = 'network-security'
  AND NOT EXISTS (SELECT 1 FROM public.domain_learning_paths WHERE domain_id = d.id AND name = 'Advanced Network Defense');

INSERT INTO public.domain_learning_paths (domain_id, name, description, difficulty_level, estimated_hours, sort_order)
SELECT d.id, 'Cloud Security Basics', 'Introduction to cloud security concepts', 'beginner', 15, 1
FROM public.domains d
WHERE d.slug = 'cloud-security'
  AND NOT EXISTS (SELECT 1 FROM public.domain_learning_paths WHERE domain_id = d.id AND name = 'Cloud Security Basics');

INSERT INTO public.domain_learning_paths (domain_id, name, description, difficulty_level, estimated_hours, sort_order)
SELECT d.id, 'Multi-Cloud Security', 'Advanced multi-cloud security strategies', 'advanced', 35, 2
FROM public.domains d
WHERE d.slug = 'cloud-security'
  AND NOT EXISTS (SELECT 1 FROM public.domain_learning_paths WHERE domain_id = d.id AND name = 'Multi-Cloud Security');

INSERT INTO public.domain_learning_paths (domain_id, name, description, difficulty_level, estimated_hours, sort_order)
SELECT d.id, 'IR Fundamentals', 'Basic incident response procedures', 'intermediate', 25, 1
FROM public.domains d
WHERE d.slug = 'incident-response'
  AND NOT EXISTS (SELECT 1 FROM public.domain_learning_paths WHERE domain_id = d.id AND name = 'IR Fundamentals');

INSERT INTO public.domain_learning_paths (domain_id, name, description, difficulty_level, estimated_hours, sort_order)
SELECT d.id, 'Advanced IR & Forensics', 'Advanced incident response and forensics', 'advanced', 40, 2
FROM public.domains d
WHERE d.slug = 'incident-response'
  AND NOT EXISTS (SELECT 1 FROM public.domain_learning_paths WHERE domain_id = d.id AND name = 'Advanced IR & Forensics');

INSERT INTO public.domain_learning_paths (domain_id, name, description, difficulty_level, estimated_hours, sort_order)
SELECT d.id, 'Ethical Hacking Basics', 'Introduction to ethical hacking', 'intermediate', 30, 1
FROM public.domains d
WHERE d.slug = 'ethical-hacking'
  AND NOT EXISTS (SELECT 1 FROM public.domain_learning_paths WHERE domain_id = d.id AND name = 'Ethical Hacking Basics');

INSERT INTO public.domain_learning_paths (domain_id, name, description, difficulty_level, estimated_hours, sort_order)
SELECT d.id, 'Advanced Ethical Hacking', 'Advanced penetration testing techniques', 'advanced', 50, 2
FROM public.domains d
WHERE d.slug = 'ethical-hacking'
  AND NOT EXISTS (SELECT 1 FROM public.domain_learning_paths WHERE domain_id = d.id AND name = 'Advanced Ethical Hacking');

INSERT INTO public.domain_learning_paths (domain_id, name, description, difficulty_level, estimated_hours, sort_order)
SELECT d.id, 'Cybersecurity Basics', 'Essential cybersecurity knowledge for everyone', 'beginner', 10, 1
FROM public.domains d
WHERE d.slug = 'security-awareness'
  AND NOT EXISTS (SELECT 1 FROM public.domain_learning_paths WHERE domain_id = d.id AND name = 'Cybersecurity Basics');

INSERT INTO public.domain_learning_paths (domain_id, name, description, difficulty_level, estimated_hours, sort_order)
SELECT d.id, 'CISSP Domain 1-4', 'First half of CISSP domains', 'advanced', 60, 1
FROM public.domains d
WHERE d.slug = 'cissp-preparation'
  AND NOT EXISTS (SELECT 1 FROM public.domain_learning_paths WHERE domain_id = d.id AND name = 'CISSP Domain 1-4');

INSERT INTO public.domain_learning_paths (domain_id, name, description, difficulty_level, estimated_hours, sort_order)
SELECT d.id, 'CISSP Domain 5-8', 'Second half of CISSP domains', 'advanced', 60, 2
FROM public.domains d
WHERE d.slug = 'cissp-preparation'
  AND NOT EXISTS (SELECT 1 FROM public.domain_learning_paths WHERE domain_id = d.id AND name = 'CISSP Domain 5-8');
