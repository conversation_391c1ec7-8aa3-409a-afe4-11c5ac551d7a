// Script to preview email templates
// Note: This script is now deprecated as email templates are managed by Supabase with Resend

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import open from 'open';

// Set up __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Create preview directory if it doesn't exist
const previewDir = path.resolve(__dirname, '../email-previews');
if (!fs.existsSync(previewDir)) {
  fs.mkdirSync(previewDir);
}

// This function is now deprecated as email templates are managed by Supabase with Resend
function extractEmailTemplates() {
  console.log('Email templates are now managed by Supabase with Resend');
  
  return {
    verification: null,
    passwordReset: null
  };
}

// This function is now deprecated as email templates are managed by Supabase with Resend
function saveTemplatePreviews(templates) {
  console.log('Email templates are now managed by Supabase with Resend');
  
  // Create placeholder files with information
  fs.writeFileSync(
    path.resolve(previewDir, 'email-templates-info.html'),
    `<html>
      <body>
        <h1>Email Templates Information</h1>
        <p>Email templates are now managed by Supabase with Resend.</p>
        <p>To customize email templates, please refer to the Supabase dashboard.</p>
      </body>
    </html>`
  );
}

// Open info in browser
async function openPreviews() {
  const infoPath = path.resolve(previewDir, 'email-templates-info.html');
  
  if (fs.existsSync(infoPath)) {
    console.log('Opening email templates information...');
    await open(infoPath);
  }
}

// Main function
async function main() {
  console.log('Email templates are now managed by Supabase with Resend');
  const templates = extractEmailTemplates();
  
  console.log('Creating information file...');
  saveTemplatePreviews(templates);
  
  console.log('Opening information in browser...');
  await openPreviews();
  
  console.log('Done!');
}

main();
