/**
 * Tests for Question Pool Alerts Component
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import QuestionPoolAlerts from '../QuestionPoolAlerts';
import QuestionPoolAnalyticsService from '@/services/question-pool-analytics-service';

// Mock the analytics service
vi.mock('@/services/question-pool-analytics-service');

// Mock the supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => Promise.resolve({
        data: [
          { id: 'topic-1', title: 'Test Topic 1' },
          { id: 'topic-2', title: 'Test Topic 2' }
        ],
        error: null
      }))
    }))
  }
}));

// Mock the toast hook
const mockToast = vi.fn();
vi.mock('@/components/ui/use-toast', () => ({
  useToast: () => ({ toast: mockToast })
}));

const mockAnalyticsService = vi.mocked(QuestionPoolAnalyticsService);

describe('QuestionPoolAlerts', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock topic insights with alerts
    mockAnalyticsService.getTopicInsights.mockResolvedValue({
      topicId: 'topic-1',
      topicTitle: 'Test Topic',
      stats: {
        total_questions: 15,
        avg_usage_count: 3.2,
        avg_correct_rate: 45,
        questions_never_used: 8,
        questions_low_performance: 5
      },
      questionVariety: 'insufficient',
      alerts: [
        {
          type: 'insufficient_questions',
          severity: 'high',
          message: 'Topic has only 15 questions. Minimum 20 recommended for good randomization.',
          count: 15
        }
      ],
      recommendations: []
    });
  });

  it('should render alerts component', async () => {
    render(<QuestionPoolAlerts />);

    await waitFor(() => {
      expect(screen.getByText('Question Pool Alerts')).toBeInTheDocument();
    });
  });

  it('should display alert settings', async () => {
    render(<QuestionPoolAlerts />);

    await waitFor(() => {
      expect(screen.getByText('Alert Thresholds')).toBeInTheDocument();
    });

    expect(screen.getByText('Min Questions per Topic')).toBeInTheDocument();
    expect(screen.getByText('Min Correct Rate (%)')).toBeInTheDocument();
    expect(screen.getByText('Max Unused (%)')).toBeInTheDocument();
  });

  it('should show healthy state when no alerts', async () => {
    // Mock no alerts
    mockAnalyticsService.getTopicInsights.mockResolvedValue({
      topicId: 'topic-1',
      topicTitle: 'Healthy Topic',
      stats: {
        total_questions: 50,
        avg_usage_count: 8.2,
        avg_correct_rate: 75,
        questions_never_used: 2,
        questions_low_performance: 1
      },
      questionVariety: 'excellent',
      alerts: [],
      recommendations: []
    });

    render(<QuestionPoolAlerts />);

    await waitFor(() => {
      expect(screen.getByText('No alerts at this time. All topics are healthy!')).toBeInTheDocument();
    });
  });

  it('should handle service errors gracefully', async () => {
    mockAnalyticsService.getTopicInsights.mockRejectedValue(
      new Error('Service error')
    );

    render(<QuestionPoolAlerts />);

    // Component should still render even with service errors
    await waitFor(() => {
      expect(screen.getByText('Question Pool Alerts')).toBeInTheDocument();
    });

    // Should show healthy state when no alerts can be loaded
    expect(screen.getByText('No alerts at this time. All topics are healthy!')).toBeInTheDocument();
  });
});