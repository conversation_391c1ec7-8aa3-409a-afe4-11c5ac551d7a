/**
 * Test script to verify quiz randomization service is working
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testRandomizationService() {
  console.log('Testing quiz randomization service...');

  try {
    // Test 1: Check if tables exist
    console.log('\n1. Checking if tables exist...');
    
    const { data: sessionsTest, error: sessionsError } = await supabase
      .from('quiz_sessions')
      .select('id')
      .limit(1);

    if (sessionsError) {
      console.log('❌ quiz_sessions table issue:', sessionsError.message);
    } else {
      console.log('✅ quiz_sessions table is accessible');
    }

    const { data: analyticsTest, error: analyticsError } = await supabase
      .from('question_analytics')
      .select('id')
      .limit(1);

    if (analyticsError) {
      console.log('❌ question_analytics table issue:', analyticsError.message);
    } else {
      console.log('✅ question_analytics table is accessible');
    }

    // Test 2: Get a sample topic with questions
    console.log('\n2. Finding a topic with questions...');
    
    const { data: topics, error: topicsError } = await supabase
      .from('topics')
      .select('id, title')
      .limit(5);

    if (topicsError) {
      console.log('❌ Error fetching topics:', topicsError.message);
      return;
    }

    if (!topics || topics.length === 0) {
      console.log('❌ No topics found');
      return;
    }

    console.log('Available topics:', topics.map(t => `${t.title} (${t.id})`));

    // Find a topic with questions
    let testTopic = null;
    for (const topic of topics) {
      const { data: questions, error: questionsError } = await supabase
        .from('questions')
        .select('id')
        .eq('topic_id', topic.id)
        .limit(1);

      if (!questionsError && questions && questions.length > 0) {
        testTopic = topic;
        break;
      }
    }

    if (!testTopic) {
      console.log('❌ No topics with questions found');
      return;
    }

    console.log(`✅ Using topic: ${testTopic.title} (${testTopic.id})`);

    // Test 3: Try to create a quiz session
    console.log('\n3. Testing quiz session creation...');
    
    const testUserId = 'test_user_' + Date.now();
    const sessionData = {
      user_id: testUserId,
      topic_id: testTopic.id,
      questions_data: {
        questions: [
          {
            id: 'test_question_1',
            originalCorrectIndex: 0,
            shuffledCorrectIndex: 1,
            optionMapping: [1, 0, 2, 3]
          }
        ],
        metadata: {
          totalQuestions: 1,
          topicId: testTopic.id,
          createdAt: new Date().toISOString()
        }
      },
      quiz_length: 1,
      total_questions: 1
    };

    const { data: sessionResult, error: sessionError } = await supabase
      .from('quiz_sessions')
      .insert(sessionData)
      .select()
      .single();

    if (sessionError) {
      console.log('❌ Error creating quiz session:', sessionError.message);
    } else {
      console.log('✅ Quiz session created successfully:', sessionResult.id);

      // Test 4: Try to record analytics
      console.log('\n4. Testing question analytics...');
      
      const analyticsData = {
        question_id: 'test_question_1',
        user_id: testUserId,
        quiz_session_id: sessionResult.id,
        answered_correctly: true,
        selected_option: 1,
        time_to_answer: 30
      };

      const { data: analyticsResult, error: analyticsInsertError } = await supabase
        .from('question_analytics')
        .insert(analyticsData)
        .select()
        .single();

      if (analyticsInsertError) {
        console.log('❌ Error recording analytics:', analyticsInsertError.message);
      } else {
        console.log('✅ Question analytics recorded successfully:', analyticsResult.id);
      }

      // Clean up test data
      console.log('\n5. Cleaning up test data...');
      
      await supabase.from('question_analytics').delete().eq('user_id', testUserId);
      await supabase.from('quiz_sessions').delete().eq('user_id', testUserId);
      
      console.log('✅ Test data cleaned up');
    }

    console.log('\n🎉 Quiz randomization service test completed!');
    console.log('The advanced randomization service should now be working properly.');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testRandomizationService();