/**
 * User Preferences Service Tests
 * Tests for user preferences including default quiz length
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { UserPreferencesService, DEFAULT_PREFERENCES } from '../user-preferences-service';
import type { User } from '@supabase/supabase-js';

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn()
        }))
      })),
      upsert: vi.fn()
    }))
  }
}));

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

describe('UserPreferencesService', () => {
  const mockUser: User = {
    id: 'test-user-id',
    email: '<EMAIL>',
    aud: 'authenticated',
    role: 'authenticated',
    created_at: '2023-01-01T00:00:00Z',
    app_metadata: {},
    user_metadata: {}
  };

  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('getUserPreferences', () => {
    it('should return default preferences for guest users', async () => {
      const preferences = await UserPreferencesService.getUserPreferences(null);
      expect(preferences).toEqual(DEFAULT_PREFERENCES);
    });

    it('should return preferences from localStorage for guest users', async () => {
      const storedPrefs = { defaultQuizLength: 20, autoStartQuiz: true };
      localStorageMock.getItem.mockReturnValue(JSON.stringify(storedPrefs));

      const preferences = await UserPreferencesService.getUserPreferences(null);
      expect(preferences).toEqual({ ...DEFAULT_PREFERENCES, ...storedPrefs });
    });

    it('should return preferences from database for authenticated users', async () => {
      const { supabase } = await import('@/integrations/supabase/client');
      const dbPrefs = { defaultQuizLength: 25, showQuizInstructions: false };
      
      vi.mocked(supabase.from).mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: {
                setting_key: 'quiz_preferences_test-user-id',
                setting_value: JSON.stringify(dbPrefs)
              },
              error: null
            })
          })
        })
      } as any);

      const preferences = await UserPreferencesService.getUserPreferences(mockUser);
      expect(preferences).toEqual({ ...DEFAULT_PREFERENCES, ...dbPrefs });
    });

    it('should fallback to localStorage if database query fails', async () => {
      const { supabase } = await import('@/integrations/supabase/client');
      const storedPrefs = { defaultQuizLength: 20 };
      localStorageMock.getItem.mockReturnValue(JSON.stringify(storedPrefs));
      
      vi.mocked(supabase.from).mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: null,
              error: new Error('Database error')
            })
          })
        })
      } as any);

      const preferences = await UserPreferencesService.getUserPreferences(mockUser);
      expect(preferences).toEqual({ ...DEFAULT_PREFERENCES, ...storedPrefs });
    });
  });

  describe('saveUserPreferences', () => {
    it('should save preferences to localStorage for guest users', async () => {
      const preferences = { defaultQuizLength: 20 };
      const result = await UserPreferencesService.saveUserPreferences(null, preferences);

      expect(result).toBe(true);
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'quiz_preferences',
        JSON.stringify({ ...DEFAULT_PREFERENCES, ...preferences })
      );
    });

    it('should save preferences to database for authenticated users', async () => {
      const { supabase } = await import('@/integrations/supabase/client');
      const preferences = { defaultQuizLength: 25 };
      
      const mockUpsert = vi.fn().mockResolvedValue({ error: null });
      vi.mocked(supabase.from).mockReturnValue({
        upsert: mockUpsert
      } as any);

      const result = await UserPreferencesService.saveUserPreferences(mockUser, preferences);

      expect(result).toBe(true);
      expect(supabase.from).toHaveBeenCalledWith('settings');
      expect(mockUpsert).toHaveBeenCalledWith({
        setting_key: 'quiz_preferences_test-user-id',
        setting_value: JSON.stringify({ ...DEFAULT_PREFERENCES, ...preferences }),
        description: 'User quiz preferences',
        is_public: false
      });
    });

    it('should fallback to localStorage if database save fails', async () => {
      const { supabase } = await import('@/integrations/supabase/client');
      const preferences = { defaultQuizLength: 25 };
      
      const mockUpsert = vi.fn().mockResolvedValue({ error: new Error('Database error') });
      vi.mocked(supabase.from).mockReturnValue({
        upsert: mockUpsert
      } as any);

      const result = await UserPreferencesService.saveUserPreferences(mockUser, preferences);

      expect(result).toBe(false);
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'quiz_preferences',
        JSON.stringify({ ...DEFAULT_PREFERENCES, ...preferences })
      );
    });
  });

  describe('getDefaultQuizLength', () => {
    it('should return default quiz length from preferences', async () => {
      const storedPrefs = { defaultQuizLength: 20 };
      localStorageMock.getItem.mockReturnValue(JSON.stringify(storedPrefs));

      const length = await UserPreferencesService.getDefaultQuizLength(null);
      expect(length).toBe(20);
    });

    it('should return default value if preferences not found', async () => {
      const length = await UserPreferencesService.getDefaultQuizLength(null);
      expect(length).toBe(DEFAULT_PREFERENCES.defaultQuizLength);
    });
  });

  describe('setDefaultQuizLength', () => {
    it('should set default quiz length', async () => {
      localStorageMock.getItem.mockReturnValue(JSON.stringify(DEFAULT_PREFERENCES));

      const result = await UserPreferencesService.setDefaultQuizLength(null, 20);
      expect(result).toBe(true);
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'quiz_preferences',
        JSON.stringify({ ...DEFAULT_PREFERENCES, defaultQuizLength: 20 })
      );
    });

    it('should reject invalid quiz lengths', async () => {
      const result1 = await UserPreferencesService.setDefaultQuizLength(null, 0);
      const result2 = await UserPreferencesService.setDefaultQuizLength(null, 100);

      expect(result1).toBe(false);
      expect(result2).toBe(false);
    });
  });

  describe('validateQuizLength', () => {
    it('should validate quiz length against available questions', () => {
      const result1 = UserPreferencesService.validateQuizLength(15, 20);
      expect(result1.isValid).toBe(true);
      expect(result1.adjustedLength).toBe(15);
      expect(result1.severity).toBe('info');

      const result2 = UserPreferencesService.validateQuizLength(25, 15);
      expect(result2.isValid).toBe(true);
      expect(result2.adjustedLength).toBe(15);
      expect(result2.severity).toBe('warning');
      expect(result2.message).toContain('Only 15 questions available');

      const result3 = UserPreferencesService.validateQuizLength(10, 0);
      expect(result3.isValid).toBe(false);
      expect(result3.adjustedLength).toBe(0);
      expect(result3.severity).toBe('error');
    });

    it('should handle edge cases', () => {
      const result1 = UserPreferencesService.validateQuizLength(0, 10);
      expect(result1.isValid).toBe(false);
      expect(result1.severity).toBe('error');

      const result2 = UserPreferencesService.validateQuizLength(100, 10);
      expect(result2.isValid).toBe(false);
      expect(result2.severity).toBe('error');
    });

    it('should provide info message for limited question pools', () => {
      const result = UserPreferencesService.validateQuizLength(10, 15);
      expect(result.isValid).toBe(true);
      expect(result.severity).toBe('info');
      expect(result.message).toContain('limited questions');
    });
  });

  describe('getValidQuizLengthOptions', () => {
    it('should return valid options based on available questions', () => {
      const options1 = UserPreferencesService.getValidQuizLengthOptions(30);
      expect(options1).toEqual([10, 15, 20, 25, 30]);

      const options2 = UserPreferencesService.getValidQuizLengthOptions(12);
      expect(options2).toEqual([10, 12]);

      const options3 = UserPreferencesService.getValidQuizLengthOptions(0);
      expect(options3).toEqual([]);
    });

    it('should include exact number if not in standard options', () => {
      const options = UserPreferencesService.getValidQuizLengthOptions(18);
      expect(options).toEqual([10, 15, 18]);
    });

    it('should not include unreasonable numbers', () => {
      const options1 = UserPreferencesService.getValidQuizLengthOptions(3);
      expect(options1).toEqual([]);

      const options2 = UserPreferencesService.getValidQuizLengthOptions(100);
      expect(options2).toEqual([10, 15, 20, 25]);
    });
  });

  describe('migrateGuestPreferences', () => {
    it('should migrate preferences from localStorage to database', async () => {
      const { supabase } = await import('@/integrations/supabase/client');
      const guestPrefs = { defaultQuizLength: 20 };
      localStorageMock.getItem.mockReturnValue(JSON.stringify(guestPrefs));
      
      // Mock no existing preferences in database
      vi.mocked(supabase.from).mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: null,
              error: new Error('Not found')
            })
          })
        }),
        upsert: vi.fn().mockResolvedValue({ error: null })
      } as any);

      const result = await UserPreferencesService.migrateGuestPreferences(mockUser);
      expect(result).toBe(true);
    });

    it('should not migrate if user already has preferences', async () => {
      const { supabase } = await import('@/integrations/supabase/client');
      
      const mockUpsert = vi.fn();
      vi.mocked(supabase.from).mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: { setting_key: 'quiz_preferences_test-user-id' },
              error: null
            })
          })
        }),
        upsert: mockUpsert
      } as any);

      const result = await UserPreferencesService.migrateGuestPreferences(mockUser);
      expect(result).toBe(true);
      expect(mockUpsert).not.toHaveBeenCalled();
    });
  });

  describe('resetPreferences', () => {
    it('should reset preferences to defaults', async () => {
      const result = await UserPreferencesService.resetPreferences(null);
      expect(result).toBe(true);
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'quiz_preferences',
        JSON.stringify(DEFAULT_PREFERENCES)
      );
    });
  });
});