import React from 'react';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { 
  AlertCircle, 
  AlertTriangle, 
  CheckCircle2, 
  ChevronDown, 
  ChevronRight,
  Info,
  FileText,
  Download,
  RefreshCw,
  HelpCircle,
  Lightbulb
} from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import type { 
  MultiTopicImportResult, 
  TopicImportResult,
  BatchImportResult,
  BatchImportError,
  TopicBatchResult
} from "@/utils/csv-import";

/**
 * Enhanced error reporting interface for comprehensive import error display
 */
export interface ImportErrorReport {
  // Import results
  importResult?: MultiTopicImportResult;
  batchResult?: BatchImportResult;
  
  // Error categorization
  errorsByTopic: Map<string, ImportTopicErrors>;
  globalErrors: ImportError[];
  
  // Statistics
  statistics: ImportStatistics;
  
  // Suggestions and recovery options
  suggestions: ImportSuggestion[];
  recoveryOptions: RecoveryOption[];
}

/**
 * Errors grouped by topic for better organization
 */
export interface ImportTopicErrors {
  topicId: string;
  topicName: string;
  isNewTopic: boolean;
  errors: ImportError[];
  warnings: ImportError[];
  questionsProcessed: number;
  questionsSuccessful: number;
  questionsFailed: number;
}

/**
 * Enhanced error information with actionable suggestions
 */
export interface ImportError {
  id: string;
  row?: number;
  field?: string;
  message: string;
  severity: 'error' | 'warning' | 'info';
  category: 'topic' | 'question' | 'format' | 'duplicate' | 'system';
  suggestion?: string;
  actionable: boolean;
  canRetry: boolean;
}

/**
 * Import statistics for comprehensive reporting
 */
export interface ImportStatistics {
  totalRows: number;
  totalTopics: number;
  newTopicsCreated: number;
  questionsImported: number;
  questionsFailed: number;
  errorsCount: number;
  warningsCount: number;
  duration?: number;
  successRate: number;
}

/**
 * Actionable suggestions for resolving import issues
 */
export interface ImportSuggestion {
  id: string;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  category: 'fix-required' | 'improvement' | 'optimization';
  actionText?: string;
  onAction?: () => void;
}

/**
 * Recovery options for failed imports
 */
export interface RecoveryOption {
  id: string;
  title: string;
  description: string;
  actionText: string;
  onAction: () => void;
  destructive?: boolean;
}

/**
 * Props for the ImportErrorReporting component
 */
export interface ImportErrorReportingProps {
  report: ImportErrorReport;
  onRetry?: () => void;
  onDownloadErrorReport?: () => void;
  onFixAndRetry?: (fixedData: any) => void;
  className?: string;
}

/**
 * Comprehensive error reporting component for multi-topic CSV imports
 */
export function ImportErrorReporting({ 
  report, 
  onRetry, 
  onDownloadErrorReport,
  onFixAndRetry,
  className 
}: ImportErrorReportingProps) {
  const [expandedTopics, setExpandedTopics] = React.useState<Set<string>>(new Set());
  const [showSuggestions, setShowSuggestions] = React.useState(true);
  const [showStatistics, setShowStatistics] = React.useState(true);

  const toggleTopicExpansion = (topicId: string) => {
    const newExpanded = new Set(expandedTopics);
    if (newExpanded.has(topicId)) {
      newExpanded.delete(topicId);
    } else {
      newExpanded.add(topicId);
    }
    setExpandedTopics(newExpanded);
  };

  const getSeverityIcon = (severity: ImportError['severity']) => {
    switch (severity) {
      case 'error':
        return <AlertCircle className="h-4 w-4 text-destructive" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'info':
        return <Info className="h-4 w-4 text-blue-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getSeverityColor = (severity: ImportError['severity']) => {
    switch (severity) {
      case 'error':
        return 'border-destructive bg-destructive/5';
      case 'warning':
        return 'border-yellow-500 bg-yellow-50';
      case 'info':
        return 'border-blue-500 bg-blue-50';
      default:
        return 'border-muted bg-muted/5';
    }
  };

  const getPriorityColor = (priority: ImportSuggestion['priority']) => {
    switch (priority) {
      case 'high':
        return 'border-red-500 bg-red-50';
      case 'medium':
        return 'border-yellow-500 bg-yellow-50';
      case 'low':
        return 'border-blue-500 bg-blue-50';
      default:
        return 'border-muted bg-muted/5';
    }
  };

  const renderOverallStatus = () => {
    const { statistics } = report;
    const hasErrors = statistics.errorsCount > 0;
    const hasWarnings = statistics.warningsCount > 0;
    const isSuccess = statistics.successRate > 0;

    let statusIcon;
    let statusTitle;
    let statusVariant: "default" | "destructive" = "default";

    if (hasErrors) {
      statusIcon = <AlertCircle className="h-5 w-5 text-destructive" />;
      statusTitle = statistics.successRate > 0 ? "Import Completed with Errors" : "Import Failed";
      statusVariant = "destructive";
    } else if (hasWarnings) {
      statusIcon = <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      statusTitle = "Import Completed with Warnings";
    } else if (isSuccess) {
      statusIcon = <CheckCircle2 className="h-5 w-5 text-green-600" />;
      statusTitle = "Import Completed Successfully";
    } else {
      statusIcon = <AlertCircle className="h-5 w-5 text-muted-foreground" />;
      statusTitle = "Import Status Unknown";
    }

    return (
      <Alert variant={statusVariant} className="mb-6">
        {statusIcon}
        <AlertTitle className="flex items-center gap-2">
          {statusTitle}
          {statistics.duration && (
            <Badge variant="outline" className="ml-2">
              {Math.round(statistics.duration / 1000)}s
            </Badge>
          )}
        </AlertTitle>
        <AlertDescription>
          <div className="mt-2 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">Questions Imported</p>
              <p className="font-medium text-green-600">{statistics.questionsImported}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Questions Failed</p>
              <p className="font-medium text-destructive">{statistics.questionsFailed}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Topics Processed</p>
              <p className="font-medium">{statistics.totalTopics}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Success Rate</p>
              <p className="font-medium">{Math.round(statistics.successRate * 100)}%</p>
            </div>
          </div>
          
          {statistics.newTopicsCreated > 0 && (
            <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded text-sm text-blue-800">
              ✅ Created {statistics.newTopicsCreated} new topic(s)
            </div>
          )}
        </AlertDescription>
      </Alert>
    );
  };

  const renderStatistics = () => {
    if (!showStatistics) return null;

    const { statistics } = report;

    return (
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Import Statistics
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowStatistics(false)}
            >
              <ChevronDown className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="text-center p-3 bg-muted/50 rounded">
              <p className="text-2xl font-bold">{statistics.totalRows}</p>
              <p className="text-sm text-muted-foreground">Total Rows</p>
            </div>
            <div className="text-center p-3 bg-green-50 border border-green-200 rounded">
              <p className="text-2xl font-bold text-green-600">{statistics.questionsImported}</p>
              <p className="text-sm text-green-700">Imported</p>
            </div>
            <div className="text-center p-3 bg-red-50 border border-red-200 rounded">
              <p className="text-2xl font-bold text-red-600">{statistics.questionsFailed}</p>
              <p className="text-sm text-red-700">Failed</p>
            </div>
            <div className="text-center p-3 bg-blue-50 border border-blue-200 rounded">
              <p className="text-2xl font-bold text-blue-600">{statistics.totalTopics}</p>
              <p className="text-sm text-blue-700">Topics</p>
            </div>
            <div className="text-center p-3 bg-purple-50 border border-purple-200 rounded">
              <p className="text-2xl font-bold text-purple-600">{statistics.newTopicsCreated}</p>
              <p className="text-sm text-purple-700">New Topics</p>
            </div>
            <div className="text-center p-3 bg-yellow-50 border border-yellow-200 rounded">
              <p className="text-2xl font-bold text-yellow-600">{Math.round(statistics.successRate * 100)}%</p>
              <p className="text-sm text-yellow-700">Success Rate</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderGlobalErrors = () => {
    if (report.globalErrors.length === 0) return null;

    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-destructive" />
            Global Issues ({report.globalErrors.length})
          </CardTitle>
          <CardDescription>
            Issues that affect the entire import process
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {report.globalErrors.map((error) => (
              <div
                key={error.id}
                className={`p-3 rounded border ${getSeverityColor(error.severity)}`}
              >
                <div className="flex items-start gap-3">
                  {getSeverityIcon(error.severity)}
                  <div className="flex-1">
                    <p className="font-medium">{error.message}</p>
                    {error.suggestion && (
                      <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-sm text-blue-800">
                        <div className="flex items-start gap-2">
                          <Lightbulb className="h-4 w-4 mt-0.5 flex-shrink-0" />
                          <p>{error.suggestion}</p>
                        </div>
                      </div>
                    )}
                    {error.row && (
                      <p className="text-sm text-muted-foreground mt-1">
                        Row {error.row}
                      </p>
                    )}
                  </div>
                  <div className="flex gap-1">
                    <Badge variant="outline" className="text-xs">
                      {error.category}
                    </Badge>
                    {error.actionable && (
                      <Badge variant="secondary" className="text-xs">
                        Actionable
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderTopicErrors = () => {
    if (report.errorsByTopic.size === 0) return null;

    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Topic-Specific Issues
          </CardTitle>
          <CardDescription>
            Issues grouped by topic for easier resolution
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from(report.errorsByTopic.entries()).map(([topicId, topicErrors]) => {
              const isExpanded = expandedTopics.has(topicId);
              const hasErrors = topicErrors.errors.length > 0;
              const hasWarnings = topicErrors.warnings.length > 0;
              const totalIssues = topicErrors.errors.length + topicErrors.warnings.length;

              return (
                <div key={topicId} className="border rounded-lg">
                  <Collapsible>
                    <CollapsibleTrigger asChild>
                      <Button
                        variant="ghost"
                        className="w-full justify-between p-4 h-auto"
                        onClick={() => toggleTopicExpansion(topicId)}
                      >
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-2">
                            {isExpanded ? (
                              <ChevronDown className="h-4 w-4" />
                            ) : (
                              <ChevronRight className="h-4 w-4" />
                            )}
                            <h4 className="font-medium">{topicErrors.topicName}</h4>
                            {topicErrors.isNewTopic && (
                              <Badge variant="outline" className="text-xs">
                                New
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="text-sm text-muted-foreground">
                            {topicErrors.questionsSuccessful}/{topicErrors.questionsProcessed} questions
                          </div>
                          {hasErrors && (
                            <Badge variant="destructive" className="text-xs">
                              {topicErrors.errors.length} errors
                            </Badge>
                          )}
                          {hasWarnings && (
                            <Badge variant="secondary" className="text-xs">
                              {topicErrors.warnings.length} warnings
                            </Badge>
                          )}
                        </div>
                      </Button>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <div className="px-4 pb-4">
                        <Separator className="mb-4" />
                        
                        {/* Topic Statistics */}
                        <div className="grid grid-cols-3 gap-4 mb-4 text-sm">
                          <div className="text-center p-2 bg-green-50 border border-green-200 rounded">
                            <p className="font-medium text-green-600">{topicErrors.questionsSuccessful}</p>
                            <p className="text-green-700">Successful</p>
                          </div>
                          <div className="text-center p-2 bg-red-50 border border-red-200 rounded">
                            <p className="font-medium text-red-600">{topicErrors.questionsFailed}</p>
                            <p className="text-red-700">Failed</p>
                          </div>
                          <div className="text-center p-2 bg-muted/50 rounded">
                            <p className="font-medium">{totalIssues}</p>
                            <p className="text-muted-foreground">Issues</p>
                          </div>
                        </div>

                        {/* Errors */}
                        {topicErrors.errors.length > 0 && (
                          <div className="mb-4">
                            <h5 className="font-medium text-destructive mb-2 flex items-center gap-2">
                              <AlertCircle className="h-4 w-4" />
                              Errors ({topicErrors.errors.length})
                            </h5>
                            <div className="space-y-2">
                              {topicErrors.errors.map((error) => (
                                <div
                                  key={error.id}
                                  className="p-3 bg-destructive/5 border border-destructive/20 rounded"
                                >
                                  <div className="flex items-start gap-2">
                                    <AlertCircle className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
                                    <div className="flex-1">
                                      <p className="text-sm font-medium text-destructive">
                                        {error.row && `Row ${error.row}: `}{error.message}
                                      </p>
                                      {error.suggestion && (
                                        <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs text-blue-800">
                                          <div className="flex items-start gap-1">
                                            <Lightbulb className="h-3 w-3 mt-0.5 flex-shrink-0" />
                                            <p>{error.suggestion}</p>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                    {error.actionable && (
                                      <Badge variant="outline" className="text-xs">
                                        Fixable
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Warnings */}
                        {topicErrors.warnings.length > 0 && (
                          <div>
                            <h5 className="font-medium text-yellow-600 mb-2 flex items-center gap-2">
                              <AlertTriangle className="h-4 w-4" />
                              Warnings ({topicErrors.warnings.length})
                            </h5>
                            <div className="space-y-2">
                              {topicErrors.warnings.map((warning) => (
                                <div
                                  key={warning.id}
                                  className="p-3 bg-yellow-50 border border-yellow-200 rounded"
                                >
                                  <div className="flex items-start gap-2">
                                    <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                                    <div className="flex-1">
                                      <p className="text-sm font-medium text-yellow-800">
                                        {warning.row && `Row ${warning.row}: `}{warning.message}
                                      </p>
                                      {warning.suggestion && (
                                        <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs text-blue-800">
                                          <div className="flex items-start gap-1">
                                            <Lightbulb className="h-3 w-3 mt-0.5 flex-shrink-0" />
                                            <p>{warning.suggestion}</p>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </CollapsibleContent>
                  </Collapsible>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderSuggestions = () => {
    if (!showSuggestions || report.suggestions.length === 0) return null;

    return (
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5 text-yellow-600" />
              Suggestions for Improvement
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSuggestions(false)}
            >
              <ChevronDown className="h-4 w-4" />
            </Button>
          </div>
          <CardDescription>
            Actionable recommendations to improve your import process
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {report.suggestions.map((suggestion) => (
              <div
                key={suggestion.id}
                className={`p-4 rounded border ${getPriorityColor(suggestion.priority)}`}
              >
                <div className="flex items-start justify-between gap-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h5 className="font-medium">{suggestion.title}</h5>
                      <Badge 
                        variant={suggestion.priority === 'high' ? 'destructive' : 'secondary'}
                        className="text-xs"
                      >
                        {suggestion.priority}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {suggestion.category}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{suggestion.description}</p>
                  </div>
                  {suggestion.onAction && suggestion.actionText && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={suggestion.onAction}
                    >
                      {suggestion.actionText}
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderRecoveryOptions = () => {
    if (report.recoveryOptions.length === 0) return null;

    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5 text-blue-600" />
            Recovery Options
          </CardTitle>
          <CardDescription>
            Actions you can take to resolve import issues
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-2">
            {report.recoveryOptions.map((option) => (
              <div
                key={option.id}
                className="p-4 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <h5 className="font-medium mb-2">{option.title}</h5>
                <p className="text-sm text-muted-foreground mb-3">{option.description}</p>
                <Button
                  size="sm"
                  variant={option.destructive ? "destructive" : "default"}
                  onClick={option.onAction}
                  className="w-full"
                >
                  {option.actionText}
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderActionButtons = () => {
    const hasActionableErrors = report.globalErrors.some(e => e.actionable) ||
      Array.from(report.errorsByTopic.values()).some(te => 
        te.errors.some(e => e.actionable) || te.warnings.some(w => w.actionable)
      );

    return (
      <div className="flex flex-wrap gap-3">
        {onRetry && (
          <Button onClick={onRetry} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Retry Import
          </Button>
        )}
        
        {onDownloadErrorReport && (
          <Button variant="outline" onClick={onDownloadErrorReport} className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Download Error Report
          </Button>
        )}

        {hasActionableErrors && onFixAndRetry && (
          <Button variant="secondary" onClick={() => onFixAndRetry(null)} className="flex items-center gap-2">
            <HelpCircle className="h-4 w-4" />
            Fix & Retry
          </Button>
        )}
      </div>
    );
  };

  return (
    <div className={className}>
      {renderOverallStatus()}
      {renderStatistics()}
      {renderGlobalErrors()}
      {renderTopicErrors()}
      {renderSuggestions()}
      {renderRecoveryOptions()}
      {renderActionButtons()}
    </div>
  );
}