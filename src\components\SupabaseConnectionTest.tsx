import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';

const SupabaseConnectionTest: React.FC = () => {
  const [testResult, setTestResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const testConnection = async () => {
    setIsLoading(true);
    setTestResult(null);

    try {
      console.log('🔗 Testing Supabase connection...');
      
      // Test 1: Basic client initialization
      const clientTest = {
        supabaseUrl: supabase.supabaseUrl,
        supabaseKey: supabase.supabaseKey ? 'SET' : 'NOT SET',
        clientInitialized: !!supabase
      };

      // Test 2: Simple query to test connection
      console.log('📡 Testing basic query...');
      const { data, error } = await supabase
        .from('user_profiles')
        .select('count')
        .limit(1);

      // Test 3: Auth settings
      console.log('🔐 Testing auth settings...');
      const authTest = await fetch(`${supabase.supabaseUrl}/auth/v1/settings`, {
        headers: {
          'apikey': supabase.supabaseKey,
          'Authorization': `Bearer ${supabase.supabaseKey}`
        }
      });

      const authSettings = authTest.ok ? await authTest.json() : null;

      const result = {
        timestamp: new Date().toISOString(),
        client: clientTest,
        query: {
          success: !error,
          error: error?.message,
          data: data ? 'Data received' : 'No data'
        },
        auth: {
          status: authTest.status,
          settings: authSettings
        },
        network: {
          userAgent: navigator.userAgent,
          online: navigator.onLine,
          connection: (navigator as any).connection?.effectiveType || 'unknown'
        }
      };

      setTestResult(result);
      console.log('🧪 Test result:', result);

    } catch (error) {
      console.error('❌ Connection test failed:', error);
      setTestResult({
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = () => {
    if (!testResult) return <Badge variant="secondary">Not Tested</Badge>;
    if (testResult.error) return <Badge variant="destructive">Failed</Badge>;
    if (testResult.query?.success) return <Badge variant="default" className="bg-green-500">Connected</Badge>;
    return <Badge variant="destructive">Connection Issues</Badge>;
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          🔗 Supabase Connection Test
          {getStatusBadge()}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button 
          onClick={testConnection} 
          disabled={isLoading}
          className="w-full"
        >
          {isLoading ? 'Testing Connection...' : 'Test Supabase Connection'}
        </Button>

        {testResult && (
          <div className="space-y-4">
            {testResult.error ? (
              <div className="bg-red-50 border border-red-200 rounded p-3">
                <h3 className="font-semibold text-red-700 mb-2">Connection Error</h3>
                <p className="text-red-600 text-sm">{testResult.error}</p>
                {testResult.stack && (
                  <details className="mt-2">
                    <summary className="text-red-600 text-sm cursor-pointer">Stack Trace</summary>
                    <pre className="text-xs text-red-500 mt-1 overflow-auto">{testResult.stack}</pre>
                  </details>
                )}
              </div>
            ) : (
              <div className="space-y-3">
                <div className="bg-blue-50 border border-blue-200 rounded p-3">
                  <h3 className="font-semibold text-blue-700 mb-2">Client Configuration</h3>
                  <div className="text-sm space-y-1">
                    <p><strong>URL:</strong> {testResult.client?.supabaseUrl}</p>
                    <p><strong>API Key:</strong> {testResult.client?.supabaseKey}</p>
                    <p><strong>Client:</strong> {testResult.client?.clientInitialized ? '✅ Initialized' : '❌ Not Initialized'}</p>
                  </div>
                </div>

                <div className="bg-green-50 border border-green-200 rounded p-3">
                  <h3 className="font-semibold text-green-700 mb-2">Database Query Test</h3>
                  <div className="text-sm space-y-1">
                    <p><strong>Success:</strong> {testResult.query?.success ? '✅ Yes' : '❌ No'}</p>
                    {testResult.query?.error && <p><strong>Error:</strong> {testResult.query.error}</p>}
                    <p><strong>Data:</strong> {testResult.query?.data}</p>
                  </div>
                </div>

                <div className="bg-purple-50 border border-purple-200 rounded p-3">
                  <h3 className="font-semibold text-purple-700 mb-2">Auth Service Test</h3>
                  <div className="text-sm space-y-1">
                    <p><strong>Status:</strong> {testResult.auth?.status}</p>
                    <p><strong>Email Auth:</strong> {testResult.auth?.settings?.external?.email ? '✅ Enabled' : '❌ Disabled'}</p>
                    <p><strong>Signup:</strong> {testResult.auth?.settings?.disable_signup ? '❌ Disabled' : '✅ Enabled'}</p>
                  </div>
                </div>

                <div className="bg-gray-50 border border-gray-200 rounded p-3">
                  <h3 className="font-semibold text-gray-700 mb-2">Network Information</h3>
                  <div className="text-sm space-y-1">
                    <p><strong>Online:</strong> {testResult.network?.online ? '✅ Yes' : '❌ No'}</p>
                    <p><strong>Connection:</strong> {testResult.network?.connection}</p>
                  </div>
                </div>
              </div>
            )}

            <div className="text-xs text-gray-500">
              Test completed at: {testResult.timestamp}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SupabaseConnectionTest;
